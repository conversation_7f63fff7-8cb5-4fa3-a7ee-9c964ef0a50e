import { useState, useEffect, useRef } from 'react';
import { 
  Phone, PhoneOff, Clock, User, CreditCard, Crown, Volume2, VolumeX, 
  MicOff, Mic, ChevronLeft, Plus, Brain, Ear, X, Play, Pause, Signal,
  MessageSquare, Heart, Share2, Bookmark, Star, Zap, Sparkles
} from 'lucide-react';
import characterAvatar from './assets/沈婉.png';

const VoiceCallPage = () => {
  // 用户状态相关
  const [isVIP, setIsVIP] = useState(false);
  const [remainingTrialTime, setRemainingTrialTime] = useState(60); // 分钟
  const [remainingPaidTime, setRemainingPaidTime] = useState(0); // 分钟
  const [remainingVIPTime, setRemainingVIPTime] = useState(0); // 分钟
  const [hasUsedTrial, setHasUsedTrial] = useState(false);
  
  // 通话状态相关
  const [isInCall, setIsInCall] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(true); // 默认开启扬声器
  const [isCharacterSpeaking, setIsCharacterSpeaking] = useState(false);
  const [isUserSpeaking, setIsUserSpeaking] = useState(false);
  const [isCharacterThinking, setIsCharacterThinking] = useState(false);
  const [networkQuality, setNetworkQuality] = useState(4); // 1-5，5为最佳
  const [isReconnecting, setIsReconnecting] = useState(false);
  
  // 开场白和推荐话题
  const [isPlayingGreeting, setIsPlayingGreeting] = useState(false);
  const [selectedTopic, setSelectedTopic] = useState(null);
  
  // 界面控制
  const [showPurchaseDrawer, setShowPurchaseDrawer] = useState(false);
  const [showVIPDrawer, setShowVIPDrawer] = useState(false);
  const [showTimeUpNotice, setShowTimeUpNotice] = useState(false);
  const [showNetworkWarning, setShowNetworkWarning] = useState(false);
  
  // 角色信息 - 更新为实际图片和更丰富的角色设定
  const character = {
    name: "沈婉",
    avatar: characterAvatar,
    description: "你的AI虚拟伙伴，随时陪你聊天解闷",
    greeting: "嗨，很高兴见到你！我是沈婉，我喜欢听音乐、阅读和了解新鲜事物。今天想聊点什么呢？",
    personality: "活泼、温柔、善解人意",
    topics: [
      { id: 1, title: "聊聊你最近看的电影吧", icon: "🎬" },
      { id: 2, title: "最近有什么让你开心的事？", icon: "😊" },
      { id: 3, title: "想听听我能给你的建议吗？", icon: "💡" },
      { id: 4, title: "讲个有趣的故事给我听吧", icon: "📚" }
    ]
  };
  
  // 商品定价
  const pricing = {
    coinPerHour: 30,
    vipMonthlyPrice: 68,
    vipMonthlyHours: 10
  };

  // 语音播放器引用
  const audioRef = useRef(null);

  // 初始化模拟音频
  useEffect(() => {
    // 模拟创建音频元素（在实际应用中，这里会链接到真实的TTS服务）
    audioRef.current = new Audio();
    audioRef.current.onended = () => {
      setIsPlayingGreeting(false);
      setIsCharacterSpeaking(false);
    };
    
    // 模拟网络质量变化
    const networkInterval = setInterval(() => {
      // 随机模拟网络波动，但保持在较高水平
      if (isInCall) {
        const quality = Math.max(2, Math.floor(Math.random() * 5) + 1);
        setNetworkQuality(quality);
        
        // 偶尔模拟短暂的重连
        if (Math.random() < 0.05) {
          setIsReconnecting(true);
          setShowNetworkWarning(true);
          setTimeout(() => {
            setIsReconnecting(false);
            setShowNetworkWarning(false);
          }, 2000);
        }
      }
    }, 10000);
    
    return () => {
      clearInterval(networkInterval);
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [isInCall]);

  // 模拟通话计时器
  useEffect(() => {
    let timer = null;
    
    if (isInCall) {
      timer = setInterval(() => {
        setCallDuration(prev => prev + 1);
        
        // 减少剩余时间
        if (isVIP && remainingVIPTime > 0) {
          setRemainingVIPTime(prev => Math.max(0, prev - 1/60));
        } else if (remainingPaidTime > 0) {
          setRemainingPaidTime(prev => Math.max(0, prev - 1/60));
        } else if (!hasUsedTrial && remainingTrialTime > 0) {
          setRemainingTrialTime(prev => Math.max(0, prev - 1/60));
        }
        
        // 检查是否时间用完
        const totalRemainingTime = getTotalRemainingMinutes();
        if (totalRemainingTime <= 0 && isInCall) {
          endCall();
          setShowTimeUpNotice(true);
          // 提前30秒提醒
        } else if (totalRemainingTime <= 0.5 && !showTimeUpNotice) {
          setShowTimeUpNotice(true);
        }
      }, 1000);
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isInCall, remainingTrialTime, remainingPaidTime, remainingVIPTime, hasUsedTrial, isVIP]);
  
  // 模拟对话效果
  useEffect(() => {
    let speakingInterval;
    
    if (isInCall) {
      // 模拟对话交替
      speakingInterval = setInterval(() => {
        // 随机模拟角色说话、用户说话和角色思考的状态
        const randomAction = Math.random();
        
        if (randomAction < 0.3) {
          // 角色说话
          setIsCharacterSpeaking(true);
          setIsUserSpeaking(false);
          setIsCharacterThinking(false);
        } else if (randomAction < 0.6) {
          // 用户说话
          setIsCharacterSpeaking(false);
          setIsUserSpeaking(true);
          setIsCharacterThinking(false);
        } else if (randomAction < 0.85) {
          // 角色思考
          setIsCharacterSpeaking(false);
          setIsUserSpeaking(false);
          setIsCharacterThinking(true);
        } else {
          // 短暂停顿
          setIsCharacterSpeaking(false);
          setIsUserSpeaking(false);
          setIsCharacterThinking(false);
        }
      }, 3000);
    } else {
      setIsCharacterSpeaking(false);
      setIsUserSpeaking(false);
      setIsCharacterThinking(false);
    }
    
    return () => {
      if (speakingInterval) clearInterval(speakingInterval);
    };
  }, [isInCall]);
  
  // 获取总的剩余分钟数
  const getTotalRemainingMinutes = () => {
    if (isVIP && remainingVIPTime > 0) {
      return remainingVIPTime;
    } else if (remainingPaidTime > 0) {
      return remainingPaidTime;
    } else if (!hasUsedTrial && remainingTrialTime > 0) {
      return remainingTrialTime;
    }
    return 0;
  };
  
  // 获取格式化的剩余时间
  const getFormattedRemainingTime = () => {
    const minutes = Math.floor(getTotalRemainingMinutes());
    const seconds = Math.floor((getTotalRemainingMinutes() - minutes) * 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  // 获取格式化的通话时长
  const getFormattedCallDuration = () => {
    const minutes = Math.floor(callDuration / 60);
    const seconds = callDuration % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  // 开始通话
  const startCall = () => {
    setIsInCall(true);
    setCallDuration(0);
    setIsSpeakerOn(true);
    setSelectedTopic(null);
    setShowTimeUpNotice(false);
    
    // 如果是首次使用，标记试用已使用
    if (!hasUsedTrial && remainingTrialTime > 0 && !isVIP && remainingPaidTime <= 0) {
      setHasUsedTrial(true);
    }
  };
  
  // 结束通话
  const endCall = () => {
    setIsInCall(false);
    setIsCharacterSpeaking(false);
    setIsUserSpeaking(false);
    setIsCharacterThinking(false);
  };
  
  // 购买通话时间
  const purchaseTime = (hours) => {
    setRemainingPaidTime(prev => prev + hours * 60);
    setShowPurchaseDrawer(false);
    
    // 显示成功提示
    const successMessage = document.createElement('div');
    successMessage.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-green-100 text-green-800 px-4 py-2 rounded-full shadow-md text-sm flex items-center';
    successMessage.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><polyline points="20 6 9 17 4 12"></polyline></svg> 购买成功，已添加 ${hours} 小时通话时长`;
    document.body.appendChild(successMessage);
    setTimeout(() => {
      document.body.removeChild(successMessage);
    }, 3000);
  };
  
  // 成为VIP会员
  const becomeVIP = () => {
    setIsVIP(true);
    setRemainingVIPTime(pricing.vipMonthlyHours * 60);
    setShowVIPDrawer(false);
    
    // 显示成功提示
    const successMessage = document.createElement('div');
    successMessage.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full shadow-md text-sm flex items-center';
    successMessage.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg> 恭喜！VIP会员开通成功`;
    document.body.appendChild(successMessage);
    setTimeout(() => {
      document.body.removeChild(successMessage);
    }, 3000);
  };
  
  // 切换麦克风状态
  const toggleMute = () => {
    setIsMuted(prev => !prev);
  };
  
  // 切换扬声器状态
  const toggleSpeaker = () => {
    setIsSpeakerOn(prev => !prev);
  };
  
  // 播放开场白
  const toggleGreeting = () => {
    if (isPlayingGreeting) {
      // 暂停播放
      if (audioRef.current) {
        audioRef.current.pause();
      }
      setIsPlayingGreeting(false);
      setIsCharacterSpeaking(false);
    } else {
      // 开始播放
      setIsPlayingGreeting(true);
      setIsCharacterSpeaking(true);
      
      // 模拟播放音频
      setTimeout(() => {
        setIsPlayingGreeting(false);
        setIsCharacterSpeaking(false);
      }, 5000);
    }
  };
  
  // 选择话题
  const selectTopic = (topic) => {
    setSelectedTopic(topic);
    // 在实际应用中，这里可以用于预设对话内容
  };
  
  // 开始特定话题的通话
  const startTopicCall = () => {
    startCall();
    // 实际应用中，这里可以将选定的话题传递给AI
  };
  
  // 状态指示文本
  const getStatusText = () => {
    if (isInCall) {
      if (isReconnecting) {
        return '正在重新连接...';
      }
      return '通话中';
    }
    
    if (getTotalRemainingMinutes() <= 0) {
      return '通话时间已用完';
    }
    
    if (isVIP) {
      return 'VIP会员';
    }
    
    if (!hasUsedTrial && remainingTrialTime > 0) {
      return '免费体验期';
    }
    
    if (remainingPaidTime > 0) {
      return '已购时长';
    }
    
    return '开始体验语音通话';
  };
  
  // 获取网络质量指示
  const getNetworkQualityDisplay = () => {
    if (!isInCall) return null;
    
    return (
      <div className="absolute top-16 right-4 flex items-center space-x-1 bg-white/80 px-2 py-1 rounded-full text-xs shadow-sm">
        <Signal size={14} className={`
          ${networkQuality >= 4 ? 'text-green-500' : 
            networkQuality >= 3 ? 'text-yellow-500' : 'text-red-500'}
        `} />
        <span className={`
          ${networkQuality >= 4 ? 'text-green-600' : 
            networkQuality >= 3 ? 'text-yellow-600' : 'text-red-600'}
        `}>
          {networkQuality >= 4 ? '良好' : 
            networkQuality >= 3 ? '一般' : '较差'}
        </span>
      </div>
    );
  };
  
  return (
    <div className="mx-auto max-w-md h-screen overflow-hidden flex flex-col bg-gradient-to-b from-indigo-50 to-purple-50 text-gray-800 border border-gray-200 shadow-xl">
      {/* 头部信息栏 */}
      <div className="flex items-center justify-between p-4 bg-white shadow-sm">
        <div className="flex items-center space-x-3">
          <button className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100">
            <ChevronLeft size={20} className="text-gray-600" />
          </button>
          <div>
            <h2 className="font-bold text-lg">{character.name}</h2>
            <p className="text-xs text-gray-500">{getStatusText()}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {isVIP && (
            <div className="flex items-center bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs">
              <Crown size={14} className="mr-1" />
              <span>VIP</span>
            </div>
          )}
          <div className="flex items-center">
            <div className="flex items-center bg-indigo-100 text-indigo-800 px-2 py-1 rounded-l-full text-xs">
              <Clock size={14} className="mr-1" />
              <span>{getFormattedRemainingTime()}</span>
            </div>
            <button 
              onClick={() => setShowPurchaseDrawer(true)}
              className="flex items-center justify-center bg-indigo-500 text-white px-2 py-1 rounded-r-full text-xs"
            >
              <Plus size={14} />
            </button>
          </div>
        </div>
      </div>
      
      {/* 主内容区 */}
      <div className="flex-1 flex flex-col items-center justify-center p-6 relative">
        {/* 网络质量指示器 */}
        {getNetworkQualityDisplay()}
        
        {/* 时间不足警告 - 嵌入式而非模态框 */}
        {showTimeUpNotice && !showPurchaseDrawer && !showVIPDrawer && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-red-50 text-red-600 px-4 py-2 rounded-lg shadow-md text-sm flex items-center z-10 animate-pulse w-4/5 justify-center">
            <Clock size={16} className="mr-2" />
            <span>通话时间已用完，请及时续费</span>
            <button 
              onClick={() => setShowTimeUpNotice(false)}
              className="ml-2 text-red-400 hover:text-red-600"
            >
              <X size={14} />
            </button>
          </div>
        )}
        
        {/* 网络警告 - 嵌入式 */}
        {showNetworkWarning && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-yellow-50 text-yellow-600 px-4 py-2 rounded-lg shadow-md text-sm flex items-center z-10 animate-pulse w-4/5 justify-center">
            <Signal size={16} className="mr-2" />
            <span>网络连接不稳定，正在重新连接...</span>
          </div>
        )}
        
        {/* 通话状态和头像 */}
        <div className={`relative mb-8 transition-all duration-500 ${isInCall ? 'scale-110' : 'scale-100'}`}>
          <img 
            src={character.avatar} 
            alt={character.name} 
            className={`w-40 h-40 rounded-full object-cover border-4 ${
              isInCall ? 'border-green-400 shadow-lg shadow-green-200' : 'border-purple-200'
            }`}
          />
          
          {/* 角色说话时的波纹动画 */}
          {(isInCall || isPlayingGreeting) && isCharacterSpeaking && (
            <div className="absolute -inset-4 flex items-center justify-center">
              <div className="absolute w-48 h-48 rounded-full border-4 border-green-300 opacity-30 animate-ping"></div>
              <div className="absolute w-52 h-52 rounded-full border-4 border-green-200 opacity-20 animate-ping animation-delay-500"></div>
              <div className="absolute w-56 h-56 rounded-full border-4 border-green-100 opacity-10 animate-ping animation-delay-1000"></div>
            </div>
          )}
          
          {/* 用户说话时的波纹动画 - 从底部向上 */}
          {isInCall && isUserSpeaking && (
            <div className="absolute -bottom-4 left-0 right-0 flex items-center justify-center">
              <div className="absolute w-40 h-40 rounded-full border-4 border-blue-300 opacity-30 animate-ping"></div>
              <div className="absolute w-32 h-32 rounded-full border-4 border-blue-400 opacity-20 animate-ping animation-delay-300"></div>
              <div className="absolute w-24 h-24 rounded-full border-4 border-blue-500 opacity-10 animate-ping animation-delay-600"></div>
            </div>
          )}
          
          {/* 角色思考时的动画 */}
          {isInCall && isCharacterThinking && (
            <div className="absolute -inset-2 flex items-center justify-center">
              <div className="absolute w-44 h-44 rounded-full border-2 border-purple-200 border-dashed animate-spin" style={{ animationDuration: '8s' }}></div>
              <div className="absolute w-48 h-48 rounded-full border-2 border-purple-100 border-dashed animate-spin" style={{ animationDuration: '12s', animationDirection: 'reverse' }}></div>
            </div>
          )}
          
          {/* 角色状态指示 */}
          <div className={`absolute bottom-0 right-0 w-10 h-10 rounded-full flex items-center justify-center ${
            isCharacterSpeaking ? 'bg-green-500' : (isCharacterThinking ? 'bg-purple-400' : (isUserSpeaking ? 'bg-blue-400' : (isInCall ? 'bg-green-300' : 'bg-gray-200')))
          }`}>
            {isCharacterSpeaking && <Volume2 size={20} className="text-white" />}
            {isUserSpeaking && <Ear size={20} className="text-white" />}
            {isCharacterThinking && <Brain size={20} className="text-white" />}
            {!isCharacterSpeaking && !isUserSpeaking && !isCharacterThinking && <Volume2 size={20} className="text-white opacity-60" />}
          </div>
          
          {/* 非通话状态下的开场白播放按钮 */}
          {!isInCall && (
            <button 
              onClick={toggleGreeting}
              className={`absolute top-0 left-0 w-10 h-10 rounded-full flex items-center justify-center bg-white shadow-md ${isPlayingGreeting ? 'text-red-500' : 'text-purple-600'}`}
            >
              {isPlayingGreeting ? <Pause size={20} /> : <Play size={20} />}
            </button>
          )}
        </div>
        
        {/* 通话状态指示 */}
        {isInCall && (
          <div className="text-center mb-6">
            <p className="text-lg font-medium text-gray-700">与 {character.name} 通话中</p>
            <p className="text-sm text-gray-500 mb-2">通话时间: {getFormattedCallDuration()}</p>
            
            {/* 增加当前话题显示 */}
            {selectedTopic && (
              <div className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-xs inline-flex items-center">
                <span className="mr-1">{selectedTopic.icon}</span>
                {selectedTopic.title}
              </div>
            )}
          </div>
        )}
        
        {/* 功能介绍和角色开场白 (非通话状态时显示) */}
        {!isInCall && (
          <div className="text-center mb-4 max-w-xs">
            {isPlayingGreeting ? (
              // 播放开场白状态
              <div className="bg-white rounded-xl p-4 shadow-md mb-4">
                <p className="text-gray-700 text-sm italic">"{character.greeting}"</p>
              </div>
            ) : (
              // 默认展示
              <div>
                <h3 className="font-bold text-xl mb-2 text-purple-800">语音通话体验</h3>
                <p className="text-gray-600 mb-3">{character.description}</p>
                
                {/* 角色个性标签 */}
                <div className="flex justify-center space-x-2 mb-4">
                  {character.personality.split('、').map((trait, index) => (
                    <span key={index} className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full text-xs">
                      {trait}
                    </span>
                  ))}
                </div>
              </div>
            )}
            
                          {/* 推荐话题 */}
            <div className="bg-white rounded-lg shadow-md p-3 mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <MessageSquare size={14} className="mr-1 text-purple-600" />
                推荐话题
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {character.topics.map(topic => (
                  <button
                    key={topic.id}
                    onClick={() => {
                      selectTopic(topic);
                      // 如果用户有通话时长，直接开始通话
                      if (getTotalRemainingMinutes() > 0) {
                        startTopicCall();
                      } else {
                        // 如果没有通话时长，显示购买界面
                        setShowPurchaseDrawer(true);
                      }
                    }}
                    className={`p-2 rounded-lg text-left text-xs ${
                      selectedTopic?.id === topic.id 
                        ? 'bg-purple-100 border-2 border-purple-300'
                        : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    <span className="mr-1">{topic.icon}</span>
                    {topic.title}
                  </button>
                ))}
              </div>
            </div>
            
            {/* 体验优势展示 - 更强调价值感知 */}
            <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-3 mb-4 border border-purple-100">
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <Sparkles size={14} className="mr-1 text-purple-600" />
                语音通话的独特体验
              </h4>
              <div className="space-y-2">
                <div className="flex items-start">
                  <div className="bg-purple-100 p-1 rounded-full mr-2 mt-0.5 flex-shrink-0">
                    <Zap size={12} className="text-purple-700" />
                  </div>
                  <span className="text-xs text-left text-gray-600">比文字聊天更自然，感受真实交流的情感起伏</span>
                </div>
                <div className="flex items-start">
                  <div className="bg-purple-100 p-1 rounded-full mr-2 mt-0.5 flex-shrink-0">
                    <Star size={12} className="text-purple-700" />
                  </div>
                  <span className="text-xs text-left text-gray-600">解放双手，边走边聊，随时随地享受陪伴</span>
                </div>
                <div className="flex items-start">
                  <div className="bg-purple-100 p-1 rounded-full mr-2 mt-0.5 flex-shrink-0">
                    <Heart size={12} className="text-purple-700" />
                  </div>
                  <span className="text-xs text-left text-gray-600">语音中能感受到的情绪和语调让对话更加生动</span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* 操作按钮区 */}
        <div className="flex flex-col items-center space-y-4">
          {/* 通话控制按钮 - 较低位置 */}
          {isInCall ? (
            <div className="absolute bottom-4 left-0 right-0 flex items-center justify-center space-x-5">
              <div className="flex flex-col items-center">
                <button 
                  onClick={toggleMute} 
                  className={`w-14 h-14 rounded-full flex items-center justify-center ${
                    isMuted ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'
                  } shadow-md mb-1`}
                >
                  {isMuted ? <MicOff size={24} /> : <Mic size={24} />}
                </button>
                <span className="text-xs text-gray-600 bg-white/80 px-2 py-0.5 rounded-full whitespace-nowrap shadow-sm">
                  {isMuted ? '麦克风已关' : '麦克风已开'}
                </span>
              </div>
              
              <div className="flex flex-col items-center">
                <button 
                  onClick={endCall} 
                  className="w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white shadow-lg mb-1"
                >
                  <PhoneOff size={26} />
                </button>
                <span className="text-xs text-gray-600 bg-white/80 px-2 py-0.5 rounded-full whitespace-nowrap shadow-sm">
                  结束通话
                </span>
              </div>
              
              <div className="flex flex-col items-center">
                <button 
                  onClick={toggleSpeaker} 
                  className={`w-14 h-14 rounded-full flex items-center justify-center ${
                    isSpeakerOn ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                  } shadow-md mb-1`}
                >
                  {isSpeakerOn ? <Volume2 size={24} /> : <VolumeX size={24} />}
                </button>
                <span className="text-xs text-gray-600 bg-white/80 px-2 py-0.5 rounded-full whitespace-nowrap shadow-sm">
                  {isSpeakerOn ? '扬声器已开' : '扬声器已关'}
                </span>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-4">
              {getTotalRemainingMinutes() > 0 && (
                <button 
                  onClick={selectedTopic ? startTopicCall : startCall} 
                  className="w-16 h-16 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center text-white shadow-lg transform transition-transform hover:scale-105"
                >
                  <Phone size={28} />
                </button>
              )}
              
              {/* 购买时间按钮 */}
              <div className="flex space-x-3">
                <button 
                  onClick={() => setShowPurchaseDrawer(true)} 
                  className="px-4 py-2 bg-indigo-100 hover:bg-indigo-200 text-indigo-800 rounded-full text-sm flex items-center"
                >
                  <CreditCard size={16} className="mr-1" /> 
                  购买时长
                </button>
                
                {!isVIP && (
                  <button 
                    onClick={() => setShowVIPDrawer(true)} 
                    className="px-4 py-2 bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-white rounded-full text-sm flex items-center shadow-md"
                  >
                    <Crown size={16} className="mr-1" /> 
                    成为VIP
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* 购买时长抽屉 - 替代模态框 */}
      {showPurchaseDrawer && (
        <div className="fixed inset-0 bg-black bg-opacity-30 z-50" onClick={() => setShowPurchaseDrawer(false)}>
          <div 
            className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white rounded-t-2xl p-6 shadow-xl animate-slide-up"
            onClick={e => e.stopPropagation()}
          >
            <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
            <h3 className="text-xl font-bold mb-4 text-center">购买通话时长</h3>
            <p className="text-sm text-gray-600 mb-6 text-center">选择你想要的通话时长，立即开启语音畅聊</p>
            
            <div className="space-y-3 mb-6">
              <button 
                onClick={() => purchaseTime(1)} 
                className="w-full p-4 bg-white border border-indigo-200 rounded-lg flex items-center justify-between hover:bg-indigo-50"
              >
                <span className="font-medium">1小时</span>
                <span className="text-indigo-600 font-bold">{pricing.coinPerHour} 星光币</span>
              </button>
              <button 
                onClick={() => purchaseTime(3)} 
                className="w-full p-4 bg-white border border-indigo-200 rounded-lg flex items-center justify-between hover:bg-indigo-50"
              >
                <div>
                  <span className="font-medium">3小时</span>
                  <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded-full">省10%</span>
                </div>
                <span className="text-indigo-600 font-bold">{Math.floor(pricing.coinPerHour * 3 * 0.9)} 星光币</span>
              </button>
              <button 
                onClick={() => purchaseTime(10)} 
                className="w-full p-4 bg-indigo-50 border border-indigo-300 rounded-lg flex items-center justify-between hover:bg-indigo-100"
              >
                <div>
                  <span className="font-medium">10小时</span>
                  <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded-full">省20%</span>
                </div>
                <span className="text-indigo-600 font-bold">{Math.floor(pricing.coinPerHour * 10 * 0.8)} 星光币</span>
              </button>
            </div>
            
            <div className="flex space-x-3">
              <button 
                onClick={() => setShowPurchaseDrawer(false)} 
                className="flex-1 py-3 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-600"
              >
                取消
              </button>
              <button 
                onClick={() => {
                  setShowPurchaseDrawer(false);
                  setShowVIPDrawer(true);
                }} 
                className="flex-1 py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white rounded-lg flex items-center justify-center"
              >
                <Crown size={16} className="mr-1" />
                成为VIP更划算
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* VIP会员抽屉 - 替代模态框 */}
      {showVIPDrawer && (
        <div className="fixed inset-0 bg-black bg-opacity-30 z-50" onClick={() => setShowVIPDrawer(false)}>
          <div 
            className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white rounded-t-2xl p-6 shadow-xl animate-slide-up"
            onClick={e => e.stopPropagation()}
          >
            <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
            <div className="text-center mb-4">
              <span className="inline-block p-3 bg-yellow-100 rounded-full mb-2">
                <Crown size={24} className="text-yellow-600" />
              </span>
              <h3 className="text-xl font-bold">VIP会员特权</h3>
              <p className="text-sm text-gray-600 mt-1">每月仅需 {pricing.vipMonthlyPrice} 元，尊享多重特权</p>
            </div>
            
            {/* 优势比较表 - 增强价值感知 */}
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden mb-6">
              <div className="grid grid-cols-3">
                <div className="p-2 bg-gray-50 border-b border-r border-gray-200">
                  <span className="text-xs text-gray-500">功能特权</span>
                </div>
                <div className="p-2 bg-gray-50 border-b border-r border-gray-200">
                  <span className="text-xs text-gray-500">普通用户</span>
                </div>
                <div className="p-2 bg-gradient-to-r from-yellow-50 to-amber-50 border-b border-gray-200">
                  <span className="text-xs text-yellow-700 font-medium">VIP会员</span>
                </div>
                
                <div className="p-2 border-b border-r border-gray-200">
                  <span className="text-xs">月赠通话时长</span>
                </div>
                <div className="p-2 border-b border-r border-gray-200">
                  <span className="text-xs">无</span>
                </div>
                <div className="p-2 border-b border-gray-200 bg-yellow-50">
                  <span className="text-xs text-yellow-700 font-medium">{pricing.vipMonthlyHours}小时</span>
                </div>
                
                <div className="p-2 border-b border-r border-gray-200">
                  <span className="text-xs">语音质量</span>
                </div>
                <div className="p-2 border-b border-r border-gray-200">
                  <span className="text-xs">标准</span>
                </div>
                <div className="p-2 border-b border-gray-200 bg-yellow-50">
                  <span className="text-xs text-yellow-700 font-medium">高清音质</span>
                </div>
                
                <div className="p-2 border-b border-r border-gray-200">
                  <span className="text-xs">专属客服</span>
                </div>
                <div className="p-2 border-b border-r border-gray-200 text-center">
                  <span className="text-xs">✕</span>
                </div>
                <div className="p-2 border-b border-gray-200 bg-yellow-50 text-center">
                  <span className="text-xs text-yellow-700 font-medium">✓</span>
                </div>
                
                <div className="p-2 border-r border-gray-200">
                  <span className="text-xs">免广告体验</span>
                </div>
                <div className="p-2 border-r border-gray-200 text-center">
                  <span className="text-xs">✕</span>
                </div>
                <div className="p-2 border-gray-200 bg-yellow-50 text-center">
                  <span className="text-xs text-yellow-700 font-medium">✓</span>
                </div>
              </div>
            </div>
            
            <button 
              onClick={becomeVIP} 
              className="w-full py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-white rounded-lg font-medium shadow-md transform transition-transform hover:scale-102 mb-3"
            >
              立即开通 · {pricing.vipMonthlyPrice}元/月
            </button>
            
            <button 
              onClick={() => setShowVIPDrawer(false)} 
              className="w-full py-2 text-gray-500 hover:text-gray-700"
            >
              稍后再说
            </button>
          </div>
        </div>
      )}
      
      {/* 自定义CSS动画 */}
      <style jsx>{`
        @keyframes slide-up {
          from { transform: translateY(100%); }
          to { transform: translateY(0); }
        }
        .animate-slide-up {
          animation: slide-up 0.3s ease-out;
        }
        .animation-delay-300 {
          animation-delay: 300ms;
        }
        .animation-delay-500 {
          animation-delay: 500ms;
        }
        .animation-delay-1000 {
          animation-delay: 1000ms;
        }
        .hover:scale-102:hover {
          transform: scale(1.02);
        }
      `}</style>
    </div>
  );
};

export default VoiceCallPage;