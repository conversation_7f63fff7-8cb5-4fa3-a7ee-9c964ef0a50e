import React, { useState, useRef, useEffect } from 'react';
import { X, ChevronUp, ChevronDown, Plus, CreditCard } from 'lucide-react';

const PaymentModal = () => {
  const [isOpen, setIsOpen] = useState(true);
  const [expanded, setExpanded] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState('starlight'); // 默认选中星光币
  const modalRef = useRef(null);
  const contentRef = useRef(null);
  const [startY, setStartY] = useState(0);
  const [currentY, setCurrentY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  // 支付方式数据
  const paymentMethods = {
    starlight: {
      name: '星光币',
      price: 20,
      balance: 10,
      buttonText: '充值',
      icon: '✨'
    },
    party: {
      name: '派对币',
      price: 30,
      balance: 10,
      buttonText: '去获取',
      icon: '🎉'
    },
    cards: {
      name: '星卡',
      icon: '💳',
      items: [
        { id: 'AAA', name: 'AAA卡', image: '/api/placeholder/50/50', price: 3, balance: 1 },
        { id: 'BBB', name: 'BBB卡', image: '/api/placeholder/50/50', price: 2, balance: 0 },
        { id: 'CCC', name: 'CCC卡', image: '/api/placeholder/50/50', price: 0, balance: 5 },
        { id: 'DDD', name: 'DDD卡', image: '/api/placeholder/50/50', price: 1, balance: 2 },
        { id: 'EEE', name: 'EEE卡', image: '/api/placeholder/50/50', price: 2, balance: 1 }
      ]
    }
  };

  // 获取需要的总价和拥有的余额
  const getTotalCardPrice = () => {
    return paymentMethods.cards.items.reduce((total, card) => total + card.price, 0);
  };
  
  const getTotalCardBalance = () => {
    return paymentMethods.cards.items.reduce((total, card) => total + card.balance, 0);
  };

  // 拖拽相关逻辑
  const handleTouchStart = (e) => {
    setStartY(e.touches[0].clientY);
    setIsDragging(true);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const currentY = e.touches[0].clientY;
    setCurrentY(currentY);
    
    const deltaY = currentY - startY;
    if (deltaY > 0) { // 只允许向下拖动
      contentRef.current.style.transform = `translateY(${deltaY}px)`;
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    
    if (currentY - startY > 100) {
      // 如果拖动超过阈值，关闭模态框
      setIsOpen(false);
    } else {
      // 否则恢复原位
      contentRef.current.style.transform = 'translateY(0)';
    }
  };

  // 处理模态框关闭动画
  useEffect(() => {
    if (!isOpen && modalRef.current) {
      modalRef.current.classList.add('animate-fade-out');
      const timer = setTimeout(() => {
        modalRef.current.style.display = 'none';
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // 处理支付逻辑
  const handlePayment = () => {
    alert(`使用${
      selectedPayment === 'starlight' ? '星光币' : 
      selectedPayment === 'party' ? '派对币' : '星卡'
    }支付成功！`);
    setIsOpen(false);
  };

  return (
    <div 
      ref={modalRef}
      className={`fixed inset-0 bg-black bg-opacity-50 z-50 ${isOpen ? 'animate-fade-in' : 'animate-fade-out'}`}
      onClick={() => setIsOpen(false)}
    >
      <div 
        ref={contentRef}
        className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl px-4 pt-2 pb-8 max-h-[90vh] overflow-y-auto transition-transform duration-300 ease-out"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 拖动条 */}
        <div 
          className="w-12 h-1.5 bg-gray-300 rounded-full mx-auto mb-4 cursor-grab"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        />

        {/* 关闭按钮 */}
        <div className="absolute top-4 right-4">
          <button 
            className="p-1 rounded-full bg-gray-100 text-gray-500"
            onClick={() => setIsOpen(false)}
          >
            <X size={20} />
          </button>
        </div>

        {/* 标题 */}
        <h2 className="text-xl font-bold mb-6 text-center">收银台</h2>

        {/* 平铺显示所有支付方式 */}
        <div className="mb-6 space-y-4">
          <h3 className="font-medium text-gray-500 mb-2">选择支付方式</h3>
          
          {/* 星光币支付 */}
          <div 
            className={`bg-blue-50 rounded-xl p-4 relative ${selectedPayment === 'starlight' ? 'ring-2 ring-blue-500' : ''}`}
            onClick={() => setSelectedPayment('starlight')}
          >
            {selectedPayment === 'starlight' && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <div className="bg-white rounded-full w-3 h-3"></div>
              </div>
            )}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <span className="text-2xl mr-2">{paymentMethods.starlight.icon}</span>
                <span className="font-medium">{paymentMethods.starlight.name}</span>
              </div>
              <div className="text-blue-600 font-bold text-lg">
                {paymentMethods.starlight.price}币
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                余额: {paymentMethods.starlight.balance}币
              </div>
              <button 
                className="bg-blue-500 text-white px-4 py-1.5 rounded-full text-sm font-medium"
                onClick={(e) => {
                  e.stopPropagation(); // 防止触发父元素的点击事件
                }}
              >
                充值
              </button>
            </div>
          </div>

          {/* 派对币支付 */}
          <div 
            className={`bg-purple-50 rounded-xl p-4 relative ${selectedPayment === 'party' ? 'ring-2 ring-purple-500' : ''}`}
            onClick={() => setSelectedPayment('party')}
          >
            {selectedPayment === 'party' && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                <div className="bg-white rounded-full w-3 h-3"></div>
              </div>
            )}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <span className="text-2xl mr-2">{paymentMethods.party.icon}</span>
                <span className="font-medium">{paymentMethods.party.name}</span>
              </div>
              <div className="text-purple-600 font-bold text-lg">
                {paymentMethods.party.price}币
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                余额: {paymentMethods.party.balance}币
              </div>
              <button 
                className="bg-purple-500 text-white px-4 py-1.5 rounded-full text-sm font-medium"
                onClick={(e) => {
                  e.stopPropagation(); // 防止触发父元素的点击事件
                }}
              >
                去获取
              </button>
            </div>
          </div>

          {/* 星卡支付 */}
          <div 
            className={`bg-amber-50 rounded-xl p-4 relative ${selectedPayment === 'cards' ? 'ring-2 ring-amber-500' : ''}`}
            onClick={() => setSelectedPayment('cards')}
          >
            {selectedPayment === 'cards' && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center">
                <div className="bg-white rounded-full w-3 h-3"></div>
              </div>
            )}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <span className="text-2xl mr-2">{paymentMethods.cards.icon}</span>
                <span className="font-medium">{paymentMethods.cards.name}</span>
              </div>
              <div className="text-amber-600 font-bold text-lg">
                {getTotalCardPrice()}张
              </div>
            </div>
            
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm text-gray-500">
                余额: {getTotalCardBalance()}张
              </div>
              <button 
                className="flex items-center text-amber-600 text-sm font-medium"
                onClick={(e) => {
                  e.stopPropagation(); // 防止触发父元素的点击事件
                  setExpanded(expanded === 'cards' ? null : 'cards');
                }}
              >
                详情
                {expanded === 'cards' ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </button>
            </div>

            {expanded === 'cards' && (
              <div className="mt-3 border-t border-amber-200 pt-3">
                <div className="grid grid-cols-2 gap-3">
                  {paymentMethods.cards.items.map(card => (
                    <div key={card.id} className="bg-white p-3 rounded-lg shadow-sm">
                      <div className="flex items-center mb-2">
                        <img src={card.image} alt={card.name} className="w-6 h-6 rounded mr-2" />
                        <span className="font-medium text-sm">{card.name}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-xs text-gray-500">
                          需要: <span className="text-amber-600 font-medium">{card.price}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          拥有: <span className={`font-medium ${card.balance >= card.price ? 'text-green-500' : 'text-red-500'}`}>
                            {card.balance}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <button className="w-full mt-3 flex items-center justify-center bg-white border border-amber-300 text-amber-600 rounded-lg py-2 text-sm font-medium">
                  <Plus size={16} className="mr-1" /> 获取更多星卡
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 支付按钮 */}
        <div className="mt-6">
          <button 
            className={`w-full py-3 rounded-xl font-bold text-lg shadow-lg text-white ${
              selectedPayment === 'starlight' ? 'bg-gradient-to-r from-blue-500 to-blue-600' : 
              selectedPayment === 'party' ? 'bg-gradient-to-r from-purple-500 to-purple-600' : 
              'bg-gradient-to-r from-amber-500 to-amber-600'
            }`}
            onClick={handlePayment}
          >
            立即支付 {selectedPayment === 'starlight' ? '(星光币)' : 
                     selectedPayment === 'party' ? '(派对币)' : '(星卡)'}
          </button>
        </div>

        {/* 其他付款方式 */}
        <div className="mt-4 flex justify-center">
          <button className="flex items-center text-gray-500 text-sm">
            <CreditCard size={14} className="mr-1" />
            其他付款方式
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;