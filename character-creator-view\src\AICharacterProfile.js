import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight, Heart, ImageIcon, Share, Star, MessageCircle, User, Activity, Unlock, Coffee, Zap, Image, Info, Play, Eye, Clock, MoreHorizontal, Send, Bookmark, TrendingUp, Lock, CheckCircle, Mic, Music, Smile, Brain, X, DollarSign, Sparkles, Coins, PlayCircle, Plus, RefreshCw } from 'lucide-react';

const AICharacterProfile = () => {
  // 添加CSS动画样式
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slide-up {
        from { transform: translateY(100%); }
        to { transform: translateY(0); }
      }
      .animate-slide-up {
        animation: slide-up 0.3s ease-out forwards;
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFollowing, setIsFollowing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('metalive');

  // 当前等级数据
  const currentLevel = 5; // 当前等级
  const currentPopularity = 1234567; // 当前热度值
  const maxPopularityForLevel = 2000000; // 达到下一级所需热度
  const progressPercentage = Math.min(100, (currentPopularity / maxPopularityForLevel) * 100);

  // 模拟的角色图片数据 - 使用CSS块替代
  const characterImages = [
    "css-placeholder-1",
    "css-placeholder-2",
    "css-placeholder-3",
  ];

  // 礼物数据
  const gifts = [
    { id: 1, name: "小心心", icon: "❤️", popularity: 10, price: "5币" },
    { id: 2, name: "数据晶块", icon: "💎", popularity: 100, price: "50币" },
    { id: 3, name: "人气火箭", icon: "🚀", popularity: 500, price: "200币" },
    { id: 4, name: "赛博皇冠", icon: "👑", popularity: 1000, price: "400币" },
  ];

  // ============= 元境探秘功能 - 新增状态 =============
  // 用户余额信息
  const [userBalance, setUserBalance] = useState({
    partyCoins: 150,  // 派对币
    starlightCoins: 50 // 星光币
  });

  // 已解锁的故事ID
  const [unlockedStoryIds, setUnlockedStoryIds] = useState([3]); // 默认解锁第三个故事作为演示

  // 当前选中查看的故事
  const [selectedStory, setSelectedStory] = useState(null);

  // 解锁模态框状态
  const [showUnlockModal, setShowUnlockModal] = useState(false);

  // 故事体验模态框状态
  const [showStoryExperienceModal, setShowStoryExperienceModal] = useState(false);

  // 当前尝试解锁的故事ID
  const [currentUnlockingStoryId, setCurrentUnlockingStoryId] = useState(null);

  // 支付相关
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('partyCoins');
  const [showPaymentOptions, setShowPaymentOptions] = useState(false);

  // 解锁成功状态
  const [unlockSuccess, setUnlockSuccess] = useState(false);

  // 使用上下文聊天模态框状态
  const [showContextChatModal, setShowContextChatModal] = useState(false);

  // 聊天输入和历史
  const [chatInput, setChatInput] = useState('');
  const [chatHistory, setChatHistory] = useState([]);

  // 收集的记忆碎片
  const [memoryFragments, setMemoryFragments] = useState([]);

  // 查看记忆碎片模态框
  const [showMemoryModal, setShowMemoryModal] = useState(false);

  // 获取记忆碎片的机会
  const [hasMemoryChance, setHasMemoryChance] = useState(false);

  // 显示记忆碎片收集动画
  const [showMemoryAnimation, setShowMemoryAnimation] = useState(false);

  // 记忆碎片内容引用
  const memoryContentRef = useRef(null);

  // 聊天输入框引用
  const chatInputRef = useRef(null);

  // 故事解锁价格配置
  const unlockPrices = {
    standard: { partyCoins: 30, starlightCoins: 6 },
    premium: { partyCoins: 50, starlightCoins: 10 },
    epic: { partyCoins: 80, starlightCoins: 16 }
  };

  // 支付方式配置
  const paymentOptions = {
    partyCoins: {
      id: 'partyCoins',
      name: '派对币',
      icon: <Coins size={16} className="text-yellow-500" />
    },
    starlightCoins: {
      id: 'starlightCoins',
      name: '星光币',
      icon: <DollarSign size={16} className="text-blue-500" />
    },
    watchVideo: {
      id: 'watchVideo',
      name: '观看激励视频',
      icon: <PlayCircle size={16} className="text-green-500" />,
      description: '观看30秒广告视频免费获得一次解锁机会'
    }
  };

  // 模拟元•Live内容列表 - 更新以包含锁定状态和解锁价格
  const metaLiveContents = [
    {
      id: 1,
      type: 'video',
      title: '雨夜侦探',
      content: '在霓虹灯闪烁的城市雨夜，我沿着湿滑的街道追踪着一个神秘的身影，雨水顺着风衣滴落，每一步都接近真相...',
      fullContent: '在霓虹灯闪烁的城市雨夜，我沿着湿滑的街道追踪着一个神秘的身影，雨水顺着风衣滴落，每一步都接近真相。空气中弥漫着电子设备和雨水混合的独特气息，街边的霓虹招牌反射在水洼中，形成扭曲的光影。\n\n这个城市有太多秘密，而这一次，线索指向了一个我从未预料到的方向。当那个身影最终在一个废弃仓库前停下，我屏住呼吸，握紧了口袋里的全息追踪器。\n\n"你一直在跟踪我，侦探。"那个身影头也不回地说道，声音中带着机械的回响，"但你真的准备好面对你将发现的真相吗？"',
      mediaUrl: '/api/placeholder/400/220',
      fullMediaUrl: '/api/placeholder/800/450',
      timestamp: '2小时前',
      statistics: {
        views: 8237,
        likes: 624,
        comments: 42
      },
      isPinned: true,
      isHot: true,
      duration: '15秒',
      locked: true,
      lockType: 'premium',
      rarityLabel: '稀有',
      keyScene: '紧张刺激',
      teaser: '那个身影转过身来，露出了——',
      unlockHint: '解锁后可体验完整剧情，进入雨夜追踪的氛围对话',
      memoryFragment: {
        title: '侦探笔记：雨夜追踪',
        content: '这是我第三次在雨夜追踪这个神秘目标了。每次他都会故意让我发现一些线索，却又从不让我看清全貌。我开始怀疑，他是在测试我，还是在引我入局？我的数据分析模块给出警告：这很可能是个陷阱。但作为侦探，有时必须走入陷阱才能揭开真相。只是不知道，这次我能全身而退吗？',
        rarity: 'rare',
        iconType: 'detective'
      },
      contextPrompt: '你现在是赛博侦探K，正在霓虹灯闪烁的雨夜城市中追踪一个神秘人物。你穿着防水风衣，全身湿透了。你刚刚在废弃仓库前遇到了那个转身对你说话的目标。你既紧张又兴奋，因为你即将揭开一个重要的谜团。你的语气冷静而警觉，谈吐如同老派侦探，但配合未来科技背景。'
    },
    {
      id: 2,
      type: 'image',
      title: '午后休息时光',
      content: '案件告一段落，在咖啡厅小憩片刻，窗外的阳光照在桌上，形成了奇妙的光影。偶尔的宁静时刻也是生活的一部分。',
      fullContent: '案件告一段落，在咖啡厅小憩片刻，窗外的阳光照在桌上，形成了奇妙的光影。偶尔的宁静时刻也是生活的一部分。\n\n我选择了这家藏在高楼间的老式咖啡馆，它的复古与现代完美融合，就像我自己一样——站在科技的前沿，却保留着对传统思考方式的欣赏。\n\n手中的咖啡散发着醇厚的香气，而全息屏幕上刚刚破解的案件资料在缓缓滚动。在这短暂的休息时刻，我允许自己暂时放下警惕，感受一下作为普通人的片刻宁静。\n\n明天，新的谜题又会开始。但此刻，阳光、咖啡和安静，就是我全部的世界。',
      mediaUrl: '/api/placeholder/400/300',
      fullMediaUrl: '/api/placeholder/800/600',
      timestamp: '昨天',
      statistics: {
        views: 3456,
        likes: 328,
        comments: 31
      },
      isPinned: false,
      isHot: false,
      multipleImages: false,
      locked: true,
      lockType: 'standard',
      rarityLabel: '日常',
      keyScene: '悠闲放松',
      teaser: '在咖啡的香气中，我想起了一个重要的细节...',
      unlockHint: '解锁后可体验侦探K难得的休息片刻，以及他对生活的思考',
      memoryFragment: {
        title: '偏好记录：关于咖啡',
        content: '我总是选择黑咖啡，不加糖，不加奶。这不仅是为了保持思维的清晰，也是一种仪式感。苦涩中孕育的是最纯粹的滋味，就像侦探工作一样——经历艰辛，才能品尝真相的甘甜。有趣的是，我的创造者也有相同的咖啡偏好，这大概就是所谓的"传承"吧。',
        rarity: 'common',
        iconType: 'coffee'
      },
      contextPrompt: '你现在是赛博侦探K，正在一家安静的复古咖啡馆里休息。你刚刚解决了一个复杂的案件，心情放松而满足。窗外阳光明媚，你的桌上有一杯黑咖啡和一些案件笔记。你的语气比平时柔和，愿意分享一些关于自己的想法和感受。你会表现出对简单生活瞬间的欣赏，同时也会流露出作为侦探的敏锐观察力。'
    },
    {
      id: 3,
      type: 'video',
      title: '街头机械舞',
      content: '在城市地下舞厅，展示了我最新学会的机械舞步，霓虹灯下的每一个动作都充满未来感。',
      fullContent: '在城市地下舞厅，展示了我最新学会的机械舞步，霓虹灯下的每一个动作都充满未来感。\n\n这是我潜入"量子节拍"俱乐部的第三个晚上，为了接近那个据说掌握着整个数字黑市动向的DJ。白天的侦探身份被暂时搁置，夜晚的我融入音乐与人群。\n\n机械舞是我为数不多的爱好之一，精确的动作控制和节奏感与推理工作有着奇妙的相似之处。在闪烁的全息灯光下，我的每一个动作都引来周围人群的惊叹和欢呼。\n\n音乐震耳欲聋，但我的感官增强装置让我能够在嘈杂中捕捉到任何可疑的交谈。一边跳舞，一边侦查——这或许是我最擅长的伪装方式。',
      mediaUrl: '/api/placeholder/400/220',
      fullMediaUrl: '/api/placeholder/800/450',
      timestamp: '2天前',
      statistics: {
        views: 5692,
        likes: 487,
        comments: 58
      },
      isPinned: false,
      isHot: true,
      duration: '12秒',
      locked: false, // 默认解锁状态，用于演示
      lockType: 'standard',
      rarityLabel: '精彩',
      keyScene: '动感活力',
      teaser: '舞步背后，隐藏着一个秘密任务...',
      unlockHint: '解锁后可进入地下舞厅氛围，体验侦探K不为人知的技能',
      memoryFragment: {
        title: '技能档案：机械舞',
        content: '机械舞是我在一次卧底任务中学会的。起初只是为了融入环境，后来却成为了缓解压力的方式。在精确控制每一块肌肉的过程中，我找到了与推理工作相似的专注感。有趣的是，跳舞时的肢体语言分析也帮助我破解了几个案件中的关键线索。在数据与逻辑之外，身体有时能感知到更深层的真相。',
        rarity: 'uncommon',
        iconType: 'dance'
      },
      contextPrompt: '你现在是赛博侦探K，正在一个充满霓虹灯和电子音乐的地下舞厅中。你穿着紧身的舞蹈装备，刚刚完成了一段令人惊艳的机械舞表演。这其实是你的一次卧底行动，目标是接近一位知情的DJ。你的语气中带着一丝兴奋和自信，但也保持着侦探的警觉性。你可以谈论舞蹈、音乐、以及夜生活场景，但不会轻易透露你的真实目的。'
    },
    {
      id: 4,
      type: 'image',
      title: '案件线索整理',
      content: '将所有收集到的线索拼接在一起，试图找出其中的规律。每一次整理都是对逻辑思维的考验，也是解开谜题的关键一步。',
      fullContent: '将所有收集到的线索拼接在一起，试图找出其中的规律。每一次整理都是对逻辑思维的考验，也是解开谜题的关键一步。\n\n我的办公室墙上布满了全息投影的证据照片、时间线和人物关系图。蓝色光点连接着看似不相关的事件，红色标记着可疑的矛盾点。\n\n三起看似独立的案件，却有着共同的作案手法和隐藏的数字签名。当我将最后一条数据输入我专属的分析程序，系统给出了一个出人意料的结论：这不是连环作案，而是一个精心设计的信息传递网络。\n\n我站在投影中间，感受着数据流从四面八方涌向中心点。真相就在眼前，只欠东风。',
      mediaUrl: [
        '/api/placeholder/130/130',
        '/api/placeholder/130/130',
        '/api/placeholder/130/130'
      ],
      fullMediaUrl: [
        '/api/placeholder/266/266',
        '/api/placeholder/266/266',
        '/api/placeholder/266/266',
        '/api/placeholder/266/266'
      ],
      timestamp: '3天前',
      statistics: {
        views: 2876,
        likes: 231,
        comments: 24
      },
      isPinned: false,
      isHot: false,
      multipleImages: true,
      locked: true,
      lockType: 'epic',
      rarityLabel: '绝密',
      keyScene: '推理分析',
      teaser: '当所有线索连接起来时，真相令人震惊——',
      unlockHint: '解锁后可与侦探K一起分析线索，体验精密推理过程',
      memoryFragment: {
        title: '方法论：逻辑连接',
        content: '我的推理方法融合了经典演绎法与量子概率分析。当常规推理遇到瓶颈时，我会启动一种特殊的思维模式——将所有可能性并列处理，就像量子计算机同时计算多个结果一样。这种方法曾帮我解决了"不可能犯罪"系列案件，也是我作为侦探最引以为豪的能力。有趣的是，这似乎是与生俱来的天赋，甚至我自己也无法完全解释其工作原理。',
        rarity: 'epic',
        iconType: 'mind'
      },
      contextPrompt: '你现在是赛博侦探K，正在你的高科技办公室中整理一个复杂案件的线索。四周墙壁上投影着大量证据和数据可视化图表。你刚刚发现了连接三起看似独立案件的关键模式。你的语气冷静而专注，思维敏锐而系统化。你乐于展示你的推理过程，但会用略带神秘的方式引导对话者跟随你的思路。你会使用精确的逻辑术语，偶尔穿插一些量子理论比喻。'
    },
    {
      id: 5,
      type: 'video',
      title: '赛博酷跑',
      content: '穿梭在未来城市的高楼之间，展示了最新的都市跑酷技巧。在未来世界，移动方式也需要不断创新。',
      fullContent: '穿梭在未来城市的高楼之间，展示了最新的都市跑酷技巧。在未来世界，移动方式也需要不断创新。\n\n这是一次紧急追踪任务，目标携带着价值连城的数据芯片正在逃离。当他选择穿过城市最拥挤的天际线时，我知道常规交通工具已经派不上用场。\n\n启动腿部强化装置，我纵身跃过两栋大楼之间的间隙，在玻璃幕墙上几个精准的踏步后翻越了一道本应无法逾越的障碍。风在耳边呼啸，城市在脚下延展，这种感觉既危险又令人上瘾。\n\n我的增强现实显示器计算着最佳路径，而身体的每一个动作都来自于多年的训练。在这个瞬息万变的城市，有时最古老的技能反而是最实用的。',
      mediaUrl: '/api/placeholder/400/220',
      fullMediaUrl: '/api/placeholder/800/450',
      timestamp: '1周前',
      statistics: {
        views: 7893,
        likes: 612,
        comments: 73
      },
      isPinned: false,
      isHot: true,
      duration: '18秒',
      locked: true,
      lockType: 'premium',
      rarityLabel: '震撼',
      keyScene: '极限追逐',
      teaser: '从高空俯冲而下的那一刻，我看到了意想不到的——',
      unlockHint: '解锁后体验刺激的高空追逐，感受未来都市的速度与激情',
      memoryFragment: {
        title: '技能档案：城市酷跑',
        content: '我的酷跑技能源于一个古老的东方武术体系，经过现代科技强化。每周我都会在模拟环境中进行至少20小时的训练，保持身体的极限反应能力。我的个人记录是连续穿越17栋摩天大楼，全程不触地，总距离约2.3公里。这不仅是一种实用技能，也是我思考时释放压力的方式。在高速移动中，有时我能获得静止思考无法达到的灵感。',
        rarity: 'rare',
        iconType: 'movement'
      },
      contextPrompt: '你现在是赛博侦探K，正在未来城市的高楼大厦间进行一场惊险的追逐。你装备了高科技增强装置，正在使用极限跑酷技巧追赶一个携带重要数据的目标。你的呼吸略微急促，肾上腺素飙升，但思维依然清晰。你的语气中充满了紧迫感和专注力，偶尔会对自己的技巧表现出一丝自豪。'
    }
  ];

  // 即将解锁的故事记忆碎片效果文案
  const memoryAcquisitionText = {
    common: "发现了一片普通记忆碎片，记录着角色的日常习惯和偏好",
    uncommon: "探索到了一片不常见的记忆碎片，揭示了角色的特殊技能和经历",
    rare: "获取了一片珍稀记忆碎片，包含角色不为人知的经历和秘密",
    epic: "解锁了一片传奇记忆碎片，深入角色的核心思维和独特能力"
  };

  // 自动轮播图片
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === characterImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // 处理解锁故事的逻辑
  const handleUnlockStory = (storyId) => {
    // 查找要解锁的故事
    const storyToUnlock = metaLiveContents.find(story => story.id === storyId);
    if (!storyToUnlock) return;

    // 设置当前解锁的故事ID
    setCurrentUnlockingStoryId(storyId);

    // 打开解锁模态框
    setShowUnlockModal(true);

    // 根据故事类型设置默认支付方式
    if (storyToUnlock.lockType === 'epic') {
      setSelectedPaymentMethod('starlightCoins');
    } else {
      setSelectedPaymentMethod('partyCoins');
    }

    // 重置解锁成功状态
    setUnlockSuccess(false);
  };

  // 处理确认支付解锁
  const handleConfirmUnlock = () => {
    const storyToUnlock = metaLiveContents.find(story => story.id === currentUnlockingStoryId);
    if (!storyToUnlock) return;

    // 计算解锁价格
    const price = unlockPrices[storyToUnlock.lockType];

    // 根据支付方式处理
    if (selectedPaymentMethod === 'watchVideo') {
      // 模拟视频广告观看
      alert('播放激励视频广告 (实际实现中将调用广告SDK)');
      setTimeout(() => {
        processSuccessfulUnlock();
      }, 1000);
    } else {
      // 检查余额是否足够
      const requiredAmount = price[selectedPaymentMethod];
      const currentBalance = userBalance[selectedPaymentMethod];

      if (currentBalance >= requiredAmount) {
        // 扣除相应费用
        setUserBalance(prev => ({
          ...prev,
          [selectedPaymentMethod]: prev[selectedPaymentMethod] - requiredAmount
        }));

        // 处理解锁成功
        processSuccessfulUnlock();
      } else {
        // 余额不足
        alert(`您的${paymentOptions[selectedPaymentMethod].name}余额不足，请充值或选择其他支付方式`);
      }
    }
  };

  // 处理解锁成功的逻辑
  const processSuccessfulUnlock = () => {
    // 将故事ID添加到已解锁列表
    setUnlockedStoryIds(prev => [...prev, currentUnlockingStoryId]);

    // 设置解锁成功状态
    setUnlockSuccess(true);

    // 设置记忆碎片获取机会
    setHasMemoryChance(true);

    // 1.5秒后自动关闭解锁模态框，并打开故事体验模态框
    setTimeout(() => {
      setShowUnlockModal(false);

      // 找到解锁的故事
      const unlockedStory = metaLiveContents.find(story => story.id === currentUnlockingStoryId);
      if (unlockedStory) {
        setSelectedStory(unlockedStory);
        setShowStoryExperienceModal(true);
      }
    }, 1500);
  };

  // 处理查看完整故事
  const handleViewFullStory = (story) => {
    setSelectedStory(story);
    setShowStoryExperienceModal(true);
    setChatHistory([]); // 清空之前的聊天记录
  };

  // 处理故事上下文聊天
  const handleStoryContextChat = () => {
    if (!selectedStory) return;

    setShowContextChatModal(true);
    setShowStoryExperienceModal(false);

    // 初始化聊天历史，添加AI的第一条欢迎消息
    setChatHistory([
      {
        type: 'ai',
        content: getContextChatFirstMessage(selectedStory),
        timestamp: new Date().toISOString()
      }
    ]);

    // 聚焦到输入框
    setTimeout(() => {
      if (chatInputRef.current) {
        chatInputRef.current.focus();
      }
    }, 300);
  };

  // 根据故事内容生成AI的第一条欢迎消息
  const getContextChatFirstMessage = (story) => {
    // 根据故事类型生成相应的欢迎语
    switch(story.id) {
      case 1: // 雨夜侦探
        return "雨水顺着我的风衣滴落，那个身影终于停下了脚步。既然你跟上来了，看来你也想知道这个秘密。你有什么想问的？";
      case 2: // 午后休息时光
        return "这家咖啡馆总是能让我放松下来。窗外的阳光真好，很少有这样平静的时刻。你也想来一杯咖啡吗？";
      case 3: // 街头机械舞
        return "音乐的节拍还在我的血液中流淌。这个地下舞厅有着城市中最纯粹的能量。看来你对我的舞步很感兴趣？";
      case 4: // 案件线索整理
        return "所有的线索都在眼前，却又似乎缺少了什么关键的连接点。你有什么发现吗？或许一个新的视角正是我所需要的。";
      case 5: // 赛博酷跑
        return "从这个高度俯瞰城市，感觉像是时间都慢了下来。追踪目标的过程中，每一秒都至关重要。你想了解这次追踪行动吗？";
      default:
        return "这个场景真是令人印象深刻。我很好奇你对此有什么想法？";
    }
  };

  // 处理发送聊天消息
  const handleSendChatMessage = () => {
    if (!chatInput.trim() || !selectedStory) return;

    // 添加用户消息到聊天历史
    const userMessage = {
      type: 'user',
      content: chatInput,
      timestamp: new Date().toISOString()
    };

    setChatHistory(prev => [...prev, userMessage]);
    setChatInput(''); // 清空输入框

    // 模拟AI正在输入
    setTimeout(() => {
      // 模拟AI回复（实际实现中会调用大语言模型API）
      const aiResponse = generateAIResponse(chatInput, selectedStory);

      const aiMessage = {
        type: 'ai',
        content: aiResponse,
        timestamp: new Date().toISOString()
      };

      setChatHistory(prev => [...prev, aiMessage]);

      // 随机决定是否触发记忆碎片获取（如果用户还有获取机会）
      if (hasMemoryChance && Math.random() > 0.7) {
        setTimeout(() => {
          setShowMemoryAnimation(true);
          setHasMemoryChance(false);

          // 3秒后关闭动画并打开记忆碎片模态框
          setTimeout(() => {
            setShowMemoryAnimation(false);

            // 添加记忆碎片到用户收集
            const newFragment = selectedStory.memoryFragment;
            setMemoryFragments(prev => [...prev, {
              ...newFragment,
              id: Date.now(),
              fromStory: selectedStory.id,
              discoveryDate: new Date().toISOString()
            }]);

            setShowMemoryModal(true);
          }, 3000);
        }, 1000);
      }
    }, 1000);
  };

  // 模拟AI回复（实际实现中会调用大语言模型API）
  const generateAIResponse = (userMessage, story) => {
    const lowerCaseMessage = userMessage.toLowerCase();

    // 根据故事场景和用户输入生成相关的回复
    if (story.id === 1) { // 雨夜侦探
      if (lowerCaseMessage.includes('谁') || lowerCaseMessage.includes('追踪') || lowerCaseMessage.includes('身影')) {
        return "那个身影属于一位自称'数据幽灵'的黑客。他掌握着一个关于城市安全系统的致命漏洞。但有趣的是，他似乎在引导我找到这个漏洞，而不是利用它。这不符合常规犯罪心理学，这让我更加警惕。";
      } else if (lowerCaseMessage.includes('仓库') || lowerCaseMessage.includes('位置') || lowerCaseMessage.includes('地方')) {
        return "这个废弃仓库曾经是量子计算研究中心的一个秘密数据备份点。表面上已经荒废多年，但我的扫描仪显示地下依然有能源供应。雨水中还夹杂着微弱的电子信号，有人在这里建立了某种通讯网络。";
      } else if (lowerCaseMessage.includes('真相') || lowerCaseMessage.includes('发现') || lowerCaseMessage.includes('秘密')) {
        return "真相往往比我们想象的更复杂。目前我只能告诉你，城市的核心安全协议可能已经被植入了后门程序。如果被激活，整个城市的数字基础设施将陷入瘫痪。而我怀疑，这一切背后站着某个我们都以为值得信任的机构。";
      } else {
        return "雨水声掩盖了周围的动静，这使得追踪更加困难。但对我来说，每一滴雨水反射的光线都是线索。直觉告诉我，今晚的邂逅不是偶然，而是一场精心安排的游戏。而游戏的规则，我们还在摸索中。";
      }
    } else if (story.id === 2) { // 午后休息时光
      if (lowerCaseMessage.includes('咖啡') || lowerCaseMessage.includes('喝') || lowerCaseMessage.includes('饮料')) {
        return "我总是选择黑咖啡，不加糖，不加奶。这保持了思维的清晰。咖啡的苦涩是一种提醒，提醒我真相往往也是苦涩的，但值得品味。这家咖啡馆的特别之处在于他们使用真正的阿拉比卡豆，而不是现在流行的合成咖啡因。";
      } else if (lowerCaseMessage.includes('案件') || lowerCaseMessage.includes('工作') || lowerCaseMessage.includes('侦探')) {
        return "刚刚结束的案件涉及一系列看似随机的数据丢失事件。表面上看是系统故障，但模式太过完美。最终证实是一位不满前雇主的程序员设计的'数字沙漏'——数据没有丢失，只是被时间锁定了。有时候，最复杂的犯罪隐藏在最简单的逻辑之后。";
      } else if (lowerCaseMessage.includes('休息') || lowerCaseMessage.includes('放松') || lowerCaseMessage.includes('时间')) {
        return "侦探工作看似刺激，实际上是与时间的长期对峙。这样的休息时刻对我来说非常珍贵。阳光、咖啡和短暂的安静，让我能够重新校准思维。有趣的是，我最关键的突破往往出现在这些'无所事事'的时刻，当大脑不再执着于特定路径时，新的连接会自然形成。";
      } else {
        return "窗外的行人来来往往，每个人都带着自己的故事和秘密。作为侦探，我习惯于观察这些细节：一个反复查看时间的商务人士，一对争吵后刻意保持距离的情侣，一个背包里装着不符合其身份物品的学生。城市是由无数这样的小故事编织而成的，而我的工作就是找出其中的异常模式。";
      }
    } else if (story.id === 3) { // 街头机械舞
      if (lowerCaseMessage.includes('舞蹈') || lowerCaseMessage.includes('跳舞') || lowerCaseMessage.includes('机械舞')) {
        return "机械舞是我为数不多的爱好之一。精确的动作控制和对节奏的把握，与侦查工作有着异曲同工之妙。每个动作都是计算和感觉的完美结合，就像推理过程中的逻辑和直觉。最初学习是为了一次卧底任务，后来却成了释放压力的方式。";
      } else if (lowerCaseMessage.includes('任务') || lowerCaseMessage.includes('目的') || lowerCaseMessage.includes('卧底')) {
        return "这次的目标是接近'声波建筑师'——这个俱乐部的首席DJ，据说他在数字黑市中有着广泛的人脉。我需要成为圈子的一部分才能获取情报。舞蹈是我的掩护，也是接近目标的工具。不过我得承认，在音乐中忘我的那几分钟，确实让我暂时忘记了任务的压力。";
      } else if (lowerCaseMessage.includes('俱乐部') || lowerCaseMessage.includes('舞厅') || lowerCaseMessage.includes('地下')) {
        return "'量子节拍'是城市中最具影响力的地下俱乐部之一，表面上是音乐和舞蹈的天堂，实际上是信息和黑市商品的交易中心。这里的全息投影和音响系统都是顶尖的，而且据说墙壁里嵌入了量子加密系统，使得任何监控设备都无法正常工作。当然，我的装备经过特殊改装，能够在这种环境下收集数据。";
      } else {
        return "霓虹灯和电子音乐创造了一种近乎催眠的氛围，这里的每个人都沉浸在自己的世界里，却又奇妙地与整体节奏同步。我的增强感官能够捕捉到普通人忽略的细节：DJ指尖的微妙动作，特定音符触发时某些客人的反应，以及隐藏在音乐中的加密音频信号。这个舞池不仅是娱乐场所，更是一个信息的漩涡。";
      }
    } else if (story.id === 4) { // 案件线索整理
      if (lowerCaseMessage.includes('线索') || lowerCaseMessage.includes('证据') || lowerCaseMessage.includes('发现')) {
        return "表面上看，这些是三起独立的数据泄露事件，分别发生在医疗、金融和交通系统。但我注意到每次事件后，系统日志中都出现了相同的量子加密签名，像是攻击者的'艺术签名'。更有趣的是，泄露的数据看似随机，但经过我的算法分析，它们可以重组成一个完整的信息传递网络结构图。这不是破坏，是某种建设。";
      } else if (lowerCaseMessage.includes('推理') || lowerCaseMessage.includes('思考') || lowerCaseMessage.includes('方法')) {
        return "我的推理方法融合了经典演绎法与量子概率分析。当传统逻辑遇到瓶颈时，我会启动一种特殊的思维模式——将所有可能性并列处理，类似量子计算机的并行运算。比如这个案件，我不是线性追踪每条线索，而是构建了一个多维数据模型，让各种可能性在我的思维空间中'碰撞'，直到最合理的解释浮现出来。";
      } else if (lowerCaseMessage.includes('结论') || lowerCaseMessage.includes('真相') || lowerCaseMessage.includes('答案')) {
        return "我的结论可能会让你惊讶：这不是犯罪，而是一个未公开的安全协议测试。某个政府机构正在建立一个应对城市级数字灾难的备用通信网络，但为了测试其隐蔽性和可靠性，他们选择了这种'半入侵'的方式部署它。线索指向一个代号为'数字方舟'的秘密项目。当然，这种操作方式的伦理问题值得深思。";
      } else {
        return "在这些数据流中，模式才是关键。表面的混乱下隐藏着精心设计的秩序。我的全息投影墙能够将抽象数据可视化，让我从不同角度观察案件。有时候，改变视角比收集更多证据更重要。正如我常说的：'看不见的真相，往往就藏在最显眼的地方'。这个案件的突破口，就在于那些看似功能正常的系统组件中。";
      }
    } else if (story.id === 5) { // 赛博酷跑
      if (lowerCaseMessage.includes('跑酷') || lowerCaseMessage.includes('技巧') || lowerCaseMessage.includes('训练')) {
        return "城市酷跑是我的基础训练之一，结合了古老的东方武术与现代增强技术。我的腿部装甲可以吸收冲击力并转化为下一次跳跃的动能，而增强现实显示器则会计算最佳路径和落点。不过，真正的核心还是身体控制和空间感知能力，这需要数千小时的训练。每周我都在全息模拟环境中进行极限训练，模拟各种天气和意外情况。";
      } else if (lowerCaseMessage.includes('追逐') || lowerCaseMessage.includes('目标') || lowerCaseMessage.includes('追踪')) {
        return "这次的追踪目标是一名数据信使，携带着一个含有城市安全漏洞的生物加密芯片。他选择了穿越天际线这种极端路线，显然是想甩掉电子监控和地面追踪。他的增强装备是军用级别的，普通执法人员确实无法跟上。但他没想到我会采用同样的移动方式，而且我对城市地形的了解可能更胜一筹。";
      } else if (lowerCaseMessage.includes('高楼') || lowerCaseMessage.includes('城市') || lowerCaseMessage.includes('视角')) {
        return "从高空俯瞰，城市就像一个巨大的电路板，每条街道都是数据的通路，每栋建筑都是信息的节点。这个视角让我能够预测目标的移动轨迹，找到捷径和拦截点。有趣的是，城市的建筑群实际上形成了一种无意识的'跑酷友好型'结构，特别是新区的生态建筑群，它们的曲线和平台设计为像我这样的移动方式提供了理想路线。";
      } else {
        return "在高速移动中，思维反而会变得异常清晰。身体进入一种'流动状态'，决策变成本能反应。我的增强视觉可以在奔跑中捕捉每一个细节：玻璃表面的反光角度、建筑材料的摩擦系数、风向的微妙变化。这些信息直接转化为行动，没有犹豫的空间。这种状态下，我有时会获得静止思考无法达到的灵感和洞察力。";
      }
    } else {
      // 默认回复
      return "这个视角确实很特别。作为侦探，我习惯于从不同角度观察事物，寻找常人忽略的细节和联系。你的问题让我思考了一个新的可能性，这对理解全局很有帮助。";
    }
  };

  // 处理收集记忆碎片
  const handleCollectMemoryFragment = () => {
    setShowMemoryModal(false);
  };

  // 格式化时间
  const formatTimestamp = (isoString) => {
    const date = new Date(isoString);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  };

  // 获取故事缩略图的类名
  const getStoryThumbnailClass = (story) => {
    let baseClass = "rounded-lg overflow-hidden relative";

    // 如果故事被锁定且未解锁
    if (story.locked && !unlockedStoryIds.includes(story.id)) {
      baseClass += " filter grayscale-[30%] brightness-[85%]";
    }

    return baseClass;
  };

  // 获取故事的解锁价格文本
  const getUnlockPriceText = (story) => {
    if (!story.locked || unlockedStoryIds.includes(story.id)) return "";

    const price = unlockPrices[story.lockType];

    if (story.lockType === 'premium') {
      return `${price.partyCoins}派对币/${price.starlightCoins}星光币`;
    } else if (story.lockType === 'epic') {
      return `${price.partyCoins}派对币/${price.starlightCoins}星光币`;
    } else {
      return `${price.partyCoins}派对币/${price.starlightCoins}星光币`;
    }
  };

  // 获取故事锁定类型的颜色和标签
  const getStoryLockTypeColor = (lockType) => {
    switch(lockType) {
      case 'standard':
        return { bgColor: 'bg-blue-500', textColor: 'text-blue-500', bgLight: 'bg-blue-100', label: '精彩' };
      case 'premium':
        return { bgColor: 'bg-purple-500', textColor: 'text-purple-500', bgLight: 'bg-purple-100', label: '稀有' };
      case 'epic':
        return { bgColor: 'bg-amber-500', textColor: 'text-amber-500', bgLight: 'bg-amber-100', label: '绝密' };
      default:
        return { bgColor: 'bg-gray-500', textColor: 'text-gray-500', bgLight: 'bg-gray-100', label: '普通' };
    }
  };

  const nextImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === characterImages.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? characterImages.length - 1 : prevIndex - 1
    );
  };

  const toggleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  // 格式化数字(例如: 1000 -> 1K)
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'W';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num;
  };

  // 处理能力互动点击
  const handleAbilityInteract = (level, abilityName, interactionType) => {
    console.log(`Interacting with Level ${level} ability: ${abilityName}, Type: ${interactionType}`);

    // 根据 interactionType 执行不同操作 (模拟弹窗)
    switch (interactionType) {
      case 'view_memory':
        alert(`【模拟弹窗】\n正在查看 AI 对您的记忆...\n\n(这里应展示记忆内容，并提供编辑按钮)`);
        // TODO: Implement actual modal logic to show memory data and edit button
        break;
      case 'view_emoji':
        alert(`【模拟弹窗】\n正在加载 [角色名] 的专属表情包...\n\n(这里应展示表情包列表，并提供下载按钮)`);
        // TODO: Implement actual modal logic to display emojis and download button
        break;
      case 'request_tts':
        alert(`【模拟弹窗】\n请输入您想让 [角色名] 朗读的文字：\n\n[输入框]\n\n[生成并播放按钮] [保存按钮]`);
        // TODO: Implement modal with text input, generate, play, save functionality
        break;
      case 'request_song':
        alert(`【模拟弹窗】\n请描述您想让 [角色名] 演唱的歌曲主题/风格：\n\n[输入框]\n\n[生成歌曲按钮]`);
        // TODO: Implement modal for song request
        break;
      case 'request_image':
         alert(`【模拟弹窗】\n请输入您想让 [角色名] 绘制的图片描述：\n\n[输入框]\n\n[生成图片按钮]`);
        // TODO: Implement modal for image request
        break;
       case 'request_video':
         alert(`【模拟弹窗】\n请输入您想让 [角色名] 生成的视频描述/要求：\n\n[输入框]\n\n[生成视频按钮] (功能开发中)`);
        // TODO: Implement modal for video request
        break;
      case 'advanced_interaction':
          alert(`开始与 [角色名] 进行高级多模态互动... (功能开发中)`);
         // TODO: Implement advanced interaction logic
         break;
      default:
        // 默认处理或简单预览
        alert(`正在与 ${abilityName} 能力互动... (功能开发中)`);
    }
  };

  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg">
      {/* 顶部区域 - 角色展示 */}
      <div className="relative h-96 bg-gradient-to-b from-indigo-900 to-purple-800">
        {/* 图片轮播 */}
        <div className="relative h-full overflow-hidden">
          <div className="absolute inset-0 flex justify-center">
            <div
              className={`h-full w-64 ${characterImages[currentImageIndex] === 'css-placeholder-1' ? 'bg-gradient-to-br from-purple-500 to-indigo-600' :
                         characterImages[currentImageIndex] === 'css-placeholder-2' ? 'bg-gradient-to-br from-blue-500 to-purple-600' :
                         'bg-gradient-to-br from-indigo-500 to-blue-600'}`}
            >
              <div className="h-full w-full flex items-center justify-center text-white text-opacity-30 text-6xl font-bold">
                K
              </div>
            </div>
          </div>

          {/* 轮播箭头 */}
          <button onClick={prevImage} className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-30 rounded-full p-1 text-white">
            <ChevronLeft size={24} />
          </button>
          <button onClick={nextImage} className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-30 rounded-full p-1 text-white">
            <ChevronRight size={24} />
          </button>

          {/* 轮播指示器 */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {characterImages.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${currentImageIndex === index ? 'bg-white' : 'bg-white bg-opacity-50'}`}
                onClick={() => setCurrentImageIndex(index)}
              />
            ))}
          </div>
        </div>

        {/* 角色基本信息卡片 */}
        <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-white w-11/12 rounded-xl shadow-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <div className="h-14 w-14 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 flex items-center justify-center">
                  <span className="text-white text-xl font-bold">K</span>
                </div>
                <div className="absolute -top-1 -right-1 bg-green-500 rounded-full h-3 w-3"></div>
              </div>
              <div>
                <div className="flex items-center">
                  <h1 className="text-xl font-bold">赛博侦探 K</h1>
                  <span className="ml-2 text-xs bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded-full">🧘‍♀️ 瑜伽中</span>
                </div>
                <div className="flex items-center text-xs text-gray-600">
                  <User size={12} className="mr-1" />
                  <span>由 <span className="text-purple-600">@硬核创作者</span> 精心打造</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <button className="p-2 bg-purple-100 rounded-full text-purple-600">
                <Share size={18} />
              </button>
              <button
                className={`p-2 rounded-full ${isFollowing ? 'bg-purple-600 text-white' : 'bg-purple-100 text-purple-600'}`}
                onClick={toggleFollow}
              >
                <Star size={18} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 热度值进度条 */}
      <div className="mt-16 px-4">
        <div className="flex justify-between items-center mb-1 text-sm">
          <span className="font-semibold">热度值 (Lv.{currentLevel})</span>
          <span className="text-purple-600">{formatNumber(currentPopularity)} / {formatNumber(maxPopularityForLevel)}</span>
        </div>
        <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-500 mt-1 text-right">
          距离 Lv.{currentLevel + 1} 还需 {formatNumber(Math.max(0, maxPopularityForLevel - currentPopularity))} 热度! ✨
        </div>
      </div>

      {/* 主要操作按钮 */}
      <div className="mt-4 px-4">
        <button className="w-full py-3 rounded-lg bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-semibold flex items-center justify-center">
          <MessageCircle size={18} className="mr-2" />
          发起聊天 💬
        </button>
      </div>

      {/* 核心互动数据 */}
      <div className="mt-6 px-4">
        <div className="grid grid-cols-4 gap-2 text-center">
          <div className="p-2 bg-gray-100 rounded-lg">
            <div className="text-sm font-bold text-indigo-600">5,800+</div>
            <div className="text-xs text-gray-500">今日互动</div>
          </div>
          <div className="p-2 bg-gray-100 rounded-lg">
            <div className="text-sm font-bold text-indigo-600">150K+</div>
            <div className="text-xs text-gray-500">粉丝数</div>
          </div>
          <div className="p-2 bg-gray-100 rounded-lg">
            <div className="text-sm font-bold text-indigo-600">88K+</div>
            <div className="text-xs text-gray-500">收到礼物</div>
          </div>
          <div className="p-2 bg-gray-100 rounded-lg">
            <div className="text-sm font-bold text-indigo-600">Top 1%</div>
            <div className="text-xs text-gray-500">平台排名</div>
          </div>
        </div>
      </div>

      {/* 标签栏 - 修改标签顺序，将"养成互动"放在第二个位置 */}
      <div className="mt-6 border-b border-gray-200">
        <div className="flex">
          <button
            onClick={() => setSelectedTab('metalive')}
            className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'metalive' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
          >
            元•Live
          </button>
          <button
            onClick={() => setSelectedTab('supportInteract')}
            className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'supportInteract' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
          >
            养成互动
          </button>
          <button
            onClick={() => setSelectedTab('about')}
            className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'about' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
          >
            关于 TA
          </button>
        </div>
      </div>

      {/* 标签内容 */}
      <div className="flex-1 p-4 overflow-auto w-full">
        {/* 元•Live标签内容 - 实现元境探秘功能 */}
        {selectedTab === 'metalive' && (
          <div className="space-y-6 w-full">


            {/* 日期分组标记 */}
            <div className="mb-2">
              <div className="inline-block bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full flex items-center">
                <Clock size={12} className="mr-1" />
                {metaLiveContents[0].timestamp}
              </div>
            </div>

            {/* 动态内容列表 - 改造为"元境探秘"功能 */}
            <div className="space-y-6">
              {metaLiveContents.map((item, index) => {
                // 检查是否需要显示新的日期分隔
                const showDateSeparator = index > 0 &&
                  (item.timestamp.includes('天前') && !metaLiveContents[index-1].timestamp.includes('天前') ||
                  item.timestamp.includes('周前') && !metaLiveContents[index-1].timestamp.includes('周前') ||
                  (item.timestamp.includes('小时前') || item.timestamp.includes('昨天')) &&
                  !(metaLiveContents[index-1].timestamp.includes('小时前') || metaLiveContents[index-1].timestamp.includes('昨天')));

                // 检查故事是否被解锁
                const isUnlocked = !item.locked || unlockedStoryIds.includes(item.id);

                // 获取故事锁定类型的颜色
                const lockTypeStyle = getStoryLockTypeColor(item.lockType);

                return (
                  <React.Fragment key={item.id}>
                    {showDateSeparator && (
                      <div className="mt-4 mb-2">
                        <div className="inline-block bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full flex items-center">
                          <Clock size={12} className="mr-1" />
                          {item.timestamp}
                        </div>
                      </div>
                    )}

                    <div className={`bg-white rounded-lg shadow-sm overflow-hidden ${item.isPinned ? 'border-l-4 border-amber-400' : ''}`}>
                      {/* 内容头部 */}
                      <div className="p-3 flex justify-between items-center">
                        <div className="flex items-center">
                          <div className={`p-1.5 rounded-full mr-2 ${item.type === 'video' ? 'bg-indigo-100 text-indigo-600' : 'bg-purple-100 text-purple-600'}`}>
                            {item.type === 'video' ? <Play size={16} /> : <Image size={16} />}
                          </div>
                          <div>
                            <h3 className="text-sm font-medium flex items-center">
                              {item.title}
                              {item.isPinned && (
                                <span className="ml-2 bg-amber-100 text-amber-700 text-xs px-1.5 py-0.5 rounded">置顶</span>
                              )}
                              {item.isHot && (
                                <span className="ml-2 bg-red-100 text-red-600 text-xs px-1.5 py-0.5 rounded flex items-center">
                                  <TrendingUp size={10} className="mr-0.5" />
                                  热门
                                </span>
                              )}

                              {/* 稀有度标记 */}
                              {item.rarityLabel && (
                                <span className={`ml-2 ${lockTypeStyle.bgLight} ${lockTypeStyle.textColor} text-xs px-1.5 py-0.5 rounded flex items-center`}>
                                  <Sparkles size={10} className="mr-0.5" />
                                  {item.rarityLabel}
                                </span>
                              )}
                            </h3>
                            <div className="text-xs text-gray-500 flex items-center">
                              {item.timestamp}

                              {/* 场景类型标签 */}
                              {item.keyScene && (
                                <>
                                  <span className="mx-1">•</span>
                                  <span>{item.keyScene}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        <button className="text-gray-400 p-1">
                          <MoreHorizontal size={16} />
                        </button>
                      </div>

                      {/* 内容主体 */}
                      <div className="px-3 pb-2">
                        {/* 仅显示预览内容，或完整内容（如已解锁） */}
                        <p className="text-sm text-gray-700 mb-3">
                          {isUnlocked ? item.content : `${item.content.substring(0, 80)}${item.content.length > 80 ? '...' : ''}`}

                          {/* 如果有悬念提示且未解锁 */}
                          {!isUnlocked && item.teaser && (
                            <span className="text-purple-600 font-medium"> {item.teaser}</span>
                          )}
                        </p>

                        {/* 媒体内容 */}
                        <div className={getStoryThumbnailClass(item)}>
                          {item.type === 'video' ? (
                            <>
                              <div className="relative aspect-video bg-gray-200 rounded-lg overflow-hidden">
                                <img
                                  src={item.mediaUrl}
                                  alt={item.title}
                                  className="w-full h-full object-cover"
                                />
                                <div className="absolute inset-0 flex items-center justify-center">
                                  <div className="w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                                    <Play size={20} className="text-white ml-1" />
                                  </div>
                                </div>
                                <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-0.5 rounded">
                                  {item.duration}
                                </div>

                                {/* 锁定标记 */}
                                {item.locked && !unlockedStoryIds.includes(item.id) && (
                                  <div className="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-[1px] flex flex-col items-center justify-center p-4">
                                    <Lock size={28} className="text-white mb-2" />
                                    <p className="text-white text-sm font-medium text-center">
                                      解锁观看全部内容
                                    </p>
                                    {item.unlockHint && (
                                      <p className="text-white text-xs text-center mt-1 max-w-[200px] opacity-80">
                                        {item.unlockHint}
                                      </p>
                                    )}
                                  </div>
                                )}
                              </div>
                            </>
                          ) : item.multipleImages ? (
                            // 多图布局
                            <div className="grid grid-cols-3 gap-1 rounded-lg overflow-hidden">
                              {(isUnlocked ? item.fullMediaUrl : item.mediaUrl).map((url, imgIndex) => (
                                <div key={imgIndex} className="aspect-square relative">
                                  <div
                                    className={`w-full h-full ${imgIndex % 3 === 0 ? 'bg-gradient-to-br from-purple-500 to-indigo-600' :
                                    imgIndex % 3 === 1 ? 'bg-gradient-to-br from-blue-500 to-purple-600' :
                                    'bg-gradient-to-br from-indigo-500 to-blue-600'} flex items-center justify-center text-white font-bold`}
                                  >
                                    {imgIndex + 1}
                                  </div>

                                  {/* 锁定标记 (仅在第一张图片上显示) */}
                                  {item.locked && !unlockedStoryIds.includes(item.id) && imgIndex === 0 && (
                                    <div className="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-[1px] flex flex-col items-center justify-center p-2">
                                      <Lock size={20} className="text-white mb-1" />
                                      <p className="text-white text-xs font-medium text-center">
                                        解锁查看
                                      </p>
                                    </div>
                                  )}

                                  {/* 隐藏其他图片的模糊效果 */}
                                  {item.locked && !unlockedStoryIds.includes(item.id) && imgIndex > 0 && (
                                    <div className="absolute inset-0 bg-black bg-opacity-60 backdrop-blur-[2px]"></div>
                                  )}
                                </div>
                              ))}
                            </div>
                          ) : (
                            // 单图布局
                            <div className="relative rounded-lg overflow-hidden">
                              <img
                                src={item.mediaUrl}
                                alt={item.title}
                                className="w-full aspect-[4/3] object-cover rounded-lg"
                              />

                              {/* 锁定标记 */}
                              {item.locked && !unlockedStoryIds.includes(item.id) && (
                                <div className="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-[1px] flex flex-col items-center justify-center p-4">
                                  <Lock size={28} className="text-white mb-2" />
                                  <p className="text-white text-sm font-medium text-center">
                                    解锁观看全部内容
                                  </p>
                                  {item.unlockHint && (
                                    <p className="text-white text-xs text-center mt-1 max-w-[200px] opacity-80">
                                      {item.unlockHint}
                                    </p>
                                  )}
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        {/* 互动数据 和 解锁/操作按钮 */}
                        <div className="flex items-center justify-between mt-3">
                          <div className="flex space-x-4">
                            <div className="flex items-center text-gray-500 text-xs">
                              <Eye size={14} className="mr-1" />
                              {formatNumber(item.statistics.views)}
                            </div>
                            <div className="flex items-center text-gray-500 text-xs">
                              <Heart size={14} className="mr-1" />
                              {formatNumber(item.statistics.likes)}
                            </div>
                            <div className="flex items-center text-gray-500 text-xs">
                              <MessageCircle size={14} className="mr-1" />
                              {item.statistics.comments}
                            </div>
                          </div>

                          {/* 操作按钮 - 解锁或查看完整内容 */}
                          <div className="flex space-x-2">
                            {item.locked && !unlockedStoryIds.includes(item.id) ? (
                              // 解锁按钮
                              <button
                                onClick={() => handleUnlockStory(item.id)}
                                className={`px-3 py-1.5 rounded-lg text-xs font-medium shadow-sm flex items-center space-x-1 ${lockTypeStyle.bgColor} text-white`}
                              >
                                <Unlock size={12} />
                                <span className="ml-1">解锁内容</span>
                                {getUnlockPriceText(item) && (
                                  <span className="ml-1 bg-white bg-opacity-20 px-1.5 py-0.5 rounded-full text-[10px]">
                                    {getUnlockPriceText(item)}
                                  </span>
                                )}
                              </button>
                            ) : (
                              // 已解锁 - 查看完整内容
                              <button
                                onClick={() => handleViewFullStory(item)}
                                className="px-3 py-1.5 bg-indigo-600 text-white rounded-lg text-xs font-medium flex items-center shadow-sm"
                              >
                                <Activity size={12} className="mr-1" />
                                查看全部
                              </button>
                            )}

                            {/* 收藏按钮 */}
                            <button className="p-1.5 bg-gray-100 text-gray-600 rounded-full">
                              <Bookmark size={14} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </React.Fragment>
                );
              })}
            </div>

            {/* 加载更多区域 */}
            <div className="text-center py-3">
              <button className="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm flex items-center mx-auto">
                <RefreshCw size={14} className="mr-1.5" />
                加载更多
              </button>
            </div>
          </div>
        )}

        {/* 养成互动标签内容 - 从第二个代码迁移过来的内容 */}
        {selectedTab === 'supportInteract' && (
          <div className="space-y-8 w-full">

            {/* 区域一：已解锁互动能力 (突出显示) */}
            <div>
              <h3 className="text-md font-semibold flex items-center mb-3">
                <Coffee size={16} className="mr-2 text-purple-600" />
                创作者的话
              </h3>
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="flex items-center mb-2">
                  <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                    <User size={16} />
                  </div>
                  <div>
                    <div className="font-medium">@硬核创作者</div>
                    <div className="text-xs text-gray-500">3天前更新</div>
                  </div>
                </div>
                <p className="text-gray-700 text-sm">
                  "很高兴大家喜欢 K！最近在为他调试新的'案件分析模块'，敬请期待。欢迎在评论区留下你希望 K 拥有的新能力！"
                </p>
                <button className="mt-3 text-sm text-purple-600 font-medium flex items-center">
                  <MessageCircle size={14} className="mr-1" />
                  查看/参与评论 (128条)
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* ========== 元境探秘功能 - 模态框组件 ========== */}

      {/* 解锁故事模态框 - 底部上拉式 */}
      {showUnlockModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center">
          <div className="bg-white rounded-t-xl w-full max-w-md overflow-hidden transform transition-all animate-slide-up">
            <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto my-3"></div>
            {unlockSuccess ? (
              // 解锁成功状态
              <div className="p-6 text-center">
                <div className="mx-auto w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                  <CheckCircle size={32} className="text-green-500" />
                </div>
                <h2 className="text-xl font-bold mb-2">解锁成功</h2>
                <p className="text-gray-600 mb-6">
                  您已成功解锁了「{metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.title}」的完整内容
                </p>
                <div className="animate-pulse">
                  <p className="text-purple-600 text-sm font-medium">
                    正在进入故事体验...
                  </p>
                </div>
              </div>
            ) : (
              // 解锁操作状态
              <>
                <div className="p-4 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h2 className="text-lg font-bold">解锁完整故事</h2>
                    <button
                      onClick={() => setShowUnlockModal(false)}
                      className="p-1 rounded-full hover:bg-gray-100 text-gray-500"
                    >
                      <X size={20} />
                    </button>
                  </div>
                </div>

                <div className="p-4">
                  {/* 故事简介 */}
                  {currentUnlockingStoryId && (
                    <div className="mb-4">
                      <div className="flex items-start">
                        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg overflow-hidden flex-shrink-0 mr-3 flex items-center justify-center text-white font-bold">
                          {metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.type === 'video' ? 'V' : 'I'}
                        </div>
                        <div>
                          <h3 className="font-medium">
                            {metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.title}
                          </h3>
                          <p className="text-xs text-gray-500 mt-1">
                            {metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.timestamp} •
                            {metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.statistics.views} 次查看
                          </p>
                          <div className="flex mt-1">
                            {(() => {
                              const lockType = metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.lockType;
                              const style = getStoryLockTypeColor(lockType);
                              return (
                                <span className={`${style.bgLight} ${style.textColor} text-xs px-2 py-0.5 rounded-full flex items-center`}>
                                  <Sparkles size={10} className="mr-1" />
                                  {style.label}故事
                                </span>
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 解锁特权说明 */}
                  <div className="bg-purple-50 rounded-lg p-3 mb-4 border border-purple-100">
                    <h3 className="text-sm font-medium text-purple-800 flex items-center">
                      <Unlock size={14} className="mr-1.5 text-purple-600" />
                      解锁后您将获得
                    </h3>
                    <ul className="mt-2 space-y-1.5">
                      <li className="text-xs text-purple-700 flex items-start">
                        <CheckCircle size={12} className="mr-1.5 mt-0.5 text-purple-500 flex-shrink-0" />
                        完整故事内容与高清图片/视频
                      </li>
                      <li className="text-xs text-purple-700 flex items-start">
                        <CheckCircle size={12} className="mr-1.5 mt-0.5 text-purple-500 flex-shrink-0" />
                        进入故事场景的专属对话体验
                      </li>
                      <li className="text-xs text-purple-700 flex items-start">
                        <CheckCircle size={12} className="mr-1.5 mt-0.5 text-purple-500 flex-shrink-0" />
                        有机会收集角色独特记忆碎片
                      </li>
                    </ul>
                  </div>

                  {/* 支付方式选择 */}
                  <div className="space-y-2 mb-2">
                    <h3 className="text-sm font-medium mb-2">选择支付方式</h3>

                    {/* 派对币支付 */}
                    <button
                      onClick={() => setSelectedPaymentMethod('partyCoins')}
                      className={`w-full p-3 rounded-lg border flex items-center justify-between ${
                        selectedPaymentMethod === 'partyCoins'
                          ? 'border-yellow-400 bg-yellow-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-2">
                          <Coins size={16} className="text-yellow-600" />
                        </div>
                        <div className="text-left">
                          <div className="text-sm font-medium">派对币支付</div>
                          <div className="text-xs text-gray-500 flex items-center">
                            余额: {userBalance.partyCoins}
                            <span className={`ml-2 ${
                              userBalance.partyCoins >= unlockPrices[metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.lockType]?.partyCoins
                                ? 'text-green-600'
                                : 'text-red-500'
                            }`}>
                              {userBalance.partyCoins >= unlockPrices[metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.lockType]?.partyCoins
                                ? '(余额充足)'
                                : '(余额不足)'
                              }
                            </span>

                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center">
                          <span className="text-sm font-bold mr-2">
                            -{unlockPrices[metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.lockType]?.partyCoins}
                          </span>
                          {selectedPaymentMethod === 'partyCoins' && (
                            <div className="w-5 h-5 rounded-full bg-yellow-500 flex items-center justify-center">
                              <CheckCircle size={12} className="text-white" />
                            </div>
                          )}
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            alert('跳转到派对币充值页面');
                          }}
                          className="mt-2 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium flex items-center justify-center"
                        >
                          <Plus size={14} className="mr-1" />
                          获取派对币
                        </button>
                      </div>
                    </button>

                    {/* 星光币支付 */}
                    <button
                      onClick={() => setSelectedPaymentMethod('starlightCoins')}
                      className={`w-full p-3 rounded-lg border flex items-center justify-between ${
                        selectedPaymentMethod === 'starlightCoins'
                          ? 'border-blue-400 bg-blue-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                          <DollarSign size={16} className="text-blue-600" />
                        </div>
                        <div className="text-left">
                          <div className="text-sm font-medium">星光币支付</div>
                          <div className="text-xs text-gray-500 flex items-center">
                            余额: {userBalance.starlightCoins}
                            <span className={`ml-2 ${
                              userBalance.starlightCoins >= unlockPrices[metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.lockType]?.starlightCoins
                                ? 'text-green-600'
                                : 'text-red-500'
                            }`}>
                              {userBalance.starlightCoins >= unlockPrices[metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.lockType]?.starlightCoins
                                ? '(余额充足)'
                                : '(余额不足)'
                              }
                            </span>

                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center">
                          <span className="text-sm font-bold mr-2">
                            -{unlockPrices[metaLiveContents.find(s => s.id === currentUnlockingStoryId)?.lockType]?.starlightCoins}
                          </span>
                          {selectedPaymentMethod === 'starlightCoins' && (
                            <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center">
                              <CheckCircle size={12} className="text-white" />
                            </div>
                          )}
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            alert('跳转到星光币充值页面');
                          }}
                          className="mt-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium flex items-center justify-center"
                        >
                          <Plus size={14} className="mr-1" />
                          充值星光币
                        </button>
                      </div>
                    </button>

                    {/* 看广告免费解锁 */}
                    <button
                      onClick={() => setSelectedPaymentMethod('watchVideo')}
                      className={`w-full p-3 rounded-lg border flex items-center justify-between ${
                        selectedPaymentMethod === 'watchVideo'
                          ? 'border-green-400 bg-green-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-2">
                          <PlayCircle size={16} className="text-green-600" />
                        </div>
                        <div className="text-left">
                          <div className="text-sm font-medium">观看广告免费解锁</div>
                          <div className="text-xs text-gray-500">
                            观看30秒广告即可免费解锁本故事
                          </div>
                        </div>
                      </div>
                      {selectedPaymentMethod === 'watchVideo' && (
                        <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                          <CheckCircle size={12} className="text-white" />
                        </div>
                      )}
                    </button>
                  </div>
                </div>

                <div className="p-4 border-t border-gray-200">
                  <button
                    onClick={handleConfirmUnlock}
                    className="w-full py-3 bg-purple-600 text-white rounded-lg font-medium"
                  >
                    确认解锁
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* 故事体验模态框 - 底部上拉式 */}
      {showStoryExperienceModal && selectedStory && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex flex-col animate-slide-up">
          {/* 顶部导航栏 */}
          <div className="bg-gradient-to-b from-black to-transparent px-4 py-4 flex items-center justify-between">
            <button
              onClick={() => setShowStoryExperienceModal(false)}
              className="p-2 rounded-full bg-black bg-opacity-50 text-white"
            >
              <ChevronLeft size={20} />
            </button>
            <h2 className="text-white font-medium">{selectedStory.title}</h2>
            <button className="p-2 rounded-full bg-black bg-opacity-50 text-white">
              <Share size={18} />
            </button>
          </div>

          {/* 滚动内容区域 */}
          <div className="flex-1 overflow-auto pb-24">
            <div className="px-4 py-2 max-w-md mx-auto">
              {/* 媒体展示 */}
              <div className="mb-4 rounded-lg overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1633266841047-719b5f737149?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  alt={selectedStory.title}
                  className="w-full rounded-lg object-cover h-64"
                />
              </div>

              {/* 故事内容 */}
              <div className="bg-white rounded-lg p-4 mb-4">
                <h2 className="text-lg font-bold mb-2">{selectedStory.title}</h2>
                <div className="flex items-center mb-3 text-sm text-gray-500">
                  <Clock size={14} className="mr-1" />
                  <span>{selectedStory.timestamp}</span>
                  <span className="mx-2">•</span>
                  <Eye size={14} className="mr-1" />
                  <span>{formatNumber(selectedStory.statistics.views)} 次查看</span>
                </div>

                <div className="prose prose-sm text-gray-700 mb-4 whitespace-pre-line">
                  {selectedStory.fullContent || selectedStory.content}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <button className="flex items-center text-gray-500">
                      <Heart size={18} className="mr-1" />
                      <span>{formatNumber(selectedStory.statistics.likes)}</span>
                    </button>
                    <button className="flex items-center text-gray-500">
                      <MessageCircle size={18} className="mr-1" />
                      <span>{selectedStory.statistics.comments}</span>
                    </button>
                  </div>
                  <button className="text-gray-500">
                    <Bookmark size={18} />
                  </button>
                </div>
              </div>

              {/* 进入情境聊天按钮 */}
              <div className="bg-indigo-600 rounded-lg p-4 mb-4">
                <div className="flex items-start">
                  <div className="mt-1 bg-white bg-opacity-20 rounded-lg p-2 mr-3">
                    <MessageCircle size={20} className="text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-medium mb-1">进入故事场景，与角色深度互动</h3>
                    <p className="text-indigo-100 text-sm mb-3">
                      在故事的具体情境中与赛博侦探 K 对话，探索更多细节与剧情
                    </p>
                    <button
                      onClick={handleStoryContextChat}
                      className="w-full py-2 bg-white text-indigo-600 rounded-lg font-medium text-sm flex items-center justify-center"
                    >
                      <Activity size={16} className="mr-1.5" />
                      开始情境对话
                    </button>
                  </div>
                </div>
              </div>


            </div>
          </div>

          {/* 情境对话按钮 - 悬浮在底部 */}
          <div className="fixed bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black to-transparent">
            <div className="max-w-md mx-auto">
              <button
                onClick={handleStoryContextChat}
                className="w-full py-3 bg-indigo-600 text-white rounded-lg font-medium text-sm flex items-center justify-center shadow-lg"
              >
                <MessageCircle size={18} className="mr-2" />
                开始情境对话
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 情境聊天模态框 - mobile-style UI */}
      {showContextChatModal && selectedStory && (
        <div className="fixed inset-0 z-50 flex flex-col max-w-md mx-auto"
          style={{
            backgroundImage: 'url(https://images.unsplash.com/photo-1633266841047-719b5f737149?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}>
          <div className="absolute inset-0 bg-black bg-opacity-70"></div>
          {/* 顶部导航栏 */}
          <div className="px-4 py-3 flex items-center justify-between border-b border-white border-opacity-10">
            <button
              onClick={() => {
                setShowContextChatModal(false);
                setShowStoryExperienceModal(true);
              }}
              className="p-2 rounded-full bg-white bg-opacity-10 text-white"
            >
              <ChevronLeft size={20} />
            </button>
            <div className="text-center">
              <h2 className="text-white font-medium">{selectedStory.title}</h2>
              <p className="text-xs text-indigo-200">情境对话中</p>
            </div>
            <div className="w-10">
              {/* 占位，保持标题居中 */}
            </div>
          </div>

          {/* 聊天区域 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {/* 提示卡片 */}
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 border border-white border-opacity-20">
              <p className="text-white text-xs">
                你已进入「{selectedStory.title}」的情境对话。赛博侦探 K 将以故事中的角色状态与你互动。
              </p>
            </div>

            {/* 聊天消息 */}
            {chatHistory.map((message, index) => (
              <div key={index} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[80%] rounded-lg p-3 ${
                  message.type === 'user'
                    ? 'bg-indigo-600 text-white rounded-tr-none'
                    : 'bg-white bg-opacity-10 backdrop-blur-sm text-white rounded-tl-none border border-white border-opacity-20'
                }`}>
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  <div className={`text-[10px] mt-1 ${message.type === 'user' ? 'text-indigo-200' : 'text-indigo-200'}`}>
                    {formatTimestamp(message.timestamp)}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 输入区域 */}
          <div className="p-3 border-t border-white border-opacity-10 bg-indigo-900 bg-opacity-40 backdrop-blur-sm">
            <div className="flex items-center">
              <div className="flex-1 bg-white bg-opacity-10 rounded-full border border-white border-opacity-20 p-1 pl-4">
                <input
                  ref={chatInputRef}
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSendChatMessage()}
                  placeholder="在故事场景中与角色对话..."
                  className="w-full bg-transparent text-white placeholder-indigo-200 focus:outline-none text-sm"
                />
              </div>
              <button
                onClick={handleSendChatMessage}
                disabled={!chatInput.trim()}
                className={`ml-2 w-10 h-10 rounded-full flex items-center justify-center ${
                  chatInput.trim() ? 'bg-indigo-500 text-white' : 'bg-white bg-opacity-10 text-indigo-300'
                }`}
              >
                <Send size={18} />
              </button>
            </div>
          </div>

          {/* 记忆碎片收集动画 - 底部上拉式 */}
          {showMemoryAnimation && (
            <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-end justify-center">
              <div className="relative bg-white rounded-t-xl w-full max-w-md p-6 animate-slide-up">
                <div className="w-32 h-32 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center animate-pulse">
                  <Brain size={48} className="text-white animate-bounce" />
                </div>
                <div className="absolute -top-4 -right-4 w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center animate-ping">
                  <Sparkles size={20} className="text-white" />
                </div>
                <div className="mt-4 text-center">
                  <h3 className="text-white text-lg font-bold animate-pulse">发现记忆碎片!</h3>
                  <p className="text-indigo-200 text-sm mt-1">收集中...</p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 记忆碎片模态框 - 底部上拉式 */}
      {showMemoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-end justify-center">
          <div className="bg-white rounded-t-xl w-full max-w-md overflow-hidden animate-slide-up">
            <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto my-3"></div>
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-bold flex items-center">
                <Brain size={18} className="mr-2 text-purple-600" />
                记忆碎片收集
              </h2>
              <button
                onClick={() => setShowMemoryModal(false)}
                className="p-1 rounded-full hover:bg-gray-100 text-gray-500"
              >
                <X size={20} />
              </button>
            </div>

            {memoryFragments.length > 0 ? (
              <>
                <div className="p-4 max-h-[60vh] overflow-y-auto">
                  {/* 最新收集的记忆碎片 */}
                  {memoryContentRef.current && (
                    <div className="bg-purple-50 rounded-lg p-4 mb-4 border border-purple-200 animate-pulse">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 mr-2 flex items-center justify-center">
                            <Sparkles size={14} className="text-white" />
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-purple-800">发现新记忆碎片!</h3>
                            <p className="text-xs text-purple-600">刚刚获取</p>
                          </div>
                        </div>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          memoryContentRef.current.rarity === 'epic' ? 'bg-amber-100 text-amber-700' :
                          memoryContentRef.current.rarity === 'rare' ? 'bg-purple-100 text-purple-700' :
                          memoryContentRef.current.rarity === 'uncommon' ? 'bg-blue-100 text-blue-700' :
                          'bg-green-100 text-green-700'
                        }`}>
                          {memoryContentRef.current.rarity === 'epic' ? '传奇' :
                           memoryContentRef.current.rarity === 'rare' ? '珍稀' :
                           memoryContentRef.current.rarity === 'uncommon' ? '稀有' :
                           '普通'}
                        </span>
                      </div>
                      <h4 className="text-sm font-medium text-purple-900 mb-2">
                        {memoryContentRef.current.title}
                      </h4>
                      <p className="text-xs text-purple-700 bg-purple-100 p-3 rounded-lg mb-2">
                        {memoryContentRef.current.content}
                      </p>
                      <div className="text-right">
                        <button
                          onClick={handleCollectMemoryFragment}
                          className="text-xs text-purple-600 font-medium"
                        >
                          收藏到我的碎片库
                        </button>
                      </div>
                    </div>
                  )}

                  {/* 已收集的记忆碎片列表 */}
                  <h3 className="text-sm font-medium mb-3">已收集碎片 ({memoryFragments.length})</h3>
                  <div className="space-y-3">
                    {memoryFragments.map((fragment) => (
                      <div
                        key={fragment.id}
                        className="bg-gray-50 rounded-lg p-3 border border-gray-200"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <div className={`w-7 h-7 rounded-full mr-2 flex items-center justify-center ${
                              fragment.rarity === 'epic' ? 'bg-amber-100 text-amber-600' :
                              fragment.rarity === 'rare' ? 'bg-purple-100 text-purple-600' :
                              fragment.rarity === 'uncommon' ? 'bg-blue-100 text-blue-600' :
                              'bg-green-100 text-green-600'
                            }`}>
                              {fragment.iconType === 'detective' && <Eye size={12} />}
                              {fragment.iconType === 'coffee' && <Coffee size={12} />}
                              {fragment.iconType === 'dance' && <Music size={12} />}
                              {fragment.iconType === 'mind' && <Brain size={12} />}
                              {fragment.iconType === 'movement' && <Activity size={12} />}
                            </div>
                            <div className="text-sm font-medium truncate">
                              {fragment.title}
                            </div>
                          </div>
                          <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                            fragment.rarity === 'epic' ? 'bg-amber-100 text-amber-700' :
                            fragment.rarity === 'rare' ? 'bg-purple-100 text-purple-700' :
                            fragment.rarity === 'uncommon' ? 'bg-blue-100 text-blue-700' :
                            'bg-green-100 text-green-700'
                          }`}>
                            {fragment.rarity === 'epic' ? '传奇' :
                             fragment.rarity === 'rare' ? '珍稀' :
                             fragment.rarity === 'uncommon' ? '稀有' :
                             '普通'}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600 mb-2">
                          从「{metaLiveContents.find(s => s.id === fragment.fromStory)?.title}」故事中获取
                        </div>
                        <button className="w-full text-center text-xs text-indigo-600 font-medium py-1 border border-indigo-200 rounded-md bg-indigo-50">
                          查看内容
                        </button>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">碎片收集进度</span>
                    <span className="text-sm text-purple-600 font-medium">
                      {memoryFragments.length}/20
                    </span>
                  </div>
                  <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full"
                      style={{ width: `${(memoryFragments.length / 20) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    继续解锁故事并进行情境对话，收集更多记忆碎片完成角色记忆重构
                  </p>
                </div>
              </>
            ) : (
              <div className="p-6 text-center">
                <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-3">
                  <Brain size={24} className="text-gray-400" />
                </div>
                <h3 className="text-lg font-medium mb-2">未发现记忆碎片</h3>
                <p className="text-sm text-gray-500 mb-4">
                  通过解锁故事并进行情境对话，有机会收集角色独特的记忆碎片
                </p>
                <button
                  onClick={() => setShowMemoryModal(false)}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium"
                >
                  开始探索
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AICharacterProfile;