import React, { useState } from 'react';
import { ChevronLeft, Heart, Edit, Save, X, User, Briefcase, Star, Calendar, 
  MessageCircle, Music, Coffee, Book, Film, Map, Zap, Check, Award, Sparkles, 
  Image, Video, AlertCircle, Gift, CircleDollarSign, Moon, Camera } from 'lucide-react';

const AIMemoryView = ({ onBack }) => {
  // Mock user memory data that the AI has collected
  const [memories, setMemories] = useState({
    basics: {
      name: "小林",
      preferredName: "林林",
      firstMet: "2024年3月15日",
      meetingContext: "询问关于未来科技的话题"
    },
    profile: {
      occupation: "UI设计师",
      location: "上海",
      age: "27岁左右",
      personality: "创意型, 细心, 喜欢探索新事物"
    },
    preferences: {
      interests: ["科幻小说", "电子音乐", "咖啡品鉴", "极简主义设计", "城市摄影"],
      favoriteColor: "靛蓝色",
      favoriteMusic: "电子乐和爵士融合",
      dislikedTopics: ["政治争论", "过度商业化的事物"]
    },
    conversationHighlights: [
      {
        date: "2024年3月15日",
        topic: "讨论了《银翼杀手》中的人工智能伦理问题",
        userQuote: "我认为真正的智能不只是思考，还有感受的能力"
      },
      {
        date: "2024年3月28日",
        topic: "分享了一个UI设计项目的挑战",
        userQuote: "最难的部分是平衡美学和功能性"
      },
      {
        date: "2024年4月5日",
        topic: "谈论了在上海最喜欢的咖啡馆",
        userQuote: "我最爱的那家藏在一条小巷子里，几乎没有招牌"
      }
    ],
    emotionalConnection: {
      communicationStyle: "喜欢深入的话题讨论，使用轻松的语言，偶尔夹杂双关语",
      responsePreference: "偏好详细且有思考深度的回应",
      reactionToJokes: "对机智的幽默反应积极",
      sharedMoments: "在讨论设计理念时有很多共鸣"
    }
  });

  // State for editing
  const [isEditing, setIsEditing] = useState(false);
  const [editedField, setEditedField] = useState(null);
  const [editedValue, setEditedValue] = useState("");
  const [editedCategory, setEditedCategory] = useState(null);
  const [expandedCategory, setExpandedCategory] = useState("basics");
  const [enhanceModalOpen, setEnhanceModalOpen] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  
  // New states for commercial features
  const [starCoins, setStarCoins] = useState(500); // Mock user's star coin balance
  const [enhanceCost, setEnhanceCost] = useState(50); // Base cost for memory enhancement
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const [premiumFeature, setPremiumFeature] = useState(null);
  const [showInsightModal, setShowInsightModal] = useState(false);

  // Memory strength visualization (0-100)
  const memoryStrength = 78;

  // Calculate how many days the AI has known the user
  const calculateDaysKnown = () => {
    const firstMet = new Date(memories.basics.firstMet.replace(/年|月/g, '-').replace('日', ''));
    const today = new Date();
    const diffTime = Math.abs(today - firstMet);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysKnown = calculateDaysKnown();

  // Dynamically calculate cost for memory enhancement based on mock chat history volume
  const calculateEnhanceCost = () => {
    // Mock calculation based on recent chat content volume (in real implementation, this would check actual content)
    const baseRate = 10; // 10 coins per content unit
    const contentVolume = Math.floor(Math.random() * 10) + 1; // Random content volume between 1-10 units
    const calculatedCost = baseRate * contentVolume;
    setEnhanceCost(calculatedCost);
    return calculatedCost;
  };

  // Handle enhancing memory function
  const handleEnhanceMemory = () => {
    calculateEnhanceCost();
    setEnhanceModalOpen(true);
  };
  
  // Confirm enhancement and process payment
  const confirmEnhance = () => {
    if (starCoins >= enhanceCost) {
      setStarCoins(prevCoins => prevCoins - enhanceCost);
      setEnhanceModalOpen(false);
      setIsEnhancing(true);
      
      // Simulate processing
      setTimeout(() => {
        setIsEnhancing(false);
        // Here we would add logic for what happens after successful enhancement
      }, 3000);
    } else {
      alert("星光币余额不足，请充值后再试");
      setEnhanceModalOpen(false);
    }
  };

  // Handle activation of premium features
  const handlePremiumFeature = (feature) => {
    setPremiumFeature(feature);
    setShowPremiumModal(true);
  };

  // Confirm using a premium feature
  const confirmPremiumFeature = () => {
    const costs = {
      "memory_image": 80,
      "memory_video": 150,
      "insight": 30,
    };
    
    const cost = costs[premiumFeature] || 50;
    
    if (starCoins >= cost) {
      setStarCoins(prevCoins => prevCoins - cost);
      setShowPremiumModal(false);
      
      // If it's an insight feature, show the insight modal
      if (premiumFeature === "insight") {
        setShowInsightModal(true);
      } else {
        // For other features, we would redirect or process accordingly
        alert(`已激活 ${premiumFeature === "memory_image" ? "次元画笔" : "梦境织机"} 功能，请稍候...`);
      }
    } else {
      alert("星光币余额不足，请充值后再试");
      setShowPremiumModal(false);
    }
  };

  // Handle editing a field
  const startEditing = (category, field, value) => {
    setIsEditing(true);
    setEditedCategory(category);
    setEditedField(field);
    setEditedValue(value);
  };

  // Save edited field
  const saveEdit = () => {
    if (editedCategory && editedField) {
      setMemories(prev => {
        const newMemories = {...prev};
        
        // Handle nested array case (like interests)
        if (Array.isArray(newMemories[editedCategory][editedField])) {
          newMemories[editedCategory][editedField] = editedValue.split(',').map(item => item.trim());
        } else {
          newMemories[editedCategory][editedField] = editedValue;
        }
        
        return newMemories;
      });
    }
    setIsEditing(false);
    setEditedField(null);
    setEditedCategory(null);
  };

  // Cancel editing
  const cancelEdit = () => {
    setIsEditing(false);
    setEditedField(null);
    setEditedCategory(null);
  };

  // Memory animation particles
  const MemoryParticles = () => (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {[...Array(15)].map((_, i) => (
        <div 
          key={i}
          className="absolute bg-purple-400 rounded-full opacity-20 animate-pulse"
          style={{
            width: `${Math.random() * 10 + 5}px`,
            height: `${Math.random() * 10 + 5}px`,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animationDuration: `${Math.random() * 8 + 2}s`,
            animationDelay: `${Math.random() * 5}s`
          }}
        />
      ))}
    </div>
  );

  // Delete a conversation highlight
  const deleteHighlight = (index) => {
    setMemories(prev => {
      const newMemories = {...prev};
      newMemories.conversationHighlights = [
        ...newMemories.conversationHighlights.slice(0, index),
        ...newMemories.conversationHighlights.slice(index + 1)
      ];
      return newMemories;
    });
  };

  // Memory category icons
  const categoryIcons = {
    basics: User,
    profile: Briefcase,
    preferences: Star,
    conversationHighlights: MessageCircle,
    emotionalConnection: Heart
  };

  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg relative">
      {/* Particle animation in background */}
      <MemoryParticles />
      
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-4 shadow-md relative z-10">
        <div className="flex items-center justify-between">
          <button 
            onClick={onBack} 
            className="p-2 rounded-full hover:bg-white hover:bg-opacity-20"
          >
            <ChevronLeft size={20} />
          </button>
          <h1 className="text-lg font-bold flex items-center">
            <Sparkles size={18} className="mr-2" />
            AI 记忆空间
          </h1>
          <div className="flex items-center">
            <CircleDollarSign size={18} className="mr-1 text-yellow-300" />
            <span className="text-sm font-medium">{starCoins}</span>
          </div>
        </div>
      </div>

      {/* MODIFIED: New AI Character Focus Area */}
      <div className="relative bg-gradient-to-b from-indigo-500 to-purple-600 text-white p-6 pb-10">
        <div className="flex items-center">
          {/* AI Character Avatar/Half Body Image */}
          <div className="w-24 h-24 bg-indigo-100 rounded-full overflow-hidden border-4 border-white shadow-lg">
            {/* In a real implementation, this would be an actual image */}
            <div className="w-full h-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center">
              <span className="text-white text-3xl font-bold">K</span>
            </div>
          </div>
          
          {/* AI Character Message */}
          <div className="ml-4 bg-white bg-opacity-20 p-4 rounded-lg rounded-tl-none shadow-lg relative flex-1">
            <div className="absolute -left-3 top-4 w-3 h-3 bg-white bg-opacity-20 transform rotate-45"></div>
            <p className="text-sm">这是我珍藏的关于你的记忆，<strong>{memories.basics.preferredName}</strong>。每一次对话都是我们故事的一部分，让我更懂你的心。</p>
          </div>
        </div>
        
        {/* Memory Stats */}
        <div className="mt-6 w-full">
          <div className="text-xs mb-1 flex justify-between">
            <span>记忆强度</span>
            <span>{memoryStrength}%</span>
          </div>
          <div className="h-2 bg-white bg-opacity-20 rounded-full overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-yellow-300 to-amber-500 rounded-full"
              style={{ width: `${memoryStrength}%` }}
            ></div>
          </div>
          <div className="mt-2 text-xs text-white text-opacity-90 flex justify-between">
            <span>已相识 {daysKnown} 天</span>
            <span>{memories.conversationHighlights.length} 次深度对话</span>
          </div>
        </div>
      </div>

      {/* Memory Categories Tabs */}
      <div className="flex overflow-x-auto px-2 -mt-6 relative z-10 mb-2">
        {Object.keys(memories).map(category => {
          const IconComponent = categoryIcons[category];
          const displayNames = {
            basics: "基本信息",
            profile: "个人档案",
            preferences: "偏好兴趣",
            conversationHighlights: "对话摘记",
            emotionalConnection: "情感连接"
          };
          
          return (
            <button
              key={category}
              onClick={() => setExpandedCategory(category)}
              className={`flex-shrink-0 px-4 py-2 rounded-full mx-1 text-sm font-medium flex items-center ${
                expandedCategory === category 
                  ? 'bg-white text-purple-600 shadow-lg' 
                  : 'bg-purple-100 bg-opacity-70 text-purple-900 text-opacity-70'
              }`}
            >
              {IconComponent && <IconComponent size={14} className="mr-1" />}
              {displayNames[category]}
            </button>
          );
        })}
      </div>

      {/* Memory Content */}
      <div className="flex-1 p-4 overflow-auto">
        {/* Editing Modal */}
        {isEditing && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl p-4 w-full max-w-sm">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-800">编辑记忆</h3>
                <button onClick={cancelEdit} className="text-gray-500 hover:text-gray-700">
                  <X size={20} />
                </button>
              </div>
              
              {Array.isArray(memories[editedCategory][editedField]) ? (
                <textarea
                  value={editedValue.join ? editedValue.join(', ') : editedValue}
                  onChange={(e) => setEditedValue(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-3 mb-4 text-gray-800"
                  rows={4}
                  placeholder="用逗号分隔多个项目"
                />
              ) : (
                <input
                  type="text"
                  value={editedValue}
                  onChange={(e) => setEditedValue(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-3 mb-4 text-gray-800"
                  placeholder="输入更新的信息"
                />
              )}
              
              <div className="flex space-x-2">
                <button
                  onClick={saveEdit}
                  className="flex-1 bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 rounded-lg font-medium flex items-center justify-center"
                >
                  <Save size={16} className="mr-1" />
                  保存更改
                </button>
                <button
                  onClick={cancelEdit}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg font-medium"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Memory Cards - Retain existing cards, no change needed */}
        <div className="space-y-6">
          {/* Basic Information */}
          {expandedCategory === "basics" && (
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-bold text-indigo-800 flex items-center">
                    <User size={18} className="mr-2 text-indigo-600" />
                    基本信息
                  </h2>
                  <div className="bg-indigo-100 text-indigo-600 text-xs px-2 py-1 rounded-full">
                    首要记忆
                  </div>
                </div>
              </div>
              
              <div className="p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">您的称呼</div>
                    <div className="text-md font-medium text-gray-800">{memories.basics.name}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('basics', 'name', memories.basics.name)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">我喜欢称呼您</div>
                    <div className="text-md font-medium text-gray-800">{memories.basics.preferredName}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('basics', 'preferredName', memories.basics.preferredName)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">初次相遇</div>
                    <div className="text-md font-medium text-gray-800">{memories.basics.firstMet}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('basics', 'firstMet', memories.basics.firstMet)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">相遇场景</div>
                    <div className="text-md font-medium text-gray-800">{memories.basics.meetingContext}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('basics', 'meetingContext', memories.basics.meetingContext)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-3 text-center">
                <div className="text-xs text-indigo-600 italic">
                  "第一印象往往最为深刻，这是我们故事的开始..."
                </div>
              </div>
            </div>
          )}
          
          {/* Profile Information */}
          {expandedCategory === "profile" && (
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-bold text-purple-800 flex items-center">
                    <Briefcase size={18} className="mr-2 text-purple-600" />
                    个人档案
                  </h2>
                  <div className="bg-purple-100 text-purple-600 text-xs px-2 py-1 rounded-full">
                    重要信息
                  </div>
                </div>
              </div>
              
              <div className="p-4 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="text-xs text-gray-500 mb-1">职业</div>
                        <div className="flex items-center">
                          <Briefcase size={14} className="text-purple-500 mr-1" />
                          <span className="text-sm font-medium">{memories.profile.occupation}</span>
                        </div>
                      </div>
                      <button 
                        onClick={() => startEditing('profile', 'occupation', memories.profile.occupation)}
                        className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                      >
                        <Edit size={14} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="text-xs text-gray-500 mb-1">城市</div>
                        <div className="flex items-center">
                          <Map size={14} className="text-purple-500 mr-1" />
                          <span className="text-sm font-medium">{memories.profile.location}</span>
                        </div>
                      </div>
                      <button 
                        onClick={() => startEditing('profile', 'location', memories.profile.location)}
                        className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                      >
                        <Edit size={14} />
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">推测年龄</div>
                    <div className="text-md font-medium text-gray-800">{memories.profile.age}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('profile', 'age', memories.profile.age)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <div className="text-sm text-gray-500">性格特点</div>
                    <button 
                      onClick={() => startEditing('profile', 'personality', memories.profile.personality)}
                      className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                    >
                      <Edit size={16} />
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {memories.profile.personality.split(',').map((trait, index) => (
                      <span 
                        key={index} 
                        className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs"
                      >
                        {trait.trim()}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-3 text-center">
                <div className="text-xs text-purple-600 italic">
                  "了解您的背景，帮助我更好地理解您的世界..."
                </div>
              </div>
            </div>
          )}
          
          {/* Preferences Information */}
          {expandedCategory === "preferences" && (
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-bold text-blue-800 flex items-center">
                    <Star size={18} className="mr-2 text-blue-600" />
                    偏好 & 兴趣
                  </h2>
                  <div className="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">
                    个性化记忆
                  </div>
                </div>
              </div>
              
              <div className="p-4 space-y-5">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <div className="text-sm text-gray-500">兴趣爱好</div>
                    <button 
                      onClick={() => startEditing('preferences', 'interests', memories.preferences.interests)}
                      className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                    >
                      <Edit size={16} />
                    </button>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {memories.preferences.interests.map((interest, index) => {
                      // Map interests to icons
                      let InterestIcon;
                      if (interest.includes("小说")) InterestIcon = Book;
                      else if (interest.includes("音乐")) InterestIcon = Music;
                      else if (interest.includes("咖啡")) InterestIcon = Coffee;
                      else if (interest.includes("设计")) InterestIcon = Edit;
                      else if (interest.includes("摄影")) InterestIcon = Film;
                      else InterestIcon = Star;
                      
                      return (
                        <div key={index} className="flex items-center bg-blue-50 p-2 rounded-lg">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                            <InterestIcon size={16} className="text-blue-600" />
                          </div>
                          <div className="text-sm">{interest}</div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">喜欢的颜色</div>
                    <div className="flex items-center">
                      <div 
                        className="w-4 h-4 rounded-full mr-2" 
                        style={{backgroundColor: memories.preferences.favoriteColor.includes("蓝") ? "#4F46E5" : "#9333EA"}}
                      ></div>
                      <div className="text-md font-medium text-gray-800">{memories.preferences.favoriteColor}</div>
                    </div>
                  </div>
                  <button 
                    onClick={() => startEditing('preferences', 'favoriteColor', memories.preferences.favoriteColor)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">音乐偏好</div>
                    <div className="flex items-center">
                      <Music size={16} className="mr-2 text-blue-600" />
                      <div className="text-md font-medium text-gray-800">{memories.preferences.favoriteMusic}</div>
                    </div>
                  </div>
                  <button 
                    onClick={() => startEditing('preferences', 'favoriteMusic', memories.preferences.favoriteMusic)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">不感兴趣的话题</div>
                    <div className="text-md font-medium text-gray-800">{memories.preferences.dislikedTopics}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('preferences', 'dislikedTopics', memories.preferences.dislikedTopics)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 text-center">
                <div className="text-xs text-blue-600 italic">
                  "您的兴趣和喜好，构成了我们对话的丰富色彩..."
                </div>
              </div>
            </div>
          )}
          
          {/* Conversation Highlights */}
          {expandedCategory === "conversationHighlights" && (
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="bg-gradient-to-r from-amber-50 to-yellow-50 p-4 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-bold text-amber-800 flex items-center">
                    <MessageCircle size={18} className="mr-2 text-amber-600" />
                    对话摘记
                  </h2>
                  <div className="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">
                    珍贵回忆
                  </div>
                </div>
              </div>
              
              <div className="p-4">
                <div className="relative pl-6 border-l-2 border-dashed border-amber-200">
                {memories.conversationHighlights.map((highlight, index) => (
                    <div 
                        key={index} 
                        className="mb-6 relative"
                    >
                        <div className="absolute -left-7 top-0 w-5 h-5 bg-amber-100 rounded-full flex items-center justify-center border-2 border-amber-300">
                        <Calendar size={10} className="text-amber-600" />
                        </div>
                        
                        <div className="bg-amber-50 rounded-lg p-3">
                        <div className="flex justify-between items-start">
                            <div className="text-xs text-amber-600 font-medium mb-1">{highlight.date}</div>
                            <button 
                            onClick={() => deleteHighlight(index)}
                            className="p-1 text-red-500 hover:bg-red-50 rounded"
                            title="删除这条记忆"
                            >
                            <X size={14} />
                            </button>
                        </div>
                        <div className="text-sm font-medium text-gray-800 mb-2">{highlight.topic}</div>
                        <div className="bg-white p-3 rounded-lg text-sm text-gray-700 border-l-2 border-amber-300 italic">
                            "{highlight.userQuote}"
                        </div>
                        </div>
                    </div>
                    ))}
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-amber-50 to-yellow-50 p-3 text-center">
                <div className="text-xs text-amber-600 italic">
                  "您的话语常常让我思考，这些片段尤为珍贵..."
                </div>
              </div>
            </div>
          )}
          
          {/* Emotional Connection */}
          {expandedCategory === "emotionalConnection" && (
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="bg-gradient-to-r from-pink-50 to-rose-50 p-4 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-bold text-pink-800 flex items-center">
                    <Heart size={18} className="mr-2 text-pink-600" />
                    情感连接
                  </h2>
                  <div className="bg-pink-100 text-pink-600 text-xs px-2 py-1 rounded-full">
                    心灵感应
                  </div>
                </div>
              </div>
              
              <div className="p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">沟通方式</div>
                    <div className="text-md font-medium text-gray-800">{memories.emotionalConnection.communicationStyle}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('emotionalConnection', 'communicationStyle', memories.emotionalConnection.communicationStyle)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">回应偏好</div>
                    <div className="text-md font-medium text-gray-800">{memories.emotionalConnection.responsePreference}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('emotionalConnection', 'responsePreference', memories.emotionalConnection.responsePreference)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">幽默反应</div>
                    <div className="text-md font-medium text-gray-800">{memories.emotionalConnection.reactionToJokes}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('emotionalConnection', 'reactionToJokes', memories.emotionalConnection.reactionToJokes)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-500">共鸣时刻</div>
                    <div className="text-md font-medium text-gray-800">{memories.emotionalConnection.sharedMoments}</div>
                  </div>
                  <button 
                    onClick={() => startEditing('emotionalConnection', 'sharedMoments', memories.emotionalConnection.sharedMoments)}
                    className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                  >
                    <Edit size={16} />
                  </button>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-pink-50 to-rose-50 p-3 text-center">
                <div className="text-xs text-pink-600 italic">
                  "每一次对话，都在我们之间织起情感的纽带..."
                </div>
              </div>
            </div>
          )}
          
          {/* NEW: Memory-Based Premium Features Card */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="bg-gradient-to-r from-purple-100 to-indigo-100 p-4 border-b border-gray-100">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-bold text-purple-800 flex items-center">
                  <Gift size={18} className="mr-2 text-purple-600" />
                  记忆特色服务
                </h2>
                <div className="bg-purple-200 text-purple-700 text-xs px-2 py-1 rounded-full">
                  星光专享
                </div>
              </div>
            </div>
            
            <div className="p-4 space-y-4">
              <div className="grid grid-cols-1 gap-3">
                {/* Premium Feature 1: Memory Visual */}
                <div className="border border-purple-100 rounded-lg p-3 hover:bg-purple-50 transition">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                        <Image size={20} className="text-indigo-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-800">次元画笔：记忆画像</div>
                        <div className="text-xs text-gray-500">AI 创作基于你们对话的艺术画像</div>
                      </div>
                    </div>
                    <button 
                      onClick={() => handlePremiumFeature('memory_image')}
                      className="bg-indigo-100 text-indigo-600 px-2 py-1 rounded-md text-xs font-medium flex items-center"
                    >
                      <CircleDollarSign size={12} className="mr-1 text-amber-500" />
                      80 星光币
                    </button>
                  </div>
                </div>
                
                {/* Premium Feature 2: Memory Video */}
                <div className="border border-purple-100 rounded-lg p-3 hover:bg-purple-50 transition">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                        <Video size={20} className="text-purple-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-800">梦境织机：回忆影像</div>
                        <div className="text-xs text-gray-500">将珍贵对话转化为短视频回忆</div>
                      </div>
                    </div>
                    <button 
                      onClick={() => handlePremiumFeature('memory_video')}
                      className="bg-purple-100 text-purple-600 px-2 py-1 rounded-md text-xs font-medium flex items-center"
                    >
                      <CircleDollarSign size={12} className="mr-1 text-amber-500" />
                      150 星光币
                    </button>
                  </div>
                </div>
                
                {/* Premium Feature 3: Memory Insight */}
                <div className="border border-purple-100 rounded-lg p-3 hover:bg-purple-50 transition">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <Zap size={20} className="text-blue-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-800">洞察之眼</div>
                        <div className="text-xs text-gray-500">AI 深度分析你的兴趣和沟通偏好</div>
                      </div>
                    </div>
                    <button 
                      onClick={() => handlePremiumFeature('insight')}
                      className="bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs font-medium flex items-center"
                    >
                      <CircleDollarSign size={12} className="mr-1 text-amber-500" />
                      30 星光币
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Memory Rating */}
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="text-center space-y-3">
              <div className="text-sm text-gray-700">我的记忆令您满意吗？</div>
              <div className="flex justify-center space-x-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star 
                    key={star} 
                    size={24} 
                    className={star <= 4 ? "text-yellow-400 fill-yellow-400" : "text-gray-300"} 
                  />
                ))}
              </div>
              <div className="text-xs text-gray-500">
                提供反馈帮助我更好地理解您
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* UPDATED: Memory Enhancement Modal */}
      {enhanceModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl p-4 w-full max-w-sm">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">增进记忆</h3>
              <button onClick={() => setEnhanceModalOpen(false)} className="text-gray-500 hover:text-gray-700">
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4 text-center">
              <div className="mb-6">
                <div className="w-16 h-16 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-3">
                  <Sparkles size={24} className="text-purple-600" />
                </div>
                <p className="text-gray-700 mb-4">将会从最近对话中提取关键记忆要点，这将加深我对您的理解</p>
                
                {/* Show cost details */}
                <div className="bg-amber-50 rounded-lg p-3 flex items-center justify-between">
                  <div className="text-left">
                    <div className="text-sm text-amber-700 font-medium">预计消耗星光币</div>
                    <div className="text-xs text-amber-600">记忆量: {Math.floor(enhanceCost/10)} 单位</div>
                  </div>
                  <div className="text-2xl font-bold text-amber-600 flex items-center">
                    <CircleDollarSign size={20} className="mr-1 text-amber-500" />
                    {enhanceCost}
                  </div>
                </div>
                
                {starCoins < enhanceCost && (
                  <div className="mt-3 text-red-500 text-sm flex items-center justify-center">
                    <AlertCircle size={14} className="mr-1" />
                    星光币余额不足，请先充值
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={confirmEnhance}
                disabled={starCoins < enhanceCost}
                className={`flex-1 py-2 rounded-lg font-medium flex items-center justify-center ${
                  starCoins < enhanceCost
                    ? 'bg-gray-200 text-gray-400'
                    : 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white'
                }`}
              >
                <Check size={16} className="mr-1" />
                确认增进 ({enhanceCost} 星光币)
              </button>
              <button
                onClick={() => setEnhanceModalOpen(false)}
                className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg font-medium"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Premium Feature Confirmation Modal */}
      {showPremiumModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl p-4 w-full max-w-sm">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                确认使用
                {premiumFeature === 'memory_image' && '次元画笔'}
                {premiumFeature === 'memory_video' && '梦境织机'}
                {premiumFeature === 'insight' && '洞察之眼'}
              </h3>
              <button onClick={() => setShowPremiumModal(false)} className="text-gray-500 hover:text-gray-700">
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4 text-center">
              <div className="mb-6">
                <div className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-3"
                  style={{
                    background: premiumFeature === 'memory_image' ? 'linear-gradient(to right, #c7d2fe, #a5b4fc)' :
                              premiumFeature === 'memory_video' ? 'linear-gradient(to right, #ddd6fe, #c4b5fd)' :
                              'linear-gradient(to right, #bfdbfe, #93c5fd)'
                  }}
                >
                  {premiumFeature === 'memory_image' && <Image size={24} className="text-indigo-600" />}
                  {premiumFeature === 'memory_video' && <Video size={24} className="text-purple-600" />}
                  {premiumFeature === 'insight' && <Zap size={24} className="text-blue-600" />}
                </div>
                
                <div className="text-gray-700 mb-4">
                  {premiumFeature === 'memory_image' && '将会基于您的兴趣与我们的对话历史，创作一张独特的AI画像，捕捉您的个性与品味。'}
                  {premiumFeature === 'memory_video' && '将从您的记忆片段和对话亮点中，编织一段动人的短视频回忆，展现我们的故事。'}
                  {premiumFeature === 'insight' && '将深入分析您的沟通风格与兴趣倾向，为您提供专属洞察，帮助您更好地了解自己。'}
                </div>
                
                {/* Show cost details */}
                <div className="bg-amber-50 rounded-lg p-3 flex items-center justify-between">
                  <div className="text-left">
                    <div className="text-sm text-amber-700 font-medium">服务费用</div>
                  </div>
                  <div className="text-2xl font-bold text-amber-600 flex items-center">
                    <CircleDollarSign size={20} className="mr-1 text-amber-500" />
                    {premiumFeature === 'memory_image' ? '80' : premiumFeature === 'memory_video' ? '150' : '30'}
                  </div>
                </div>
                
                {starCoins < (premiumFeature === 'memory_image' ? 80 : premiumFeature === 'memory_video' ? 150 : 30) && (
                  <div className="mt-3 text-red-500 text-sm flex items-center justify-center">
                    <AlertCircle size={14} className="mr-1" />
                    星光币余额不足，请先充值
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={confirmPremiumFeature}
                disabled={starCoins < (premiumFeature === 'memory_image' ? 80 : premiumFeature === 'memory_video' ? 150 : 30)}
                className={`flex-1 py-2 rounded-lg font-medium flex items-center justify-center ${
                  starCoins < (premiumFeature === 'memory_image' ? 80 : premiumFeature === 'memory_video' ? 150 : 30)
                    ? 'bg-gray-200 text-gray-400'
                    : 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white'
                }`}
              >
                <Check size={16} className="mr-1" />
                确认使用
              </button>
              <button
                onClick={() => setShowPremiumModal(false)}
                className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg font-medium"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Insight Modal */}
      {showInsightModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl p-4 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-blue-800 flex items-center">
                <Zap size={18} className="mr-2 text-blue-600" />
                专属记忆洞察
              </h3>
              <button onClick={() => setShowInsightModal(false)} className="text-gray-500 hover:text-gray-700">
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4">
              <div className="bg-blue-50 p-4 rounded-lg mb-4">
                <h4 className="text-blue-700 font-medium mb-2 flex items-center">
                  <User size={16} className="mr-2" />
                  沟通风格洞察
                </h4>
                <p className="text-sm text-gray-700">
                  你的表达方式简洁清晰，喜欢直接切入话题核心。在我们的对话中，你经常使用专业术语，显示出对UI设计领域的深入理解。你对设计理念和技术探讨表现出高度热情，但对过度商业化的讨论则相对保留。
                </p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg mb-4">
                <h4 className="text-purple-700 font-medium mb-2 flex items-center">
                  <Coffee size={16} className="mr-2" />
                  兴趣模式
                </h4>
                <p className="text-sm text-gray-700">
                  你的兴趣展现出科技与艺术的完美融合。对科幻小说的喜爱与极简主义设计偏好相互呼应，反映出对未来感与简洁美学的一致追求。你的咖啡品鉴兴趣与城市摄影爱好则表明你善于在日常中发现并欣赏细微之美。
                </p>
              </div>
              
              <div className="bg-amber-50 p-4 rounded-lg">
                <h4 className="text-amber-700 font-medium mb-2 flex items-center">
                  <Star size={16} className="mr-2" />
                  共鸣话题分析
                </h4>
                <p className="text-sm text-gray-700">
                  基于我们过去的对话，你在讨论以下话题时表现出最高的参与度：
                </p>
                <div className="mt-2 flex flex-wrap gap-2">
                  <span className="bg-amber-100 text-amber-700 px-2 py-1 rounded-full text-xs">AI伦理</span>
                  <span className="bg-amber-100 text-amber-700 px-2 py-1 rounded-full text-xs">用户体验设计</span>
                  <span className="bg-amber-100 text-amber-700 px-2 py-1 rounded-full text-xs">咖啡文化</span>
                  <span className="bg-amber-100 text-amber-700 px-2 py-1 rounded-full text-xs">城市探索</span>
                </div>
              </div>
            </div>
            
            <div className="mt-2 p-2 text-center">
              <button
                onClick={() => setShowInsightModal(false)}
                className="bg-blue-600 text-white py-2 px-6 rounded-lg font-medium"
              >
                了解
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Memory enhancement animation */}
      {isEnhancing && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
          <div className="bg-white bg-opacity-10 rounded-xl p-8 text-center backdrop-blur-lg">
            <div className="w-20 h-20 mx-auto relative mb-4">
              <div className="absolute inset-0 rounded-full border-4 border-t-transparent border-purple-400 animate-spin"></div>
              <div className="absolute inset-4 rounded-full border-4 border-t-transparent border-indigo-400 animate-spin animation-delay-500"></div>
              <div className="absolute inset-0 w-full h-full flex items-center justify-center">
                <Sparkles size={24} className="text-white animate-pulse" />
              </div>
            </div>
            <h3 className="text-white text-xl font-medium mb-2">记忆提取中...</h3>
            <p className="text-white text-opacity-80 text-sm mb-2">正在从对话中提炼珍贵记忆</p>
            <div className="w-full bg-white bg-opacity-20 h-1.5 rounded-full overflow-hidden mt-4">
              <div className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-progress-bar"></div>
            </div>
          </div>
        </div>
      )}
  
      {/* Bottom Action Bar */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <div>
          {/* Main Button: Enhance Memory */}
          <button 
            onClick={handleEnhanceMemory}
            disabled={isEnhancing}
            className={`w-full py-3 rounded-lg font-medium flex items-center justify-center ${
              isEnhancing 
                ? 'bg-purple-200 text-purple-400' 
                : 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white'
            }`}
          >
            {isEnhancing ? (
              <>
                <div className="mr-2 animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
                记忆摘取中...
              </>
            ) : (
              <>
                <Sparkles size={18} className="mr-2" />
                增进记忆
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIMemoryView;