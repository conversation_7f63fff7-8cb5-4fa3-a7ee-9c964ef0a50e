import React, { useState, useRef, useEffect } from 'react';
import { 
  ChevronLeft, Send, Mic, Plus, TrendingUp, TrendingDown, 
  BarChart3, Star, MessageCircle, DollarSign, AlertCircle,
  Calendar, Target, Shield, Sparkles, ChevronRight, Eye,
  RefreshCw, PieChart, Activity, Info, Search, Volume2, Share2,
  Edit3, Phone, Heart, MoreHorizontal, Smile, X
} from 'lucide-react';

// 导入角色图片
import characterAvatar from './assets/沈婉.png';
import characterAvatarWholeBody from './assets/沈婉全身照.png';

const StockChatInterface = () => {
  const messagesEndRef = useRef(null);
  const [inputValue, setInputValue] = useState('');
  const [showQuickActions, setShowQuickActions] = useState(true);
  const [waitingForStockInput, setWaitingForStockInput] = useState(false);
  
  // 模拟不同场景的初始消息
  const getInitialMessages = (context) => {
    const baseMessages = [
      {
        id: 'welcome',
        sender: 'ai',
        content: '你好呀主人！我是沈婉，你的专属投资小助手~ 今天市场表现不错，有什么想了解的吗？',
        time: '14:30',
        type: 'text'
      }
    ];

    // 根据不同场景添加特定消息
    if (context === 'stock-discussion') {
      return [...baseMessages, {
        id: 'stock-1',
        sender: 'ai',
        content: '你想跟我聊聊这只股票么？',
        time: '14:31',
        type: 'text'
      }, {
        id: 'stock-2',
        sender: 'ai',
        content: '股票概览',
        time: '14:31',
        type: 'stock-overview',
        data: {
          name: '科技未来',
          code: '000001',
          currentPrice: 28.5,
          change: 2.82,
          changePercent: 11.01,
          volume: '63.76万',
          turnover: '6.49%',
          analysis: '这只股票今天表现真的很棒呢！涨幅超过11%，成交量也在放大，看起来很有活力~ 有什么想深入了解的吗？'
        }
      }];
    }

    return baseMessages;
  };

  const [messages, setMessages] = useState(getInitialMessages('stock-discussion'));

  // 快捷操作选项
  const quickActions = [
    { icon: TrendingUp, text: '今日收益', color: 'text-cyan-400' },
    { icon: PieChart, text: '持仓分析', color: 'text-blue-400' },
    { icon: Activity, text: '热门板块', color: 'text-purple-400' },
    { icon: Search, text: '个股分析', color: 'text-pink-400' },
  ];

  // 处理发送消息
  const handleSend = () => {
    if (inputValue.trim() === '') return;
    
    const newUserMessage = {
      id: Date.now().toString(),
      sender: 'user',
      content: inputValue,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      type: 'text'
    };
    
    setMessages([...messages, newUserMessage]);
    
    // 检查是否在等待股票输入
    if (waitingForStockInput) {
      const stockName = extractStockName(inputValue);
      setTimeout(() => {
        const aiResponse = {
          id: Date.now().toString(),
          sender: 'ai',
          content: `好的收到，我已经在后台建了分析${stockName}的任务，待会报告出来了会通知你哦，请给我点时间哦~ 🔍`,
          time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
          type: 'text'
        };
        setMessages(prev => [...prev, aiResponse]);
      }, 1000);
      setWaitingForStockInput(false);
    } else {
      // 模拟AI回复
      setTimeout(() => {
        const aiResponse = generateAIResponse(inputValue);
        setMessages(prev => [...prev, aiResponse]);
      }, 1000);
    }
    
    setInputValue('');
  };

  // 提取股票名称或代码
  const extractStockName = (input) => {
    const cleaned = input.replace(/[请帮忙给出这只股票的分析报告：]/g, '').trim();
    return cleaned || '该股票';
  };

  // 生成AI回复（根据用户输入）
  const generateAIResponse = (userInput) => {
    const input = userInput.toLowerCase();
    const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    
    // 检查是否包含市场解读关键词
    if (input.includes('市场') && (input.includes('解读') || input.includes('分析') || input.includes('行情'))) {
      return {
        id: Date.now().toString(),
        sender: 'ai',
        content: '市场解读',
        time,
        type: 'market-analysis',
        data: {
          sentiment: '谨慎',
          mainIndex: [
            { name: '上证指数', value: 3340.69, change: -0.18 },
            { name: '深证成指', value: 10029.11, change: -0.61 },
            { name: '创业板指', value: 1991.64, change: -0.68 }
          ],
          hotSectors: ['科技', '新能源', '医药'],
          analysis: '今天的市场虽然三大指数都在小幅调整，但我觉得这更像是在积蓄力量呢！科技股依然坚挺，特别是人工智能和新能源板块。不过呢，短期涨幅较大的个股要注意风险哦~'
        }
      };
    } else if (input.includes('分析') && (input.includes('股票') || input.includes('个股'))) {
      const stockName = extractStockName(userInput);
      return {
        id: Date.now().toString(),
        sender: 'ai',
        content: `好的收到，我已经在后台建了分析${stockName}的任务，待会报告出来了会通知你哦，请给我点时间哦~ 🔍`,
        time,
        type: 'text'
      };
    } else if (input.includes('收益') || input.includes('赚') || input.includes('亏')) {
      return {
        id: Date.now().toString(),
        sender: 'ai',
        content: '收益报告',
        time,
        type: 'profit-report',
        data: {
          todayProfit: 158,
          todayProfitRate: 1.26,
          weekProfit: 520,
          weekProfitRate: 2.85,
          monthProfit: 1680,
          monthProfitRate: 5.42,
          holdings: [
            { name: '招商银行', profit: 280, rate: 3.2 },
            { name: '比亚迪', profit: -120, rate: -1.5 },
            { name: '宁德时代', profit: 450, rate: 5.8 }
          ]
        }
      };
    } else if (input.includes('买什么') || input.includes('推荐')) {
      return {
        id: Date.now().toString(),
        sender: 'ai',
        content: '为你精选了几只优质股票~',
        time,
        type: 'stock-recommendation',
        data: [
          {
            name: '腾讯控股',
            code: '00700',
            price: 328.40,
            change: 1.25,
            reason: '互联网龙头，长期看好',
            riskLevel: '中'
          },
          {
            name: '贵州茅台',
            code: '600519',
            price: 1680.00,
            change: -0.31,
            reason: '消费白马，调整即机会',
            riskLevel: '低'
          }
        ]
      };
    } else {
      return {
        id: Date.now().toString(),
        sender: 'ai',
        content: '嗯嗯，我明白了！投资路上有我陪着你，咱们一起稳稳地前进~ 有什么具体想了解的吗？',
        time,
        type: 'text'
      };
    }
  };

  // 处理快捷操作点击
  const handleQuickAction = (action) => {
    let aiResponse;
    const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    
    switch (action.text) {
      case '今日收益':
        aiResponse = generateAIResponse('今日收益');
        break;
      case '持仓分析':
        aiResponse = {
          id: Date.now().toString(),
          sender: 'ai',
          content: '持仓分析',
          time,
          type: 'portfolio-analysis',
          data: {
            totalValue: 125680,
            distribution: [
              { sector: '金融', percentage: 35, color: '#3B82F6' },
              { sector: '科技', percentage: 25, color: '#8B5CF6' },
              { sector: '新能源', percentage: 20, color: '#10B981' },
              { sector: '消费', percentage: 15, color: '#F59E0B' },
              { sector: '现金', percentage: 5, color: '#6B7280' }
            ],
            suggestion: '当前持仓比较均衡，金融板块占比较高，建议适当增加新能源配置~'
          }
        };
        break;
      case '热门板块':
        aiResponse = {
          id: Date.now().toString(),
          sender: 'ai',
          content: '今日热门板块',
          time,
          type: 'hot-sectors',
          data: [
            { name: '人工智能', change: 3.25, stocks: ['科大讯飞', '商汤科技'] },
            { name: '新能源车', change: 2.18, stocks: ['比亚迪', '理想汽车'] },
            { name: '半导体', change: -1.32, stocks: ['中芯国际', '华虹半导体'] }
          ]
        };
        break;
      case '个股分析':
        aiResponse = {
          id: Date.now().toString(),
          sender: 'ai',
          content: '请帮忙给出这只股票的分析报告：（输入股票名称或代码）',
          time,
          type: 'text'
        };
        setWaitingForStockInput(true);
        break;
      default:
        return;
    }
    
    setMessages(prev => [...prev, aiResponse]);
  };

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 渲染消息内容
  const renderMessageContent = (message) => {
    switch (message.type) {
      case 'text':
        return <p className="text-sm">{message.content}</p>;
      
      case 'stock-overview':
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="w-4 h-4 text-purple-400" />
              <span className="font-medium text-white">股票概览</span>
            </div>
            
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-3">
              {/* 股票基本信息 */}
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h4 className="font-bold text-white text-lg">{message.data.name}</h4>
                  <p className="text-xs text-gray-400">{message.data.code}</p>
                </div>
                <div className="text-right">
                  <p className="text-xl font-bold text-white">¥{message.data.currentPrice}</p>
                  <p className={`text-sm font-medium ${message.data.change > 0 ? 'text-red-400' : 'text-green-400'}`}>
                    {message.data.change > 0 ? '+' : ''}{message.data.change} ({message.data.change > 0 ? '+' : ''}{message.data.changePercent}%)
                  </p>
                </div>
              </div>
              
              {/* 关键指标 */}
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div className="bg-black/30 rounded-lg p-2">
                  <p className="text-xs text-gray-400">成交量</p>
                  <p className="text-sm font-bold text-white">{message.data.volume}</p>
                </div>
                <div className="bg-black/30 rounded-lg p-2">
                  <p className="text-xs text-gray-400">换手率</p>
                  <p className="text-sm font-bold text-white">{message.data.turnover}</p>
                </div>
              </div>
              
              {/* AI点评 */}
              <div className="bg-purple-500/20 rounded-lg p-2 flex items-start gap-2">
                <Sparkles className="w-4 h-4 text-purple-300 mt-0.5 flex-shrink-0" />
                <p className="text-xs text-purple-200 leading-relaxed">
                  {message.data.analysis}
                </p>
              </div>
            </div>
          </div>
        );
      
      case 'market-analysis':
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="w-4 h-4 text-purple-400" />
              <span className="font-medium text-white">市场解读</span>
            </div>
            
            {/* 市场情绪 */}
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-3">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-300">市场情绪</span>
                <span className="text-sm font-medium text-purple-300">
                  {message.data.sentiment} 😟
                </span>
              </div>
              
              {/* 主要指数 */}
              <div className="grid grid-cols-3 gap-2 mb-3">
                {message.data.mainIndex.map((index, i) => (
                  <div key={i} className="bg-black/30 rounded-lg p-2 text-center">
                    <p className="text-xs text-gray-400">{index.name}</p>
                    <p className="text-sm font-bold text-white">{index.value}</p>
                    <p className={`text-xs ${index.change < 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {index.change > 0 ? '+' : ''}{index.change}%
                    </p>
                  </div>
                ))}
              </div>
              
              {/* 热门板块 */}
              <div className="flex gap-1 mb-2">
                {message.data.hotSectors.map((sector, i) => (
                  <span key={i} className="bg-purple-500/30 text-purple-300 text-xs px-2 py-1 rounded-full backdrop-blur-sm">
                    {sector}
                  </span>
                ))}
              </div>
              
              {/* 分析文字 */}
              <p className="text-xs text-gray-300 leading-relaxed">
                {message.data.analysis}
              </p>
            </div>
          </div>
        );
      
      case 'stock-analysis':
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-2">
              <Target className="w-4 h-4 text-blue-400" />
              <span className="font-medium text-white">股票分析报告</span>
            </div>
            
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
              {/* 股票基本信息 */}
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h4 className="font-bold text-white">{message.data.name}</h4>
                  <p className="text-xs text-gray-400">{message.data.code}</p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-white">¥{message.data.currentPrice}</p>
                  <p className={`text-xs ${message.data.change < 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {message.data.change > 0 ? '+' : ''}{message.data.change}%
                  </p>
                </div>
              </div>
              
              {/* 建议和风险 */}
              <div className="flex gap-2 mb-3">
                <span className="bg-red-500/30 text-red-300 text-xs px-2 py-1 rounded-full font-medium">
                  {message.data.suggestion}
                </span>
                <span className="bg-green-500/30 text-green-300 text-xs px-2 py-1 rounded-full">
                  风险{message.data.riskLevel}
                </span>
                <span className="bg-purple-500/30 text-purple-300 text-xs px-2 py-1 rounded-full">
                  目标价 ¥{message.data.targetPrice}
                </span>
              </div>
              
              {/* 关键指标 */}
              <div className="grid grid-cols-4 gap-2 mb-3">
                <div className="bg-black/30 rounded-lg p-2 text-center">
                  <p className="text-xs text-gray-400">市盈率</p>
                  <p className="text-sm font-bold text-white">{message.data.pe}</p>
                </div>
                <div className="bg-black/30 rounded-lg p-2 text-center">
                  <p className="text-xs text-gray-400">市净率</p>
                  <p className="text-sm font-bold text-white">{message.data.pb}</p>
                </div>
                <div className="bg-black/30 rounded-lg p-2 text-center">
                  <p className="text-xs text-gray-400">ROE</p>
                  <p className="text-sm font-bold text-white">{message.data.roe}</p>
                </div>
                <div className="bg-black/30 rounded-lg p-2 text-center">
                  <p className="text-xs text-gray-400">股息率</p>
                  <p className="text-sm font-bold text-white">{message.data.dividendYield}</p>
                </div>
              </div>
              
              {/* 分析说明 */}
              <div className="bg-black/20 rounded-lg p-3 mb-3">
                <p className="text-xs text-gray-300 leading-relaxed">{message.data.analysis}</p>
              </div>
              
              {/* 关键点 */}
              <div className="space-y-1">
                {message.data.keyPoints.map((point, i) => (
                  <div key={i} className="flex items-center gap-2 text-xs text-gray-300">
                    <Star className="w-3 h-3 text-yellow-400" />
                    <span>{point}</span>
                  </div>
                ))}
              </div>
              
              {/* 操作按钮 */}
              <div className="flex gap-2 mt-3">
                <button className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs py-2 rounded-lg font-medium">
                  加入自选
                </button>
                <button className="flex-1 bg-white/10 text-white text-xs py-2 rounded-lg font-medium border border-white/20">
                  查看详情
                </button>
              </div>
            </div>
          </div>
        );
      
      case 'profit-report':
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="w-4 h-4 text-green-400" />
              <span className="font-medium text-white">收益报告</span>
            </div>
            
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
              {/* 收益概览 */}
              <div className="grid grid-cols-3 gap-2 mb-3">
                <div className="bg-black/30 rounded-lg p-2 text-center">
                  <p className="text-xs text-gray-400">今日收益</p>
                  <p className={`text-sm font-bold ${message.data.todayProfit > 0 ? 'text-red-400' : 'text-green-400'}`}>
                    {message.data.todayProfit > 0 ? '+' : ''}¥{message.data.todayProfit}
                  </p>
                  <p className="text-xs text-gray-400">
                    {message.data.todayProfitRate > 0 ? '+' : ''}{message.data.todayProfitRate}%
                  </p>
                </div>
                <div className="bg-black/30 rounded-lg p-2 text-center">
                  <p className="text-xs text-gray-400">本周收益</p>
                  <p className={`text-sm font-bold ${message.data.weekProfit > 0 ? 'text-red-400' : 'text-green-400'}`}>
                    +¥{message.data.weekProfit}
                  </p>
                  <p className="text-xs text-gray-400">+{message.data.weekProfitRate}%</p>
                </div>
                <div className="bg-black/30 rounded-lg p-2 text-center">
                  <p className="text-xs text-gray-400">本月收益</p>
                  <p className={`text-sm font-bold ${message.data.monthProfit > 0 ? 'text-red-400' : 'text-green-400'}`}>
                    +¥{message.data.monthProfit}
                  </p>
                  <p className="text-xs text-gray-400">+{message.data.monthProfitRate}%</p>
                </div>
              </div>
              
              {/* 持仓收益 */}
              <div className="space-y-2">
                <p className="text-xs text-gray-300 font-medium">持仓收益TOP3</p>
                {message.data.holdings.map((stock, i) => (
                  <div key={i} className="flex justify-between items-center bg-black/20 rounded-lg p-2">
                    <span className="text-xs font-medium text-gray-200">{stock.name}</span>
                    <div className="flex items-center gap-2">
                      <span className={`text-xs ${stock.profit > 0 ? 'text-red-400' : 'text-green-400'}`}>
                        {stock.profit > 0 ? '+' : ''}¥{stock.profit}
                      </span>
                      <span className={`text-xs ${stock.rate > 0 ? 'text-red-400' : 'text-green-400'}`}>
                        ({stock.rate > 0 ? '+' : ''}{stock.rate}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              
              <p className="text-xs text-gray-300 mt-3 text-center">
                主人，虽然有些波动，但整体收益还不错哦！继续保持~ 💪
              </p>
            </div>
          </div>
        );
      
      case 'stock-recommendation':
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="w-4 h-4 text-purple-400" />
              <span className="font-medium text-white">精选推荐</span>
            </div>
            
            <div className="space-y-2">
              {message.data.map((stock, i) => (
                <div key={i} className="bg-white/10 backdrop-blur-md rounded-xl p-3">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h5 className="font-medium text-white">{stock.name}</h5>
                      <p className="text-xs text-gray-400">{stock.code}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-bold text-white">¥{stock.price}</p>
                      <p className={`text-xs ${stock.change > 0 ? 'text-red-400' : 'text-green-400'}`}>
                        {stock.change > 0 ? '+' : ''}{stock.change}%
                      </p>
                    </div>
                  </div>
                  <p className="text-xs text-gray-300 mb-2">{stock.reason}</p>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      stock.riskLevel === '低' ? 'bg-green-500/30 text-green-300' :
                      stock.riskLevel === '中' ? 'bg-yellow-500/30 text-yellow-300' : 
                      'bg-red-500/30 text-red-300'
                    }`}>
                      风险{stock.riskLevel}
                    </span>
                    <button className="text-xs text-purple-300 font-medium">查看详情 →</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      
      case 'portfolio-analysis':
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-2">
              <PieChart className="w-4 h-4 text-blue-400" />
              <span className="font-medium text-white">持仓分析</span>
            </div>
            
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
              <div className="mb-3">
                <p className="text-xs text-gray-400">总资产</p>
                <p className="text-xl font-bold text-white">¥{message.data.totalValue.toLocaleString()}</p>
              </div>
              
              {/* 持仓分布 */}
              <div className="space-y-2 mb-3">
                {message.data.distribution.map((item, i) => (
                  <div key={i} className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                    <span className="text-xs text-gray-300 flex-1">{item.sector}</span>
                    <span className="text-xs font-medium text-white">{item.percentage}%</span>
                    <div className="w-20 h-2 bg-black/30 rounded-full overflow-hidden">
                      <div 
                        className="h-full transition-all duration-500" 
                        style={{ width: `${item.percentage}%`, backgroundColor: item.color }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="bg-black/20 rounded-lg p-2">
                <p className="text-xs text-gray-300">{message.data.suggestion}</p>
              </div>
            </div>
          </div>
        );
      
      case 'hot-sectors':
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="w-4 h-4 text-red-400" />
              <span className="font-medium text-white">热门板块</span>
            </div>
            
            <div className="space-y-2">
              {message.data.map((sector, i) => (
                <div key={i} className="bg-white/10 backdrop-blur-md rounded-xl p-3">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-white">{sector.name}</span>
                    <span className={`text-sm font-bold ${
                      sector.change > 0 ? 'text-red-400' : 'text-green-400'
                    }`}>
                      {sector.change > 0 ? '+' : ''}{sector.change}%
                    </span>
                  </div>
                  <div className="flex gap-1 flex-wrap">
                    {sector.stocks.map((stock, j) => (
                      <span key={j} className="text-xs bg-white/10 px-2 py-0.5 rounded text-gray-300">
                        {stock}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      
      default:
        return <p className="text-sm">{message.content}</p>;
    }
  };

  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-black h-screen overflow-hidden relative">
      {/* 背景图片 */}
      <div className="absolute inset-0 z-0">
        <img 
          src={characterAvatarWholeBody}
          alt="沈婉全身照" 
          className="w-full h-full object-cover object-center opacity-50"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-purple-900/80 via-black/60 to-black/90"></div>
      </div>

      {/* 内容区域 */}
      <div className="relative z-10 flex flex-col h-full">
        {/* 状态栏 */}
        <div className="bg-black/20 backdrop-blur-sm text-white px-4 py-1 flex justify-between items-center text-xs">
          <span>17:54</span>
          <div className="flex items-center gap-1">
            <span className="text-xs">5G</span>
            <span className="bg-white/80 rounded-sm px-1 text-black font-medium">91</span>
          </div>
        </div>

        {/* 顶部导航 */}
        <div className="bg-gradient-to-b from-purple-900/40 to-transparent backdrop-blur-sm text-white p-4 flex items-center">
          <button className="p-2 -ml-2">
            <ChevronLeft className="w-6 h-6" />
          </button>
          
          <div className="flex-1 text-center">
            <h1 className="font-medium text-lg">沈婉</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <button className="p-2">
              <Volume2 className="w-5 h-5" />
            </button>
            <button className="p-2">
              <Share2 className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* 消息区域 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {/* 消息列表 */}
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-[85%] ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>
                {message.sender === 'ai' && (
                  <div className="flex items-start gap-2 mb-1">
                    <img 
                      src={characterAvatar}
                      alt="沈婉" 
                      className="w-8 h-8 rounded-full border border-purple-400/50"
                    />
                    <span className="text-xs text-purple-300">沈婉</span>
                  </div>
                )}
                
                <div className={`rounded-2xl p-3 ${
                  message.sender === 'user' 
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white ml-12' 
                    : 'bg-black/40 backdrop-blur-md text-white border border-purple-500/20'
                }`}>
                  {renderMessageContent(message)}
                  
                  <div className={`text-xs mt-1 ${
                    message.sender === 'user' ? 'text-white/70' : 'text-gray-400'
                  }`}>
                    {message.time}
                  </div>
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* 快捷操作区 */}
        {showQuickActions && (
          <div className="bg-black/60 backdrop-blur-md border-t border-purple-500/20 p-3 relative">
            {/* 关闭按钮 */}
            <button
              onClick={() => setShowQuickActions(false)}
              className="absolute top-2 right-2 p-1 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
            >
              <X className="w-4 h-4 text-gray-300" />
            </button>
            
            <div className="grid grid-cols-4 gap-2">
              {quickActions.map((action, i) => (
                <button
                  key={i}
                  onClick={() => handleQuickAction(action)}
                  className="bg-purple-500/20 hover:bg-purple-500/30 rounded-xl p-3 flex flex-col items-center gap-1 transition-colors backdrop-blur-sm border border-purple-500/20"
                >
                  <action.icon className={`w-5 h-5 ${action.color}`} />
                  <span className="text-xs text-gray-200">{action.text}</span>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* 底部区域 */}
        <div className="bg-black/80 backdrop-blur-md border-t border-purple-500/20">
          {/* 角色信息和操作按钮 */}
          <div className="p-3 flex items-center justify-between border-b border-purple-500/20">
            <div className="flex items-center gap-2">
              <img 
                src={characterAvatar}
                alt="沈婉" 
                className="w-8 h-8 rounded-full border border-purple-400/50"
              />
              <div>
                <p className="text-sm text-white font-medium">沈婉</p>
                <p className="text-xs text-purple-300">@小姑娘</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button className="p-2 text-gray-400 hover:text-white">
                <Edit3 className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-white">
                <Phone className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-white">
                <Heart className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-white">
                <MoreHorizontal className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* 输入区域 */}
          <div className="p-3">
            {/* 当快捷操作栏关闭时，显示打开按钮 */}
            {!showQuickActions && (
              <button
                onClick={() => setShowQuickActions(true)}
                className="w-full mb-2 bg-purple-500/20 hover:bg-purple-500/30 rounded-lg py-2 text-xs text-purple-300 font-medium transition-colors backdrop-blur-sm border border-purple-500/20 flex items-center justify-center gap-1"
              >
                <BarChart3 className="w-4 h-4" />
                显示股票快捷操作
              </button>
            )}
            
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-gray-800/50 rounded-full px-4 py-2 flex items-center backdrop-blur-sm border border-purple-500/20">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSend()}
                  placeholder={waitingForStockInput ? "请输入股票名称或代码..." : "自由输入..."}
                  className="flex-1 bg-transparent outline-none text-sm text-white placeholder-gray-400"
                />
                <button className="ml-2 text-gray-400 hover:text-white">
                  <Smile className="w-5 h-5" />
                </button>
              </div>
              
              {inputValue.trim() ? (
                <button 
                  onClick={handleSend}
                  className="p-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg transition-all"
                >
                  <Send className="w-5 h-5" />
                </button>
              ) : (
                <button className="p-2 rounded-full bg-gray-800/50 text-gray-400 hover:text-white backdrop-blur-sm border border-purple-500/20">
                  <Mic className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StockChatInterface;