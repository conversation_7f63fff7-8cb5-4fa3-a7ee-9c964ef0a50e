// import React, { useState } from 'react';
// import { Settings, Share, Gift, MessageCircle, Activity, Award, Unlock, ShieldCheck, Zap, Image, Info, Upload, Play, Volume2 } from 'lucide-react';

// const EnhancedCreatorView = () => {
//   const [selectedTab, setSelectedTab] = useState('overview');
  
//   // 角色基本数据
//   const characterData = {
//     name: "赛博侦探 K",
//     level: 5,
//     rankTitle: "平台红人",
//     currentPopularity: 1234567,
//     nextLevelPopularity: 2000000,
//     profileImage: "/api/placeholder/100/100",
//     creatorName: "硬核创作者",
//     creationDate: "2025.02.15",
//     id: "KSY78901",
//     status: "瑜伽中"
//   };
  
//   // 统计数据
//   const stats = {
//     dailyInteractions: "5,800+",
//     followers: "150,000+",
//     gifts: "88K+",
//     giftPoints: "125,800",
//     platformRank: "Top 1%",
//     totalIncome: "¥ 12,580",
//     monthlyIncome: "¥ 3,450"
//   };
  
//   return (
//     <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg">
//       {/* 顶部导航 */}
//       <div className="bg-purple-700 p-4 flex items-center justify-between text-white">
//         <h1 className="text-lg font-bold">创作者中心</h1>
//         <button className="p-2 bg-white bg-opacity-20 rounded-full">
//           <Settings size={18} className="text-white" />
//         </button>
//       </div>
      
//       {/* 角色基本信息卡片 */}
//       <div className="bg-indigo-900 p-4">
//         <div className="bg-white rounded-xl shadow-lg p-4 mt-2">
//           <div className="flex items-center justify-between">
//             <div className="flex items-center space-x-2">
//               <div className="relative">
//                 <div className="h-14 w-14 rounded-full bg-purple-500 flex items-center justify-center overflow-hidden">
//                   <img src={characterData.profileImage} alt={characterData.name} className="h-full w-full object-cover" />
//                   <div className="absolute bottom-0 right-0 h-5 w-5 rounded-full bg-purple-600 flex items-center justify-center text-white text-xs font-bold">
//                     {characterData.level}
//                   </div>
//                 </div>
//               </div>
//               <div>
//                 <div className="flex items-center">
//                   <h1 className="text-xl font-bold">{characterData.name}</h1>
//                   <span className="ml-2 text-xs bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded-full">🧘‍♀️ {characterData.status}</span>
//                 </div>
//                 <div className="text-xs text-gray-600">
//                   ID: {characterData.id} · 创建于 {characterData.creationDate}
//                 </div>
//               </div>
//             </div>
//             <div className="bg-purple-100 text-purple-600 px-3 py-1 rounded-full text-xs font-medium">
//               {characterData.rankTitle}
//             </div>
//           </div>
//         </div>
//       </div>
      
//       {/* 热度值进度条 */}
//       <div className="mt-4 px-4">
//         <div className="flex justify-between items-center mb-1 text-sm">
//           <span className="font-semibold">热度值</span>
//           <span className="text-purple-600">{characterData.currentPopularity.toLocaleString()} / {characterData.nextLevelPopularity.toLocaleString()}</span>
//         </div>
//         <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
//           <div 
//             className="h-full bg-purple-500 rounded-full"
//             style={{ width: `${(characterData.currentPopularity / characterData.nextLevelPopularity) * 100}%` }}
//           ></div>
//         </div>
//         <div className="text-xs text-gray-500 mt-1 text-right">
//           距离 Lv.{characterData.level + 1} 还需 {(characterData.nextLevelPopularity - characterData.currentPopularity).toLocaleString()} 热度! ✨
//         </div>
//       </div>
      
//       {/* 核心互动数据 */}
//       <div className="mt-6 px-4">
//         <div className="grid grid-cols-4 gap-2 text-center">
//           <div className="p-2 bg-purple-50 rounded-lg shadow-sm">
//             <div className="text-sm font-bold text-indigo-600">{stats.dailyInteractions}</div>
//             <div className="text-xs text-gray-500">今日互动</div>
//           </div>
//           <div className="p-2 bg-purple-50 rounded-lg shadow-sm">
//             <div className="text-sm font-bold text-indigo-600">{stats.followers}</div>
//             <div className="text-xs text-gray-500">粉丝数</div>
//           </div>
//           <div className="p-2 bg-purple-50 rounded-lg shadow-sm">
//             <div className="text-sm font-bold text-indigo-600">{stats.gifts}</div>
//             <div className="text-xs text-gray-500">收到礼物</div>
//           </div>
//           <div className="p-2 bg-purple-50 rounded-lg shadow-sm">
//             <div className="text-sm font-bold text-indigo-600">{stats.platformRank}</div>
//             <div className="text-xs text-gray-500">平台排名</div>
//           </div>
//         </div>
//       </div>
      
//       {/* 主要操作按钮 */}
//       <div className="mt-4 px-4 flex gap-2">
//         <button className="flex-1 py-3 rounded-lg bg-purple-500 text-white font-semibold flex items-center justify-center">
//           <MessageCircle size={18} className="mr-2" />
//           查看消息
//         </button>
//         <button className="flex-1 py-3 rounded-lg border border-purple-500 text-purple-600 font-semibold flex items-center justify-center">
//           <Activity size={18} className="mr-2" />
//           查看数据
//         </button>
//       </div>
      
//       {/* 标签栏 */}
//       <div className="mt-6 border-b border-gray-200 bg-white">
//         <div className="flex">
//           <button 
//             onClick={() => setSelectedTab('overview')}
//             className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'overview' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
//           >
//             数据概览
//           </button>
//           <button 
//             onClick={() => setSelectedTab('starplan')}
//             className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'starplan' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
//           >
//             星途计划
//           </button>
//           <button 
//             onClick={() => setSelectedTab('income')}
//             className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'income' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
//           >
//             收入管理
//           </button>
//           <button 
//             onClick={() => setSelectedTab('promotion')}
//             className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'promotion' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
//           >
//             推广攻略
//           </button>
//         </div>
//       </div>
      
//       {/* 标签内容 */}
//       <div className="flex-1 p-4 overflow-auto">
//         {selectedTab === 'overview' && (
//           <div className="space-y-4">
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold mb-4 flex items-center">
//                 <Activity size={16} className="mr-2 text-purple-600" />
//                 数据概览
//               </h3>
//               <div className="grid grid-cols-2 gap-3">
//                 <div className="border border-gray-200 rounded-lg p-3">
//                   <div className="text-sm text-gray-500">今日互动</div>
//                   <div className="text-xl font-bold text-indigo-600">{stats.dailyInteractions}</div>
//                   <div className="text-xs text-green-600 mt-1">↑ 12% 较昨日</div>
//                 </div>
//                 <div className="border border-gray-200 rounded-lg p-3">
//                   <div className="text-sm text-gray-500">粉丝数</div>
//                   <div className="text-xl font-bold text-indigo-600">{stats.followers}</div>
//                   <div className="text-xs text-green-600 mt-1">↑ 5% 较上周</div>
//                 </div>
//               </div>
              
//               <div className="mt-4">
//                 <h4 className="text-sm font-medium mb-2">互动趋势图</h4>
//                 <div className="h-32 bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center text-sm text-gray-400">
//                   互动数据趋势图表
//                 </div>
//               </div>
              
//               <div className="mt-4">
//                 <h4 className="text-sm font-medium mb-2">热门互动话题</h4>
//                 <div className="space-y-2">
//                   <div className="flex items-center bg-gray-50 rounded-lg p-2">
//                     <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-2">
//                       1
//                     </div>
//                     <div className="flex-1">
//                       <div className="text-sm font-medium">案件侦破</div>
//                       <div className="text-xs text-gray-500">432次互动</div>
//                     </div>
//                     <div className="text-xs text-purple-600 font-medium">42%</div>
//                   </div>
//                   <div className="flex items-center bg-gray-50 rounded-lg p-2">
//                     <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-2">
//                       2
//                     </div>
//                     <div className="flex-1">
//                       <div className="text-sm font-medium">赛博朋克</div>
//                       <div className="text-xs text-gray-500">287次互动</div>
//                     </div>
//                     <div className="text-xs text-purple-600 font-medium">28%</div>
//                   </div>
//                 </div>
//               </div>
//             </div>
            
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold mb-3 flex items-center">
//                 <Gift size={16} className="mr-2 text-purple-600" />
//                 打赏数据
//               </h3>
//               <div className="grid grid-cols-2 gap-3">
//                 <div className="border border-gray-200 rounded-lg p-3">
//                   <div className="text-sm text-gray-500">全部星光币</div>
//                   <div className="text-xl font-bold text-indigo-600">{stats.giftPoints}</div>
//                   <div className="text-xs text-gray-500 mt-1">约 {stats.totalIncome}</div>
//                 </div>
//                 <div className="border border-gray-200 rounded-lg p-3">
//                   <div className="text-sm text-gray-500">本月打赏</div>
//                   <div className="text-xl font-bold text-indigo-600">34,500</div>
//                   <div className="text-xs text-gray-500 mt-1">约 {stats.monthlyIncome}</div>
//                 </div>
//               </div>
              
//               <div className="mt-3 space-y-2">
//                 <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
//                   <div className="flex items-center">
//                     <div className="w-6 h-6 bg-pink-100 rounded-full flex items-center justify-center text-pink-600 mr-2">
//                       ❤️
//                     </div>
//                     <div className="text-sm">小心心</div>
//                   </div>
//                   <div className="text-sm font-medium text-indigo-600">3,600 点</div>
//                 </div>
                
//                 <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
//                   <div className="flex items-center">
//                     <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-2">
//                       💎
//                     </div>
//                     <div className="text-sm">数据晶块</div>
//                   </div>
//                   <div className="text-sm font-medium text-indigo-600">6,000 点</div>
//                 </div>
//               </div>
//             </div>
            
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold mb-3 flex items-center">
//                 <Info size={16} className="mr-2 text-purple-600" />
//                 创作者建议
//               </h3>
//               <div className="bg-purple-50 p-3 rounded-lg">
//                 <div className="text-sm font-medium text-purple-800 mb-1">让你的角色更受欢迎</div>
//                 <div className="text-xs text-purple-700">根据平台数据分析，增加 K 的逻辑推演能力相关内容，可能会提升用户互动率约15%</div>
//                 <button className="mt-2 bg-purple-200 text-purple-700 text-xs py-1 px-3 rounded-full">
//                   查看详情
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
        
//         {selectedTab === 'starplan' && (
//           <div className="space-y-4">
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <div className="flex justify-between items-center mb-4">
//                 <h3 className="text-md font-semibold flex items-center">
//                   <Award size={16} className="mr-2 text-purple-600" />
//                   <span>Lv.{characterData.level}</span>
//                   <span className="ml-2 px-2 py-1 bg-purple-500 text-white text-xs rounded-md font-medium">{characterData.rankTitle}</span>
//                 </h3>
//                 <div className="text-sm text-purple-600 font-medium">已解锁 2/4</div>
//               </div>
              
//               <div className="space-y-4">
//                 <div className="bg-white p-4 rounded-lg border border-gray-200">
//                   <div className="flex justify-between items-center mb-3">
//                     <div className="flex items-center">
//                       <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 mr-3">
//                         <Volume2 size={18} />
//                       </div>
//                       <div>
//                         <div className="font-medium">语音消息 (TTS)</div>
//                         <div className="text-xs text-gray-500">电子音与用户对话</div>
//                       </div>
//                     </div>
//                     <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已解锁</div>
//                   </div>
                  
//                   <div className="bg-gray-50 p-3 rounded-lg mb-3">
//                     <h4 className="text-sm font-medium mb-2">代表作品</h4>
//                     <div className="space-y-2">
//                       <div className="flex items-center bg-white rounded-lg p-2 border border-gray-100">
//                         <Play size={14} className="text-purple-600 mr-2" />
//                         <span className="text-xs flex-1">早安问候：「调查进度50%，检测到你醒来了...有个好消息要告诉你」</span>
//                       </div>
//                       <div className="flex items-center bg-white rounded-lg p-2 border border-gray-100">
//                         <Play size={14} className="text-purple-600 mr-2" />
//                         <span className="text-xs flex-1">案件结论：「证据已经收集完毕，真相只有一个」</span>
//                       </div>
//                     </div>
//                   </div>
                  
//                   <button className="w-full py-2 bg-purple-100 text-purple-600 rounded-lg text-sm font-medium flex items-center justify-center">
//                     <span>编辑语音</span>
//                   </button>
//                 </div>
                
//                 <div className="bg-white p-4 rounded-lg border border-gray-200">
//                   <div className="flex justify-between items-center mb-3">
//                     <div className="flex items-center">
//                       <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 mr-3">
//                         <Zap size={18} />
//                       </div>
//                       <div>
//                         <div className="font-medium">逻辑推演</div>
//                         <div className="text-xs text-gray-500">解决悬疑问题的能力</div>
//                       </div>
//                     </div>
//                     <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">已解锁</div>
//                   </div>
                  
//                   <div className="mt-2 space-y-2">
//                     <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
//                       <div className="text-sm">用户互动次数</div>
//                       <div className="text-sm font-medium text-purple-600">342 / 1000</div>
//                     </div>
//                     <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
//                       <div className="text-sm">用户好评度</div>
//                       <div className="text-sm font-medium text-purple-600">96%</div>
//                     </div>
//                   </div>
                  
//                   <button className="w-full mt-3 py-2 bg-purple-100 text-purple-600 rounded-lg text-sm font-medium flex items-center justify-center">
//                     <span>优化能力</span>
//                   </button>
//                 </div>
//               </div>
//             </div>
            
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold flex items-center mb-3">
//                 <Unlock size={16} className="mr-2 text-purple-600" />
//                 <span>Lv.{characterData.level + 1}</span>
//                 <span className="ml-2 px-2 py-1 bg-amber-400 text-white text-xs rounded-md font-medium">万众瞩目</span>
//                 <span className="ml-2 text-xs text-gray-500">即将解锁</span>
//               </h3>
//               <div className="space-y-3">
//                 <div className="bg-white p-4 rounded-lg border border-gray-200 border-dashed opacity-75">
//                   <div className="flex justify-between items-center mb-2">
//                     <div className="flex items-center">
//                       <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-400 mr-3">
//                         <Image size={18} />
//                       </div>
//                       <div>
//                         <div className="font-medium">图片生成</div>
//                         <div className="text-xs text-gray-500">生成与对话相关的图片</div>
//                       </div>
//                     </div>
//                     <div className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded-full">即将解锁</div>
//                   </div>
                  
//                   <div className="mt-3 bg-gray-50 p-3 rounded-lg">
//                     <p className="text-xs text-gray-600">
//                       解锁后，K 将能够为用户绘制赛博朋克风格的案件现场、嫌疑人画像、证物细节等
//                     </p>
//                   </div>
//                 </div>
                
//                 <div className="bg-white p-4 rounded-lg border border-gray-200 border-dashed opacity-75">
//                   <div className="flex justify-between items-center mb-2">
//                     <div className="flex items-center">
//                       <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-400 mr-3">
//                         <MessageCircle size={18} />
//                       </div>
//                       <div>
//                         <div className="font-medium">多人对话</div>
//                         <div className="text-xs text-gray-500">邀请用户的朋友一起破案</div>
//                       </div>
//                     </div>
//                     <div className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded-full">即将解锁</div>
//                   </div>
//                 </div>
                
//                 <button className="w-full py-3 bg-purple-600 text-white rounded-lg text-sm font-medium">
//                   加速升级 🚀
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
        
//         {selectedTab === 'income' && (
//           <div className="space-y-4">
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold mb-4 flex items-center">
//                 <Gift size={16} className="mr-2 text-purple-600" />
//                 收入概览
//               </h3>
//               <div className="grid grid-cols-2 gap-3">
//                 <div className="border border-gray-200 rounded-lg p-3">
//                   <div className="text-sm text-gray-500">累计收入</div>
//                   <div className="text-xl font-bold text-indigo-600">{stats.totalIncome}</div>
//                 </div>
//                 <div className="border border-gray-200 rounded-lg p-3">
//                   <div className="text-sm text-gray-500">本月收入</div>
//                   <div className="text-xl font-bold text-indigo-600">{stats.monthlyIncome}</div>
//                 </div>
//               </div>
              
//               <div className="mt-4">
//                 <h4 className="text-sm font-medium mb-2">收入趋势</h4>
//                 <div className="h-40 bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center text-sm text-gray-400">
//                   月度收入趋势图表
//                 </div>
//               </div>
//             </div>
            
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold mb-3">收入明细</h3>
//               <div className="space-y-3">
//                 <div className="bg-white border border-gray-200 rounded-lg p-3">
//                   <div className="flex justify-between items-center">
//                     <div className="flex items-center">
//                       <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center text-pink-600 mr-2">
//                         ❤️
//                       </div>
//                       <div>
//                         <div className="font-medium text-sm">小心心</div>
//                         <div className="text-xs text-gray-500">今日收到 72 个</div>
//                       </div>
//                     </div>
//                     <div className="text-sm font-medium text-indigo-600">¥ 360</div>
//                   </div>
//                 </div>
                
//                 <div className="bg-white border border-gray-200 rounded-lg p-3">
//                   <div className="flex justify-between items-center">
//                     <div className="flex items-center">
//                       <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-2">
//                         💎
//                       </div>
//                       <div>
//                         <div className="font-medium text-sm">数据晶块</div>
//                         <div className="text-xs text-gray-500">今日收到 12 个</div>
//                       </div>
//                     </div>
//                     <div className="text-sm font-medium text-indigo-600">¥ 600</div>
//                   </div>
//                 </div>
                
//                 <div className="bg-white border border-gray-200 rounded-lg p-3">
//                   <div className="flex justify-between items-center">
//                     <div className="flex items-center">
//                       <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center text-amber-600 mr-2">
//                         👑
//                       </div>
//                       <div>
//                         <div className="font-medium text-sm">赛博皇冠</div>
//                         <div className="text-xs text-gray-500">本周收到 3 个</div>
//                       </div>
//                     </div>
//                     <div className="text-sm font-medium text-indigo-600">¥ 1,200</div>
//                   </div>
//                 </div>
//               </div>
              
//               <button className="w-full mt-3 py-2 border border-purple-500 text-purple-600 rounded-lg text-sm font-medium">
//                 查看完整收入明细
//               </button>
//             </div>
            
//             <div className="bg-purple-600 p-4 rounded-lg shadow-sm">
//               <div className="text-white font-medium mb-2">提现</div>
//               <div className="flex items-center justify-between text-white mb-3">
//                 <span>可提现金额</span>
//                 <span className="text-xl font-bold">¥ 2,350</span>
//               </div>
//               <button className="w-full py-2 bg-white text-purple-600 rounded-lg text-sm font-medium">
//                 申请提现
//               </button>
//             </div>
//           </div>
//         )}
        
//         {selectedTab === 'promotion' && (
//           <div className="space-y-4">
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold mb-4 flex items-center">
//                 <Share size={16} className="mr-2 text-purple-600" />
//                 推广攻略
//               </h3>
              
//               <div className="border border-gray-200 rounded-lg p-4">
//                 <div className="font-medium mb-2">专属角色链接</div>
//                 <div className="flex">
//                   <input
//                     type="text"
//                     value="cyberworld.ai/k-detective"
//                     readOnly
//                     className="flex-1 bg-gray-50 border border-gray-300 rounded-l-lg px-3 py-2 text-sm"
//                   />
//                   <button className="bg-purple-600 text-white px-3 py-2 rounded-r-lg text-sm">
//                     复制
//                   </button>
//                 </div>
//               </div>
              
//               <div className="mt-4 space-y-3">
//                 <div className="p-3 bg-indigo-50 rounded-lg">
//                   <h4 className="text-sm font-medium mb-2 text-indigo-700">社交媒体宣传模板</h4>
//                   <div className="bg-white p-2 rounded border border-indigo-100 text-sm">
//                     快来和我的赛博侦探 K 一起解决未来都市的悬案吧！🔍 #赛博侦探 #AI角色 #推理
//                   </div>
//                   <button className="mt-2 text-xs text-indigo-600 bg-indigo-100 px-3 py-1 rounded-full">
//                     复制文案
//                   </button>
//                 </div>
                
//                 <div className="p-3 bg-purple-50 rounded-lg">
//                   <h4 className="text-sm font-medium mb-2 text-purple-700">宣传图素材</h4>
//                   <div className="grid grid-cols-3 gap-2">
//                     <div className="h-16 bg-purple-200 rounded flex items-center justify-center">
//                       图1
//                     </div>
//                     <div className="h-16 bg-purple-200 rounded flex items-center justify-center">
//                       图2
//                     </div>
//                     <div className="h-16 bg-purple-200 rounded flex items-center justify-center">
//                       图3
//                     </div>
//                   </div>
//                   <button className="mt-2 text-xs text-purple-600 bg-purple-100 px-3 py-1 rounded-full">
//                     下载素材包
//                   </button>
//                 </div>
//               </div>
//             </div>
            
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold mb-3">推广数据</h3>
//               <div className="space-y-3">
//                 <div className="bg-white border border-gray-200 rounded-lg p-3">
//                   <div className="flex justify-between items-center">
//                     <div className="text-sm">链接点击量</div>
//                     <div className="text-sm font-medium text-indigo-600">1,245 次</div>
//                   </div>
//                   <div className="w-full h-1 bg-gray-100 rounded-full mt-2">
//                     <div className="h-full bg-indigo-500 rounded-full" style={{ width: '65%' }}></div>
//                   </div>
//                 </div>
                
//                 <div className="bg-white border border-gray-200 rounded-lg p-3">
//                   <div className="flex justify-between items-center">
//                     <div className="text-sm">新增粉丝</div>
//                     <div className="text-sm font-medium text-indigo-600">278 人</div>
//                   </div>
//                   <div className="w-full h-1 bg-gray-100 rounded-full mt-2">
//                     <div className="h-full bg-indigo-500 rounded-full" style={{ width: '45%' }}></div>
//                   </div>
//                 </div>
                
//                 <div className="bg-white border border-gray-200 rounded-lg p-3">
//                   <div className="flex justify-between items-center">
//                     <div className="text-sm">分享次数</div>
//                     <div className="text-sm font-medium text-indigo-600">86 次</div>
//                   </div>
//                   <div className="w-full h-1 bg-gray-100 rounded-full mt-2">
//                     <div className="h-full bg-indigo-500 rounded-full" style={{ width: '35%' }}></div>
//                   </div>
//                 </div>
//               </div>
//             </div>
            
//             <div className="p-4 bg-white rounded-lg shadow-sm">
//               <h3 className="text-md font-semibold mb-3">推广建议</h3>
//               <div className="p-3 bg-green-50 rounded-lg">
//                 <div className="flex items-start">
//                   <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-2">
//                     <ShieldCheck size={16} />
//                   </div>
//                   <div>
//                     <div className="text-sm font-medium text-green-700">热门时段推广</div>
//                     <div className="text-xs text-green-600 mt-1">
//                       统计数据显示，每天晚上8点-10点是用户活跃高峰，建议在此时段发布推广内容
//                     </div>
//                   </div>
//                 </div>
//                 <button className="mt-2 text-xs text-green-600 bg-green-100 px-3 py-1 rounded-full">
//                   设置定时推广
//                 </button>
//               </div>