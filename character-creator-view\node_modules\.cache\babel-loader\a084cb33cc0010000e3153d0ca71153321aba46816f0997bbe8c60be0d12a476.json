{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u89D2\\u8272\\u5347\\u7EA7\\u8BA1\\u5212\\\\code\\\\character-creator-view\\\\src\\\\StarCompassPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ChevronLeft, TrendingUp, TrendingDown, Sparkles, Target, BarChart3, Star, RefreshCw, ArrowUp, ArrowDown, Calendar, Zap, Info, Eye, MessageCircle, AlertCircle, Shield, Heart } from 'lucide-react';\n\n// 导入头像图片\nimport characterAvatar from './assets/沈婉.png';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StarCompassPage = () => {\n  _s();\n  const [refreshing, setRefreshing] = useState(false);\n  const [todayProfit] = useState(158);\n  const [profitRate] = useState(1.26);\n  const [showDetailView, setShowDetailView] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState('shanghai');\n  const [showToast, setShowToast] = useState(false);\n  const [toastMessage, setToastMessage] = useState('');\n  const [followedStocks, setFollowedStocks] = useState([]);\n  const [analyzingStocks, setAnalyzingStocks] = useState([]);\n  const [showReport, setShowReport] = useState(false);\n  const [reportStock, setReportStock] = useState(null);\n\n  // 角色信息\n  const character = {\n    name: '沈婉',\n    avatar: characterAvatar,\n    investmentStyle: '稳健保守'\n  };\n\n  // 大盘数据\n  const marketData = {\n    shanghai: {\n      name: '上证',\n      value: 3340.69,\n      change: -0.18,\n      color: 'text-green-600'\n    },\n    shenzhen: {\n      name: '深成',\n      value: 10029.11,\n      change: -0.61,\n      color: 'text-green-600'\n    },\n    chinext: {\n      name: '创业板',\n      value: 1991.64,\n      change: -0.68,\n      color: 'text-green-600'\n    }\n  };\n\n  // 处理查看分析报告\n  const handleViewReport = stock => {\n    setReportStock(stock);\n    setShowReport(true);\n  };\n\n  // AI精选股票数据 - 最后一个股票增加心标状态\n  const aiSelectedStocks = [{\n    name: '招商银行',\n    code: '600036',\n    price: 32.56,\n    change: -0.12,\n    changePercent: -0.37,\n    targetPrice: 35.80,\n    suggestion: '买入',\n    riskLevel: '低',\n    aiComment: '大金融板块防御性强，估值处于历史低位，股息率诱人',\n    hasReport: true,\n    isBookmarked: false\n  }, {\n    name: '比亚迪',\n    code: '002594',\n    price: 245.30,\n    change: 1.85,\n    changePercent: 0.76,\n    targetPrice: 260.00,\n    suggestion: '持有',\n    riskLevel: '中',\n    aiComment: '新能源龙头，政策利好预期，但短期涨幅较大建议持有待调整',\n    hasReport: false,\n    isBookmarked: false\n  }, {\n    name: '贵州茅台',\n    code: '600519',\n    price: 1680.00,\n    change: -5.20,\n    changePercent: -0.31,\n    targetPrice: 1750.00,\n    suggestion: '观望',\n    riskLevel: '低',\n    aiComment: '消费白马，长期价值稳定，等待更好的介入时机',\n    hasReport: false,\n    isBookmarked: false\n  }, {\n    name: '宁德时代',\n    code: '300750',\n    price: 186.50,\n    change: 2.30,\n    changePercent: 1.25,\n    targetPrice: 195.00,\n    suggestion: '买入',\n    riskLevel: '中',\n    aiComment: '新能源电池龙头，行业景气度高，技术优势明显',\n    hasReport: false,\n    isBookmarked: true // 最后一个股票标记为已心标\n  }];\n\n  // 今日操作记录\n  const todayOperations = [{\n    type: 'buy',\n    stock: '科技未来',\n    time: '10:30',\n    reason: '顺势而为，AI判断上升趋势',\n    amount: 50\n  }, {\n    type: 'sell',\n    stock: '传统制造',\n    time: '14:20',\n    reason: '获利了结，规避风险',\n    amount: 30\n  }, {\n    type: 'buy',\n    stock: '新能源车',\n    time: '15:45',\n    reason: '政策利好，逢低吸纳',\n    amount: 20\n  }];\n\n  // 下拉刷新\n  const handleRefresh = () => {\n    setRefreshing(true);\n    setTimeout(() => {\n      setRefreshing(false);\n    }, 1000);\n  };\n\n  // 获取涨跌颜色\n  const getChangeColor = change => {\n    if (change > 0) return 'text-red-600';\n    if (change < 0) return 'text-green-600';\n    return 'text-gray-600';\n  };\n\n  // 市场情绪分析\n  const getMarketSentiment = () => {\n    const avgChange = (marketData.shanghai.change + marketData.shenzhen.change + marketData.chinext.change) / 3;\n    if (avgChange > 0.5) return {\n      text: '热情🔥',\n      color: 'text-red-600',\n      bg: 'bg-red-50'\n    };\n    if (avgChange > -0.5) return {\n      text: '平稳😐',\n      color: 'text-yellow-600',\n      bg: 'bg-yellow-50'\n    };\n    return {\n      text: '谨慎😟',\n      color: 'text-green-600',\n      bg: 'bg-green-50'\n    };\n  };\n  const sentiment = getMarketSentiment();\n\n  // 处理聊天按钮点击\n  const handleChatClick = context => {\n    console.log('Navigate to chat with context:', context);\n  };\n\n  // 显示Toast\n  const showToastMessage = message => {\n    setToastMessage(message);\n    setShowToast(true);\n    setTimeout(() => {\n      setShowToast(false);\n    }, 3000);\n  };\n\n  // 处理关注股票\n  const handleFollowStock = code => {\n    if (!followedStocks.includes(code)) {\n      setFollowedStocks([...followedStocks, code]);\n      showToastMessage('好的，已加入关注对象，如果今天有好的买入机会就会下手。');\n    }\n  };\n\n  // 处理分析股票\n  const handleAnalyzeStock = code => {\n    setAnalyzingStocks([...analyzingStocks, code]);\n    showToastMessage('嗯呢，我会搜索资料好好分析一下，待会把分析报告给你哦。');\n\n    // 模拟分析完成\n    setTimeout(() => {\n      setAnalyzingStocks(analyzingStocks.filter(c => c !== code));\n    }, 2000);\n  };\n\n  // 获取建议标签样式\n  const getSuggestionStyle = suggestion => {\n    switch (suggestion) {\n      case '买入':\n        return 'bg-red-50 text-red-600 border-red-200';\n      case '持有':\n        return 'bg-blue-50 text-blue-600 border-blue-200';\n      case '观望':\n        return 'bg-yellow-50 text-yellow-600 border-yellow-200';\n      case '卖出':\n        return 'bg-green-50 text-green-600 border-green-200';\n      default:\n        return 'bg-gray-50 text-gray-600 border-gray-200';\n    }\n  };\n\n  // 获取风险等级样式\n  const getRiskLevelStyle = level => {\n    switch (level) {\n      case '低':\n        return {\n          color: 'text-green-600',\n          bg: 'bg-green-50',\n          icon: '🛡️'\n        };\n      case '中':\n        return {\n          color: 'text-yellow-600',\n          bg: 'bg-yellow-50',\n          icon: '⚡'\n        };\n      case '高':\n        return {\n          color: 'text-red-600',\n          bg: 'bg-red-50',\n          icon: '🔥'\n        };\n      default:\n        return {\n          color: 'text-gray-600',\n          bg: 'bg-gray-50',\n          icon: '❓'\n        };\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-50 overflow-hidden\",\n    children: [showToast && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-20 left-1/2 transform -translate-x-1/2 z-50 animate-slide-down\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-full shadow-lg px-4 py-3 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: character.avatar,\n          alt: \"\",\n          className: \"w-8 h-8 rounded-full object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-700\",\n          children: toastMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full max-w-[390px] h-full mx-auto bg-white shadow-xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed top-0 left-0 right-0 max-w-[390px] mx-auto bg-white z-30 border-b border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between px-4 py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-2 -ml-2\",\n            children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"font-semibold text-lg flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Target, {\n              className: \"w-5 h-5 text-purple-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), \"\\u661F\\u8BED\\u7F57\\u76D8\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: `p-2 -mr-2 ${refreshing ? 'animate-spin' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 pb-4 h-full overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-bold text-gray-800\",\n                children: \"\\u4ECA\\u65E5\\u5927\\u76D8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-3 py-1 rounded-full text-sm font-medium ${sentiment.bg} ${sentiment.color}`,\n                children: [\"\\u5E02\\u573A\\u60C5\\u7EEA\\uFF1A\", sentiment.text]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-3\",\n              children: Object.entries(marketData).map(([key, data]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/60 backdrop-blur rounded-xl p-3 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: data.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg font-bold text-gray-800\",\n                  children: data.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm font-medium ${getChangeColor(data.change)}`,\n                  children: [data.change > 0 ? '+' : '', data.change, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this)]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowDetailView(true),\n              className: \"w-full mt-3 bg-white/40 backdrop-blur rounded-xl py-2 text-sm text-blue-700 font-medium flex items-center justify-center gap-1 active:bg-white/60\",\n              children: [/*#__PURE__*/_jsxDEV(Eye, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), \"\\u67E5\\u770B\\u8BE6\\u7EC6\\u6570\\u636E\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-purple-50 to-pink-100 rounded-3xl p-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-3 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n                className: \"w-5 h-5 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), \"\\u6211\\u7684\\u6536\\u76CA\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/60 backdrop-blur rounded-xl p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: \"\\u4ECA\\u65E5\\u6536\\u76CA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xl font-bold ${getChangeColor(todayProfit)}`,\n                  children: [todayProfit > 0 ? '+' : '', \"\\xA5\", todayProfit]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/60 backdrop-blur rounded-xl p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: \"\\u6536\\u76CA\\u7387\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xl font-bold ${getChangeColor(profitRate)}`,\n                  children: [\"+\", profitRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-yellow-50 to-orange-100 rounded-3xl p-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start gap-3 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: character.avatar,\n                alt: \"\",\n                className: \"w-12 h-12 rounded-full border-2 border-white shadow-md object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-800\",\n                  children: [character.name, \"\\u7684\\u5E02\\u573A\\u89E3\\u8BFB\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: new Date().toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Sparkles, {\n                className: \"w-5 h-5 text-yellow-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/60 backdrop-blur rounded-xl p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700 leading-relaxed\",\n                children: [\"\\\"\\u4ECA\\u5929\\u7684\\u5E02\\u573A\\u5C31\\u50CF\\u79CB\\u65E5\\u7684\\u5348\\u540E\\uFF0C\\u867D\\u7136\\u4E09\\u5927\\u6307\\u6570\\u90FD\\u5728\\u5C0F\\u5E45\\u8C03\\u6574\\uFF0C\\u4F46\\u6211\\u89C9\\u5F97\\u8FD9\\u66F4\\u50CF\\u662F\\u5728\\u79EF\\u84C4\\u529B\\u91CF\\u5462\\uFF01\\u770B\\u5230\\u6DA8\\u505C\\u699C\\u4E0A\\u79D1\\u6280\\u80A1\\u4F9D\\u7136\\u575A\\u633A\\uFF0C\\u7279\\u522B\\u662F\\u4E2D\\u65D7\\u80A1\\u4EFD\\u548C\\u683C\\u529B\\u535A\\uFF0C\\u5B83\\u4EEC\\u5C31\\u50CF\\u79CB\\u65E5\\u91CC\\u6700\\u540E\\u7EFD\\u653E\\u7684\\u82B1\\u6735\\uFF0C\\u683C\\u5916\\u8000\\u773C\\u2728\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 25\n                }, this), \"\\u4E3B\\u4EBA\\uFF0C\\u867D\\u7136\\u5927\\u76D8\\u6709\\u4E9B\\u75B2\\u8F6F\\uFF0C\\u4F46\\u6211\\u5DF2\\u7ECF\\u6084\\u6084\\u8C03\\u6574\\u4E86\\u4ED3\\u4F4D\\uFF0C\\u51CF\\u6301\\u4E86\\u4E00\\u4E9B\\u4F20\\u7EDF\\u5236\\u9020\\uFF0C\\u589E\\u52A0\\u4E86\\u65B0\\u80FD\\u6E90\\u7684\\u914D\\u7F6E\\u3002\\u76F8\\u4FE1\\u660E\\u5929\\u4F1A\\u6709\\u60CA\\u559C\\u54E6~\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-2 mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/40 backdrop-blur rounded-xl p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600\",\n                  children: \"\\u70ED\\u95E8\\u677F\\u5757\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-purple-600\",\n                  children: \"\\u79D1\\u6280\\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/40 backdrop-blur rounded-xl p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600\",\n                  children: \"\\u64CD\\u4F5C\\u7B56\\u7565\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-orange-600\",\n                  children: \"\\u8C03\\u4ED3\\u2696\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/40 backdrop-blur rounded-xl p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600\",\n                  children: \"\\u98CE\\u9669\\u7B49\\u7EA7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-green-600\",\n                  children: \"\\u4F4E\\u98CE\\u9669\\uD83D\\uDEE1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleChatClick('market-analysis'),\n              className: \"w-full mt-4 bg-gradient-to-r from-orange-400 to-yellow-400 text-white rounded-2xl py-3 flex items-center justify-center gap-2 font-medium shadow-lg active:scale-95 transition-transform\",\n              children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u4E0E\", character.name, \"\\u804A\\u804A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs opacity-80 ml-1\",\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-3xl shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-100\",\n              children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                  className: \"w-5 h-5 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this), \"\\u4ECA\\u65E5\\u7CBE\\u9009\\u5173\\u6CE8\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500 ml-auto\",\n                  children: \"AI\\u667A\\u80FD\\u63A8\\u8350\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divide-y divide-gray-100\",\n              children: aiSelectedStocks.map((stock, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-medium text-gray-800\",\n                        children: stock.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 347,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: stock.code\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 348,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `text-xs px-2 py-0.5 rounded-full border ${getSuggestionStyle(stock.suggestion)}`,\n                        children: stock.suggestion\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 27\n                      }, this), stock.isBookmarked && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-pink-100 text-pink-600 px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1\",\n                        children: [/*#__PURE__*/_jsxDEV(Heart, {\n                          className: \"w-3 h-3 fill-current\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 355,\n                          columnNumber: 31\n                        }, this), \"\\u5DF2\\u5FC3\\u6807\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 text-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-800\",\n                        children: [\"\\xA5\", stock.price]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `font-medium ${getChangeColor(stock.change)}`,\n                        children: [stock.change > 0 ? '+' : '', stock.change, \" (\", stock.changePercent > 0 ? '+' : '', stock.changePercent, \"%)\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mb-1\",\n                      children: \"\\u76EE\\u6807\\u4EF7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-semibold text-purple-600\",\n                      children: [\"\\xA5\", stock.targetPrice]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-xl p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-700 leading-relaxed\",\n                    children: stock.aiComment\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2 mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs px-2 py-0.5 rounded-full ${getRiskLevelStyle(stock.riskLevel).bg} ${getRiskLevelStyle(stock.riskLevel).color}`,\n                      children: [\"\\u98CE\\u9669\", stock.riskLevel, \" \", getRiskLevelStyle(stock.riskLevel).icon]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-3xl shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-100\",\n              children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"w-5 h-5 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this), \"\\u4ECA\\u65E5\\u64CD\\u4F5C\\u8F68\\u8FF9\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divide-y divide-gray-100\",\n              children: todayOperations.map((op, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-8 h-8 rounded-full flex items-center justify-center ${op.type === 'buy' ? 'bg-red-50' : 'bg-green-50'}`,\n                      children: op.type === 'buy' ? /*#__PURE__*/_jsxDEV(ArrowUp, {\n                        className: \"w-4 h-4 text-red-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 406,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(ArrowDown, {\n                        className: \"w-4 h-4 text-green-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-medium text-gray-800\",\n                          children: [op.type === 'buy' ? '买入' : '卖出', \" \", op.stock]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 412,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: op.time\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 415,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 411,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: op.reason\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 417,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-700\",\n                      children: [op.amount, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"\\u4ED3\\u4F4D\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-green-50 to-emerald-100 rounded-3xl p-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold mb-3 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Star, {\n                className: \"w-5 h-5 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), \"\\u660E\\u65E5\\u5C55\\u671B\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/60 backdrop-blur rounded-xl p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700 leading-relaxed\",\n                children: [\"\\\"\\u660E\\u5929\\u6211\\u60F3\\u50CF\\u52E4\\u52B3\\u7684\\u5C0F\\u871C\\u8702\\u4E00\\u6837\\uFF0C\\u5728\\u65B0\\u80FD\\u6E90\\u7684\\u82B1\\u4E1B\\u4E2D\\u591A\\u91C7\\u4E9B\\u871C\\uFF01\\u542C\\u8BF4\\u6709\\u653F\\u7B56\\u6625\\u98CE\\u8981\\u6765\\uFF0C\\u6211\\u5DF2\\u7ECF\\u51C6\\u5907\\u597D\\u5C0F\\u7BEE\\u5B50\\u4E86\\uD83E\\uDDFA\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 25\n                }, this), \"\\u79D1\\u6280\\u80A1\\u4ECA\\u5929\\u8868\\u73B0\\u4E0D\\u9519\\uFF0C\\u4F46\\u6DA8\\u5F97\\u6709\\u70B9\\u5FEB\\uFF0C\\u50CF\\u4E2A\\u8C03\\u76AE\\u7684\\u5B69\\u5B50\\uFF0C\\u660E\\u5929\\u6211\\u4F1A\\u627E\\u673A\\u4F1A\\u8BA9\\u5B83\\u4F11\\u606F\\u4E00\\u4E0B\\u3002\\u8FD8\\u6709\\u554A\\uFF0C\\u5982\\u679C\\u5927\\u76D8\\u7EE7\\u7EED\\u8C03\\u6574\\uFF0C\\u6211\\u53EF\\u80FD\\u4F1A\\u9022\\u4F4E\\u589E\\u52A0\\u4E00\\u4E9B\\u4F18\\u8D28\\u767D\\u9A6C\\u80A1\\u54E6~\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-3 mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/40 backdrop-blur rounded-xl p-3 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600 mb-1\",\n                  children: \"\\u660E\\u65E5\\u91CD\\u70B9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-green-600\",\n                  children: \"\\u65B0\\u80FD\\u6E90 + \\u767D\\u9A6C\\u80A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/40 backdrop-blur rounded-xl p-3 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600 mb-1\",\n                  children: \"\\u64CD\\u4F5C\\u503E\\u5411\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-blue-600\",\n                  children: \"\\u9022\\u4F4E\\u5438\\u7EB3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleChatClick('tomorrow-outlook'),\n              className: \"w-full mt-4 bg-gradient-to-r from-emerald-400 to-green-400 text-white rounded-2xl py-3 flex items-center justify-center gap-2 font-medium shadow-lg active:scale-95 transition-transform\",\n              children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u4E0E\", character.name, \"\\u804A\\u804A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs opacity-80 ml-1\",\n                children: \"\\uD83C\\uDF1F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), showDetailView && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\",\n        onClick: () => setShowDetailView(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up max-h-[70vh] overflow-y-auto\",\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold mb-4 text-center\",\n            children: \"\\u5927\\u76D8\\u8BE6\\u7EC6\\u6570\\u636E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: Object.entries(marketData).map(([key, data]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-xl p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-gray-800\",\n                  children: [data.name, \"\\u6307\\u6570\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-sm font-medium ${getChangeColor(data.change)}`,\n                  children: [data.change > 0 ? '+' : '', data.change, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-800 mb-2\",\n                children: data.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u4ECA\\u5F00: 3342.15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u6628\\u6536: 3346.35\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u6700\\u9AD8: 3355.82\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"\\u6700\\u4F4E: 3338.91\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 21\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowDetailView(false),\n            className: \"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold mt-6\",\n            children: \"\\u77E5\\u9053\\u4E86\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this), showReport && reportStock && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black/50 z-50 flex items-end\",\n        onClick: () => setShowReport(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-t-3xl w-full max-w-[390px] mx-auto animate-slide-up max-h-[85vh] overflow-hidden flex flex-col\",\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-1 bg-gray-300 rounded-full mx-auto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-y-auto px-4 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold\",\n                    children: reportStock.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: reportStock.code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"\\u76EE\\u6807\\u4EF7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xl font-bold text-purple-600\",\n                    children: [\"\\xA5\", reportStock.targetPrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-red-100 text-red-600 px-3 py-1 rounded-full text-sm font-medium\",\n                  children: \"\\u5F3A\\u70C8\\u63A8\\u8350\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"2025\\u5E741\\u670828\\u65E5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold mb-2 flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                  className: \"w-4 h-4 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 21\n                }, this), \"\\u6838\\u5FC3\\u89C2\\u70B9\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700 leading-relaxed\",\n                children: \"\\u62DB\\u5546\\u94F6\\u884C\\u4F5C\\u4E3A\\u80A1\\u4EFD\\u5236\\u94F6\\u884C\\u9F99\\u5934\\uFF0C\\u8D44\\u4EA7\\u8D28\\u91CF\\u4F18\\u5F02\\uFF0C\\u96F6\\u552E\\u4E1A\\u52A1\\u62A4\\u57CE\\u6CB3\\u6DF1\\u539A\\u3002\\u5F53\\u524D\\u4F30\\u503C\\u5904\\u4E8E\\u5386\\u53F2\\u4F4E\\u4F4D\\uFF0C\\u80A1\\u606F\\u7387\\u8D85\\u8FC75%\\uFF0C\\u5177\\u5907\\u8F83\\u9AD8\\u7684\\u5B89\\u5168\\u8FB9\\u9645\\u548C\\u6295\\u8D44\\u4EF7\\u503C\\u3002\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold mb-3\",\n                children: \"\\u6295\\u8D44\\u4EAE\\u70B9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-bold text-blue-600\",\n                      children: \"1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800 mb-1\",\n                      children: \"\\u96F6\\u552E\\u4E4B\\u738B\\u5730\\u4F4D\\u7A33\\u56FA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"\\u96F6\\u552E\\u5BA2\\u6237\\u6570\\u7A81\\u78341.8\\u4EBF\\uFF0C\\u79C1\\u4EBA\\u94F6\\u884C\\u5BA2\\u6237AUM\\u8D853\\u4E07\\u4EBF\\uFF0C\\u9AD8\\u51C0\\u503C\\u5BA2\\u6237\\u57FA\\u7840\\u96C4\\u539A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-bold text-blue-600\",\n                      children: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 569,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800 mb-1\",\n                      children: \"\\u8D44\\u4EA7\\u8D28\\u91CF\\u884C\\u4E1A\\u9886\\u5148\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"\\u4E0D\\u826F\\u7387\\u4EC50.94%\\uFF0C\\u62E8\\u5907\\u8986\\u76D6\\u7387\\u8D85\\u8FC7450%\\uFF0C\\u98CE\\u9669\\u62B5\\u5FA1\\u80FD\\u529B\\u5F3A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-bold text-blue-600\",\n                      children: \"3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800 mb-1\",\n                      children: \"\\u9AD8\\u80A1\\u606F\\u7387\\u9632\\u5FA1\\u5C5E\\u6027\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"\\u8FDE\\u7EED10\\u5E74\\u7A33\\u5B9A\\u5206\\u7EA2\\uFF0C\\u5F53\\u524D\\u80A1\\u606F\\u73875.2%\\uFF0C\\u9002\\u5408\\u957F\\u671F\\u6301\\u6709\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-2xl p-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold mb-3\",\n                children: \"\\u5173\\u952E\\u8D22\\u52A1\\u6307\\u6807\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mb-1\",\n                    children: \"\\u5E02\\u76C8\\u7387(PE)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold\",\n                    children: \"4.8x\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-green-600\",\n                    children: \"\\u4F4E\\u4E8E\\u884C\\u4E1A\\u5747\\u503C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mb-1\",\n                    children: \"\\u5E02\\u51C0\\u7387(PB)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold\",\n                    children: \"0.65x\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-green-600\",\n                    children: \"\\u5386\\u53F2\\u4F4E\\u4F4D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mb-1\",\n                    children: \"ROE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold\",\n                    children: \"13.8%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-blue-600\",\n                    children: \"\\u884C\\u4E1A\\u7B2C\\u4E00\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 606,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mb-1\",\n                    children: \"\\u51C0\\u5229\\u6DA6\\u589E\\u901F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold\",\n                    children: \"+6.2%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-blue-600\",\n                    children: \"\\u7A33\\u5065\\u589E\\u957F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold mb-3\",\n                children: \"\\u6280\\u672F\\u9762\\u5206\\u6790\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 rounded-xl p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u5F53\\u524D\\u4EF7\\u683C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"\\xA532.56\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"20\\u65E5\\u5747\\u7EBF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: \"\\xA532.20\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u652F\\u6491\\u4F4D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold text-green-600\",\n                    children: \"\\xA531.50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u538B\\u529B\\u4F4D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold text-red-600\",\n                    children: \"\\xA533.80\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-orange-50 rounded-2xl p-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold mb-2 flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this), \"\\u98CE\\u9669\\u63D0\\u793A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-sm text-gray-700 space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 \\u5B8F\\u89C2\\u7ECF\\u6D4E\\u4E0B\\u884C\\u53EF\\u80FD\\u5F71\\u54CD\\u8D44\\u4EA7\\u8D28\\u91CF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 \\u51C0\\u606F\\u5DEE\\u6536\\u7A84\\u538B\\u529B\\u6301\\u7EED\\u5B58\\u5728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 \\u623F\\u5730\\u4EA7\\u98CE\\u9669\\u655E\\u53E3\\u9700\\u6301\\u7EED\\u5173\\u6CE8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-yellow-50 to-orange-100 rounded-2xl p-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start gap-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: character.avatar,\n                  alt: \"\",\n                  className: \"w-10 h-10 rounded-full border-2 border-white shadow-md object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-gray-800\",\n                    children: \"\\u6C88\\u5A49\\u7684\\u70B9\\u8BC4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 657,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"\\u7A33\\u5065\\u6295\\u8D44\\u8005\\u89C6\\u89D2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700 leading-relaxed\",\n                children: \"\\\"\\u62DB\\u5546\\u94F6\\u884C\\u5C31\\u50CF\\u4E00\\u4F4D\\u7A33\\u91CD\\u7684\\u5927\\u54E5\\u54E5\\uFF0C\\u867D\\u7136\\u4E0D\\u4F1A\\u7ED9\\u4F60\\u60CA\\u559C\\u7684\\u66B4\\u6DA8\\uFF0C\\u4F46\\u6BCF\\u5E74\\u7A33\\u7A33\\u7684\\u5206\\u7EA2\\u5C31\\u50CF\\u5B9A\\u671F\\u6536\\u79DF\\u4E00\\u6837\\u8BA9\\u4EBA\\u5B89\\u5FC3\\u3002\\u73B0\\u5728\\u7684\\u4EF7\\u683C\\u5C31\\u50CF\\u5728\\u6253\\u6298\\u7684\\u4F18\\u8D28\\u8D44\\u4EA7\\uFF0C\\u6211\\u5DF2\\u7ECF\\u6084\\u6084\\u5EFA\\u4ED3\\u4E86\\u5462\\uFF01\\u5BF9\\u4E8E\\u54B1\\u4EEC\\u7A33\\u5065\\u578B\\u6295\\u8D44\\u8005\\u6765\\u8BF4\\uFF0C\\u8FD9\\u662F\\u975E\\u5E38\\u597D\\u7684\\u957F\\u671F\\u914D\\u7F6E\\u6807\\u7684\\u54E6~ \\uD83D\\uDC8E\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-t border-gray-200 p-4 bg-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  handleChatClick('stock-analysis');\n                  setShowReport(false);\n                },\n                className: \"flex-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-medium flex items-center justify-center gap-2 shadow-lg active:scale-95 transition-transform\",\n                children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this), \"\\u4E0E\", character.name, \"\\u804A\\u804A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  handleFollowStock(reportStock.code);\n                  setShowReport(false);\n                },\n                disabled: followedStocks.includes(reportStock.code),\n                className: `flex-1 py-3 rounded-2xl font-medium flex items-center justify-center gap-2 transition-all ${followedStocks.includes(reportStock.code) ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-700 active:bg-gray-200'}`,\n                children: followedStocks.includes(reportStock.code) ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Star, {\n                    className: \"w-5 h-5 fill-current\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 25\n                  }, this), \"\\u5DF2\\u5173\\u6CE8\"]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Star, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 25\n                  }, this), \"\\u91CD\\u70B9\\u5173\\u6CE8\"]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes scale-up {\n          from {\n            transform: scale(0.9);\n            opacity: 0;\n          }\n          to {\n            transform: scale(1);\n            opacity: 1;\n          }\n        }\n        \n        @keyframes slide-down {\n          from {\n            transform: translateX(-50%) translateY(-20px);\n            opacity: 0;\n          }\n          to {\n            transform: translateX(-50%) translateY(0);\n            opacity: 1;\n          }\n        }\n        \n        .animate-scale-up {\n          animation: scale-up 0.3s ease-out;\n        }\n        \n        .animate-slide-down {\n          animation: slide-down 0.3s ease-out;\n        }\n        \n        @keyframes slide-up {\n        from {\n            transform: translateY(100%);\n        }\n        to {\n            transform: translateY(0);\n        }\n        }\n\n        .animate-slide-up {\n        animation: slide-up 0.3s ease-out;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n};\n_s(StarCompassPage, \"pKgzb/w1rGXahwQKjcpXDOt92KU=\");\n_c = StarCompassPage;\nexport default StarCompassPage;\nvar _c;\n$RefreshReg$(_c, \"StarCompassPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ChevronLeft", "TrendingUp", "TrendingDown", "<PERSON><PERSON><PERSON>", "Target", "BarChart3", "Star", "RefreshCw", "ArrowUp", "ArrowDown", "Calendar", "Zap", "Info", "Eye", "MessageCircle", "AlertCircle", "Shield", "Heart", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StarCompassPage", "_s", "refreshing", "setRefreshing", "todayProfit", "profitRate", "showDetailView", "setShowDetailView", "selectedIndex", "setSelectedIndex", "showToast", "setShowToast", "toastMessage", "setToastMessage", "followedStocks", "setFollowedStocks", "analyzingStocks", "setAnalyzingStocks", "showReport", "setShowReport", "reportStock", "setReportStock", "character", "name", "avatar", "investmentStyle", "marketData", "shanghai", "value", "change", "color", "<PERSON><PERSON><PERSON>", "chinext", "handleViewReport", "stock", "aiSelectedStocks", "code", "price", "changePercent", "targetPrice", "suggestion", "riskLevel", "aiComment", "hasReport", "isBookmarked", "todayOperations", "type", "time", "reason", "amount", "handleRefresh", "setTimeout", "getChangeColor", "getMarketSentiment", "avgChange", "text", "bg", "sentiment", "handleChatClick", "context", "console", "log", "showToastMessage", "message", "handleFollowStock", "includes", "handleAnalyzeStock", "filter", "c", "getSuggestionStyle", "getRiskLevelStyle", "level", "icon", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "Object", "entries", "map", "key", "data", "Date", "toLocaleString", "index", "op", "e", "stopPropagation", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/角色升级计划/code/character-creator-view/src/StarCompassPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ChevronLeft, TrendingUp, TrendingDown, Sparkles, Target, BarChart3, Star, RefreshCw, ArrowUp, ArrowDown, Calendar, Zap, Info, Eye, MessageCircle, AlertCircle, Shield, Heart } from 'lucide-react';\r\n\r\n// 导入头像图片\r\nimport characterAvatar from './assets/沈婉.png';\r\n\r\nconst StarCompassPage = () => {\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [todayProfit] = useState(158);\r\n  const [profitRate] = useState(1.26);\r\n  const [showDetailView, setShowDetailView] = useState(false);\r\n  const [selectedIndex, setSelectedIndex] = useState('shanghai');\r\n  const [showToast, setShowToast] = useState(false);\r\n  const [toastMessage, setToastMessage] = useState('');\r\n  const [followedStocks, setFollowedStocks] = useState([]);\r\n  const [analyzingStocks, setAnalyzingStocks] = useState([]);\r\n  const [showReport, setShowReport] = useState(false);\r\n  const [reportStock, setReportStock] = useState(null);\r\n\r\n  // 角色信息\r\n  const character = {\r\n    name: '沈婉',\r\n    avatar: characterAvatar,\r\n    investmentStyle: '稳健保守'\r\n  };\r\n\r\n  // 大盘数据\r\n  const marketData = {\r\n    shanghai: { name: '上证', value: 3340.69, change: -0.18, color: 'text-green-600' },\r\n    shenzhen: { name: '深成', value: 10029.11, change: -0.61, color: 'text-green-600' },\r\n    chinext: { name: '创业板', value: 1991.64, change: -0.68, color: 'text-green-600' }\r\n  };\r\n\r\n  // 处理查看分析报告\r\n  const handleViewReport = (stock) => {\r\n   setReportStock(stock);\r\n   setShowReport(true);\r\n  };\r\n\r\n  // AI精选股票数据 - 最后一个股票增加心标状态\r\n  const aiSelectedStocks = [\r\n    {\r\n      name: '招商银行',\r\n      code: '600036',\r\n      price: 32.56,\r\n      change: -0.12,\r\n      changePercent: -0.37,\r\n      targetPrice: 35.80,\r\n      suggestion: '买入',\r\n      riskLevel: '低',\r\n      aiComment: '大金融板块防御性强，估值处于历史低位，股息率诱人',\r\n      hasReport: true,\r\n      isBookmarked: false\r\n    },\r\n    {\r\n      name: '比亚迪',\r\n      code: '002594',\r\n      price: 245.30,\r\n      change: 1.85,\r\n      changePercent: 0.76,\r\n      targetPrice: 260.00,\r\n      suggestion: '持有',\r\n      riskLevel: '中',\r\n      aiComment: '新能源龙头，政策利好预期，但短期涨幅较大建议持有待调整',\r\n      hasReport: false,\r\n      isBookmarked: false\r\n    },\r\n    {\r\n      name: '贵州茅台',\r\n      code: '600519',\r\n      price: 1680.00,\r\n      change: -5.20,\r\n      changePercent: -0.31,\r\n      targetPrice: 1750.00,\r\n      suggestion: '观望',\r\n      riskLevel: '低',\r\n      aiComment: '消费白马，长期价值稳定，等待更好的介入时机',\r\n      hasReport: false,\r\n      isBookmarked: false\r\n    },\r\n    {\r\n      name: '宁德时代',\r\n      code: '300750',\r\n      price: 186.50,\r\n      change: 2.30,\r\n      changePercent: 1.25,\r\n      targetPrice: 195.00,\r\n      suggestion: '买入',\r\n      riskLevel: '中',\r\n      aiComment: '新能源电池龙头，行业景气度高，技术优势明显',\r\n      hasReport: false,\r\n      isBookmarked: true  // 最后一个股票标记为已心标\r\n    }\r\n  ];\r\n\r\n  // 今日操作记录\r\n  const todayOperations = [\r\n    { type: 'buy', stock: '科技未来', time: '10:30', reason: '顺势而为，AI判断上升趋势', amount: 50 },\r\n    { type: 'sell', stock: '传统制造', time: '14:20', reason: '获利了结，规避风险', amount: 30 },\r\n    { type: 'buy', stock: '新能源车', time: '15:45', reason: '政策利好，逢低吸纳', amount: 20 }\r\n  ];\r\n\r\n  // 下拉刷新\r\n  const handleRefresh = () => {\r\n    setRefreshing(true);\r\n    setTimeout(() => {\r\n      setRefreshing(false);\r\n    }, 1000);\r\n  };\r\n\r\n  // 获取涨跌颜色\r\n  const getChangeColor = (change) => {\r\n    if (change > 0) return 'text-red-600';\r\n    if (change < 0) return 'text-green-600';\r\n    return 'text-gray-600';\r\n  };\r\n\r\n  // 市场情绪分析\r\n  const getMarketSentiment = () => {\r\n    const avgChange = (marketData.shanghai.change + marketData.shenzhen.change + marketData.chinext.change) / 3;\r\n    if (avgChange > 0.5) return { text: '热情🔥', color: 'text-red-600', bg: 'bg-red-50' };\r\n    if (avgChange > -0.5) return { text: '平稳😐', color: 'text-yellow-600', bg: 'bg-yellow-50' };\r\n    return { text: '谨慎😟', color: 'text-green-600', bg: 'bg-green-50' };\r\n  };\r\n\r\n  const sentiment = getMarketSentiment();\r\n\r\n  // 处理聊天按钮点击\r\n  const handleChatClick = (context) => {\r\n    console.log('Navigate to chat with context:', context);\r\n  };\r\n\r\n  // 显示Toast\r\n  const showToastMessage = (message) => {\r\n    setToastMessage(message);\r\n    setShowToast(true);\r\n    setTimeout(() => {\r\n      setShowToast(false);\r\n    }, 3000);\r\n  };\r\n\r\n  // 处理关注股票\r\n  const handleFollowStock = (code) => {\r\n    if (!followedStocks.includes(code)) {\r\n      setFollowedStocks([...followedStocks, code]);\r\n      showToastMessage('好的，已加入关注对象，如果今天有好的买入机会就会下手。');\r\n    }\r\n  };\r\n\r\n  // 处理分析股票\r\n  const handleAnalyzeStock = (code) => {\r\n    setAnalyzingStocks([...analyzingStocks, code]);\r\n    showToastMessage('嗯呢，我会搜索资料好好分析一下，待会把分析报告给你哦。');\r\n    \r\n    // 模拟分析完成\r\n    setTimeout(() => {\r\n      setAnalyzingStocks(analyzingStocks.filter(c => c !== code));\r\n    }, 2000);\r\n  };\r\n\r\n  // 获取建议标签样式\r\n  const getSuggestionStyle = (suggestion) => {\r\n    switch (suggestion) {\r\n      case '买入':\r\n        return 'bg-red-50 text-red-600 border-red-200';\r\n      case '持有':\r\n        return 'bg-blue-50 text-blue-600 border-blue-200';\r\n      case '观望':\r\n        return 'bg-yellow-50 text-yellow-600 border-yellow-200';\r\n      case '卖出':\r\n        return 'bg-green-50 text-green-600 border-green-200';\r\n      default:\r\n        return 'bg-gray-50 text-gray-600 border-gray-200';\r\n    }\r\n  };\r\n\r\n  // 获取风险等级样式\r\n  const getRiskLevelStyle = (level) => {\r\n    switch (level) {\r\n      case '低':\r\n        return { color: 'text-green-600', bg: 'bg-green-50', icon: '🛡️' };\r\n      case '中':\r\n        return { color: 'text-yellow-600', bg: 'bg-yellow-50', icon: '⚡' };\r\n      case '高':\r\n        return { color: 'text-red-600', bg: 'bg-red-50', icon: '🔥' };\r\n      default:\r\n        return { color: 'text-gray-600', bg: 'bg-gray-50', icon: '❓' };\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-50 overflow-hidden\">\r\n      {/* Toast通知 */}\r\n      {showToast && (\r\n        <div className=\"fixed top-20 left-1/2 transform -translate-x-1/2 z-50 animate-slide-down\">\r\n          <div className=\"bg-white rounded-full shadow-lg px-4 py-3 flex items-center gap-3\">\r\n            <img src={character.avatar} alt=\"\" className=\"w-8 h-8 rounded-full object-cover\" />\r\n            <p className=\"text-sm text-gray-700\">{toastMessage}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 手机端容器 */}\r\n      <div className=\"relative w-full max-w-[390px] h-full mx-auto bg-white shadow-xl\">\r\n        {/* 顶部导航栏 */}\r\n        <div className=\"fixed top-0 left-0 right-0 max-w-[390px] mx-auto bg-white z-30 border-b border-gray-100\">\r\n          <div className=\"flex items-center justify-between px-4 py-3\">\r\n            <button className=\"p-2 -ml-2\">\r\n              <ChevronLeft className=\"w-6 h-6\" />\r\n            </button>\r\n            <h1 className=\"font-semibold text-lg flex items-center gap-2\">\r\n              <Target className=\"w-5 h-5 text-purple-500\" />\r\n              星语罗盘\r\n            </h1>\r\n            <button \r\n              onClick={handleRefresh}\r\n              className={`p-2 -mr-2 ${refreshing ? 'animate-spin' : ''}`}\r\n            >\r\n              <RefreshCw className=\"w-5 h-5 text-gray-600\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 主内容区域 */}\r\n        <div className=\"pt-16 pb-4 h-full overflow-y-auto\">\r\n          \r\n          {/* 大盘指数 - 最重要信息置顶 */}\r\n          <div className=\"px-4 py-4\">\r\n            <div className=\"bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-5\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h2 className=\"text-lg font-bold text-gray-800\">今日大盘</h2>\r\n                <div className={`px-3 py-1 rounded-full text-sm font-medium ${sentiment.bg} ${sentiment.color}`}>\r\n                  市场情绪：{sentiment.text}\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"grid grid-cols-3 gap-3\">\r\n                {Object.entries(marketData).map(([key, data]) => (\r\n                  <div key={key} className=\"bg-white/60 backdrop-blur rounded-xl p-3 text-center\">\r\n                    <p className=\"text-sm text-gray-600 mb-1\">{data.name}</p>\r\n                    <p className=\"text-lg font-bold text-gray-800\">{data.value}</p>\r\n                    <p className={`text-sm font-medium ${getChangeColor(data.change)}`}>\r\n                      {data.change > 0 ? '+' : ''}{data.change}%\r\n                    </p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              \r\n              <button \r\n                onClick={() => setShowDetailView(true)}\r\n                className=\"w-full mt-3 bg-white/40 backdrop-blur rounded-xl py-2 text-sm text-blue-700 font-medium flex items-center justify-center gap-1 active:bg-white/60\"\r\n              >\r\n                <Eye className=\"w-4 h-4\" />\r\n                查看详细数据\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 个人收益概览 */}\r\n          <div className=\"px-4 mb-4\">\r\n            <div className=\"bg-gradient-to-br from-purple-50 to-pink-100 rounded-3xl p-5\">\r\n              <h3 className=\"text-lg font-bold mb-3 flex items-center gap-2\">\r\n                <BarChart3 className=\"w-5 h-5 text-purple-600\" />\r\n                我的收益\r\n              </h3>\r\n              <div className=\"grid grid-cols-2 gap-3\">\r\n                <div className=\"bg-white/60 backdrop-blur rounded-xl p-3\">\r\n                  <p className=\"text-sm text-gray-600 mb-1\">今日收益</p>\r\n                  <p className={`text-xl font-bold ${getChangeColor(todayProfit)}`}>\r\n                    {todayProfit > 0 ? '+' : ''}¥{todayProfit}\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-white/60 backdrop-blur rounded-xl p-3\">\r\n                  <p className=\"text-sm text-gray-600 mb-1\">收益率</p>\r\n                  <p className={`text-xl font-bold ${getChangeColor(profitRate)}`}>\r\n                    +{profitRate}%\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* AI角色的市场解读 */}\r\n          <div className=\"px-4 mb-4\">\r\n            <div className=\"bg-gradient-to-br from-yellow-50 to-orange-100 rounded-3xl p-5\">\r\n              <div className=\"flex items-start gap-3 mb-4\">\r\n                <img src={character.avatar} alt=\"\" className=\"w-12 h-12 rounded-full border-2 border-white shadow-md object-cover\" />\r\n                <div className=\"flex-1\">\r\n                  <p className=\"font-medium text-gray-800\">{character.name}的市场解读</p>\r\n                  <p className=\"text-xs text-gray-500\">{new Date().toLocaleString()}</p>\r\n                </div>\r\n                <Sparkles className=\"w-5 h-5 text-yellow-500\" />\r\n              </div>\r\n              \r\n              <div className=\"bg-white/60 backdrop-blur rounded-xl p-4\">\r\n                <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n                  \"今天的市场就像秋日的午后，虽然三大指数都在小幅调整，但我觉得这更像是在积蓄力量呢！看到涨停榜上科技股依然坚挺，特别是中旗股份和格力博，它们就像秋日里最后绽放的花朵，格外耀眼✨ \r\n                  <br /><br />\r\n                  主人，虽然大盘有些疲软，但我已经悄悄调整了仓位，减持了一些传统制造，增加了新能源的配置。相信明天会有惊喜哦~\"\r\n                </p>\r\n              </div>\r\n\r\n              {/* 关键指标 */}\r\n              <div className=\"grid grid-cols-3 gap-2 mt-3\">\r\n                <div className=\"bg-white/40 backdrop-blur rounded-xl p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-600\">热门板块</p>\r\n                  <p className=\"text-sm font-bold text-purple-600\">科技🚀</p>\r\n                </div>\r\n                <div className=\"bg-white/40 backdrop-blur rounded-xl p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-600\">操作策略</p>\r\n                  <p className=\"text-sm font-bold text-orange-600\">调仓⚖️</p>\r\n                </div>\r\n                <div className=\"bg-white/40 backdrop-blur rounded-xl p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-600\">风险等级</p>\r\n                  <p className=\"text-sm font-bold text-green-600\">低风险🛡️</p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 与AI对话按钮 */}\r\n              <button\r\n                onClick={() => handleChatClick('market-analysis')}\r\n                className=\"w-full mt-4 bg-gradient-to-r from-orange-400 to-yellow-400 text-white rounded-2xl py-3 flex items-center justify-center gap-2 font-medium shadow-lg active:scale-95 transition-transform\"\r\n              >\r\n                <MessageCircle className=\"w-5 h-5\" />\r\n                <span>与{character.name}聊聊</span>\r\n                <span className=\"text-xs opacity-80 ml-1\">💬</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* AI精选关注 - 替换原来的涨幅榜 */}\r\n          <div className=\"px-4 mb-4\">\r\n            <div className=\"bg-white rounded-3xl shadow-sm\">\r\n              <div className=\"p-4 border-b border-gray-100\">\r\n                <h4 className=\"font-semibold flex items-center gap-2\">\r\n                  <Sparkles className=\"w-5 h-5 text-purple-500\" />\r\n                  今日精选关注\r\n                  <span className=\"text-xs text-gray-500 ml-auto\">AI智能推荐</span>\r\n                </h4>\r\n              </div>\r\n              <div className=\"divide-y divide-gray-100\">\r\n                {aiSelectedStocks.map((stock, index) => (\r\n                  <div key={index} className=\"p-4\">\r\n                    <div className=\"flex justify-between items-start mb-3\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"flex items-center gap-2 mb-1\">\r\n                          <p className=\"font-medium text-gray-800\">{stock.name}</p>\r\n                          <p className=\"text-xs text-gray-500\">{stock.code}</p>\r\n                          <span className={`text-xs px-2 py-0.5 rounded-full border ${getSuggestionStyle(stock.suggestion)}`}>\r\n                            {stock.suggestion}\r\n                          </span>\r\n                          {/* 心标标识 */}\r\n                          {stock.isBookmarked && (\r\n                            <span className=\"bg-pink-100 text-pink-600 px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1\">\r\n                              <Heart className=\"w-3 h-3 fill-current\" />\r\n                              已心标\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2 text-sm\">\r\n                          <span className=\"font-semibold text-gray-800\">¥{stock.price}</span>\r\n                          <span className={`font-medium ${getChangeColor(stock.change)}`}>\r\n                            {stock.change > 0 ? '+' : ''}{stock.change} ({stock.changePercent > 0 ? '+' : ''}{stock.changePercent}%)\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <p className=\"text-xs text-gray-500 mb-1\">目标价</p>\r\n                        <p className=\"text-sm font-semibold text-purple-600\">¥{stock.targetPrice}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* AI评语和风险等级 */}\r\n                    <div className=\"bg-gray-50 rounded-xl p-3\">\r\n                      <p className=\"text-xs text-gray-700 leading-relaxed\">{stock.aiComment}</p>\r\n                      <div className=\"flex items-center gap-2 mt-2\">\r\n                        <span className={`text-xs px-2 py-0.5 rounded-full ${getRiskLevelStyle(stock.riskLevel).bg} ${getRiskLevelStyle(stock.riskLevel).color}`}>\r\n                          风险{stock.riskLevel} {getRiskLevelStyle(stock.riskLevel).icon}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 今日操作轨迹 */}\r\n          <div className=\"px-4 mb-4\">\r\n            <div className=\"bg-white rounded-3xl shadow-sm\">\r\n              <div className=\"p-4 border-b border-gray-100\">\r\n                <h4 className=\"font-semibold flex items-center gap-2\">\r\n                  <Calendar className=\"w-5 h-5 text-blue-500\" />\r\n                  今日操作轨迹\r\n                </h4>\r\n              </div>\r\n              <div className=\"divide-y divide-gray-100\">\r\n                {todayOperations.map((op, index) => (\r\n                  <div key={index} className=\"p-4\">\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\r\n                          op.type === 'buy' ? 'bg-red-50' : 'bg-green-50'\r\n                        }`}>\r\n                          {op.type === 'buy' ? \r\n                            <ArrowUp className=\"w-4 h-4 text-red-500\" /> : \r\n                            <ArrowDown className=\"w-4 h-4 text-green-500\" />\r\n                          }\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            <p className=\"font-medium text-gray-800\">\r\n                              {op.type === 'buy' ? '买入' : '卖出'} {op.stock}\r\n                            </p>\r\n                            <span className=\"text-xs text-gray-500\">{op.time}</span>\r\n                          </div>\r\n                          <p className=\"text-sm text-gray-600\">{op.reason}</p>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <p className=\"text-sm font-medium text-gray-700\">{op.amount}%</p>\r\n                        <p className=\"text-xs text-gray-500\">仓位</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 明日展望 */}\r\n          <div className=\"px-4 mb-6\">\r\n            <div className=\"bg-gradient-to-br from-green-50 to-emerald-100 rounded-3xl p-5\">\r\n              <h4 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n                <Star className=\"w-5 h-5 text-green-600\" />\r\n                明日展望\r\n              </h4>\r\n              <div className=\"bg-white/60 backdrop-blur rounded-xl p-4\">\r\n                <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n                  \"明天我想像勤劳的小蜜蜂一样，在新能源的花丛中多采些蜜！听说有政策春风要来，我已经准备好小篮子了🧺 \r\n                  <br /><br />\r\n                  科技股今天表现不错，但涨得有点快，像个调皮的孩子，明天我会找机会让它休息一下。还有啊，如果大盘继续调整，我可能会逢低增加一些优质白马股哦~\"\r\n                </p>\r\n              </div>\r\n              \r\n              <div className=\"grid grid-cols-2 gap-3 mt-3\">\r\n                <div className=\"bg-white/40 backdrop-blur rounded-xl p-3 text-center\">\r\n                  <p className=\"text-xs text-gray-600 mb-1\">明日重点</p>\r\n                  <p className=\"text-sm font-bold text-green-600\">新能源 + 白马股</p>\r\n                </div>\r\n                <div className=\"bg-white/40 backdrop-blur rounded-xl p-3 text-center\">\r\n                  <p className=\"text-xs text-gray-600 mb-1\">操作倾向</p>\r\n                  <p className=\"text-sm font-bold text-blue-600\">逢低吸纳</p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 与AI对话按钮 */}\r\n              <button\r\n                onClick={() => handleChatClick('tomorrow-outlook')}\r\n                className=\"w-full mt-4 bg-gradient-to-r from-emerald-400 to-green-400 text-white rounded-2xl py-3 flex items-center justify-center gap-2 font-medium shadow-lg active:scale-95 transition-transform\"\r\n              >\r\n                <MessageCircle className=\"w-5 h-5\" />\r\n                <span>与{character.name}聊聊</span>\r\n                <span className=\"text-xs opacity-80 ml-1\">🌟</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 详细数据弹窗 */}\r\n        {showDetailView && (\r\n          <div className=\"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\" onClick={() => setShowDetailView(false)}>\r\n            <div className=\"bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up max-h-[70vh] overflow-y-auto\" onClick={e => e.stopPropagation()}>\r\n              <h3 className=\"text-lg font-bold mb-4 text-center\">大盘详细数据</h3>\r\n              \r\n              <div className=\"space-y-4\">\r\n                {Object.entries(marketData).map(([key, data]) => (\r\n                  <div key={key} className=\"bg-gray-50 rounded-xl p-4\">\r\n                    <div className=\"flex justify-between items-center mb-2\">\r\n                      <h4 className=\"font-semibold text-gray-800\">{data.name}指数</h4>\r\n                      <span className={`text-sm font-medium ${getChangeColor(data.change)}`}>\r\n                        {data.change > 0 ? '+' : ''}{data.change}%\r\n                      </span>\r\n                    </div>\r\n                    <p className=\"text-2xl font-bold text-gray-800 mb-2\">{data.value}</p>\r\n                    <div className=\"grid grid-cols-2 gap-2 text-sm text-gray-600\">\r\n                      <div>今开: 3342.15</div>\r\n                      <div>昨收: 3346.35</div>\r\n                      <div>最高: 3355.82</div>\r\n                      <div>最低: 3338.91</div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              \r\n              <button\r\n                onClick={() => setShowDetailView(false)}\r\n                className=\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold mt-6\"\r\n              >\r\n                知道了\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* 分析报告弹窗 */}\r\n        {showReport && reportStock && (\r\n        <div className=\"fixed inset-0 bg-black/50 z-50 flex items-end\" onClick={() => setShowReport(false)}>\r\n            <div \r\n            className=\"bg-white rounded-t-3xl w-full max-w-[390px] mx-auto animate-slide-up max-h-[85vh] overflow-hidden flex flex-col\"\r\n            onClick={e => e.stopPropagation()}\r\n            >\r\n            {/* 拖动指示器 */}\r\n            <div className=\"py-2\">\r\n                <div className=\"w-12 h-1 bg-gray-300 rounded-full mx-auto\"></div>\r\n            </div>\r\n            \r\n            {/* 报告内容 */}\r\n            <div className=\"flex-1 overflow-y-auto px-4 pb-4\">\r\n                {/* 报告头部 */}\r\n                <div className=\"mb-4\">\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                    <div>\r\n                    <h3 className=\"text-xl font-bold\">{reportStock.name}</h3>\r\n                    <p className=\"text-sm text-gray-500\">{reportStock.code}</p>\r\n                    </div>\r\n                    <div className=\"text-right\">\r\n                    <p className=\"text-sm text-gray-500\">目标价</p>\r\n                    <p className=\"text-xl font-bold text-purple-600\">¥{reportStock.targetPrice}</p>\r\n                    </div>\r\n                </div>\r\n                \r\n                {/* 评级 */}\r\n                <div className=\"flex items-center gap-2 mb-4\">\r\n                    <span className=\"bg-red-100 text-red-600 px-3 py-1 rounded-full text-sm font-medium\">\r\n                    强烈推荐\r\n                    </span>\r\n                    <span className=\"text-sm text-gray-500\">2025年1月28日</span>\r\n                </div>\r\n                </div>\r\n\r\n                {/* 核心观点 */}\r\n                <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-4 mb-4\">\r\n                <h4 className=\"font-semibold mb-2 flex items-center gap-2\">\r\n                    <Sparkles className=\"w-4 h-4 text-purple-500\" />\r\n                    核心观点\r\n                </h4>\r\n                <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n                    招商银行作为股份制银行龙头，资产质量优异，零售业务护城河深厚。当前估值处于历史低位，股息率超过5%，具备较高的安全边际和投资价值。\r\n                </p>\r\n                </div>\r\n\r\n                {/* 投资亮点 */}\r\n                <div className=\"mb-4\">\r\n                <h4 className=\"font-semibold mb-3\">投资亮点</h4>\r\n                <div className=\"space-y-3\">\r\n                    <div className=\"flex gap-3\">\r\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                        <span className=\"text-sm font-bold text-blue-600\">1</span>\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"font-medium text-gray-800 mb-1\">零售之王地位稳固</p>\r\n                        <p className=\"text-sm text-gray-600\">零售客户数突破1.8亿，私人银行客户AUM超3万亿，高净值客户基础雄厚</p>\r\n                    </div>\r\n                    </div>\r\n                    \r\n                    <div className=\"flex gap-3\">\r\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                        <span className=\"text-sm font-bold text-blue-600\">2</span>\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"font-medium text-gray-800 mb-1\">资产质量行业领先</p>\r\n                        <p className=\"text-sm text-gray-600\">不良率仅0.94%，拨备覆盖率超过450%，风险抵御能力强</p>\r\n                    </div>\r\n                    </div>\r\n                    \r\n                    <div className=\"flex gap-3\">\r\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                        <span className=\"text-sm font-bold text-blue-600\">3</span>\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"font-medium text-gray-800 mb-1\">高股息率防御属性</p>\r\n                        <p className=\"text-sm text-gray-600\">连续10年稳定分红，当前股息率5.2%，适合长期持有</p>\r\n                    </div>\r\n                    </div>\r\n                </div>\r\n                </div>\r\n\r\n                {/* 关键数据 */}\r\n                <div className=\"bg-gray-50 rounded-2xl p-4 mb-4\">\r\n                <h4 className=\"font-semibold mb-3\">关键财务指标</h4>\r\n                <div className=\"grid grid-cols-2 gap-3\">\r\n                    <div className=\"bg-white rounded-xl p-3\">\r\n                    <p className=\"text-xs text-gray-500 mb-1\">市盈率(PE)</p>\r\n                    <p className=\"text-lg font-bold\">4.8x</p>\r\n                    <p className=\"text-xs text-green-600\">低于行业均值</p>\r\n                    </div>\r\n                    <div className=\"bg-white rounded-xl p-3\">\r\n                    <p className=\"text-xs text-gray-500 mb-1\">市净率(PB)</p>\r\n                    <p className=\"text-lg font-bold\">0.65x</p>\r\n                    <p className=\"text-xs text-green-600\">历史低位</p>\r\n                    </div>\r\n                    <div className=\"bg-white rounded-xl p-3\">\r\n                    <p className=\"text-xs text-gray-500 mb-1\">ROE</p>\r\n                    <p className=\"text-lg font-bold\">13.8%</p>\r\n                    <p className=\"text-xs text-blue-600\">行业第一</p>\r\n                    </div>\r\n                    <div className=\"bg-white rounded-xl p-3\">\r\n                    <p className=\"text-xs text-gray-500 mb-1\">净利润增速</p>\r\n                    <p className=\"text-lg font-bold\">+6.2%</p>\r\n                    <p className=\"text-xs text-blue-600\">稳健增长</p>\r\n                    </div>\r\n                </div>\r\n                </div>\r\n\r\n                {/* 技术面分析 */}\r\n                <div className=\"mb-4\">\r\n                <h4 className=\"font-semibold mb-3\">技术面分析</h4>\r\n                <div className=\"bg-blue-50 rounded-xl p-4\">\r\n                    <div className=\"flex items-center justify-between mb-2\">\r\n                    <span className=\"text-sm text-gray-600\">当前价格</span>\r\n                    <span className=\"font-semibold\">¥32.56</span>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between mb-2\">\r\n                    <span className=\"text-sm text-gray-600\">20日均线</span>\r\n                    <span className=\"font-semibold\">¥32.20</span>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between mb-2\">\r\n                    <span className=\"text-sm text-gray-600\">支撑位</span>\r\n                    <span className=\"font-semibold text-green-600\">¥31.50</span>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                    <span className=\"text-sm text-gray-600\">压力位</span>\r\n                    <span className=\"font-semibold text-red-600\">¥33.80</span>\r\n                    </div>\r\n                </div>\r\n                </div>\r\n\r\n                {/* 风险提示 */}\r\n                <div className=\"bg-orange-50 rounded-2xl p-4 mb-4\">\r\n                <h4 className=\"font-semibold mb-2 flex items-center gap-2\">\r\n                    <AlertCircle className=\"w-4 h-4 text-orange-500\" />\r\n                    风险提示\r\n                </h4>\r\n                <ul className=\"text-sm text-gray-700 space-y-1\">\r\n                    <li>• 宏观经济下行可能影响资产质量</li>\r\n                    <li>• 净息差收窄压力持续存在</li>\r\n                    <li>• 房地产风险敞口需持续关注</li>\r\n                </ul>\r\n                </div>\r\n\r\n                {/* AI点评 */}\r\n                <div className=\"bg-gradient-to-br from-yellow-50 to-orange-100 rounded-2xl p-4 mb-4\">\r\n                <div className=\"flex items-start gap-3 mb-2\">\r\n                    <img src={character.avatar} alt=\"\" className=\"w-10 h-10 rounded-full border-2 border-white shadow-md object-cover\" />\r\n                    <div className=\"flex-1\">\r\n                    <p className=\"font-medium text-gray-800\">沈婉的点评</p>\r\n                    <p className=\"text-xs text-gray-500\">稳健投资者视角</p>\r\n                    </div>\r\n                </div>\r\n                <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n                    \"招商银行就像一位稳重的大哥哥，虽然不会给你惊喜的暴涨，但每年稳稳的分红就像定期收租一样让人安心。现在的价格就像在打折的优质资产，我已经悄悄建仓了呢！对于咱们稳健型投资者来说，这是非常好的长期配置标的哦~ 💎\"\r\n                </p>\r\n                </div>\r\n            </div>\r\n\r\n            {/* 底部操作按钮 */}\r\n            <div className=\"border-t border-gray-200 p-4 bg-white\">\r\n                <div className=\"flex gap-3\">\r\n                <button\r\n                    onClick={() => {\r\n                    handleChatClick('stock-analysis');\r\n                    setShowReport(false);\r\n                    }}\r\n                    className=\"flex-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-medium flex items-center justify-center gap-2 shadow-lg active:scale-95 transition-transform\"\r\n                >\r\n                    <MessageCircle className=\"w-5 h-5\" />\r\n                    与{character.name}聊聊\r\n                </button>\r\n                \r\n                <button\r\n                    onClick={() => {\r\n                    handleFollowStock(reportStock.code);\r\n                    setShowReport(false);\r\n                    }}\r\n                    disabled={followedStocks.includes(reportStock.code)}\r\n                    className={`flex-1 py-3 rounded-2xl font-medium flex items-center justify-center gap-2 transition-all ${\r\n                    followedStocks.includes(reportStock.code)\r\n                        ? 'bg-green-100 text-green-600'\r\n                        : 'bg-gray-100 text-gray-700 active:bg-gray-200'\r\n                    }`}\r\n                >\r\n                    {followedStocks.includes(reportStock.code) ? (\r\n                    <>\r\n                        <Star className=\"w-5 h-5 fill-current\" />\r\n                        已关注\r\n                    </>\r\n                    ) : (\r\n                    <>\r\n                        <Star className=\"w-5 h-5\" />\r\n                        重点关注\r\n                    </>\r\n                    )}\r\n                </button>\r\n                </div>\r\n            </div>\r\n            </div>\r\n        </div>\r\n        )}\r\n      </div>\r\n\r\n      <style jsx>{`\r\n        @keyframes scale-up {\r\n          from {\r\n            transform: scale(0.9);\r\n            opacity: 0;\r\n          }\r\n          to {\r\n            transform: scale(1);\r\n            opacity: 1;\r\n          }\r\n        }\r\n        \r\n        @keyframes slide-down {\r\n          from {\r\n            transform: translateX(-50%) translateY(-20px);\r\n            opacity: 0;\r\n          }\r\n          to {\r\n            transform: translateX(-50%) translateY(0);\r\n            opacity: 1;\r\n          }\r\n        }\r\n        \r\n        .animate-scale-up {\r\n          animation: scale-up 0.3s ease-out;\r\n        }\r\n        \r\n        .animate-slide-down {\r\n          animation: slide-down 0.3s ease-out;\r\n        }\r\n        \r\n        @keyframes slide-up {\r\n        from {\r\n            transform: translateY(100%);\r\n        }\r\n        to {\r\n            transform: translateY(0);\r\n        }\r\n        }\r\n\r\n        .animate-slide-up {\r\n        animation: slide-up 0.3s ease-out;\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StarCompassPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,aAAa,EAAEC,WAAW,EAAEC,MAAM,EAAEC,KAAK,QAAQ,cAAc;;AAE3M;AACA,OAAOC,eAAe,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,WAAW,CAAC,GAAG7B,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAAC8B,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACnC,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM+C,SAAS,GAAG;IAChBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE7B,eAAe;IACvB8B,eAAe,EAAE;EACnB,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG;IACjBC,QAAQ,EAAE;MAAEJ,IAAI,EAAE,IAAI;MAAEK,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEC,KAAK,EAAE;IAAiB,CAAC;IAChFC,QAAQ,EAAE;MAAER,IAAI,EAAE,IAAI;MAAEK,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEC,KAAK,EAAE;IAAiB,CAAC;IACjFE,OAAO,EAAE;MAAET,IAAI,EAAE,KAAK;MAAEK,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEC,KAAK,EAAE;IAAiB;EACjF,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAIC,KAAK,IAAK;IACnCb,cAAc,CAACa,KAAK,CAAC;IACrBf,aAAa,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMgB,gBAAgB,GAAG,CACvB;IACEZ,IAAI,EAAE,MAAM;IACZa,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,KAAK;IACZR,MAAM,EAAE,CAAC,IAAI;IACbS,aAAa,EAAE,CAAC,IAAI;IACpBC,WAAW,EAAE,KAAK;IAClBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAE,0BAA0B;IACrCC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE;EAChB,CAAC,EACD;IACErB,IAAI,EAAE,KAAK;IACXa,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,MAAM;IACbR,MAAM,EAAE,IAAI;IACZS,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAE,6BAA6B;IACxCC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACErB,IAAI,EAAE,MAAM;IACZa,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,OAAO;IACdR,MAAM,EAAE,CAAC,IAAI;IACbS,aAAa,EAAE,CAAC,IAAI;IACpBC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAE,uBAAuB;IAClCC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE;EAChB,CAAC,EACD;IACErB,IAAI,EAAE,MAAM;IACZa,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,MAAM;IACbR,MAAM,EAAE,IAAI;IACZS,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAE,uBAAuB;IAClCC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,IAAI,CAAE;EACtB,CAAC,CACF;;EAED;EACA,MAAMC,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,KAAK;IAAEZ,KAAK,EAAE,MAAM;IAAEa,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,eAAe;IAAEC,MAAM,EAAE;EAAG,CAAC,EAClF;IAAEH,IAAI,EAAE,MAAM;IAAEZ,KAAK,EAAE,MAAM;IAAEa,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,WAAW;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC/E;IAAEH,IAAI,EAAE,KAAK;IAAEZ,KAAK,EAAE,MAAM;IAAEa,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,WAAW;IAAEC,MAAM,EAAE;EAAG,CAAC,CAC/E;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B/C,aAAa,CAAC,IAAI,CAAC;IACnBgD,UAAU,CAAC,MAAM;MACfhD,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMiD,cAAc,GAAIvB,MAAM,IAAK;IACjC,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,cAAc;IACrC,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,gBAAgB;IACvC,OAAO,eAAe;EACxB,CAAC;;EAED;EACA,MAAMwB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,SAAS,GAAG,CAAC5B,UAAU,CAACC,QAAQ,CAACE,MAAM,GAAGH,UAAU,CAACK,QAAQ,CAACF,MAAM,GAAGH,UAAU,CAACM,OAAO,CAACH,MAAM,IAAI,CAAC;IAC3G,IAAIyB,SAAS,GAAG,GAAG,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEzB,KAAK,EAAE,cAAc;MAAE0B,EAAE,EAAE;IAAY,CAAC;IACpF,IAAIF,SAAS,GAAG,CAAC,GAAG,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEzB,KAAK,EAAE,iBAAiB;MAAE0B,EAAE,EAAE;IAAe,CAAC;IAC3F,OAAO;MAAED,IAAI,EAAE,MAAM;MAAEzB,KAAK,EAAE,gBAAgB;MAAE0B,EAAE,EAAE;IAAc,CAAC;EACrE,CAAC;EAED,MAAMC,SAAS,GAAGJ,kBAAkB,CAAC,CAAC;;EAEtC;EACA,MAAMK,eAAe,GAAIC,OAAO,IAAK;IACnCC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,OAAO,CAAC;EACxD,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAIC,OAAO,IAAK;IACpClD,eAAe,CAACkD,OAAO,CAAC;IACxBpD,YAAY,CAAC,IAAI,CAAC;IAClBwC,UAAU,CAAC,MAAM;MACfxC,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMqD,iBAAiB,GAAI5B,IAAI,IAAK;IAClC,IAAI,CAACtB,cAAc,CAACmD,QAAQ,CAAC7B,IAAI,CAAC,EAAE;MAClCrB,iBAAiB,CAAC,CAAC,GAAGD,cAAc,EAAEsB,IAAI,CAAC,CAAC;MAC5C0B,gBAAgB,CAAC,6BAA6B,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMI,kBAAkB,GAAI9B,IAAI,IAAK;IACnCnB,kBAAkB,CAAC,CAAC,GAAGD,eAAe,EAAEoB,IAAI,CAAC,CAAC;IAC9C0B,gBAAgB,CAAC,6BAA6B,CAAC;;IAE/C;IACAX,UAAU,CAAC,MAAM;MACflC,kBAAkB,CAACD,eAAe,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKhC,IAAI,CAAC,CAAC;IAC7D,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMiC,kBAAkB,GAAI7B,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,IAAI;QACP,OAAO,uCAAuC;MAChD,KAAK,IAAI;QACP,OAAO,0CAA0C;MACnD,KAAK,IAAI;QACP,OAAO,gDAAgD;MACzD,KAAK,IAAI;QACP,OAAO,6CAA6C;MACtD;QACE,OAAO,0CAA0C;IACrD;EACF,CAAC;;EAED;EACA,MAAM8B,iBAAiB,GAAIC,KAAK,IAAK;IACnC,QAAQA,KAAK;MACX,KAAK,GAAG;QACN,OAAO;UAAEzC,KAAK,EAAE,gBAAgB;UAAE0B,EAAE,EAAE,aAAa;UAAEgB,IAAI,EAAE;QAAM,CAAC;MACpE,KAAK,GAAG;QACN,OAAO;UAAE1C,KAAK,EAAE,iBAAiB;UAAE0B,EAAE,EAAE,cAAc;UAAEgB,IAAI,EAAE;QAAI,CAAC;MACpE,KAAK,GAAG;QACN,OAAO;UAAE1C,KAAK,EAAE,cAAc;UAAE0B,EAAE,EAAE,WAAW;UAAEgB,IAAI,EAAE;QAAK,CAAC;MAC/D;QACE,OAAO;UAAE1C,KAAK,EAAE,eAAe;UAAE0B,EAAE,EAAE,YAAY;UAAEgB,IAAI,EAAE;QAAI,CAAC;IAClE;EACF,CAAC;EAED,oBACE3E,OAAA;IAAK4E,SAAS,EAAC,0CAA0C;IAAAC,QAAA,GAEtDhE,SAAS,iBACRb,OAAA;MAAK4E,SAAS,EAAC,0EAA0E;MAAAC,QAAA,eACvF7E,OAAA;QAAK4E,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAChF7E,OAAA;UAAK8E,GAAG,EAAErD,SAAS,CAACE,MAAO;UAACoD,GAAG,EAAC,EAAE;UAACH,SAAS,EAAC;QAAmC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFnF,OAAA;UAAG4E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAE9D;QAAY;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnF,OAAA;MAAK4E,SAAS,EAAC,iEAAiE;MAAAC,QAAA,gBAE9E7E,OAAA;QAAK4E,SAAS,EAAC,yFAAyF;QAAAC,QAAA,eACtG7E,OAAA;UAAK4E,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC1D7E,OAAA;YAAQ4E,SAAS,EAAC,WAAW;YAAAC,QAAA,eAC3B7E,OAAA,CAACpB,WAAW;cAACgG,SAAS,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACTnF,OAAA;YAAI4E,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC3D7E,OAAA,CAAChB,MAAM;cAAC4F,SAAS,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEhD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnF,OAAA;YACEoF,OAAO,EAAE/B,aAAc;YACvBuB,SAAS,EAAE,aAAavE,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;YAAAwE,QAAA,eAE3D7E,OAAA,CAACb,SAAS;cAACyF,SAAS,EAAC;YAAuB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnF,OAAA;QAAK4E,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAGhD7E,OAAA;UAAK4E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB7E,OAAA;YAAK4E,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBAC3E7E,OAAA;cAAK4E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD7E,OAAA;gBAAI4E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzDnF,OAAA;gBAAK4E,SAAS,EAAE,8CAA8ChB,SAAS,CAACD,EAAE,IAAIC,SAAS,CAAC3B,KAAK,EAAG;gBAAA4C,QAAA,GAAC,gCAC1F,EAACjB,SAAS,CAACF,IAAI;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnF,OAAA;cAAK4E,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCQ,MAAM,CAACC,OAAO,CAACzD,UAAU,CAAC,CAAC0D,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,IAAI,CAAC,kBAC1CzF,OAAA;gBAAe4E,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBAC7E7E,OAAA;kBAAG4E,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEY,IAAI,CAAC/D;gBAAI;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzDnF,OAAA;kBAAG4E,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAEY,IAAI,CAAC1D;gBAAK;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/DnF,OAAA;kBAAG4E,SAAS,EAAE,uBAAuBrB,cAAc,CAACkC,IAAI,CAACzD,MAAM,CAAC,EAAG;kBAAA6C,QAAA,GAChEY,IAAI,CAACzD,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEyD,IAAI,CAACzD,MAAM,EAAC,GAC3C;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA,GALIK,GAAG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnF,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAM1E,iBAAiB,CAAC,IAAI,CAAE;cACvCkE,SAAS,EAAC,mJAAmJ;cAAAC,QAAA,gBAE7J7E,OAAA,CAACP,GAAG;gBAACmF,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnF,OAAA;UAAK4E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB7E,OAAA;YAAK4E,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBAC3E7E,OAAA;cAAI4E,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC5D7E,OAAA,CAACf,SAAS;gBAAC2F,SAAS,EAAC;cAAyB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnF,OAAA;cAAK4E,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC7E,OAAA;gBAAK4E,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvD7E,OAAA;kBAAG4E,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClDnF,OAAA;kBAAG4E,SAAS,EAAE,qBAAqBrB,cAAc,CAAChD,WAAW,CAAC,EAAG;kBAAAsE,QAAA,GAC9DtE,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,MAAC,EAACA,WAAW;gBAAA;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNnF,OAAA;gBAAK4E,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvD7E,OAAA;kBAAG4E,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjDnF,OAAA;kBAAG4E,SAAS,EAAE,qBAAqBrB,cAAc,CAAC/C,UAAU,CAAC,EAAG;kBAAAqE,QAAA,GAAC,GAC9D,EAACrE,UAAU,EAAC,GACf;gBAAA;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnF,OAAA;UAAK4E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB7E,OAAA;YAAK4E,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAC7E7E,OAAA;cAAK4E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C7E,OAAA;gBAAK8E,GAAG,EAAErD,SAAS,CAACE,MAAO;gBAACoD,GAAG,EAAC,EAAE;gBAACH,SAAS,EAAC;cAAqE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrHnF,OAAA;gBAAK4E,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB7E,OAAA;kBAAG4E,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GAAEpD,SAAS,CAACC,IAAI,EAAC,gCAAK;gBAAA;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEnF,OAAA;kBAAG4E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE,IAAIa,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNnF,OAAA,CAACjB,QAAQ;gBAAC6F,SAAS,EAAC;cAAyB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eAENnF,OAAA;cAAK4E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvD7E,OAAA;gBAAG4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,GAAC,8gBAEnD,eAAA7E,OAAA;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAAAnF,OAAA;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,qUAEd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNnF,OAAA;cAAK4E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C7E,OAAA;gBAAK4E,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE7E,OAAA;kBAAG4E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7CnF,OAAA;kBAAG4E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNnF,OAAA;gBAAK4E,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE7E,OAAA;kBAAG4E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7CnF,OAAA;kBAAG4E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNnF,OAAA;gBAAK4E,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE7E,OAAA;kBAAG4E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7CnF,OAAA;kBAAG4E,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnF,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC,iBAAiB,CAAE;cAClDe,SAAS,EAAC,0LAA0L;cAAAC,QAAA,gBAEpM7E,OAAA,CAACN,aAAa;gBAACkF,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrCnF,OAAA;gBAAA6E,QAAA,GAAM,QAAC,EAACpD,SAAS,CAACC,IAAI,EAAC,cAAE;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChCnF,OAAA;gBAAM4E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnF,OAAA;UAAK4E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB7E,OAAA;YAAK4E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C7E,OAAA;cAAK4E,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C7E,OAAA;gBAAI4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACnD7E,OAAA,CAACjB,QAAQ;kBAAC6F,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wCAEhD,eAAAnF,OAAA;kBAAM4E,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNnF,OAAA;cAAK4E,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtCvC,gBAAgB,CAACiD,GAAG,CAAC,CAAClD,KAAK,EAAEuD,KAAK,kBACjC5F,OAAA;gBAAiB4E,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAC9B7E,OAAA;kBAAK4E,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD7E,OAAA;oBAAK4E,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrB7E,OAAA;sBAAK4E,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3C7E,OAAA;wBAAG4E,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAExC,KAAK,CAACX;sBAAI;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzDnF,OAAA;wBAAG4E,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAExC,KAAK,CAACE;sBAAI;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrDnF,OAAA;wBAAM4E,SAAS,EAAE,2CAA2CJ,kBAAkB,CAACnC,KAAK,CAACM,UAAU,CAAC,EAAG;wBAAAkC,QAAA,EAChGxC,KAAK,CAACM;sBAAU;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,EAEN9C,KAAK,CAACU,YAAY,iBACjB/C,OAAA;wBAAM4E,SAAS,EAAC,gGAAgG;wBAAAC,QAAA,gBAC9G7E,OAAA,CAACH,KAAK;0BAAC+E,SAAS,EAAC;wBAAsB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,sBAE5C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACNnF,OAAA;sBAAK4E,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9C7E,OAAA;wBAAM4E,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GAAC,MAAC,EAACxC,KAAK,CAACG,KAAK;sBAAA;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACnEnF,OAAA;wBAAM4E,SAAS,EAAE,eAAerB,cAAc,CAAClB,KAAK,CAACL,MAAM,CAAC,EAAG;wBAAA6C,QAAA,GAC5DxC,KAAK,CAACL,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEK,KAAK,CAACL,MAAM,EAAC,IAAE,EAACK,KAAK,CAACI,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEJ,KAAK,CAACI,aAAa,EAAC,IACxG;sBAAA;wBAAAuC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnF,OAAA;oBAAK4E,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB7E,OAAA;sBAAG4E,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACjDnF,OAAA;sBAAG4E,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,GAAC,MAAC,EAACxC,KAAK,CAACK,WAAW;oBAAA;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNnF,OAAA;kBAAK4E,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxC7E,OAAA;oBAAG4E,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAExC,KAAK,CAACQ;kBAAS;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1EnF,OAAA;oBAAK4E,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,eAC3C7E,OAAA;sBAAM4E,SAAS,EAAE,oCAAoCH,iBAAiB,CAACpC,KAAK,CAACO,SAAS,CAAC,CAACe,EAAE,IAAIc,iBAAiB,CAACpC,KAAK,CAACO,SAAS,CAAC,CAACX,KAAK,EAAG;sBAAA4C,QAAA,GAAC,cACtI,EAACxC,KAAK,CAACO,SAAS,EAAC,GAAC,EAAC6B,iBAAiB,CAACpC,KAAK,CAACO,SAAS,CAAC,CAAC+B,IAAI;oBAAA;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAtCES,KAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnF,OAAA;UAAK4E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB7E,OAAA;YAAK4E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C7E,OAAA;cAAK4E,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C7E,OAAA;gBAAI4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACnD7E,OAAA,CAACV,QAAQ;kBAACsF,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wCAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNnF,OAAA;cAAK4E,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtC7B,eAAe,CAACuC,GAAG,CAAC,CAACM,EAAE,EAAED,KAAK,kBAC7B5F,OAAA;gBAAiB4E,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAC9B7E,OAAA;kBAAK4E,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C7E,OAAA;oBAAK4E,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC7E,OAAA;sBAAK4E,SAAS,EAAE,yDACdiB,EAAE,CAAC5C,IAAI,KAAK,KAAK,GAAG,WAAW,GAAG,aAAa,EAC9C;sBAAA4B,QAAA,EACAgB,EAAE,CAAC5C,IAAI,KAAK,KAAK,gBAChBjD,OAAA,CAACZ,OAAO;wBAACwF,SAAS,EAAC;sBAAsB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAC5CnF,OAAA,CAACX,SAAS;wBAACuF,SAAS,EAAC;sBAAwB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/C,CAAC,eACNnF,OAAA;sBAAK4E,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACrB7E,OAAA;wBAAK4E,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,gBAC3C7E,OAAA;0BAAG4E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,GACrCgB,EAAE,CAAC5C,IAAI,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI,EAAC,GAAC,EAAC4C,EAAE,CAACxD,KAAK;wBAAA;0BAAA2C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,eACJnF,OAAA;0BAAM4E,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAEgB,EAAE,CAAC3C;wBAAI;0BAAA8B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACNnF,OAAA;wBAAG4E,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEgB,EAAE,CAAC1C;sBAAM;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnF,OAAA;oBAAK4E,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB7E,OAAA;sBAAG4E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAAEgB,EAAE,CAACzC,MAAM,EAAC,GAAC;oBAAA;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACjEnF,OAAA;sBAAG4E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAzBES,KAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnF,OAAA;UAAK4E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB7E,OAAA;YAAK4E,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAC7E7E,OAAA;cAAI4E,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACxD7E,OAAA,CAACd,IAAI;gBAAC0F,SAAS,EAAC;cAAwB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnF,OAAA;cAAK4E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvD7E,OAAA;gBAAG4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,GAAC,0SAEnD,eAAA7E,OAAA;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAAAnF,OAAA;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,+ZAEd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENnF,OAAA;cAAK4E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C7E,OAAA;gBAAK4E,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE7E,OAAA;kBAAG4E,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClDnF,OAAA;kBAAG4E,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNnF,OAAA;gBAAK4E,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE7E,OAAA;kBAAG4E,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClDnF,OAAA;kBAAG4E,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnF,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC,kBAAkB,CAAE;cACnDe,SAAS,EAAC,0LAA0L;cAAAC,QAAA,gBAEpM7E,OAAA,CAACN,aAAa;gBAACkF,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrCnF,OAAA;gBAAA6E,QAAA,GAAM,QAAC,EAACpD,SAAS,CAACC,IAAI,EAAC,cAAE;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChCnF,OAAA;gBAAM4E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL1E,cAAc,iBACbT,OAAA;QAAK4E,SAAS,EAAC,qEAAqE;QAACQ,OAAO,EAAEA,CAAA,KAAM1E,iBAAiB,CAAC,KAAK,CAAE;QAAAmE,QAAA,eAC3H7E,OAAA;UAAK4E,SAAS,EAAC,wFAAwF;UAACQ,OAAO,EAAEU,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAE;UAAAlB,QAAA,gBACxI7E,OAAA;YAAI4E,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE9DnF,OAAA;YAAK4E,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBQ,MAAM,CAACC,OAAO,CAACzD,UAAU,CAAC,CAAC0D,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,IAAI,CAAC,kBAC1CzF,OAAA;cAAe4E,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBAClD7E,OAAA;gBAAK4E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD7E,OAAA;kBAAI4E,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GAAEY,IAAI,CAAC/D,IAAI,EAAC,cAAE;gBAAA;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9DnF,OAAA;kBAAM4E,SAAS,EAAE,uBAAuBrB,cAAc,CAACkC,IAAI,CAACzD,MAAM,CAAC,EAAG;kBAAA6C,QAAA,GACnEY,IAAI,CAACzD,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEyD,IAAI,CAACzD,MAAM,EAAC,GAC3C;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNnF,OAAA;gBAAG4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEY,IAAI,CAAC1D;cAAK;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEnF,OAAA;gBAAK4E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D7E,OAAA;kBAAA6E,QAAA,EAAK;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtBnF,OAAA;kBAAA6E,QAAA,EAAK;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtBnF,OAAA;kBAAA6E,QAAA,EAAK;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtBnF,OAAA;kBAAA6E,QAAA,EAAK;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA,GAbEK,GAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcR,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnF,OAAA;YACEoF,OAAO,EAAEA,CAAA,KAAM1E,iBAAiB,CAAC,KAAK,CAAE;YACxCkE,SAAS,EAAC,oGAAoG;YAAAC,QAAA,EAC/G;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA9D,UAAU,IAAIE,WAAW,iBAC1BvB,OAAA;QAAK4E,SAAS,EAAC,+CAA+C;QAACQ,OAAO,EAAEA,CAAA,KAAM9D,aAAa,CAAC,KAAK,CAAE;QAAAuD,QAAA,eAC/F7E,OAAA;UACA4E,SAAS,EAAC,iHAAiH;UAC3HQ,OAAO,EAAEU,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAE;UAAAlB,QAAA,gBAGlC7E,OAAA;YAAK4E,SAAS,EAAC,MAAM;YAAAC,QAAA,eACjB7E,OAAA;cAAK4E,SAAS,EAAC;YAA2C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eAGNnF,OAAA;YAAK4E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAE7C7E,OAAA;cAAK4E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACrB7E,OAAA;gBAAK4E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACnD7E,OAAA;kBAAA6E,QAAA,gBACA7E,OAAA;oBAAI4E,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAEtD,WAAW,CAACG;kBAAI;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzDnF,OAAA;oBAAG4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEtD,WAAW,CAACgB;kBAAI;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNnF,OAAA;kBAAK4E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAC3B7E,OAAA;oBAAG4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5CnF,OAAA;oBAAG4E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAAC,MAAC,EAACtD,WAAW,CAACmB,WAAW;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNnF,OAAA;gBAAK4E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBACzC7E,OAAA;kBAAM4E,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,EAAC;gBAErF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPnF,OAAA;kBAAM4E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNnF,OAAA;cAAK4E,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBACjF7E,OAAA;gBAAI4E,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACtD7E,OAAA,CAACjB,QAAQ;kBAAC6F,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnF,OAAA;gBAAG4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAErD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNnF,OAAA;cAAK4E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACrB7E,OAAA;gBAAI4E,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5CnF,OAAA;gBAAK4E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtB7E,OAAA;kBAAK4E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAC3B7E,OAAA;oBAAK4E,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,eAC5F7E,OAAA;sBAAM4E,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACNnF,OAAA;oBAAA6E,QAAA,gBACI7E,OAAA;sBAAG4E,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DnF,OAAA;sBAAG4E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAmC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENnF,OAAA;kBAAK4E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAC3B7E,OAAA;oBAAK4E,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,eAC5F7E,OAAA;sBAAM4E,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACNnF,OAAA;oBAAA6E,QAAA,gBACI7E,OAAA;sBAAG4E,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DnF,OAAA;sBAAG4E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENnF,OAAA;kBAAK4E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAC3B7E,OAAA;oBAAK4E,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,eAC5F7E,OAAA;sBAAM4E,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACNnF,OAAA;oBAAA6E,QAAA,gBACI7E,OAAA;sBAAG4E,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DnF,OAAA;sBAAG4E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA0B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNnF,OAAA;cAAK4E,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAChD7E,OAAA;gBAAI4E,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CnF,OAAA;gBAAK4E,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACnC7E,OAAA;kBAAK4E,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACxC7E,OAAA;oBAAG4E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACrDnF,OAAA;oBAAG4E,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzCnF,OAAA;oBAAG4E,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNnF,OAAA;kBAAK4E,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACxC7E,OAAA;oBAAG4E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACrDnF,OAAA;oBAAG4E,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1CnF,OAAA;oBAAG4E,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNnF,OAAA;kBAAK4E,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACxC7E,OAAA;oBAAG4E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACjDnF,OAAA;oBAAG4E,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1CnF,OAAA;oBAAG4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNnF,OAAA;kBAAK4E,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACxC7E,OAAA;oBAAG4E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACnDnF,OAAA;oBAAG4E,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1CnF,OAAA;oBAAG4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNnF,OAAA;cAAK4E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACrB7E,OAAA;gBAAI4E,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7CnF,OAAA;gBAAK4E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACtC7E,OAAA;kBAAK4E,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACvD7E,OAAA;oBAAM4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnDnF,OAAA;oBAAM4E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNnF,OAAA;kBAAK4E,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACvD7E,OAAA;oBAAM4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpDnF,OAAA;oBAAM4E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNnF,OAAA;kBAAK4E,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACvD7E,OAAA;oBAAM4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDnF,OAAA;oBAAM4E,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACNnF,OAAA;kBAAK4E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAClD7E,OAAA;oBAAM4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDnF,OAAA;oBAAM4E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNnF,OAAA;cAAK4E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAClD7E,OAAA;gBAAI4E,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACtD7E,OAAA,CAACL,WAAW;kBAACiF,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnF,OAAA;gBAAI4E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC3C7E,OAAA;kBAAA6E,QAAA,EAAI;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzBnF,OAAA;kBAAA6E,QAAA,EAAI;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtBnF,OAAA;kBAAA6E,QAAA,EAAI;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGNnF,OAAA;cAAK4E,SAAS,EAAC,qEAAqE;cAAAC,QAAA,gBACpF7E,OAAA;gBAAK4E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBACxC7E,OAAA;kBAAK8E,GAAG,EAAErD,SAAS,CAACE,MAAO;kBAACoD,GAAG,EAAC,EAAE;kBAACH,SAAS,EAAC;gBAAqE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrHnF,OAAA;kBAAK4E,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACvB7E,OAAA;oBAAG4E,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClDnF,OAAA;oBAAG4E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNnF,OAAA;gBAAG4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAErD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNnF,OAAA;YAAK4E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eAClD7E,OAAA;cAAK4E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC3B7E,OAAA;gBACIoF,OAAO,EAAEA,CAAA,KAAM;kBACfvB,eAAe,CAAC,gBAAgB,CAAC;kBACjCvC,aAAa,CAAC,KAAK,CAAC;gBACpB,CAAE;gBACFsD,SAAS,EAAC,mLAAmL;gBAAAC,QAAA,gBAE7L7E,OAAA,CAACN,aAAa;kBAACkF,SAAS,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UACpC,EAAC1D,SAAS,CAACC,IAAI,EAAC,cACrB;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETnF,OAAA;gBACIoF,OAAO,EAAEA,CAAA,KAAM;kBACfjB,iBAAiB,CAAC5C,WAAW,CAACgB,IAAI,CAAC;kBACnCjB,aAAa,CAAC,KAAK,CAAC;gBACpB,CAAE;gBACF0E,QAAQ,EAAE/E,cAAc,CAACmD,QAAQ,CAAC7C,WAAW,CAACgB,IAAI,CAAE;gBACpDqC,SAAS,EAAE,6FACX3D,cAAc,CAACmD,QAAQ,CAAC7C,WAAW,CAACgB,IAAI,CAAC,GACnC,6BAA6B,GAC7B,8CAA8C,EACjD;gBAAAsC,QAAA,EAEF5D,cAAc,CAACmD,QAAQ,CAAC7C,WAAW,CAACgB,IAAI,CAAC,gBAC1CvC,OAAA,CAAAE,SAAA;kBAAA2E,QAAA,gBACI7E,OAAA,CAACd,IAAI;oBAAC0F,SAAS,EAAC;kBAAsB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAE7C;gBAAA,eAAE,CAAC,gBAEHnF,OAAA,CAAAE,SAAA;kBAAA2E,QAAA,gBACI7E,OAAA,CAACd,IAAI;oBAAC0F,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,4BAEhC;gBAAA,eAAE;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnF,OAAA;MAAOiG,GAAG;MAAApB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC/E,EAAA,CA/uBID,eAAe;AAAA+F,EAAA,GAAf/F,eAAe;AAivBrB,eAAeA,eAAe;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}