import React, { useState, useEffect, useRef } from 'react';
import { 
  ChevronLeft, Music, Upload, RefreshCw, <PERSON>rkles, 
  Trash2, X, Check, AlertTriangle, Loader, 
  MessageCircle, Clock, ArrowLeft, Mic, Wand2,
  Music2, RotateCw, PlayCircle, PauseCircle, Copy, Download,
  PenLine, FileMusic, ChevronDown, CheckCircle, FileImage, 
  Headphones, Heart, Radio, Save, Send, Coins, DollarSign, Plus
} from 'lucide-react';

const CreateMusicPage = ({ 
  onBack, 
  onMusicCreated, 
  characterData, 
  existingAssets = [],
  userBalance = { partyCoins: 150, starlightCoins: 50 }, 
  preferredPaymentMethod = 'partyCoins' 
}) => {
  // States for the creation process
  const [step, setStep] = useState('edit'); // 'edit', 'processing', 'complete'
  const [creationMode, setCreationMode] = useState('simple'); // 'simple', 'lyrics'
  const [musicPrompt, setMusicPrompt] = useState('');
  const [lyrics, setLyrics] = useState('');
  const [originalLyrics, setOriginalLyrics] = useState('');
  const [isEnhancingContent, setIsEnhancingContent] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('waiting'); // 'waiting', 'processing', 'error'
  const [processingProgress, setProcessingProgress] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState('约2秒');
  const [resultMusic, setResultMusic] = useState(null);
  const [resultCover, setResultCover] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  
  // Music title states
  const [musicTitle, setMusicTitle] = useState('');
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const titleInputRef = useRef(null);
  
  // Payment method states
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(preferredPaymentMethod);
  const [tempPaymentMethod, setTempPaymentMethod] = useState(selectedPaymentMethod);
  
  // For demo purposes - to simulate processing
  const processingTimer = useRef(null);
  const audioRef = useRef(null);
  
  // Reference to the textarea for focusing
  const promptRef = useRef(null);
  const lyricsRef = useRef(null);
  
  // Payment costs
  const paymentOptions = {
    partyCoins: { id: 'partyCoins', name: '派对币', icon: <Coins size={16} className="text-yellow-500" />, amount: 80 },
    starlightCoins: { id: 'starlightCoins', name: '星光币', icon: <DollarSign size={16} className="text-blue-500" />, amount: 15 },
    watchVideo: { id: 'watchVideo', name: '观看激励视频', icon: <PlayCircle size={16} className="text-green-500" />, description: '观看30秒广告视频免费创作' }
  };
  
  // Sample inspirations for demo
  const promptInspirations = [
    "一首轻松欢快的流行歌曲，适合春日驾车出游",
    "科技感十足的电子音乐，反映侦探K的未来派气质",
    "带有些许忧伤的钢琴曲，表达侦探工作中的孤独感",
    "充满活力的摇滚曲风，彰显侦探K的冒险精神",
    "神秘感强烈的爵士乐，展现城市夜晚的氛围",
    "温柔动人的民谣，讲述侦探K的成长故事",
    "结合传统与现代元素的融合音乐，展现侦探的多面性",
    "适合推理场景的紧张背景音乐，增强悬疑氛围"
  ];

  const lyricsInspirations = [
    "在霓虹闪烁的城市夜空下\n我追寻着每一个线索的痕迹\n数据流中隐藏着真相\n而我，只是一个寻找答案的侦探",
    "穿越未来的迷雾\n解开过去的谜团\n在虚拟与现实的边界\n我找到了自己的使命",
    "城市是我的棋盘\n每个人都是一枚棋子\n移动，思考，预判\n这场游戏永无止境"
  ];
  
  // Music style options
  const styleOptions = [
    { id: 'pop', name: '流行', description: '主流音乐风格，节奏明快' },
    { id: 'electronic', name: '电子音乐', description: '科技感十足，未来派风格' },
    { id: 'piano', name: '钢琴曲', description: '优雅抒情，情感丰富' },
    { id: 'rock', name: '摇滚', description: '热情奔放，充满力量' },
    { id: 'jazz', name: '爵士', description: '复杂和声，即兴风格' },
    { id: 'classical', name: '古典', description: '正统优雅，永恒经典' },
    { id: 'lofi', name: 'Lo-Fi', description: '放松氛围，低保真质感' },
    { id: 'cinematic', name: '电影配乐', description: '戏剧性强，场景感丰富' },
    { id: 'custom', name: '自定义', description: '输入您自己的风格描述' }
  ];
  
  const [selectedStyle, setSelectedStyle] = useState(styleOptions[0]); // Default to pop style
  const [customStyle, setCustomStyle] = useState('');
  const [showStyleOptions, setShowStyleOptions] = useState(false);

  // Voice options for the character
  const voiceOptions = [
    { id: 'natural', name: '自然人声', description: '真实自然的人声演唱' },
    { id: 'character', name: '角色配音', description: '使用角色的AI声音' },
    { id: 'instrumental', name: '纯音乐', description: '无人声的纯音乐版本' }
  ];

  const [selectedVoice, setSelectedVoice] = useState(voiceOptions[1]); // Default to character voice
  const [showVoiceOptions, setShowVoiceOptions] = useState(false);

  // Generate music title based on content
  const generateMusicTitle = (content) => {
    let generatedTitle = '';
    
    if (content) {
      if (creationMode === 'simple') {
        // For simple mode, use the first part of the prompt
        const firstSentence = content.split(/[.!?。？！]/)[0].trim();
        if (firstSentence.length <= 20) {
          generatedTitle = firstSentence;
        } else {
          generatedTitle = content.substring(0, 20) + '...';
        }
      } else {
        // For lyrics mode, use the first line
        const firstLine = content.split('\n')[0].trim();
        if (firstLine.length <= 20) {
          generatedTitle = firstLine;
        } else {
          generatedTitle = firstLine.substring(0, 20) + '...';
        }
      }
    } else {
      generatedTitle = '我的原创音乐';
    }
    
    setMusicTitle(generatedTitle);
  };

  // Effect to auto resize textarea
  useEffect(() => {
    if (promptRef.current) {
      promptRef.current.style.height = 'auto';
      promptRef.current.style.height = `${promptRef.current.scrollHeight}px`;
    }
    if (lyricsRef.current) {
      lyricsRef.current.style.height = 'auto';
      lyricsRef.current.style.height = `${lyricsRef.current.scrollHeight}px`;
    }
  }, [musicPrompt, lyrics, creationMode]);

  // Handle audio playback
  useEffect(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.play();
      } else {
        audioRef.current.pause();
      }
    }
    
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [isPlaying]);

  // Payment method selection effect
  useEffect(() => {
    if (showPaymentMethods) {
      setTempPaymentMethod(selectedPaymentMethod);
    }
  }, [showPaymentMethods, selectedPaymentMethod]);

  // Simulate processing progress when in processing state
  useEffect(() => {
    if (step === 'processing') {
      let progress = 0;
      setProcessingProgress(0);
      setProcessingStatus('waiting');
      
      // Wait 0.5 second to simulate queueing
      setTimeout(() => {
        setProcessingStatus('processing');
        
        processingTimer.current = setInterval(() => {
          progress += 10; // Faster progression for 2-second completion
          if (progress >= 100) {
            progress = 100;
            clearInterval(processingTimer.current);
            
            // Simulate completion
            setTimeout(() => {
              setStep('complete');
              setResultMusic('/sample-music.mp3'); // In real app, this would be the actual generated music URL
              setResultCover('/api/placeholder/400/400'); // In real app, this would be the generated cover art
              generateMusicTitle(creationMode === 'simple' ? musicPrompt : lyrics);
            }, 200);
          }
          setProcessingProgress(progress);
        }, 200); // Faster interval for 2-second completion
      }, 500);
    }
    
    return () => {
      if (processingTimer.current) {
        clearInterval(processingTimer.current);
      }
    };
  }, [step]);

  // Get appropriate processing status text
  const getProcessingStatusText = () => {
    if (processingStatus === 'waiting') return '排队中';
    if (processingStatus === 'processing') {
      if (processingProgress < 20) return '分析音乐风格';
      if (processingProgress < 40) return '构建音乐结构';
      if (processingProgress < 60) return '生成旋律与和声';
      if (processingProgress < 80) return creationMode === 'lyrics' ? '配合歌词创作' : '优化音乐细节';
      if (processingProgress < 95) return '最终混音中';
      return '即将完成';
    }
    if (processingStatus === 'error') return '处理出错';
    return '处理中';
  };

  // Determine the best default payment method based on user's balance
  const determineDefaultPaymentMethod = () => {
    const partyCoinsNeeded = paymentOptions.partyCoins.amount;
    const starlightCoinsNeeded = paymentOptions.starlightCoins.amount;
    
    // If user has preferred method with sufficient balance, use that
    if (preferredPaymentMethod === 'partyCoins' && userBalance.partyCoins >= partyCoinsNeeded) {
      return 'partyCoins';
    } else if (preferredPaymentMethod === 'starlightCoins' && userBalance.starlightCoins >= starlightCoinsNeeded) {
      return 'starlightCoins';
    }
    
    // Otherwise, choose based on available balance
    if (userBalance.partyCoins >= partyCoinsNeeded) {
      return 'partyCoins';
    } else if (userBalance.starlightCoins >= starlightCoinsNeeded) {
      return 'starlightCoins';
    } else {
      return 'watchVideo';
    }
  };

  // Handler for selecting payment method
  const handleSelectPaymentMethod = (methodId) => {
    setSelectedPaymentMethod(methodId);
    setShowPaymentMethods(false);
  };

  // Handler for watch video payment method
  const handleWatchVideo = () => {
    // This would integrate with ad SDK in a real app
    alert('调用广告SDK，播放30秒激励视频');
    // Simulate successful video watch
    setTimeout(() => {
      handleCreateMusic();
    }, 1500);
  };

  // Check if selected payment method is valid (sufficient balance)
  const isSelectedPaymentValid = () => {
    if (!selectedPaymentMethod) return false;
    
    if (selectedPaymentMethod === 'watchVideo') return true;
    
    const required = paymentOptions[selectedPaymentMethod].amount;
    const available = userBalance[selectedPaymentMethod];
    return available >= required;
  };

  // Get color class based on balance sufficiency
  const getBalanceColorClass = (coinType, required) => {
    const balance = userBalance[coinType] || 0;
    return balance >= required ? "text-green-600" : "text-red-500";
  };

  // Handler for starting music creation
  const handleCreateMusic = () => {
    if (creationMode === 'simple' && !musicPrompt.trim()) {
      alert('请输入音乐描述');
      return;
    }
    if (creationMode === 'lyrics' && !lyrics.trim()) {
      alert('请输入歌词');
      return;
    }
    
    if (!selectedPaymentMethod) {
      setShowPaymentMethods(true);
      return;
    }

    // Check if user has enough coins
    if (selectedPaymentMethod === 'partyCoins' && userBalance.partyCoins < paymentOptions.partyCoins.amount) {
      alert(`派对币余额不足，您当前有 ${userBalance.partyCoins} 派对币，需要 ${paymentOptions.partyCoins.amount} 派对币`);
      return;
    }
    if (selectedPaymentMethod === 'starlightCoins' && userBalance.starlightCoins < paymentOptions.starlightCoins.amount) {
      alert(`星光币余额不足，您当前有 ${userBalance.starlightCoins} 星光币，需要 ${paymentOptions.starlightCoins.amount} 星光币`);
      return;
    }

    // Handle watch video payment method
    if (selectedPaymentMethod === 'watchVideo') {
      handleWatchVideo();
      return;
    }
    
    setStep('processing');
  };

  // Handler for canceling creation
  const handleCancelCreation = () => {
    if (window.confirm('确定要取消当前创建吗？所有进度将丢失。')) {
      if (processingTimer.current) {
        clearInterval(processingTimer.current);
      }
      setStep('edit');
      setProcessingProgress(0);
    }
  };

  // Handler for enhancing the prompt with AI
  const handleEnhancePrompt = async () => {
    if (!musicPrompt.trim()) {
      alert('请先输入一些基础描述');
      return;
    }
    
    setIsEnhancingContent(true);
    
    // Save original to allow reverting
    if (!originalLyrics) {
      setOriginalLyrics(musicPrompt);
    }
    
    // Simulate AI enhancement (this would call the actual API in production)
    setTimeout(() => {
      const baseText = musicPrompt;
      const enhanced = `${baseText}，旋律轻快流畅，带有梦幻般的电子合成器音色，节奏鲜明但不过于强烈，配以柔和的弦乐背景，营造出既现代又略带复古感的氛围，适合反映角色在城市中穿梭的感觉。`;
      setMusicPrompt(enhanced);
      setIsEnhancingContent(false);
    }, 1500);
  };

  // Handler for enhancing the lyrics with AI
  const handleEnhanceLyrics = async () => {
    if (!lyrics.trim()) {
      alert('请先输入一些基础歌词');
      return;
    }
    
    setIsEnhancingContent(true);
    
    // Save original to allow reverting
    if (!originalLyrics) {
      setOriginalLyrics(lyrics);
    }
    
    // Simulate AI enhancement (this would call the actual API in production)
    setTimeout(() => {
      const baseText = lyrics;
      const enhanced = baseText.split('\n').map(line => 
        line.trim() ? `${line}` : line
      ).join('\n') + '\n\n这城市的谜题在霓虹灯下闪烁\n我的双眼追寻每一条线索\n数据与现实交织的世界\n只有我能看透这虚幻的真相';
      
      setLyrics(enhanced);
      setIsEnhancingContent(false);
    }, 1500);
  };

  // Handler for generating AI lyrics
  const handleGenerateLyrics = async () => {
    setIsEnhancingContent(true);
    
    // Save original if any
    if (lyrics.trim() && !originalLyrics) {
      setOriginalLyrics(lyrics);
    }
    
    // Simulate AI lyrics generation (this would call the actual API in production)
    setTimeout(() => {
      // Sample lyrics based on selected style
      let generatedLyrics;
      
      if (selectedStyle.id === 'electronic' || selectedStyle.id === 'cyberpunk') {
        generatedLyrics = "数字雨落下的城市\n我踏过霓虹的海洋\n解码每一次心跳\n在虚拟与现实之间穿行\n\n我的双眼看透表象\n破译隐藏的密码\n在数据的迷宫中\n寻找真相的碎片\n\n（副歌）\n我是赛博侦探\n穿梭在光与影之间\n解开谜题是我的使命\n在这未来的迷城";
      } else if (selectedStyle.id === 'piano' || selectedStyle.id === 'classical') {
        generatedLyrics = "寂静的夜 思绪飞扬\n案件的线索在脑海浮现\n城市的灯火是我的伴侣\n孤独的旅程没有终点\n\n翻开过去的章节\n记忆中的画面依然鲜明\n我用理性的眼光\n照亮真相的方向\n\n（副歌）\n在时光的长河中寻找答案\n我的使命是守护这座城市\n即使无人知晓我的付出\n我依然前行不止";
      } else {
        generatedLyrics = "穿行在城市的迷宫\n我是谜题的解读者\n每一个案件背后\n都有不为人知的故事\n\n我的世界充满线索\n拼图等待被完成\n在混沌中寻找秩序\n是我生命的意义\n\n（副歌）\n追寻真相的道路\n没有捷径可走\n但我从不退缩\n因为正义终将到来";
      }
      
      setLyrics(generatedLyrics);
      setIsEnhancingContent(false);
    }, 2000);
  };

  // Handler for getting prompt inspiration
  const handleGetPromptInspiration = () => {
    // Randomly select an inspiration
    const randomInspiration = promptInspirations[Math.floor(Math.random() * promptInspirations.length)];
    setMusicPrompt(randomInspiration);
    
    // Focus the textarea after setting inspiration
    if (promptRef.current) {
      promptRef.current.focus();
    }
  };

  // Handler for getting lyrics inspiration
  const handleGetLyricsInspiration = () => {
    // Randomly select an inspiration
    const randomInspiration = lyricsInspirations[Math.floor(Math.random() * lyricsInspirations.length)];
    setLyrics(randomInspiration);
    
    // Focus the textarea after setting inspiration
    if (lyricsRef.current) {
      lyricsRef.current.focus();
    }
  };

  // Handler for clearing the prompt
  const handleClearPrompt = () => {
    if (musicPrompt.trim() && !window.confirm('确定要清空当前描述吗？')) {
      return;
    }
    setMusicPrompt('');
    
    // Focus the textarea after clearing
    if (promptRef.current) {
      promptRef.current.focus();
    }
  };

  // Handler for clearing the lyrics
  const handleClearLyrics = () => {
    if (lyrics.trim() && !window.confirm('确定要清空当前歌词吗？')) {
      return;
    }
    setLyrics('');
    setOriginalLyrics('');
    
    // Focus the textarea after clearing
    if (lyricsRef.current) {
      lyricsRef.current.focus();
    }
  };

  // Handler for undoing AI enhancement
  const handleUndoEnhancement = () => {
    if (originalLyrics) {
      if (creationMode === 'simple') {
        setMusicPrompt(originalLyrics);
      } else {
        setLyrics(originalLyrics);
      }
      setOriginalLyrics('');
    }
  };

  // Handler for music creation completion
  const handleCompleteCreation = () => {
    // In a real app, this would add the music to the asset library
    if (onMusicCreated) {
      onMusicCreated({
        id: Date.now(),
        type: 'music',
        url: resultMusic,
        coverUrl: resultCover,
        title: musicTitle,
        createdAt: '刚刚',
        duration: '01:45',
        usedCount: 0,
        likes: 0,
        style: selectedStyle.id === 'custom' ? customStyle : selectedStyle.name,
      });
    }
    
    // Return to previous screen
    if (onBack) {
      onBack();
    }
  };

  // Handler for toggling audio playback
  const handleTogglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  // Filter music assets from all assets
  const musicAssets = existingAssets.filter(asset => asset.type === 'music');

  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white sticky top-0 z-10">
        <div className="flex items-center">
          <button 
            onClick={onBack} 
            className="p-2 mr-2 rounded-full hover:bg-white hover:bg-opacity-20"
          >
            <ChevronLeft size={20} />
          </button>
          <h1 className="text-lg font-bold">创建音乐</h1>
        </div>
        
        {step === 'complete' && (
          <button 
            onClick={handleCompleteCreation}
            className="px-4 py-1.5 rounded-lg bg-green-500 text-white text-sm font-medium"
          >
            保存到素材库
          </button>
        )}
      </div>
      
      {/* Main content */}
      <div className="flex-1 p-4 overflow-auto">
        {step === 'edit' && (
          <>
            {/* Character info reminder */}
            {characterData && (
              <div className="mb-4 p-3 bg-purple-50 rounded-lg border border-purple-100 flex items-center">
                <div className="w-10 h-10 bg-purple-200 rounded-full overflow-hidden flex-shrink-0 mr-3">
                  {characterData.profileImage && (
                    <img 
                      src={characterData.profileImage} 
                      alt={characterData.name}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-purple-800">正在为 {characterData.name} 创建音乐</h3>
                  <p className="text-xs text-purple-600">创建的音乐将添加到角色素材库</p>
                </div>
              </div>
            )}
            
            {/* Creation mode selection */}
            <div className="mb-4">
              <div className="bg-white rounded-lg overflow-hidden border border-gray-200">
                <div className="flex border-b border-gray-100">
                  <button
                    onClick={() => setCreationMode('simple')}
                    className={`flex-1 py-3 text-sm font-medium ${
                      creationMode === 'simple' 
                        ? 'text-purple-600 bg-purple-50 border-b-2 border-purple-600' 
                        : 'text-gray-600'
                    }`}
                  >
                    简单模式
                  </button>
                  <button
                    onClick={() => setCreationMode('lyrics')}
                    className={`flex-1 py-3 text-sm font-medium ${
                      creationMode === 'lyrics' 
                        ? 'text-purple-600 bg-purple-50 border-b-2 border-purple-600' 
                        : 'text-gray-600'
                    }`}
                  >
                    歌词定制
                  </button>
                </div>
                
                <div className="p-3 bg-purple-50">
                  <p className="text-xs text-purple-800">
                    {creationMode === 'simple' 
                      ? '简单模式：只需描述你想要的音乐风格和氛围，AI将为你创作一首完整歌曲。' 
                      : '歌词定制：输入你的歌词，AI将根据歌词内容创作一首原创歌曲。'}
                  </p>
                </div>
              </div>
            </div>
            
            {/* Style selection */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Music size={16} className="mr-1 text-purple-600" />
                  音乐风格
                </label>
              </div>
              
              <div className="relative">
                <button
                  onClick={() => setShowStyleOptions(!showStyleOptions)}
                  className="w-full p-3 bg-white rounded-lg border border-gray-200 flex justify-between items-center"
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-2">
                      <Radio size={16} className="text-purple-600" />
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium">
                        {selectedStyle.id === 'custom' 
                          ? '自定义风格' 
                          : selectedStyle.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {selectedStyle.id === 'custom' && customStyle 
                          ? customStyle 
                          : selectedStyle.description}
                      </div>
                    </div>
                  </div>
                  <ChevronDown size={18} className="text-gray-400" />
                </button>
                
                {showStyleOptions && (
                  <div className="absolute z-20 top-full left-0 right-0 mt-1 bg-white rounded-lg border border-gray-200 shadow-lg max-h-64 overflow-y-auto">
                    {styleOptions.map((style) => (
                      <button
                        key={style.id}
                        onClick={() => {
                          setSelectedStyle(style);
                          setShowStyleOptions(false);
                        }}
                        className={`w-full p-3 flex items-center border-b border-gray-100 last:border-b-0 ${
                          selectedStyle.id === style.id ? 'bg-purple-50' : 'hover:bg-gray-50'
                        }`}
                      >
                        <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-2">
                          <Radio size={16} className="text-purple-600" />
                        </div>
                        <div className="text-left flex-1">
                          <div className="text-sm font-medium">{style.name}</div>
                          <div className="text-xs text-gray-500">{style.description}</div>
                        </div>
                        {selectedStyle.id === style.id && (
                          <CheckCircle size={18} className="text-purple-600 ml-2" />
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Custom style input */}
              {selectedStyle.id === 'custom' && (
                <div className="mt-2">
                  <input
                    type="text"
                    value={customStyle}
                    onChange={(e) => setCustomStyle(e.target.value)}
                    placeholder="输入自定义风格描述，例如：融合爵士和电子元素的轻松旋律..."
                    className="w-full p-3 text-sm border border-gray-200 rounded-lg focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              )}
            </div>
            
            {/* Voice selection */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Mic size={16} className="mr-1 text-purple-600" />
                  演唱声音
                </label>
              </div>
              
              <div className="relative">
                <button
                  onClick={() => setShowVoiceOptions(!showVoiceOptions)}
                  className="w-full p-3 bg-white rounded-lg border border-gray-200 flex justify-between items-center"
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-2">
                      <Headphones size={16} className="text-purple-600" />
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium">{selectedVoice.name}</div>
                      <div className="text-xs text-gray-500">{selectedVoice.description}</div>
                    </div>
                  </div>
                  <ChevronDown size={18} className="text-gray-400" />
                </button>
                
                {showVoiceOptions && (
                  <div className="absolute z-20 top-full left-0 right-0 mt-1 bg-white rounded-lg border border-gray-200 shadow-lg">
                    {voiceOptions.map((voice) => (
                      <button
                        key={voice.id}
                        onClick={() => {
                          setSelectedVoice(voice);
                          setShowVoiceOptions(false);
                        }}
                        className={`w-full p-3 flex items-center border-b border-gray-100 last:border-b-0 ${
                          selectedVoice.id === voice.id ? 'bg-purple-50' : 'hover:bg-gray-50'
                        }`}
                      >
                        <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-2">
                          <Headphones size={16} className="text-purple-600" />
                        </div>
                        <div className="text-left flex-1">
                          <div className="text-sm font-medium">{voice.name}</div>
                          <div className="text-xs text-gray-500">{voice.description}</div>
                        </div>
                        {selectedVoice.id === voice.id && (
                          <CheckCircle size={18} className="text-purple-600 ml-2" />
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            {/* Content input - different based on mode */}
            {creationMode === 'simple' ? (
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label htmlFor="musicPrompt" className="text-sm font-medium text-gray-700 flex items-center">
                    <PenLine size={16} className="mr-1 text-purple-600" />
                    音乐描述
                  </label>
                  <button 
                    onClick={handleGetPromptInspiration}
                    className="text-xs px-2 py-1 bg-amber-100 text-amber-700 rounded-full flex items-center"
                  >
                    <Sparkles size={14} className="mr-1" />
                    给我灵感
                  </button>
                </div>
                
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <textarea
                    ref={promptRef}
                    id="musicPrompt"
                    value={musicPrompt}
                    onChange={(e) => setMusicPrompt(e.target.value)}
                    placeholder="描述你想要的音乐风格、情绪、节奏和氛围，例如：一首充满科技感的电子音乐，节奏明快，带有未来感的合成器音色..."
                    className="w-full p-3 min-h-[120px] text-sm border-none focus:ring-0 focus:outline-none resize-none"
                    disabled={isEnhancingContent}
                  ></textarea>
                  
                  <div className="flex border-t border-gray-100">
                    <button
                      onClick={handleEnhancePrompt}
                      disabled={!musicPrompt.trim() || isEnhancingContent}
                      className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                        !musicPrompt.trim() || isEnhancingContent ? 'text-gray-400' : 'text-purple-600'
                      }`}
                    >
                      {isEnhancingContent ? <Loader size={14} className="mr-1 animate-spin" /> : <Wand2 size={14} className="mr-1" />}
                      润色
                    </button>
                    <div className="w-px h-8 bg-gray-100 my-auto"></div>
                    <button
                      onClick={handleClearPrompt}
                      disabled={!musicPrompt.trim() || isEnhancingContent}
                      className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                        !musicPrompt.trim() || isEnhancingContent ? 'text-gray-400' : 'text-red-600'
                      }`}
                    >
                      <Trash2 size={14} className="mr-1" />
                      清空
                    </button>
                  </div>
                </div>
                
                {originalLyrics && creationMode === 'simple' && (
                  <div className="mt-2 flex justify-end">
                    <button
                      onClick={handleUndoEnhancement}
                      className="text-xs text-gray-600 flex items-center"
                    >
                      <ArrowLeft size={12} className="mr-1" />
                      恢复原始描述
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label htmlFor="lyrics" className="text-sm font-medium text-gray-700 flex items-center">
                    <PenLine size={16} className="mr-1 text-purple-600" />
                    歌词内容
                  </label>
                  <div className="flex space-x-1">
                    <button 
                      onClick={handleGetLyricsInspiration}
                      className="text-xs px-2 py-1 bg-amber-100 text-amber-700 rounded-full flex items-center"
                    >
                      <Sparkles size={14} className="mr-1" />
                      给我灵感
                    </button>
                  </div>
                </div>
                
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <textarea
                    ref={lyricsRef}
                    id="lyrics"
                    value={lyrics}
                    onChange={(e) => setLyrics(e.target.value)}
                    placeholder="输入歌词内容，每行一句，空行表示分段或副歌部分..."
                    className="w-full p-3 min-h-[160px] text-sm border-none focus:ring-0 focus:outline-none resize-none"
                    disabled={isEnhancingContent}
                  ></textarea>
                  
                  <div className="flex border-t border-gray-100">
                    <button
                      onClick={handleEnhanceLyrics}
                      disabled={!lyrics.trim() || isEnhancingContent}
                      className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                        !lyrics.trim() || isEnhancingContent ? 'text-gray-400' : 'text-purple-600'
                      }`}
                    >
                      {isEnhancingContent ? <Loader size={14} className="mr-1 animate-spin" /> : <Wand2 size={14} className="mr-1" />}
                      润色
                    </button>
                    <div className="w-px h-8 bg-gray-100 my-auto"></div>
                    <button
                      onClick={handleClearLyrics}
                      disabled={!lyrics.trim() || isEnhancingContent}
                      className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                        !lyrics.trim() || isEnhancingContent ? 'text-gray-400' : 'text-red-600'
                      }`}
                    >
                      <Trash2 size={14} className="mr-1" />
                      清空
                    </button>
                  </div>
                </div>
                
                {originalLyrics && creationMode === 'lyrics' && (
                  <div className="mt-2 flex justify-end">
                    <button
                      onClick={handleUndoEnhancement}
                      className="text-xs text-gray-600 flex items-center"
                    >
                      <ArrowLeft size={12} className="mr-1" />
                      恢复原始歌词
                    </button>
                  </div>
                )}
              </div>
            )}
            
            {/* Helpful tips */}
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
              <h3 className="text-sm font-medium text-blue-800 flex items-center mb-1">
                <Sparkles size={16} className="mr-1 text-blue-500" />
                创作提示
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  {creationMode === 'simple' 
                    ? '详细描述音乐风格、情感和氛围可以获得更好的结果' 
                    : '写出富有情感和节奏感的歌词有助于创作优质歌曲'}
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  选择与角色个性相符的音乐风格能更好地展现角色特点
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  生成过程大约需要2-3秒，您可以稍后查看结果
                </li>
              </ul>
            </div>

            {/* Bottom spacer for fixed button */}
            <div className="h-16"></div>
          </>
        )}
        
        {step === 'processing' && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="w-full max-w-sm mb-8">
              <div className="flex justify-between items-center mb-2">
                <div className="text-sm font-medium text-gray-700 flex items-center">
                  <RotateCw size={16} className={`mr-1 text-blue-600 ${processingStatus === 'processing' ? 'animate-spin' : ''}`} />
                  {getProcessingStatusText()}
                </div>
                <div className="text-xs text-gray-500">
                  {Math.round(processingProgress)}%
                </div>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2 mb-1 overflow-hidden">
                <div 
                  className="h-full bg-blue-600 transition-all duration-300 ease-out"
                  style={{ width: `${processingProgress}%` }}
                ></div>
              </div>
              
              <div className="text-xs text-gray-500 flex justify-between">
                <span>预计等待时间: {estimatedTime}</span>
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-4 shadow-md w-full max-w-sm">
              <div className="aspect-square mb-4 relative">
                <div className="absolute inset-0 rounded-lg overflow-hidden bg-gradient-to-br from-purple-400 via-indigo-500 to-blue-600 loading-waves">
                  {/* Animated background */}
                </div>
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <Music size={40} className="text-white mb-2 animate-bounce" />
                  <div className="bg-white bg-opacity-20 backdrop-blur-sm rounded-full h-8 w-64 flex items-center justify-start px-1 overflow-hidden">
                    <div 
                      className="h-6 bg-white rounded-full transition-all duration-300"
                      style={{ width: `${processingProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-white text-sm mt-3 font-medium text-center">音乐创作中</p>
                  <div className="flex mt-2 space-x-2">
                    <div className="h-1 w-3 bg-white rounded-full animate-music-bar1"></div>
                    <div className="h-1 w-3 bg-white rounded-full animate-music-bar2"></div>
                    <div className="h-1 w-3 bg-white rounded-full animate-music-bar3"></div>
                    <div className="h-1 w-3 bg-white rounded-full animate-music-bar4"></div>
                    <div className="h-1 w-3 bg-white rounded-full animate-music-bar5"></div>
                  </div>
                </div>
              </div>
              
              <div className="bg-blue-50 rounded-lg p-3 text-xs text-blue-800">
                <div className="flex items-center mb-2">
                  <MessageCircle size={14} className="mr-1 text-blue-600" />
                  <span className="font-medium">有用提示</span>
                </div>
                <p>AI正在为您创作原创音乐，稍等片刻即可完成。您可以放心离开此页面继续其他操作，音乐生成完成后我们会通知您。</p>
              </div>
            </div>
          </div>
        )}
        
        {step === 'complete' && (
          <div className="flex flex-col items-center py-4">
            <div className="bg-white rounded-xl p-4 shadow-md w-full mb-4">
              {/* Album cover and player */}
              <div className="rounded-lg overflow-hidden mb-4 relative">
                <img 
                  src={resultCover} 
                  alt="专辑封面"
                  className="w-full aspect-square object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                  <button 
                    onClick={handleTogglePlay}
                    className="w-16 h-16 bg-white bg-opacity-20 backdrop-blur-sm rounded-full flex items-center justify-center border-2 border-white text-white hover:bg-opacity-30 transition-all"
                  >
                    {isPlaying ? <PauseCircle size={36} /> : <PlayCircle size={36} />}
                  </button>
                </div>
                {/* Hidden audio element */}
                <audio 
                  ref={audioRef}
                  src={resultMusic}
                  onEnded={() => setIsPlaying(false)}
                  loop={false}
                />
              </div>
              
              {/* Music title */}
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-700 mb-1">音乐标题</h3>
                <div className="bg-gray-50 rounded-lg p-3 relative">
                  {isEditingTitle ? (
                    <form 
                      onSubmit={(e) => {
                        e.preventDefault();
                        setIsEditingTitle(false);
                      }}
                      className="flex"
                    >
                      <input
                        ref={titleInputRef}
                        type="text"
                        value={musicTitle}
                        onChange={(e) => setMusicTitle(e.target.value)}
                        className="flex-1 text-sm border-none bg-transparent focus:ring-0 focus:outline-none p-0"
                        autoFocus
                      />
                      <button 
                        type="submit" 
                        className="text-purple-600 ml-2 flex-shrink-0"
                      >
                        <Check size={16} />
                      </button>
                    </form>
                  ) : (
                    <>
                      <span className="text-sm font-medium">{musicTitle}</span>
                      <button 
                        onClick={() => {
                          setIsEditingTitle(true);
                          // 渲染后聚焦输入框
                          setTimeout(() => {
                            if (titleInputRef.current) {
                              titleInputRef.current.focus();
                            }
                          }, 0);
                        }} 
                        className="absolute top-2 right-2 text-purple-600 opacity-50 hover:opacity-100"
                      >
                        <PenLine size={14} />
                      </button>
                    </>
                  )}
                </div>
              </div>
              
              <div className="flex justify-between mb-4">
                <button className="flex-1 mr-1 py-2 rounded-lg bg-purple-100 text-purple-700 text-sm font-medium flex items-center justify-center">
                  <Headphones size={16} className="mr-1" />
                  试听全曲
                </button>
                <button className="flex-1 ml-1 py-2 rounded-lg bg-gray-100 text-gray-700 text-sm font-medium flex items-center justify-center">
                  <Download size={16} className="mr-1" />
                  下载
                </button>
              </div>
              
              {/* Content details */}
              <div className="space-y-3 border-t border-gray-100 pt-3">
                {creationMode === 'simple' ? (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-1">音乐描述</h3>
                    <div className="text-xs text-gray-600 bg-gray-50 rounded-lg p-3">
                      {musicPrompt}
                      <button className="ml-2 text-purple-600 inline-flex items-center">
                        <Copy size={12} />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-1">歌词内容</h3>
                    <div className="text-xs text-gray-600 bg-gray-50 rounded-lg p-3 whitespace-pre-line">
                      {lyrics}
                      <button className="ml-2 text-purple-600 inline-flex items-center">
                        <Copy size={12} />
                      </button>
                    </div>
                  </div>
                )}
                
                <div className="flex justify-between text-xs text-gray-500">
                  <span>音乐风格: {selectedStyle.id === 'custom' ? customStyle : selectedStyle.name}</span>
                  <span>演唱声音: {selectedVoice.name}</span>
                </div>
              </div>
            </div>
            
            <div className="w-full bg-green-50 rounded-lg p-4 border border-green-100 mb-4">
              <div className="flex items-start">
                <CheckCircle size={18} className="text-green-600 mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-green-800 mb-1">音乐已成功生成</h3>
                  <p className="text-xs text-green-700">点击「用它发动态」将音乐用于发布动态，或保存到素材库中备用。</p>
                </div>
              </div>
            </div>
            
            <div className="w-full">
              <div className="flex justify-between mb-3">
                <button 
                  onClick={() => setStep('edit')}
                  className="flex-1 mr-1 py-3 rounded-lg border border-purple-500 text-purple-600 text-sm font-medium flex items-center justify-center"
                >
                  <RefreshCw size={16} className="mr-1" />
                  修改重新生成
                </button>
                <button 
                  onClick={handleCompleteCreation}
                  className="flex-1 ml-1 py-3 rounded-lg bg-green-500 text-white text-sm font-medium flex items-center justify-center"
                >
                  <Send size={16} className="mr-1" />
                  用它发动态
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Fixed bottom buttons */}
      {step === 'edit' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          {/* Payment Method Selection */}
          <div className="mb-3 p-2 bg-gray-50 rounded-lg flex items-center justify-between">
            <div className="flex items-center">
              {selectedPaymentMethod && paymentOptions[selectedPaymentMethod].icon}
              <span className="ml-2 text-sm font-medium">
                {selectedPaymentMethod ? paymentOptions[selectedPaymentMethod].name : '选择支付方式'}
              </span>
              {selectedPaymentMethod && (selectedPaymentMethod === 'partyCoins' || selectedPaymentMethod === 'starlightCoins') && (
                <span className="ml-2 text-sm">
                  需消耗 <span className="font-bold">{paymentOptions[selectedPaymentMethod].amount}</span>
                  <span className={`ml-1 text-xs ${getBalanceColorClass(selectedPaymentMethod, paymentOptions[selectedPaymentMethod].amount)}`}>
                    (余额: {userBalance[selectedPaymentMethod]})
                  </span>
                </span>
              )}
              {selectedPaymentMethod && selectedPaymentMethod === 'watchVideo' && (
                <span className="ml-2 text-xs text-green-600">
                  观看30秒视频
                </span>
              )}
            </div>
            <button 
              onClick={() => setShowPaymentMethods(true)}
              className="text-xs text-purple-600 border border-purple-200 rounded-lg px-2 py-1"
            >
              {selectedPaymentMethod ? '更换' : '选择'}
            </button>
          </div>
          
          <button 
            onClick={handleCreateMusic}
            disabled={(creationMode === 'simple' && !musicPrompt.trim()) || (creationMode === 'lyrics' && !lyrics.trim()) || !isSelectedPaymentValid()}
            className={`w-full py-3 rounded-lg text-sm font-medium ${
              ((creationMode === 'simple' && musicPrompt.trim()) || (creationMode === 'lyrics' && lyrics.trim())) && isSelectedPaymentValid()
                ? 'bg-green-500 text-white' 
                : 'bg-gray-300 text-gray-500'
            }`}
          >
            创建音乐
          </button>
        </div>
      )}
      
      {step === 'processing' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          <button 
            onClick={handleCancelCreation}
            className="w-full py-3 rounded-lg bg-red-500 text-white text-sm font-medium"
          >
            取消创建
          </button>
        </div>
      )}
      
      {/* Payment method selection modal */}
      {showPaymentMethods && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center p-4">
          <div className="bg-white rounded-t-xl w-full max-w-md">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-bold">选择支付方式</h2>
              <button 
                onClick={() => setShowPaymentMethods(false)}
                className="text-gray-500 p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4">
              <div className="grid grid-cols-1 gap-3">
                {/* Party Coins Option */}
                <div className={`p-4 rounded-lg text-left border ${
                  tempPaymentMethod === 'partyCoins' 
                    ? 'bg-yellow-50 border-yellow-300' 
                    : 'bg-white border-gray-200'
                }`}>
                  <button 
                    onClick={() => setTempPaymentMethod('partyCoins')}
                    className="w-full flex items-center"
                  >
                    <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                      <Coins size={20} className="text-yellow-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium">派对币</h3>
                        <span className={`text-sm font-medium ${getBalanceColorClass('partyCoins', paymentOptions.partyCoins.amount)}`}>
                          余额: {userBalance.partyCoins}
                        </span>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-gray-500">用派对币支付音乐生成费用</p>
                        <span className="text-xs font-bold text-yellow-600">
                          -  {paymentOptions.partyCoins.amount}
                        </span>
                      </div>
                    </div>
                    {tempPaymentMethod === 'partyCoins' && (
                      <CheckCircle size={18} className="text-yellow-600 ml-2" />
                    )}
                  </button>
                  
                  {/* Add Get Party Coins button */}
                  <div className="mt-3 pl-13">
                    <button 
                      onClick={() => alert('跳转到派对币获取页面')}
                      className="ml-12 px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium flex items-center justify-center"
                    >
                      <Plus size={14} className="mr-1" />
                      获取派对币
                    </button>
                  </div>
                </div>
                
                {/* Starlight Coins Option */}
                <div className={`p-4 rounded-lg text-left border ${
                  tempPaymentMethod === 'starlightCoins' 
                    ? 'bg-blue-50 border-blue-300' 
                    : 'bg-white border-gray-200'
                }`}>
                  <button 
                    onClick={() => setTempPaymentMethod('starlightCoins')}
                    className="w-full flex items-center"
                  >
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <DollarSign size={20} className="text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium">星光币</h3>
                        <span className={`text-sm font-medium ${getBalanceColorClass('starlightCoins', paymentOptions.starlightCoins.amount)}`}>
                          余额: {userBalance.starlightCoins}
                        </span>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-gray-500">用星光币支付音乐生成费用</p>
                        <span className="text-xs font-bold text-blue-600">
                          - {paymentOptions.starlightCoins.amount}
                        </span>
                      </div>
                    </div>
                    {tempPaymentMethod === 'starlightCoins' && (
                      <CheckCircle size={18} className="text-blue-600 ml-2" />
                    )}
                  </button>
                  
                  {/* Add Recharge Starlight Coins button */}
                  <div className="mt-3 pl-13">
                    <button 
                      onClick={() => alert('跳转到星光币充值页面')}
                      className="ml-12 px-3 py-1.5 bg-blue-100 text-blue-700 rounded-full text-xs font-medium flex items-center justify-center"
                    >
                      <Plus size={14} className="mr-1" />
                      充值星光币
                    </button>
                  </div>
                </div>
                
                {/* Watch Video Option */}
                <button 
                  onClick={() => setTempPaymentMethod('watchVideo')}
                  className={`p-4 rounded-lg text-left flex items-center border ${
                    tempPaymentMethod === 'watchVideo' 
                      ? 'bg-green-50 border-green-300' 
                      : 'bg-white border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <PlayCircle size={20} className="text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">观看激励视频</h3>
                    <p className="text-xs text-gray-500 mt-1">观看短视频广告以免费创作</p>
                  </div>
                  {tempPaymentMethod === 'watchVideo' && (
                    <CheckCircle size={18} className="text-green-600 ml-2" />
                  )}
                </button>
              </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <button 
                onClick={() => {
                  setSelectedPaymentMethod(tempPaymentMethod);
                  setShowPaymentMethods(false);
                }}
                className="w-full py-3 bg-purple-600 rounded-lg text-white font-medium"
              >
                确认支付方式
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Style for animations and special effects */}
      <style jsx>{`
        .loading-waves {
          background: linear-gradient(-45deg, #6366f1, #8b5cf6, #3b82f6, #9333ea);
          background-size: 400% 400%;
          animation: gradient 3s ease infinite;
        }
        
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        
        @keyframes music-bar1 {
          0%, 100% { height: 4px; }
          50% { height: 12px; }
        }
        
        @keyframes music-bar2 {
          0%, 100% { height: 4px; }
          40% { height: 18px; }
        }
        
        @keyframes music-bar3 {
          0%, 100% { height: 4px; }
          30% { height: 14px; }
        }
        
        @keyframes music-bar4 {
          0%, 100% { height: 4px; }
          60% { height: 10px; }
        }
        
        @keyframes music-bar5 {
          0%, 100% { height: 4px; }
          70% { height: 16px; }
        }
        
        .animate-music-bar1 {
          animation: music-bar1 1.2s ease-in-out infinite;
        }
        
        .animate-music-bar2 {
          animation: music-bar2 1.7s ease-in-out infinite;
        }
        
        .animate-music-bar3 {
          animation: music-bar3 1.4s ease-in-out infinite;
        }
        
        .animate-music-bar4 {
          animation: music-bar4 1.3s ease-in-out infinite;
        }
        
        .animate-music-bar5 {
          animation: music-bar5 1.5s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default CreateMusicPage;