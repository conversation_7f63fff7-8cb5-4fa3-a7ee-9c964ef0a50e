import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import EnhancedCreatorView from './EnhancedCreatorView';
import TalentsView from './TalentsView';
import CreateTalentVideo from './CreateTalentVideo';
import MetaLive from './MetaLive';
import ImagePublishPage from './ImagePublishPage';
import AICharacterProfile from './AICharacterProfile';
import AssetLibraryPage from './AssetLibraryPage';
import PublishStatusPage from './PublishStatusPage';
import CreateVideo from './CreateVideo';
import AIMemoryView from './AIMemoryView';
import CreateImagePage from './CreateImagePage';
import CreateMusicPage from './CreateMusicPage';
import CreatorVideoStudio from './CreatorVideoStudio';
import RoadmapVisualization from './RoadmapVisualization';
import OptimizedDimensionBrush from './OptimizedDimensionBrush';
import AIChatInterface from './AIChatInterface';
import <PERSON>hanced<PERSON><PERSON>hatInterface from './EnhancedAIChatInterface';
import UploadMediaPage from './UploadMediaPage';
import StarPathPlanPage from './StarPathPlanPage';
import PicAIChatInterface from './PicAIChatInterface';
import DreamWeaverVideoCreator from './DreamWeaverVideoCreator';
import MetaLiveScheduleManager from './MetaLiveScheduleManager';
import LivingNarrativeScreen from './LivingNarrativeScreen';
import CustomDreamCreator from './CustomDreamCreator';
import VoiceCallPage from './VoiceCallPage';
import AICharacterInvestmentPage from './AICharacterInvestmentPage';
import StarRankingPage from './StarRankingPage';
import StarCompassPage from './StarCompassPage'; 
import StockChatInterface from './StockChatInterface';
import AIStockDetailPage from './AIStockDetailPage';

import './App.css';

// 创建一个首页组件
function HomePage() {
  return (
    <div className="home-page">
      <h1>欢迎来到应用首页</h1>
      <div className="nav-buttons">
        <Link to="/enhanced-creator" className="nav-button">创作者AI角色视图</Link>
        <Link to="/talents" className="nav-button">才艺视图</Link>
        <Link to="/create-talent-video" className="nav-button">创建才艺视频</Link>
        <Link to="/meta-live" className="nav-button">元•Live动态</Link>
        <Link to="/image-publish" className="nav-button">图片发布页面</Link>
        <Link to="/ai-character" className="nav-button">AI角色简介</Link>
        <Link to="/AssetLibraryPage" className="nav-button">素材库</Link>
        <Link to="/PublishStatusPage" className="nav-button">发布动态</Link>
        <Link to="/CreateVideo" className="nav-button">创建视频</Link>
        <Link to="/AIMemoryView" className="nav-button">查看记忆</Link>
        <Link to="/CreateImagePage" className="nav-button">创建图片</Link>
        <Link to="/CreateMusicPage" className="nav-button">创建音乐</Link>
        <Link to="/CreatorVideoStudio" className="nav-button">创建视频PLUS</Link>
        <Link to="/MetaLiveScheduleManager" className="nav-button">元•Live计划管理</Link>
        <Link to="/OptimizedDimensionBrush" className="nav-button">次元画笔</Link>
        <Link to="/DreamWeaverVideoCreator" className="nav-button">梦境织机</Link>
        <Link to="/CustomDreamCreator" className="nav-button">自定义梦境</Link>
        <Link to="/EnhancedAIChatInterface" className="nav-button">增强聊天界面</Link>
        <Link to="/EnhancedAIChatInterface" className="nav-button">聊天界面withMCP</Link>
        <Link to="/UploadMediaPage" className="nav-button">上传文件</Link>
        <Link to="/StarPathPlanPage" className="nav-button">星途计划介绍</Link>
        <Link to="/PicAIChatInterface" className="nav-button">生图聊天界面</Link>
        <Link to="/RoadmapVisualization" className="nav-button">Roadmap</Link>
        <Link to="/LivingNarrativeScreen" className="nav-button">生动叙事</Link>
        <Link to="/VoiceCallPage" className="nav-button">语音通话界面</Link>
        <Link to="/AICharacterInvestmentPage" className="nav-button">AI角色投资</Link>
        <Link to="/StarRankingPage" className="nav-button">星耀排行</Link>
        <Link to="/StarCompassPage" className="nav-button">星语罗盘</Link>
        <Link to="/StockChatInterface" className="nav-button">股票聊天界面</Link>
        <Link to="/AIStockDetailPage" className="nav-button">AI股票详情页</Link>
      </div>
    </div>
  );
}

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/enhanced-creator" element={<EnhancedCreatorView />} />
          <Route path="/talents" element={<TalentsView />} />
          <Route path="/create-talent-video" element={<CreateTalentVideo />} />
          <Route path="/meta-live" element={<MetaLive />} />
          <Route path="/image-publish" element={<ImagePublishPage />} />
          <Route path="/ai-character" element={<AICharacterProfile />} />
          <Route path="/AssetLibraryPage" element={<AssetLibraryPage />} />
          <Route path="/PublishStatusPage" element={<PublishStatusPage />} />
          <Route path="/CreateVideo" element={<CreateVideo />} />
          <Route path="/AIMemoryView" element={<AIMemoryView />} />
          <Route path="/CreateImagePage" element={<CreateImagePage />} />
          <Route path="/CreateMusicPage" element={<CreateMusicPage />} />
          <Route path="/CreatorVideoStudio" element={<CreatorVideoStudio />} />
          <Route path="/MetaLiveScheduleManager" element={<MetaLiveScheduleManager />} />
          <Route path="/DreamWeaverVideoCreator" element={<DreamWeaverVideoCreator />} />
          <Route path="/CustomDreamCreator" element={<CustomDreamCreator />} />
          <Route path="/OptimizedDimensionBrush" element={<OptimizedDimensionBrush />} />
          <Route path="/AIChatInterface" element={<AIChatInterface />} />
          <Route path="/EnhancedAIChatInterface" element={<EnhancedAIChatInterface />} />
          <Route path="/UploadMediaPage" element={<UploadMediaPage />} />
          <Route path="/StarPathPlanPage" element={<StarPathPlanPage />} />
          <Route path="/PicAIChatInterface" element={<PicAIChatInterface />} />
          <Route path="/RoadmapVisualization" element={<RoadmapVisualization />} />
          <Route path="/LivingNarrativeScreen" element={<LivingNarrativeScreen />} />
          <Route path="/VoiceCallPage" element={<VoiceCallPage />} />
          <Route path="/AICharacterInvestmentPage" element={<AICharacterInvestmentPage />} />
          <Route path="/StarRankingPage" element={<StarRankingPage />} />
          <Route path="/StarCompassPage" element={<StarCompassPage />} />
          <Route path="/StockChatInterface" element={<StockChatInterface />} />
          <Route path="/AIStockDetailPage" element={<AIStockDetailPage />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;