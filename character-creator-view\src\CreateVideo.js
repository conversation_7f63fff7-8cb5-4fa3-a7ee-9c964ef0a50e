import React, { useState, useEffect } from 'react';
import { 
  ChevronLeft, Play, Edit, 
  Zap, Star, MessageCircle, 
  RefreshCw, X, Save, 
  Wand2, Trash2, Loader,
  Video
} from 'lucide-react';

const CreateVideo = () => {
  // 状态管理
  const [step, setStep] = useState(1); // 1: 创意描述, 2: 预览确认
  const [description, setDescription] = useState('');
  const [suggestedTemplates, setSuggestedTemplates] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [videoTitle, setVideoTitle] = useState('');
  const [isPolishingTitle, setIsPolishingTitle] = useState(false);
  const [isRewritingTitle, setIsRewritingTitle] = useState(false);
  const [previewVideo, setPreviewVideo] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('热门');
  const [isPolishing, setIsPolishing] = useState(false);
  const [isRewriting, setIsRewriting] = useState(false);
  const [activeVideoIndex, setActiveVideoIndex] = useState(0);

  // 模拟AI润色标题
  const polishTitle = () => {
    setIsPolishingTitle(true);
    setTimeout(() => {
      setVideoTitle(videoTitle ? videoTitle + "（精致版）" : "雨夜侦探（精致版）");
      setIsPolishingTitle(false);
    }, 1500);
  };
  
  // 模拟AI重写标题
  const rewriteTitle = () => {
    setIsRewritingTitle(true);
    setTimeout(() => {
      setVideoTitle("夜幕下的赛博侦探");
      setIsRewritingTitle(false);
    }, 1500);
  };

  // 创作者信息
  const creatorInfo = {
    name: "硬核创作者",
    avatar: "/api/placeholder/100/100",
    starCoins: 800, // 消耗星光币
    characterLevel: 5,
    characterName: "赛博侦探 K",
    characterAvatar: "/api/placeholder/120/120"
  };
  
  // 才艺分类
  const videoCategories = [
    { id: 'hot', name: '热门' },
    { id: 'emotion', name: '情感' },
    { id: 'dance', name: '舞蹈' },
    { id: 'sports', name: '运动' },
    { id: 'daily', name: '日常' }
  ];
  
  // 视频模板
  const videoTemplates = {
    '热门': [
      { 
        id: 1, 
        thumbnail: "/api/placeholder/160/240",
        description: "在霓虹灯闪烁的城市夜景中，主角正站在高楼天台，俯瞰整个城市，神情坚定而冷静。"
      },
      { 
        id: 2, 
        thumbnail: "/api/placeholder/160/240",
        description: "主角漫步在雨中的街道上，雨水顺着风衣滴落，霓虹灯的光芒映在雨水中形成绚丽的色彩。"
      },
      { 
        id: 3, 
        thumbnail: "/api/placeholder/160/240",
        description: "在拥挤的市场中，主角敏锐地观察着周围的一切，寻找着可能的线索。"
      }
    ],
    '情感': [
      { 
        id: 4, 
        thumbnail: "/api/placeholder/160/240",
        description: "主角站在落地窗前，望着窗外的城市夜景，表情若有所思，眼神中透露着对未来的期待。"
      },
      { 
        id: 5, 
        thumbnail: "/api/placeholder/160/240",
        description: "主角轻抚桌上的相框，眼神中流露出一丝怀念，嘴角微微上扬，似乎回忆起了美好的过去。"
      }
    ],
    '舞蹈': [
      { 
        id: 6, 
        thumbnail: "/api/placeholder/160/240",
        description: "主角在霓虹灯闪烁的空间中，随着电子音乐的节奏展现出流畅的机械舞步，动作精准而充满力量。"
      },
      { 
        id: 7, 
        thumbnail: "/api/placeholder/160/240",
        description: "在空旷的工业风格室内，主角展示着融合了街舞和现代舞的独特舞步，每个动作都充满韵律感。"
      }
    ],
    '运动': [
      { 
        id: 8, 
        thumbnail: "/api/placeholder/160/240",
        description: "主角在城市的屋顶和墙壁间敏捷地跑酷，展示出惊人的身体控制力和平衡感。"
      },
      { 
        id: 9, 
        thumbnail: "/api/placeholder/160/240",
        description: "在拳击场上，主角展示着精准的拳法和身法，每一个动作都充满力量和技巧。"
      }
    ],
    '日常': [
      { 
        id: 10, 
        thumbnail: "/api/placeholder/160/240",
        description: "主角在整理资料，专注地分析线索，桌面上散落着照片和笔记，一旁的咖啡冒着热气。"
      },
      { 
        id: 11, 
        thumbnail: "/api/placeholder/160/240",
        description: "清晨的公寓里，主角站在厨房准备简单的早餐，窗外阳光洒进来，营造出温馨的氛围。"
      }
    ]
  };
  
  // 处理分类变更，更新推荐模板
  useEffect(() => {
    if (selectedCategory) {
      setSuggestedTemplates(videoTemplates[selectedCategory] || []);
      setActiveVideoIndex(0);
    }
  }, [selectedCategory]);
  
  // 模拟AI润色功能
  const polishDescription = () => {
    setIsPolishing(true);
    setTimeout(() => {
      setDescription(description + "（画面色调偏冷，以蓝色和紫色为主，打造未来感氛围。背景可加入霓虹灯和电子显示屏元素，增强科技感和赛博朋克风格。）");
      setIsPolishing(false);
    }, 1500);
  };
  
  // 模拟AI重写功能
  const rewriteDescription = () => {
    setIsRewriting(true);
    setTimeout(() => {
      setDescription("在雨夜的霓虹街头，主角穿着标志性的风衣，站在一栋高楼的边缘俯瞰城市。背景中闪烁的霓虹灯映照出坚毅的侧脸，雨水顺着脸颊滑落。画面通过蓝紫色调营造出深邃而神秘的氛围，远处的城市灯光与雾气交织，呈现出一种朦胧的赛博朋克风格。主角的眼神中透露出坚定与思考，暗示着正在解开一个复杂的谜题。");
      setIsRewriting(false);
    }, 2000);
  };
  
  // 模拟生成视频
  const generateVideo = () => {
    setIsGenerating(true);
    // 直接进入第二步，展示loading状态
    setStep(2);
    
    // 这里可以添加实际的视频生成API调用
    // 模拟3分钟后生成完成的回调
    setTimeout(() => {
      setPreviewVideo("/api/placeholder/320/480");
      setIsGenerating(false);
    }, 10000); // 实际项目中这个时间是约3分钟，这里用10秒演示
  };
  
  // 完成并保存视频
  const saveVideo = () => {
    alert("视频已成功保存！");
    // 这里可以添加保存逻辑和跳转到视频列表页面
  };

  // 滑动到前一个视频
  const prevVideo = () => {
    setActiveVideoIndex((prev) => 
      prev === 0 ? suggestedTemplates.length - 1 : prev - 1
    );
  };

  // 滑动到后一个视频
  const nextVideo = () => {
    setActiveVideoIndex((prev) => 
      prev === suggestedTemplates.length - 1 ? 0 : prev + 1
    );
  };

  // 选择视频模板
  const selectVideoTemplate = (index) => {
    if (suggestedTemplates[index]) {
      setDescription(suggestedTemplates[index].description);
    }
  };
  
  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg">
      {/* 头部导航 */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white">
        <div className="flex items-center">
          <button className="p-1 mr-2">
            <ChevronLeft size={20} className="text-white" />
          </button>
          <h1 className="text-lg font-bold">创建视频</h1>
        </div>
        <div className="flex items-center">
          <div className="bg-purple-600 text-white text-xs px-3 py-1.5 rounded-full flex items-center">
            <Star size={14} className="mr-1.5" />
            消耗 {creatorInfo.starCoins} 星光币
          </div>
        </div>
      </div>
      
      {/* 步骤指示器 - 简化版 */}
      <div className="bg-white p-5 border-b border-gray-200">
        <div className="flex justify-between">
          <div className="flex flex-col items-center flex-1">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-500'} mb-1`}>
              1
            </div>
            <div className="text-xs font-medium text-center">
              <span className={step === 1 ? 'text-purple-600' : 'text-gray-500'}>创意描述</span>
            </div>
          </div>
          
          <div className="flex items-center mt-3 px-1">
            <div className={`h-1 w-10 ${step >= 2 ? 'bg-purple-600' : 'bg-gray-200'}`}></div>
          </div>
          
          <div className="flex flex-col items-center flex-1">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-500'} mb-1`}>
              2
            </div>
            <div className="text-xs font-medium text-center">
              <span className={step === 2 ? 'text-purple-600' : 'text-gray-500'}>预览确认</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-auto p-4">
        {/* 样式定义 */}
        <style jsx>{`
          .video-placeholder {
            background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
            background-size: 20px 20px;
            position: relative;
          }
          .video-placeholder::after {
            content: '视频预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: #666;
          }
          .template-scroll {
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
          }
          .template-scroll::-webkit-scrollbar {
            display: none;
          }
          .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #e5e7eb;
            margin: 0 3px;
            transition: all 0.3s ease;
          }
          .dot.active {
            background-color: #8b5cf6;
            width: 8px;
            height: 8px;
          }
        `}</style>
        
        {/* 步骤1: 创意描述 */}
        {step === 1 && (
          <div>
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <h3 className="text-md font-semibold mb-3 flex items-center">
                <Edit size={16} className="mr-2 text-purple-600" />
                创意描述
              </h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">视频分类</label>
                <div className="flex flex-wrap gap-2">
                  {videoCategories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.name)}
                      className={`px-3 py-1.5 rounded-full text-xs font-medium ${
                        selectedCategory === category.name 
                          ? 'bg-purple-600 text-white' 
                          : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* 视频模板 - 支持左右滑动 */}
              {suggestedTemplates.length > 0 && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">创意视频模板</label>
                  <div className="relative">
                    <div className="template-scroll">
                      <div className="flex justify-center mb-3">
                        {suggestedTemplates.length > 0 && (
                          <div className="relative w-48 h-72 rounded-lg overflow-hidden shadow-md">
                            <div className="video-placeholder h-full w-full">
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center cursor-pointer">
                                  <Play size={24} className="text-white ml-1" />
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      {/* 导航指示器 */}
                      <div className="flex justify-center mb-2">
                        {suggestedTemplates.map((_, index) => (
                          <div 
                            key={index} 
                            className={`dot ${index === activeVideoIndex ? 'active' : ''}`}
                            onClick={() => setActiveVideoIndex(index)}
                          ></div>
                        ))}
                      </div>
                      {/* 左右导航按钮 */}
                      <div className="flex justify-between px-4">
                        <button 
                          onClick={prevVideo}
                          className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600"
                        >
                          <ChevronLeft size={18} />
                        </button>
                        <button
                          onClick={() => selectVideoTemplate(activeVideoIndex)}
                          className="px-4 py-1 rounded-full bg-purple-600 text-white text-xs font-medium"
                        >
                          使用此模板
                        </button>
                        <button 
                          onClick={nextVideo}
                          className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600"
                        >
                          <RefreshCw size={18} />
                        </button>
                      </div>
                    </div>
                  </div>
                  {suggestedTemplates[activeVideoIndex] && (
                    <div className="mt-2 p-2 bg-gray-50 rounded-lg">
                      <p className="text-xs text-gray-600">{suggestedTemplates[activeVideoIndex].description}</p>
                    </div>
                  )}
                </div>
              )}
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">视频描述</label>
                <div className="relative">
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-3 h-32 text-sm"
                    placeholder="描述你想要的视频画面、角色动作、场景、氛围等..."
                  ></textarea>
                  <div className="absolute right-2 bottom-2 flex space-x-1">
                    <button 
                      onClick={polishDescription}
                      disabled={!description || isPolishing}
                      className={`p-1.5 rounded-full ${!description || isPolishing ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                      title="AI润色"
                    >
                      {isPolishing ? <Loader size={16} className="animate-spin" /> : <Wand2 size={16} />}
                    </button>
                    <button 
                      onClick={rewriteDescription}
                      disabled={!description || isRewriting}
                      className={`p-1.5 rounded-full ${!description || isRewriting ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                      title="AI重写"
                    >
                      {isRewriting ? <Loader size={16} className="animate-spin" /> : <RefreshCw size={16} />}
                    </button>
                    <button 
                      onClick={() => setDescription('')}
                      disabled={!description}
                      className={`p-1.5 rounded-full ${!description ? 'text-gray-300' : 'text-red-500 hover:bg-red-50'}`}
                      title="清空"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
                <div className="flex justify-between mt-1">
                  <div className="text-xs text-gray-500">
                    {description.length}/200 字符
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-3 mt-4">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 mr-2 flex-shrink-0">
                    <MessageCircle size={16} />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-purple-700">创作提示</div>
                    <div className="text-xs text-purple-600 mt-1">
                      详细描述场景、情感和动作能提高生成质量。可以提及角色表情、光线氛围和环境细节，让视频更生动。
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="sticky bottom-0 pt-2">
              <button 
                onClick={generateVideo} 
                disabled={!description || isGenerating}
                className={`w-full py-3 rounded-lg text-white font-medium flex items-center justify-center ${description ? 'bg-purple-600' : 'bg-gray-300'}`}
              >
                {isGenerating ? (
                  <>
                    <Loader size={16} className="animate-spin mr-2" />
                    生成中...
                  </>
                ) : (
                  <>
                    立即生成
                  </>
                )}
              </button>
            </div>
          </div>
        )}
        
        {/* 步骤2: 预览确认 */}
        {step === 2 && (
          <div>
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <h3 className="text-md font-semibold mb-3 flex items-center">
                <Play size={16} className="mr-2 text-purple-600" />
                预览视频
              </h3>
              
              <div className="mb-4 flex justify-center">
                {isGenerating ? (
                  <div className="relative w-64 h-96 rounded-lg overflow-hidden bg-gray-100 flex flex-col items-center justify-center">
                    <Loader size={40} className="text-purple-600 animate-spin mb-4" />
                    <p className="text-sm text-gray-600 text-center px-6">
                      视频生成中，您可以退出编辑或停留等待，生成成功后会有app通知
                    </p>
                  </div>
                ) : (
                  <div className="relative w-64 h-96 rounded-lg overflow-hidden video-placeholder">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                        <Play size={24} className="text-white ml-1" />
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <h4 className="text-sm font-medium mb-1">视频描述</h4>
                <p className="text-xs text-gray-600">{description}</p>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <h3 className="text-md font-semibold mb-3">为视频命名</h3>
              <div className="relative">
                <input 
                  type="text" 
                  value={videoTitle}
                  onChange={(e) => setVideoTitle(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-3 text-sm"
                  placeholder="输入视频标题 (例如: 雨夜侦探)"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                  <button 
                    onClick={polishTitle}
                    disabled={!videoTitle || isPolishingTitle}
                    className={`p-1.5 rounded-full ${!videoTitle || isPolishingTitle ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                    title="AI润色"
                  >
                    {isPolishingTitle ? <Loader size={16} className="animate-spin" /> : <Wand2 size={16} />}
                  </button>
                  <button 
                    onClick={rewriteTitle}
                    disabled={!videoTitle || isRewritingTitle}
                    className={`p-1.5 rounded-full ${!videoTitle || isRewritingTitle ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                    title="AI重写"
                  >
                    {isRewritingTitle ? <Loader size={16} className="animate-spin" /> : <RefreshCw size={16} />}
                  </button>
                </div>
              </div>
            </div>
            
            <div className="sticky bottom-0 pt-2 flex space-x-3">
              <button 
                onClick={() => setStep(1)} 
                className="flex-1 py-3 rounded-lg bg-gray-100 text-gray-700 font-medium"
                disabled={isGenerating}
              >
                返回修改
              </button>
              <button 
                onClick={saveVideo} 
                className="flex-1 py-3 rounded-lg bg-green-600 text-white font-medium flex items-center justify-center"
                disabled={isGenerating}
              >
                <Save size={16} className="mr-2" />
                保存视频
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateVideo;