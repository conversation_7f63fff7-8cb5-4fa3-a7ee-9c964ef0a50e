import React, { useState, useEffect, useRef } from 'react';
import { 
  ChevronLeft, Image, Upload, RefreshCw, Sparkles, 
  Trash2, X, Check, AlertTriangle, Loader, 
  MessageCircle, Clock, ArrowLeft, Camera, Wand2,
  Paintbrush, RotateCw, ImagePlus, Eye, Copy, Plus, Download,
  PenLine, FileImage, ChevronDown, CheckCircle, Settings,
  Film, Save, ExternalLink
} from 'lucide-react';

const UploadMediaPage = ({ onBack, onMediaUploaded, characterData, existingAssets = [] }) => {
  // States for the upload process
  const [step, setStep] = useState('select'); // 'select', 'processing', 'review'
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [mediaType, setMediaType] = useState(null); // 'image' or 'video'
  const [mediaDescription, setMediaDescription] = useState('');
  const [originalDescription, setOriginalDescription] = useState('');
  const [mediaTitle, setMediaTitle] = useState(''); // New state for media title
  const [isEditingTitle, setIsEditingTitle] = useState(false); // New state for title editing mode
  const [isAIAnalyzing, setIsAIAnalyzing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('waiting'); // 'waiting', 'uploading', 'analyzing', 'complete', 'error'
  const [processingProgress, setProcessingProgress] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState('约30秒');
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  
  // Refs
  const fileInputRef = useRef(null);
  const descriptionRef = useRef(null);
  const titleInputRef = useRef(null); // New ref for title input
  const processingTimer = useRef(null);
  
  // Effect to auto resize textarea
  useEffect(() => {
    if (descriptionRef.current) {
      descriptionRef.current.style.height = 'auto';
      descriptionRef.current.style.height = `${descriptionRef.current.scrollHeight}px`;
    }
  }, [mediaDescription]);

        // Simulate processing progress when in processing state
  useEffect(() => {
    if (step === 'processing') {
      let progress = 0;
      setProcessingProgress(0);
      setProcessingStatus('waiting');
      
      // Wait 0.2 second to simulate initial queue
      setTimeout(() => {
        setProcessingStatus('uploading');
        
        processingTimer.current = setInterval(() => {
          progress += Math.random() * 10; // Faster progress increment
          if (progress >= 50) {
            // Switch to analyzing state at 50%
            if (processingStatus !== 'analyzing') {
              setProcessingStatus('analyzing');
            }
          }
          
          if (progress >= 100) {
            progress = 100;
            clearInterval(processingTimer.current);
            
            // Simulate completion and generate AI description
            setTimeout(() => {
              generateAIDescription();
            }, 200);
          }
          setProcessingProgress(progress);
        }, 150); // Faster interval
      }, 200); // Shorter initial wait
    }
    
    return () => {
      if (processingTimer.current) {
        clearInterval(processingTimer.current);
      }
    };
  }, [step]);

  // Function to generate AI description
  const generateAIDescription = () => {
    setIsAIAnalyzing(true);
    
    // Simulate AI analyzing the image/video (this would call the actual API in production)
    setTimeout(() => {
      let aiDescription = '';
      
      // Generate a description based on media type
      if (mediaType === 'image') {
        aiDescription = selectedMedia.name.includes('outdoor') 
          ? '一个年轻人站在青山绿水间，身穿休闲装，面带微笑。背景是湛蓝的天空和远处的山脉，阳光洒在他的脸上，展现出欢快的户外氛围。' 
          : selectedMedia.name.includes('group') 
            ? '一群朋友在聚会上开心合影，大家脸上洋溢着笑容。他们举起饮料，庆祝这美好的时刻。温暖的灯光照亮了整个场景，展现友谊的温馨。' 
            : '一张精美的照片，展示了生活中的美好瞬间。光线柔和，构图精巧，色彩丰富且和谐。';
      } else {
        aiDescription = selectedMedia.name.includes('travel') 
          ? '一段旅行视频剪辑，记录了城市和自然风景的美丽画面。视频中展示了历史建筑、自然景观和当地文化，色彩鲜明，画面稳定流畅。' 
          : selectedMedia.name.includes('family') 
            ? '一段温馨的家庭视频，记录了日常生活中的欢笑时刻。视频中展现了家人之间的互动和关爱，画面温暖自然。' 
            : '一段精彩的视频内容，画面清晰，剪辑流畅，内容丰富有趣。音画同步，表现力强。';
      }
      
      // Generate a title from the description (first 10-15 characters)
      const generatedTitle = aiDescription.substring(0, Math.min(15, aiDescription.length)) + (aiDescription.length > 15 ? '...' : '');
      
      setMediaDescription(aiDescription);
      setOriginalDescription(aiDescription);
      setMediaTitle(generatedTitle); // Set the generated title
      setProcessingStatus('complete');
      setIsAIAnalyzing(false);
      setStep('review');
    }, 500);
  };

  // Get appropriate processing status text
  const getProcessingStatusText = () => {
    if (processingStatus === 'waiting') return '准备中';
    if (processingStatus === 'uploading') return '上传文件中';
    if (processingStatus === 'analyzing') return '智能识别内容中';
    if (processingStatus === 'error') return '处理出错';
    return '处理中';
  };

  // Handler for file selection
  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Create a local URL for the file
    const url = URL.createObjectURL(file);
    const type = file.type.startsWith('image/') ? 'image' : 'video';
    
    setSelectedMedia({
      file: file,
      url: url,
      name: file.name,
      size: file.size,
      type: file.type,
    });
    
    setMediaType(type);
  };

  // Handler for initiating upload
  const handleUploadMedia = () => {
    if (!selectedMedia) {
      alert('请先选择媒体文件');
      return;
    }
    setStep('processing');
  };

  // Handler for canceling upload
  const handleCancelUpload = () => {
    if (window.confirm('确定要取消当前上传吗？所有进度将丢失。')) {
      if (processingTimer.current) {
        clearInterval(processingTimer.current);
      }
      setStep('select');
      setSelectedMedia(null);
      setMediaType(null);
      setProcessingProgress(0);
    }
  };

  // Handler for changing media
  const handleChangeMedia = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handler for removing selected media
  const handleRemoveMedia = () => {
    if (window.confirm('确定要移除当前选择的媒体吗？')) {
      setSelectedMedia(null);
      setMediaType(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handler for enhancing the description with AI
  const handleEnhanceDescription = async () => {
    if (!mediaDescription.trim()) {
      alert('请先输入一些描述');
      return;
    }
    
    setIsAIAnalyzing(true);
    
    // Save original to allow reverting
    if (!originalDescription) {
      setOriginalDescription(mediaDescription);
    }
    
    // Simulate AI enhancement (this would call the actual API in production)
    setTimeout(() => {
      const baseText = mediaDescription;
      
      // Apply enhancement
      const enhanced = `${baseText}，画面构图平衡，色彩搭配和谐，细节清晰，光线柔和自然，视觉效果出色，展现出专业的审美品质。`;
      
      setMediaDescription(enhanced);
      setIsAIAnalyzing(false);
    }, 500);
  };

  // Handler for clearing the description
  const handleClearDescription = () => {
    if (mediaDescription.trim() && !window.confirm('确定要清空当前描述吗？')) {
      return;
    }
    setMediaDescription('');
    
    // Focus the textarea after clearing
    if (descriptionRef.current) {
      descriptionRef.current.focus();
    }
  };

  // Handler for undoing AI enhancement
  const handleUndoEnhancement = () => {
    if (originalDescription) {
      setMediaDescription(originalDescription);
      setOriginalDescription('');
    }
  };

  // Handler for completing upload process
  const handleCompleteUpload = () => {
    // In a real app, this would add the media to the asset library
    if (onMediaUploaded) {
      onMediaUploaded({
        id: Date.now(),
        type: mediaType,
        url: selectedMedia.url,
        title: mediaTitle || mediaDescription.slice(0, 20) + (mediaDescription.length > 20 ? '...' : ''),
        description: mediaDescription,
        createdAt: '刚刚',
        usedCount: 0,
        likes: 0,
        isHD: true,
      });
    }
    
    // Return to previous screen
    if (onBack) {
      onBack();
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + ' KB';
    } else {
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
  };

  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white sticky top-0 z-10">
        <div className="flex items-center">
          <button 
            onClick={onBack} 
            className="p-2 mr-2 rounded-full hover:bg-white hover:bg-opacity-20"
          >
            <ChevronLeft size={20} />
          </button>
          <h1 className="text-lg font-bold">上传素材</h1>
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex-1 p-4 overflow-auto">
        {step === 'select' && (
          <>
            {/* Character info reminder if applicable */}
            {characterData && (
              <div className="mb-4 p-3 bg-purple-50 rounded-lg border border-purple-100 flex items-center">
                <div className="w-10 h-10 bg-purple-200 rounded-full overflow-hidden flex-shrink-0 mr-3">
                  {characterData.profileImage && (
                    <img 
                      src={characterData.profileImage} 
                      alt={characterData.name}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-purple-800">正在为 {characterData.name} 上传素材</h3>
                  <p className="text-xs text-purple-600">上传的素材将添加到角色素材库</p>
                </div>
              </div>
            )}
            
            {/* File upload area */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <FileImage size={16} className="mr-1 text-purple-600" />
                  选择媒体文件
                </label>
              </div>
              
              <input 
                type="file" 
                ref={fileInputRef}
                onChange={handleFileSelect}
                accept="image/*,video/*" 
                className="hidden"
              />
              
              {!selectedMedia ? (
                <button
                  onClick={() => fileInputRef.current.click()}
                  className="w-full p-8 bg-white rounded-lg border-2 border-dashed border-gray-300 flex flex-col items-center justify-center hover:border-purple-400 transition-colors"
                >
                  <div className="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mb-3">
                    <Upload size={24} className="text-purple-600" />
                  </div>
                  <p className="text-sm font-medium text-gray-700 mb-1">点击选择媒体文件</p>
                  <p className="text-xs text-gray-500">支持 JPG、PNG、MP4 等格式</p>
                </button>
              ) : (
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <div className="relative">
                    {mediaType === 'image' ? (
                      <img 
                        src={selectedMedia.url} 
                        alt="选择的图片" 
                        className="w-full aspect-video object-contain bg-black"
                      />
                    ) : (
                      <video 
                        src={selectedMedia.url} 
                        controls 
                        className="w-full aspect-video object-contain bg-black"
                      />
                    )}
                    
                    <div className="absolute top-2 right-2 flex gap-2">
                      <button
                        onClick={handleChangeMedia}
                        className="w-8 h-8 rounded-full bg-black bg-opacity-50 flex items-center justify-center text-white"
                      >
                        <RefreshCw size={16} />
                      </button>
                      <button
                        onClick={handleRemoveMedia}
                        className="w-8 h-8 rounded-full bg-black bg-opacity-50 flex items-center justify-center text-white"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-3 border-t border-gray-100">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        {mediaType === 'image' ? (
                          <Image size={16} className="text-blue-500 mr-1" />
                        ) : (
                          <Film size={16} className="text-red-500 mr-1" />
                        )}
                        <span className="text-sm font-medium">
                          {mediaType === 'image' ? '图片' : '视频'}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatFileSize(selectedMedia.size)}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1 truncate">
                      {selectedMedia.name}
                    </p>
                  </div>
                </div>
              )}
            </div>
            
            {/* Helpful tips */}
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
              <h3 className="text-sm font-medium text-blue-800 flex items-center mb-1">
                <Sparkles size={16} className="mr-1 text-blue-500" />
                上传提示
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  上传后，我们会智能识别图片/视频内容并生成描述
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  高质量的照片和视频更容易被AI准确识别和描述
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  您可以在识别后编辑描述，添加更多个性化信息
                </li>
              </ul>
            </div>

            {/* Bottom spacer for fixed button */}
            <div className="h-16"></div>
          </>
        )}
        
        {step === 'processing' && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="w-full max-w-sm mb-8">
              <div className="flex justify-between items-center mb-2">
                <div className="text-sm font-medium text-gray-700 flex items-center">
                  <RotateCw size={16} className={`mr-1 text-blue-600 ${processingStatus !== 'error' ? 'animate-spin' : ''}`} />
                  {getProcessingStatusText()}
                </div>
                <div className="text-xs text-gray-500">
                  {Math.round(processingProgress)}%
                </div>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2 mb-1 overflow-hidden">
                <div 
                  className="h-full bg-blue-600 transition-all duration-300 ease-out"
                  style={{ width: `${processingProgress}%` }}
                ></div>
              </div>
              
              <div className="text-xs text-gray-500 flex justify-between">
                <span>预计等待时间: {estimatedTime}</span>
                <span>请勿关闭页面</span>
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-4 shadow-md w-full max-w-sm">
              {selectedMedia && (
                <div className="bg-gray-100 rounded-lg overflow-hidden aspect-video mb-4 flex items-center justify-center relative">
                  {mediaType === 'image' ? (
                    <img 
                      src={selectedMedia.url} 
                      alt="上传的图片" 
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-black">
                      <Film size={40} className="text-white opacity-50" />
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                    {processingStatus === 'uploading' && (
                      <div className="text-white bg-black bg-opacity-70 px-3 py-2 rounded-lg shadow-sm">
                        <Upload size={24} className="mx-auto mb-2" />
                        <p className="text-xs text-center">上传中...</p>
                      </div>
                    )}
                    {processingStatus === 'analyzing' && (
                      <div className="text-white bg-black bg-opacity-70 px-3 py-2 rounded-lg shadow-sm">
                        <Loader size={24} className="animate-spin mx-auto mb-2" />
                        <p className="text-xs text-center">AI分析中...</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              <div className="bg-blue-50 rounded-lg p-3 text-xs text-blue-800">
                <div className="flex items-center mb-2">
                  <MessageCircle size={14} className="mr-1 text-blue-600" />
                  <span className="font-medium">处理进度</span>
                </div>
                {processingStatus === 'uploading' ? (
                  <p>正在上传您的媒体文件，请稍候。上传速度取决于您的网络状况。</p>
                ) : processingStatus === 'analyzing' ? (
                  <p>正在使用AI识别媒体内容并生成描述，这可能需要几秒钟的时间。</p>
                ) : (
                  <p>您可以放心离开此页面继续其他操作，媒体处理完成后我们会通知您。</p>
                )}
              </div>
            </div>
          </div>
        )}
        
        {step === 'review' && (
          <div className="flex flex-col py-4">
            <div className="bg-white rounded-xl p-4 shadow-md w-full mb-4">
              <div className="rounded-lg overflow-hidden mb-4">
                {mediaType === 'image' ? (
                  <img 
                    src={selectedMedia.url} 
                    alt="上传的图片" 
                    className="w-full aspect-video object-contain bg-black"
                  />
                ) : (
                  <video 
                    src={selectedMedia.url} 
                    controls 
                    className="w-full aspect-video object-contain bg-black"
                  />
                )}
              </div>
              
              <div className="space-y-3">
                {/* New Title Input Section */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium text-gray-700 flex items-center">
                      <PenLine size={16} className="mr-1 text-purple-600" />
                      素材标题
                    </h3>
                    <button 
                      onClick={() => setIsEditingTitle(true)}
                      className="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full flex items-center"
                    >
                      <PenLine size={14} className="mr-1" />
                      编辑
                    </button>
                  </div>
                  
                  {isEditingTitle ? (
                    <div className="bg-white rounded-lg border border-purple-200 overflow-hidden">
                      <input
                        ref={titleInputRef}
                        value={mediaTitle}
                        onChange={(e) => setMediaTitle(e.target.value)}
                        placeholder="请输入素材标题..."
                        className="w-full p-3 text-sm border-none focus:ring-0 focus:outline-none"
                        disabled={isAIAnalyzing}
                      />
                      
                      <div className="flex border-t border-gray-100">
                        <button
                          onClick={() => setIsEditingTitle(false)}
                          className="flex-1 py-2 text-xs font-medium flex items-center justify-center text-green-600"
                        >
                          <Check size={14} className="mr-1" />
                          完成
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-700">
                      {mediaTitle || <span className="text-gray-400">暂无标题</span>}
                    </div>
                  )}
                </div>

                {/* Existing Description Section */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium text-gray-700 flex items-center">
                      <PenLine size={16} className="mr-1 text-purple-600" />
                      AI识别结果
                    </h3>
                    <button 
                      onClick={() => setIsEditingDescription(true)}
                      className="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full flex items-center"
                    >
                      <PenLine size={14} className="mr-1" />
                      编辑
                    </button>
                  </div>
                  
                  {isEditingDescription ? (
                    <div className="bg-white rounded-lg border border-purple-200 overflow-hidden">
                      <textarea
                        ref={descriptionRef}
                        value={mediaDescription}
                        onChange={(e) => setMediaDescription(e.target.value)}
                        placeholder="请输入媒体描述..."
                        className="w-full p-3 min-h-[100px] text-sm border-none focus:ring-0 focus:outline-none resize-none"
                        disabled={isAIAnalyzing}
                      ></textarea>
                      
                      <div className="flex border-t border-gray-100">
                        <button
                          onClick={handleEnhanceDescription}
                          disabled={!mediaDescription.trim() || isAIAnalyzing}
                          className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                            !mediaDescription.trim() || isAIAnalyzing ? 'text-gray-400' : 'text-purple-600'
                          }`}
                        >
                          {isAIAnalyzing ? <Loader size={14} className="mr-1 animate-spin" /> : <Wand2 size={14} className="mr-1" />}
                          AI润色
                        </button>
                        <div className="w-px h-8 bg-gray-100 my-auto"></div>
                        <button
                          onClick={handleClearDescription}
                          disabled={!mediaDescription.trim() || isAIAnalyzing}
                          className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                            !mediaDescription.trim() || isAIAnalyzing ? 'text-gray-400' : 'text-red-600'
                          }`}
                        >
                          <Trash2 size={14} className="mr-1" />
                          清空
                        </button>
                        <div className="w-px h-8 bg-gray-100 my-auto"></div>
                        <button
                          onClick={() => setIsEditingDescription(false)}
                          className="flex-1 py-2 text-xs font-medium flex items-center justify-center text-green-600"
                        >
                          <Check size={14} className="mr-1" />
                          完成
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-700">
                      {mediaDescription || <span className="text-gray-400">暂无描述</span>}
                    </div>
                  )}
                  
                  {originalDescription && isEditingDescription && (
                    <div className="mt-2 flex justify-end">
                      <button
                        onClick={handleUndoEnhancement}
                        className="text-xs text-gray-600 flex items-center"
                      >
                        <ArrowLeft size={12} className="mr-1" />
                        恢复原始描述
                      </button>
                    </div>
                  )}
                </div>
                
                <div className="flex justify-between text-xs text-gray-500">
                  <span>文件类型: {mediaType === 'image' ? '图片' : '视频'}</span>
                  <span>大小: {formatFileSize(selectedMedia.size)}</span>
                </div>
              </div>
            </div>
            

            
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-100 mb-4">
              <h3 className="text-sm font-medium text-blue-800 flex items-center mb-1">
                <Sparkles size={16} className="mr-1 text-blue-500" />
                素材使用提示
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  添加到素材库后，您可以在创建动态时直接使用此素材
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  精确的描述可以帮助您更快地找到需要的素材
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  您随时可以在素材库中管理和编辑已上传的素材
                </li>
              </ul>
            </div>
            
            <div className="w-full">
              <div className="flex flex-col space-y-3">
                <button 
                  onClick={() => {
                    // 点击发布动态的逻辑（这里可以先保存到素材库然后跳转到发布动态页面）
                    alert('跳转到发布动态页面');
                    // 实际上应该添加发布动态的逻辑，这里只是一个演示
                  }}
                  className="w-full py-3 rounded-lg bg-blue-500 text-white text-sm font-medium flex items-center justify-center"
                >
                  <MessageCircle size={16} className="mr-1" />
                  发布动态
                </button>
                
                <div className="flex justify-between">
                  <button 
                    onClick={() => fileInputRef.current.click()}
                    className="flex-1 mr-1 py-3 rounded-lg border border-purple-500 text-purple-600 text-sm font-medium flex items-center justify-center"
                  >
                    <ImagePlus size={16} className="mr-1" />
                    继续上传更多
                  </button>
                  <button 
                    onClick={handleCompleteUpload}
                    className="flex-1 ml-1 py-3 rounded-lg bg-green-500 text-white text-sm font-medium flex items-center justify-center"
                  >
                    <Save size={16} className="mr-1" />
                    保存到素材库
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Fixed bottom buttons */}
      {step === 'select' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          <button 
            onClick={handleUploadMedia}
            disabled={!selectedMedia}
            className={`w-full py-3 rounded-lg text-sm font-medium ${
              selectedMedia ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500'
            }`}
          >
            开始上传
          </button>
        </div>
      )}
      
      {step === 'processing' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          <button 
            onClick={handleCancelUpload}
            className="w-full py-3 rounded-lg bg-red-500 text-white text-sm font-medium"
          >
            取消上传
          </button>
        </div>
      )}
      
      {/* Style for animations and special effects */}
      <style jsx>{`
        .loading-animation {
          background: linear-gradient(-45deg, #e6e6e6, #f0f0f0, #ebebeb, #f5f5f5);
          background-size: 400% 400%;
          animation: gradient 2s ease infinite;
        }
        
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
      
      {/* Hidden file input */}
      <input 
        type="file" 
        ref={fileInputRef}
        onChange={handleFileSelect}
        accept="image/*,video/*" 
        className="hidden"
      />
    </div>
  );
};

export default UploadMediaPage;