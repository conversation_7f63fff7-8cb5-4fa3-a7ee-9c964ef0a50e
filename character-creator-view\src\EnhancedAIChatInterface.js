import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  ChevronLeft, Send, Mic, Camera, Smile, MoreHorizontal, 
  Palette, Image as ImageIcon, Share, Download, X, Star, Clock, 
  MessageCircle, Maximize, ThumbsUp, Film, Clapperboard,
  CheckCircle, AlertCircle, Search, Play, User 
} from 'lucide-react';

// --- Helper Functions ---
const generateUniqueId = () => '_' + Math.random().toString(36).substr(2, 9);

// Format remaining time (simplified version)
const formatTimeRemaining = (ms) => {
  if (ms < 0) return "即将完成...";
  const totalSeconds = Math.floor(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  if (minutes > 0) {
    return `约 ${minutes} 分 ${seconds} 秒`;
  }
  return `约 ${seconds} 秒`;
};

// Create image placeholder with gradient
const createPlaceholderImage = (sceneType) => {
  if (sceneType === 'sunset-beach') {
    return 'sunset-beach-gradient';
  } else if (sceneType === 'mountain') {
    return 'mountain-gradient';
  } else {
    return 'default-gradient';
  }
};

  // Create video placeholder
const createPlaceholderVideo = (sceneType) => {
  return {
    id: 'generated-video-' + Date.now(),
    coverUrl: sceneType === 'sunset-beach' ? 'sunset-beach-gradient' : 'default-gradient',
    videoUrl: `dummy_video_url_${sceneType}.mp4`,
    duration: '0:15',
    skillName: '梦境织机' // Add skill name for consistent reference
  };
};

// --- Main Component ---
const EnhancedAIChatInterface = () => {
  const messagesEndRef = useRef(null);
  const chatContainerRef = useRef(null);
  const [inputValue, setInputValue] = useState('');
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [previewedImage, setPreviewedImage] = useState(null);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [playingVideo, setPlayingVideo] = useState(null);

  // --- Task Management States ---
  const [activeTasks, setActiveTasks] = useState({});
  const [showTaskList, setShowTaskList] = useState(false);
  const [highlightedMessageId, setHighlightedMessageId] = useState(null);

  // Sample conversation history
  const [messages, setMessages] = useState([
    {
      id: 'msg-' + generateUniqueId(),
      sender: 'ai',
      content: '今天天气真好，适合出去走走。你有什么计划吗？',
      time: '14:32'
    },
    {
      id: 'msg-' + generateUniqueId(),
      sender: 'user',
      content: '我在想要不要去海边散散步，吹吹海风。',
      time: '14:33'
    },
    {
      id: 'msg-' + generateUniqueId(),
      sender: 'ai',
      content: '海边漫步是个很棒的选择！我能想象出你走在金色的沙滩上，海浪轻轻拍打岸边，远处夕阳西下...',
      time: '14:33',
      offerPainting: true,
      offerText: '我可以用次元画笔将这个美丽的海边夕阳场景画出来，要看看吗？',
      isOfferPending: true,
      estimatedImageCost: 5
    },
    {
      id: 'msg-' + generateUniqueId(),
      sender: 'user',
      content: '哇，听起来太美了！如果能变成小视频就更棒了！',
      time: '14:35'
    },
    {
      id: 'msg-' + generateUniqueId(),
      sender: 'ai',
      content: '没问题！我可以试试用"梦境织机"技能，为你捕捉这段黄昏海滩的动态光影。',
      time: '14:36',
      offerVideo: true,
      offerVideoText: '要现在开始创作吗？这个魔法需要一点时间。',
      isVideoOfferPending: true,
      estimatedVideoCost: 20
    },
  ]);

  // --- Task Progress Update Logic ---
  const updateTaskProgress = useCallback(() => {
    setActiveTasks(currentTasks => {
      const updatedTasks = { ...currentTasks };
      let changed = false;

      Object.keys(updatedTasks).forEach(taskId => {
        const task = updatedTasks[taskId];
        if (task.status === 'generating') {
          changed = true;
          const now = Date.now();
          const elapsedTime = now - task.startTime;
          // Reduced to 5 seconds for both image and video generation
          const taskDuration = 5 * 1000; 

          let progress = Math.min(100, Math.floor((elapsedTime / taskDuration) * 100));
          task.progress = progress;
          task.timeRemaining = taskDuration - elapsedTime;

          if (progress >= 100) {
            task.status = 'completed';
            
            // Generate appropriate result based on task type
            if (task.type === 'image') {
              task.result = createPlaceholderImage('sunset-beach');
            } else { // video
              task.result = createPlaceholderVideo('sunset-beach');
            }
            
            // Update corresponding message
            setMessages(prevMessages => prevMessages.map(msg => {
              if (msg.id === task.messageId) {
                if (task.type === 'image') {
                  return { 
                    ...msg, 
                    isGenerating: false, 
                    image: { 
                      id: 'generated-image-' + Date.now(),
                      url: task.result 
                    },
                    generationTaskId: null
                  };
                } else {
                  return { 
                    ...msg, 
                    isVideoGenerating: false, 
                    video: task.result, 
                    generationTaskId: null 
                  };
                }
              }
              return msg;
            }));
          }
        }
      });

      return changed ? updatedTasks : currentTasks;
    });
  }, [setMessages]);

  // Timer to update task progress
  useEffect(() => {
    const interval = setInterval(updateTaskProgress, 200); // Update more frequently for smoother progress
    return () => clearInterval(interval);
  }, [updateTaskProgress]);

  // Auto scroll to bottom
  useEffect(() => {
    if (!highlightedMessageId) {
      scrollToBottom();
    }
  }, [messages, highlightedMessageId]);

  // Clear highlight state
  useEffect(() => {
    if (highlightedMessageId) {
      const timer = setTimeout(() => {
        setHighlightedMessageId(null);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [highlightedMessageId]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // --- Event Handlers ---
  const handleSend = () => {
    if (inputValue.trim() === '') return;
    
    const newUserMessage = {
      id: 'msg-' + generateUniqueId(),
      sender: 'user',
      content: inputValue,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    };
    
    setMessages([...messages, newUserMessage]);
    setInputValue('');
    
    // Simulate AI response
    setTimeout(() => {
      const aiResponse = {
        id: 'msg-' + generateUniqueId(),
        sender: 'ai',
        content: '我想象着你站在海边，迎着海风，感受大自然的宁静与壮美。如果你愿意，我可以将这个画面记录下来。',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
        offerPainting: true,
        offerText: '让我用次元画笔为你绘制一幅海边场景，或者创作一段短视频？',
        isOfferPending: true,
        estimatedImageCost: 5,
        offerBothOptions: true, // New flag to offer both options
        estimatedVideoCost: 20
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  const handleInputChange = (e) => setInputValue(e.target.value);
  
  const handleKeyPress = (e) => { 
    if (e.key === 'Enter' && !e.shiftKey) { 
      e.preventDefault(); 
      handleSend(); 
    } 
  };

  // Image generation handler
  const handleAcceptImageOffer = (messageId, cost) => {
    // Create task ID
    const taskId = 'task-' + generateUniqueId();
    
    // Update message state
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { 
            ...msg, 
            isOfferPending: false, 
            offerBothOptions: false,
            isGenerating: true, 
            generationTaskId: taskId 
          } 
        : msg
    ));
    
    // Add to active tasks
    setActiveTasks(prev => ({
      ...prev,
      [taskId]: {
        id: taskId,
        type: 'image',
        status: 'generating',
        progress: 0,
        startTime: Date.now(),
        messageId: messageId,
        description: "生成海边夕阳图片"
      }
    }));
  };

  const handleDeclineImageOffer = (messageId) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { 
            ...msg, 
            isOfferPending: false, 
            offerDeclined: true,
            offerBothOptions: false
          } 
        : msg
    ));
  };

  // Video generation handler
  const handleAcceptVideoOffer = (messageId, cost) => {
    // Create task ID
    const taskId = 'task-' + generateUniqueId();
    
    // Update message state
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { 
            ...msg, 
            isVideoOfferPending: false, 
            offerBothOptions: false,
            isVideoGenerating: true, 
            generationTaskId: taskId 
          } 
        : msg
    ));
    
    // Add to active tasks
    setActiveTasks(prev => ({
      ...prev,
      [taskId]: {
        id: taskId,
        type: 'video',
        status: 'generating',
        progress: 0,
        startTime: Date.now(),
        messageId: messageId,
        description: "生成黄昏海滩视频"
      }
    }));
  };

  const handleDeclineVideoOffer = (messageId) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { 
            ...msg, 
            isVideoOfferPending: false, 
            offerBothOptions: false,
            offerVideoDeclined: true 
          } 
        : msg
    ));
  };

  // Content preview handlers
  const handleImageClick = (image) => {
    setPreviewedImage(image);
    setShowImagePreview(true);
  };

  const handleVideoPlayClick = (video) => {
    setPlayingVideo(video);
    setShowVideoPlayer(true);
  };

  // Task location handler
  const handleLocateMessage = (messageId) => {
    const messageElement = document.getElementById(messageId);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      setHighlightedMessageId(messageId);
      setShowTaskList(false);
    }
  };

  // Content sharing handlers
  const handleShareImage = () => {
    alert('分享功能已触发');
    // In a real app, this would open a share dialog
  };
  
  const handleDownloadImage = () => {
    alert('下载功能已触发');
    // In a real app, this would download the image
  };
  
  const handleSetAsBackground = () => {
    alert('已设置为聊天背景');
    setShowImagePreview(false);
  };

  // Task metrics
  const ongoingTaskCount = Object.values(activeTasks).filter(task => task.status === 'generating').length;
  const completedTasks = Object.values(activeTasks)
    .filter(task => task.status === 'completed' || task.status === 'failed')
    .sort((a, b) => (b.startTime + 5000) - (a.startTime + 5000));

  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-gray-50 h-screen overflow-hidden relative">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-3 flex items-center shadow-md z-20 relative">
        <button className="p-1 rounded-full hover:bg-white hover:bg-opacity-10">
          <ChevronLeft size={24} />
        </button>
        
        <div className="flex flex-1 items-center mx-3">
          <div className="relative">
            <div className="h-10 w-10 rounded-full bg-purple-400 flex items-center justify-center mr-3">
              <span className="text-white text-lg font-bold">K</span>
            </div>
            <div className="absolute -right-0.5 -bottom-0.5 bg-green-500 rounded-full h-3 w-3 border-2 border-white"></div>
          </div>
          <div>
            <h1 className="font-semibold text-lg">赛博侦探 K</h1>
            <div className="flex items-center text-xs text-white text-opacity-70">
              <div className="flex items-center mr-3">
                <Clock size={10} className="mr-1" />
                <span>刚刚在线</span>
              </div>
              <div className="flex items-center">
                <Star size={10} className="mr-1" fill="currentColor" />
                <span>Lv.5</span>
              </div>
            </div>
          </div>
        </div>

        {/* Task Status Indicator */}
        <div className="relative mr-2">
          <button
            onClick={() => setShowTaskList(!showTaskList)}
            className="p-2 rounded-full hover:bg-white hover:bg-opacity-10 relative"
            aria-label="创作任务"
          >
            <Clapperboard size={22} />
            {ongoingTaskCount > 0 && (
              <span className="absolute top-0 right-0 block h-4 w-4 rounded-full ring-2 ring-white bg-red-500 text-white text-[10px] leading-tight text-center animate-pulse">
                {ongoingTaskCount}
              </span>
            )}
            {ongoingTaskCount === 0 && completedTasks.length > 0 && !showTaskList && (
              <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-green-400 ring-1 ring-white"></span>
            )}
          </button>

          {/* Task List Dropdown */}
          {showTaskList && (
            <div className="absolute top-full right-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 z-30 text-gray-800">
              <div className="p-3 border-b border-gray-100 flex justify-between items-center">
                <h3 className="text-sm font-semibold">创作任务列表</h3>
                <button 
                  onClick={() => setShowTaskList(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={16} />
                </button>
              </div>
              <div className="max-h-60 overflow-y-auto text-sm p-2 space-y-2">
                {Object.keys(activeTasks).length === 0 && 
                  <p className="text-xs text-gray-500 p-2 text-center">暂无创作任务</p>
                }

                {/* Active Tasks */}
                {Object.values(activeTasks).filter(t => t.status === 'generating').map(task => (
                  <div key={task.id} className="p-2 rounded bg-gray-50 hover:bg-gray-100 transition-colors">
                    <div className="flex items-center mb-1">
                      {task.type === 'image' 
                        ? <ImageIcon size={14} className="mr-2 text-blue-500"/> 
                        : <Film size={14} className="mr-2 text-purple-500"/>
                      }
                      <span className="font-medium text-xs flex-1 truncate">{task.description}</span>
                      <span className="text-xs text-gray-500">{task.progress}%</span>
                    </div>
                    <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 transition-all duration-300" 
                        style={{width: `${task.progress}%`}}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-400 mt-1 text-right">{formatTimeRemaining(task.timeRemaining)}</p>
                  </div>
                ))}

                {/* Completed/Failed Tasks */}
                {completedTasks.map(task => (
                  <div 
                    key={task.id} 
                    className={`p-2 rounded transition-colors ${
                      task.status === 'failed' 
                        ? 'bg-red-50 hover:bg-red-100' 
                        : 'bg-green-50 hover:bg-green-100'
                    }`}
                  >
                    <div className="flex items-center mb-1">
                      {task.status === 'completed' 
                        ? <CheckCircle size={14} className="mr-2 text-green-500"/> 
                        : <AlertCircle size={14} className="mr-2 text-red-500"/>
                      }
                      <span className="font-medium text-xs flex-1 truncate">{task.description}</span>
                      <span className={`text-xs font-semibold ${
                        task.status === 'failed' ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {task.status === 'completed' ? '已完成' : '失败'}
                      </span>
                    </div>
                    {task.status === 'completed' && (
                      <button
                        onClick={() => handleLocateMessage(task.messageId)}
                        className="mt-1 w-full text-center text-xs text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 py-1 rounded border border-indigo-100 flex items-center justify-center transition-colors"
                      >
                        <Search size={12} className="mr-1"/> 在聊天中查看
                      </button>
                    )}
                    {task.status === 'failed' && 
                      <p className="text-xs text-red-500 mt-1">{task.error || '生成失败，请稍后再试。'}</p>
                    }
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <button className="p-1 rounded-full hover:bg-white hover:bg-opacity-10 ml-1">
          <MoreHorizontal size={24} />
        </button>
      </div>

      {/* Messages Area */}
      <div 
        ref={chatContainerRef} 
        className="flex-1 overflow-y-auto p-4 bg-gray-100 space-y-3 scroll-smooth"
      >
        {messages.map((message) => (
          <div
            key={message.id}
            id={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} ${
              highlightedMessageId === message.id ? 'message-highlight' : ''
            }`}
          >
            <div className={`max-w-[80%] ${message.sender === 'user' ? 'order-2 ml-2' : 'order-1 mr-2'}`}>
              {message.sender === 'ai' && (
                <div className="h-8 w-8 rounded-full bg-purple-400 flex items-center justify-center mb-1 shrink-0">
                  <span className="text-white text-sm font-bold">K</span>
                </div>
              )}
              <div className={`p-3 rounded-lg ${
                message.sender === 'user' 
                  ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white' 
                  : 'bg-white text-gray-800'
              } shadow-sm relative`}>
                <p className="text-sm">{message.content}</p>

                {/* Image Offer */}
                {message.offerPainting && message.isOfferPending && !message.offerBothOptions && (
                  <div className="mt-2 bg-white p-2 rounded-lg shadow-md border border-indigo-100 animate-fadeIn">
                    <p className="text-sm text-gray-600 mb-2">{message.offerText}</p>
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => handleAcceptImageOffer(message.id, message.estimatedImageCost)}
                        className="flex-1 py-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-sm rounded-lg flex items-center justify-center"
                      >
                        <Palette size={14} className="mr-1" /> 召唤次元画笔 ({message.estimatedImageCost}币)
                      </button>
                      <button 
                        onClick={() => handleDeclineImageOffer(message.id)}
                        className="flex-1 py-2 bg-gray-100 text-gray-600 text-sm rounded-lg"
                      >
                        暂不需要
                      </button>
                    </div>
                  </div>
                )}

                {/* Video Offer */}
                {message.offerVideo && message.isVideoOfferPending && !message.offerBothOptions && (
                  <div className="mt-2 bg-white p-2 rounded-lg shadow-md border border-purple-100 animate-fadeIn">
                    <p className="text-sm text-gray-600 mb-2">{message.offerVideoText}</p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleAcceptVideoOffer(message.id, message.estimatedVideoCost)}
                        className="flex-1 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm rounded-lg flex items-center justify-center"
                      >
                        <Film size={14} className="mr-1" /> 召唤梦境织机 ({message.estimatedVideoCost}币)
                      </button>
                      <button
                        onClick={() => handleDeclineVideoOffer(message.id)}
                        className="flex-1 py-2 bg-gray-100 text-gray-600 text-sm rounded-lg"
                      >
                        下次再说
                      </button>
                    </div>
                  </div>
                )}

                {/* Combined Offer (Both Image and Video) */}
                {message.offerBothOptions && (
                  <div className="mt-2 bg-white p-2 rounded-lg shadow-md border border-indigo-100 animate-fadeIn">
                    <p className="text-sm text-gray-600 mb-2">{message.offerText}</p>
                    <div className="grid grid-cols-2 gap-2 mb-2">
                      <button 
                        onClick={() => handleAcceptImageOffer(message.id, message.estimatedImageCost)}
                        className="py-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-sm rounded-lg flex items-center justify-center"
                      >
                        <Palette size={14} className="mr-1" /> 生成次元画 ({message.estimatedImageCost}币)
                      </button>
                      <button
                        onClick={() => handleAcceptVideoOffer(message.id, message.estimatedVideoCost)}
                        className="py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm rounded-lg flex items-center justify-center"
                      >
                        <Film size={14} className="mr-1" /> 生成梦境织机 ({message.estimatedVideoCost}币)
                      </button>
                    </div>
                    <div className="text-center">
                      <button 
                        onClick={() => handleDeclineImageOffer(message.id)}
                        className="px-4 py-1 text-xs text-gray-500 hover:text-gray-700 hover:underline"
                      >
                        暂不需要
                      </button>
                    </div>
                  </div>
                )}

                {/* Image Generating Indicator */}
                {message.isGenerating && (
                  <div className="mt-2 bg-white p-3 rounded-lg shadow-md border border-indigo-100">
                    <div className="flex items-center mb-1">
                      <div className="w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mr-2 animate-pulse">
                        <Palette size={12} className="text-indigo-600" />
                      </div>
                      <p className="text-xs font-medium text-gray-700">次元画笔绘制中...</p>
                    </div>
                    <div className="w-full h-1.5 bg-gray-100 rounded-full overflow-hidden">
                      {/* Progress bar will be updated by the task system */}
                      <div 
                        className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 transition-all duration-300"
                        style={{ 
                          width: `${
                            message.generationTaskId && activeTasks[message.generationTaskId] 
                              ? activeTasks[message.generationTaskId].progress 
                              : 0
                          }%` 
                        }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Video Generating Indicator */}
                {message.isVideoGenerating && (
                  <div className="mt-2 bg-white p-3 rounded-lg shadow-md border border-purple-100">
                    <div className="flex items-center mb-1">
                      <div className="w-5 h-5 rounded-full bg-purple-100 flex items-center justify-center mr-2 animate-pulse">
                        <Film size={12} className="text-purple-600" />
                      </div>
                      <p className="text-xs font-medium text-gray-700">梦境织机施法中...</p>
                    </div>
                    <div className="w-full h-1.5 bg-gray-100 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300"
                        style={{ 
                          width: `${
                            message.generationTaskId && activeTasks[message.generationTaskId] 
                              ? activeTasks[message.generationTaskId].progress 
                              : 0
                          }%` 
                        }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Image Result */}
                {message.image && (
                  <div className="mt-3 space-y-2">
                    <p className="text-xs text-gray-500">🎨 次元画笔作品：</p>
                    <div
                      className="rounded-lg overflow-hidden cursor-pointer relative group"
                      onClick={() => handleImageClick(message.image)}
                    >
                      <div className={`w-full h-48 ${
                        message.image.url === 'sunset-beach-gradient' 
                          ? 'bg-gradient-to-b from-amber-400 via-orange-500 to-indigo-800' 
                          : message.image.url === 'mountain-gradient'
                            ? 'bg-gradient-to-b from-blue-400 via-purple-500 to-indigo-800'
                            : 'bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-500'
                      } flex items-center justify-center relative`}>
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300 flex items-center justify-center">
                          <Maximize size={30} className="text-white text-opacity-0 group-hover:text-opacity-100 drop-shadow-lg transition-opacity duration-300" />
                        </div>
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-0.5 rounded">
                          次元画笔
                        </div>
                      </div>
                    </div>
                    
                    {/* Image Action Buttons */}
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => alert('已点赞！')}
                        className="flex-1 py-2 px-3 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-700 text-sm font-medium flex items-center justify-center transition-colors"
                      >
                        <ThumbsUp size={14} className="mr-1" /> 点赞
                      </button>
                      <button 
                        onClick={() => alert('跳转到次元画笔页面')}
                        className="flex-1 py-2 px-3 bg-indigo-50 hover:bg-indigo-100 text-indigo-600 rounded-lg text-sm font-medium flex items-center justify-center transition-colors"
                      >
                        <Palette size={14} className="mr-1" /> 查看次元画笔
                      </button>
                    </div>
                  </div>
                )}

                {/* Video Result */}
                {message.video && (
                  <div className="mt-3 space-y-2">
                    <p className="text-xs text-gray-500">🎬 梦境织机作品：</p>
                    <div
                      className="rounded-lg overflow-hidden cursor-pointer relative group bg-black"
                      onClick={() => handleVideoPlayClick(message.video)}
                    >
                      <div className={`w-full h-48 ${
                        message.video.coverUrl === 'sunset-beach-gradient' 
                          ? 'bg-gradient-to-b from-amber-400 via-orange-500 to-indigo-800' 
                          : 'bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-500'
                      } flex items-center justify-center relative`}>
                        <div className="absolute inset-0 bg-black bg-opacity-30 group-hover:bg-opacity-50 transition-opacity duration-300 flex items-center justify-center">
                          <Play size={36} className="text-white text-opacity-80 drop-shadow-lg transform scale-100 group-hover:scale-110 transition-transform duration-300" />
                        </div>
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-0.5 rounded">
                          {message.video.duration || '0:15'}
                        </div>
                      </div>
                    </div>
                    
                    {/* Video Action Buttons */}
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => alert('已点赞！')} 
                        className="flex-1 py-2 px-3 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-700 text-sm font-medium flex items-center justify-center transition-colors"
                      >
                        <ThumbsUp size={14} className="mr-1" /> 点赞
                      </button>
                      <button 
                        onClick={() => alert('跳转到梦境织机页面')} 
                        className="flex-1 py-2 px-3 bg-purple-50 hover:bg-purple-100 text-purple-600 rounded-lg text-sm font-medium flex items-center justify-center transition-colors"
                      >
                        <Film size={14} className="mr-1" /> 查看梦境织机
                      </button>
                    </div>
                  </div>
                )}

                {/* Declined Messages */}
                {message.offerDeclined && (
                  <div className="mt-1 text-xs text-gray-500 text-center">
                    已取消绘制
                  </div>
                )}
                {message.offerVideoDeclined && (
                  <div className="mt-1 text-xs text-gray-500 text-center">
                    已取消视频创作
                  </div>
                )}

                {/* Timestamp */}
                <div className="text-right mt-1">
                  <span className={`text-xs ${
                    message.sender === 'user' ? 'text-white text-opacity-70' : 'text-gray-500'
                  }`}>
                    {message.time}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Image Preview Modal */}
      {showImagePreview && previewedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex flex-col z-50 animate-fadeIn">
          <div className="p-4 flex items-center text-white">
            <button 
              onClick={() => setShowImagePreview(false)} 
              className="p-2 hover:bg-white hover:bg-opacity-10 rounded-full transition-colors"
              aria-label="关闭预览"
            >
              <X size={24} />
            </button>
            <h2 className="text-lg font-medium ml-2">次元画笔作品</h2>
          </div>
          
          <div className="flex-1 flex items-center justify-center p-4">
            <div 
              className={`w-full max-w-md h-80 ${
                previewedImage.url === 'sunset-beach-gradient' 
                  ? 'bg-gradient-to-b from-amber-400 via-orange-500 to-indigo-800' 
                  : previewedImage.url === 'mountain-gradient'
                    ? 'bg-gradient-to-b from-blue-400 via-purple-500 to-indigo-800'
                    : 'bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-500'
              } rounded-lg shadow-2xl`}
            ></div>
          </div>
          
          <div className="p-4 bg-black bg-opacity-30">
            <div className="flex justify-around">
              <button 
                onClick={handleShareImage}
                className="flex flex-col items-center transition-transform hover:scale-110"
              >
                <div className="w-12 h-12 rounded-full bg-white bg-opacity-10 hover:bg-opacity-20 flex items-center justify-center mb-1 transition-colors">
                  <Share size={20} className="text-white" />
                </div>
                <span className="text-xs text-white">分享</span>
              </button>
              
              <button 
                onClick={handleDownloadImage}
                className="flex flex-col items-center transition-transform hover:scale-110"
              >
                <div className="w-12 h-12 rounded-full bg-white bg-opacity-10 hover:bg-opacity-20 flex items-center justify-center mb-1 transition-colors">
                  <Download size={20} className="text-white" />
                </div>
                <span className="text-xs text-white">保存</span>
              </button>
              
              <button 
                onClick={handleSetAsBackground}
                className="flex flex-col items-center transition-transform hover:scale-110"
              >
                <div className="w-12 h-12 rounded-full bg-white bg-opacity-10 hover:bg-opacity-20 flex items-center justify-center mb-1 transition-colors">
                  <MessageCircle size={20} className="text-white" />
                </div>
                <span className="text-xs text-white">设为背景</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Video Player Modal */}
      {showVideoPlayer && playingVideo && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex flex-col items-center justify-center z-50 animate-fadeIn p-4">
          <div className="w-full max-w-md bg-black rounded-lg overflow-hidden shadow-xl">
            <div className="p-2 flex justify-between items-center">
              <h3 className="text-sm font-medium text-white ml-2">梦境织机作品</h3>
              <button 
                onClick={() => setShowVideoPlayer(false)} 
                className="text-white opacity-70 hover:opacity-100 p-1 rounded-full hover:bg-white hover:bg-opacity-10"
                aria-label="关闭播放器"
              >
                <X size={20} />
              </button>
            </div>
            
            {/* Video Player Placeholder */}
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <div className={`w-full h-full ${
                playingVideo.coverUrl === 'sunset-beach-gradient' 
                  ? 'bg-gradient-to-b from-amber-400 via-orange-500 to-indigo-800' 
                  : 'bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-500'
              } flex items-center justify-center relative`}>
                <Play size={64} className="text-white text-opacity-70 animate-pulse" />
                <div className="absolute bottom-4 left-4 right-4 flex items-center">
                  <button className="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-2">
                    <Play size={16} fill="white" />
                  </button>
                  <div className="flex-1 h-1 bg-white bg-opacity-20 rounded-full overflow-hidden">
                    <div className="h-full bg-white w-1/3"></div>
                  </div>
                  <span className="text-xs text-white ml-2">0:05 / 0:15</span>
                </div>
              </div>
            </div>
            
            {/* Video Actions */}
            <div className="p-3 flex justify-around bg-gray-900 bg-opacity-50">
              <button 
                onClick={() => alert('分享视频')} 
                className="flex flex-col items-center text-white text-xs opacity-80 hover:opacity-100 transition-opacity"
              >
                <Share size={18} className="mb-1"/> 分享
              </button>
              <button 
                onClick={() => alert('保存视频')} 
                className="flex flex-col items-center text-white text-xs opacity-80 hover:opacity-100 transition-opacity"
              >
                <Download size={18} className="mb-1"/> 保存
              </button>
              <button 
                onClick={() => {
                  alert('已添加到收藏');
                  setShowVideoPlayer(false);
                }} 
                className="flex flex-col items-center text-white text-xs opacity-80 hover:opacity-100 transition-opacity"
              >
                <Star size={18} className="mb-1"/> 收藏
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="p-2 bg-white border-t border-gray-200 z-10">
        <div className="flex items-end">
          <div className="flex space-x-1 text-gray-500 px-1">
            <button className="p-1 rounded-full hover:bg-gray-100 transition-colors">
              <Smile size={22} />
            </button>
            <button className="p-1 rounded-full hover:bg-gray-100 transition-colors">
              <Camera size={22} />
            </button>
          </div>
          <div className="flex-1 border border-gray-300 rounded-full bg-gray-100 px-4 py-2 focus-within:ring-1 focus-within:ring-indigo-500 focus-within:border-indigo-500">
            <textarea
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="说点什么..."
              className="w-full bg-transparent outline-none resize-none max-h-32 text-sm"
              rows={1}
            />
          </div>
          <div className="ml-2">
            {inputValue.trim() ? (
              <button 
                className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white flex items-center justify-center hover:shadow-md transition transform hover:scale-105"
                onClick={handleSend}
                aria-label="发送"
              >
                <Send size={18} />
              </button>
            ) : (
              <button 
                className="w-10 h-10 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center hover:bg-gray-300 transition-colors"
                aria-label="语音输入"
              >
                <Mic size={18} />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Animations */}
      <style jsx>{`
        /* Message Highlight Animation */
        .message-highlight {
          animation: highlight-bg 2s ease-out;
        }
        @keyframes highlight-bg {
          0% { background-color: rgba(129, 140, 248, 0); }
          30% { background-color: rgba(129, 140, 248, 0.3); }
          100% { background-color: rgba(129, 140, 248, 0); }
        }

        /* FadeIn Animation */
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }

        /* Pulse Animation */
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
        .animate-pulse {
          animation: pulse 1.5s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default EnhancedAIChatInterface;