import React, { useState, useEffect } from 'react';
import { ChevronLeft, Image as ImageIcon, Share, Download, RefreshCw, ThumbsUp, ThumbsDown, 
  Star, Camera, PenTool, User, Zap, MessageCircle, Wand2, Copy, Sparkles, 
  PlayCircle, CheckCircle, X, ArrowRight, Palette, FileText, RotateCcw, Video, Clock, Upload } from 'lucide-react';

const OptimizedDimensionBrush = ({ onBack, character = { name: "赛博侦探 K", avatar: "/api/placeholder/100/100" } }) => {
  // 状态管理
  const [currentStep, setCurrentStep] = useState('mode-selection'); 
  const [selectedMode, setSelectedMode] = useState(null);
  const [promptText, setPromptText] = useState('');
  const [generatedImage, setGeneratedImage] = useState(null);
  const [style, setStyle] = useState('custom');
  const [sceneTag, setSceneTag] = useState('');
  const [moodTag, setMoodTag] = useState('');
  const [includeUser, setIncludeUser] = useState(false);
  const [isGeneratingPrompt, setIsGeneratingPrompt] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [videoWatched, setVideoWatched] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [freeGenerations, setFreeGenerations] = useState(1);
  const [imageOptions, setImageOptions] = useState([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [feedbackGiven, setFeedbackGiven] = useState(false);
  const [showSelfieUpload, setShowSelfieUpload] = useState(false);
  
  // 场景标签和氛围标签
  const sceneTags = ['夜晚', '咖啡馆', '图书馆', '雨天', '海边', '科幻城市', '森林', '办公室', '学校'];
  const moodTags = ['温馨', '神秘', '酷炫', '搞笑', '浪漫', '紧张', '平静', '忧郁'];
  
  // 样式选项
  const styles = [
    { id: 'custom', name: '自定义', color: 'bg-gradient-to-br from-gray-300 to-gray-500' },
    { id: 'anime', name: '动漫风', color: 'bg-gradient-to-br from-blue-400 to-indigo-500' },
    { id: 'cute', name: 'Q版萌系', color: 'bg-gradient-to-br from-green-400 to-teal-500' },
    { id: 'realistic', name: '写实感', color: 'bg-gradient-to-br from-yellow-400 to-orange-500' },
    { id: 'random', name: '随机风格', color: 'bg-gradient-to-br from-purple-400 to-pink-500' }
  ];
  
  // 结果图片生成
  useEffect(() => {
    if (currentStep === 'result' && !generatedImage) {
      setImageOptions([1, 2, 3, 4]);
      setGeneratedImage(1);
    }
  }, [currentStep]);
  
  // 生成进度模拟
  useEffect(() => {
    if (currentStep === 'generating') {
      const interval = setInterval(() => {
        setLoadingProgress(prev => {
          const newProgress = prev + 5;
          if (newProgress >= 100) {
            clearInterval(interval);
            setTimeout(() => setCurrentStep('result'), 500);
            return 100;
          }
          return newProgress;
        });
      }, 200);
      
      return () => clearInterval(interval);
    }
  }, [currentStep]);
  
  // 生成AI提示词
  const generateAIPrompt = () => {
    setIsGeneratingPrompt(true);
    
    setTimeout(() => {
      let generatedPrompt = '';
      
      if (selectedMode === 'scene') {
        if (includeUser) {
          generatedPrompt = `${character.name}与用户在${sceneTag || '城市街头'}一起${moodTag ? moodTag + '地' : ''}互动，${style === 'custom' ? '独特风格' : style === 'random' ? '随机风格' : style === 'anime' ? '动漫风格' : style === 'cute' ? 'Q版可爱风格' : '写实风格'}`;
        } else {
          generatedPrompt = `${character.name}在${sceneTag || '神秘的实验室'}中${moodTag ? '呈现' + moodTag + '的氛围' : '专注地工作'}，周围环境细节丰富，灯光效果突出，${style === 'custom' ? '独特风格' : style === 'random' ? '随机风格' : style === 'anime' ? '动漫风格' : style === 'cute' ? 'Q版可爱风格' : '写实风格'}`;
        }
      } else if (selectedMode === 'freestyle') {
        generatedPrompt = `创意十足的${character.name}形象，具有独特的个人风格，背景充满未来感，色彩鲜明，${style === 'custom' ? '独特风格' : style === 'random' ? '随机风格' : style === 'anime' ? '动漫风格' : style === 'cute' ? 'Q版可爱风格' : '写实风格'}`;
      } else if (selectedMode === 'portrait') {
        generatedPrompt = `根据聊天记忆中用户的描述，绘制一幅用户的肖像，风格符合${character.name}的审美，背景简约但有个性，${style === 'random' ? '随机风格' : style === 'anime' ? '动漫风格' : style === 'cute' ? 'Q版可爱风格' : '写实风格'}`;
      }
      
      setPromptText(generatedPrompt);
      setIsGeneratingPrompt(false);
    }, 1500);
  };
  
  // 润色提示词
  const enhancePrompt = () => {
    setIsGeneratingPrompt(true);
    
    setTimeout(() => {
      const enhancedPrompt = promptText + "，高品质，精细的细节，和谐的构图，生动的表情，富有层次感的光影效果";
      setPromptText(enhancedPrompt);
      setIsGeneratingPrompt(false);
    }, 1500);
  };
  
  // 生成图片
  const startGeneration = () => {
    if (freeGenerations > 0) {
      setFreeGenerations(prev => prev - 1);
      setCurrentStep('generating');
      setLoadingProgress(0);
    } else {
      watchIncentiveVideo();
    }
  };
  
  // 处理激励视频
  const watchIncentiveVideo = () => {
    const hasError = Math.random() < 0.3;
    
    if (hasError) {
      setVideoError(true);
      setShowVideoModal(true);
    } else {
      setVideoWatched(true);
      
      setTimeout(() => {
        setCurrentStep('generating');
        setLoadingProgress(0);
      }, 2000);
    }
  };
  
  const retryVideo = () => {
    setVideoError(false);
    setShowVideoModal(false);
    setTimeout(watchIncentiveVideo, 500);
  };
  
  const selectImage = (index) => {
    setSelectedImageIndex(index);
    setGeneratedImage(index);
  };
  
  const shareImage = () => {
    alert('分享功能将在此实现，将图片分享到社交媒体');
  };
  
  const saveImage = () => {
    alert('保存功能将在此实现，将图片保存到设备');
  };
  
  const tryAgain = () => {
    setCurrentStep('prompt-creation');
  };
  
  const startOver = () => {
    setCurrentStep('mode-selection');
    setSelectedMode(null);
    setPromptText('');
    setGeneratedImage(null);
    setStyle('custom');
    setSceneTag('');
    setMoodTag('');
    setIncludeUser(false);
    setShowSelfieUpload(false);
    setFeedbackGiven(false);
  };
  
  const handleSelfieUploadToggle = () => {
    setShowSelfieUpload(!showSelfieUpload);
  };
  
  // 角色头像组件
  const CharacterAvatar = ({ size = "md", className = "" }) => {
    const sizeClasses = {
      sm: "w-8 h-8",
      md: "w-12 h-12",
      lg: "w-16 h-16",
    };
    
    return (
      <div className={`rounded-full bg-purple-400 flex items-center justify-center ${sizeClasses[size]} ${className}`}>
        <span className="text-white font-bold">{character.name.charAt(0)}</span>
      </div>
    );
  };
  
  // 背景效果
  const MemoryParticles = () => (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {[...Array(15)].map((_, i) => (
        <div 
          key={i}
          className="absolute bg-indigo-400 rounded-full opacity-20 animate-pulse"
          style={{
            width: `${Math.random() * 8 + 4}px`,
            height: `${Math.random() * 8 + 4}px`,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animationDuration: `${Math.random() * 8 + 2}s`,
            animationDelay: `${Math.random() * 5}s`
          }}
        />
      ))}
    </div>
  );

  // 图片占位符组件
  const PlaceholderImage = ({ className = "", aspectRatio = "1/1", gradientColor = "from-indigo-400 to-purple-500" }) => (
    <div 
      className={`rounded-lg overflow-hidden bg-gradient-to-br ${gradientColor} flex items-center justify-center ${className}`}
      style={{ aspectRatio }}
    >
      <ImageIcon className="text-white opacity-40" size={24} />
    </div>
  );

  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg relative">
      <MemoryParticles />
      
      {/* 页头 */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-4 shadow-md relative z-10">
        <div className="flex items-center justify-between">
          <button 
            onClick={onBack} 
            className="p-2 rounded-full hover:bg-white hover:bg-opacity-20"
          >
            <ChevronLeft size={20} />
          </button>
          <h1 className="text-lg font-bold flex items-center">
            <Palette size={18} className="mr-2" />
            次元画笔
          </h1>
          <div className="w-8"></div>
        </div>
      </div>
      
      {/* 主要内容区域 */}
      <div className="flex-1 overflow-auto relative">
        {currentStep === 'mode-selection' && (
          <div className="p-4 space-y-6">
            <div className="bg-white rounded-lg p-4 shadow-sm text-center">
              <div className="w-16 h-16 mx-auto bg-indigo-100 rounded-full flex items-center justify-center mb-2">
                <Wand2 size={24} className="text-indigo-600" />
              </div>
              <h2 className="text-lg font-bold text-gray-800">开启创作之旅</h2>
              <p className="text-sm text-gray-600 mt-1">选择一种创作模式，与 {character.name} 一起创造独特画作</p>
              
              {freeGenerations > 0 && (
                <div className="mt-2 inline-flex items-center px-3 py-1 bg-amber-100 text-amber-700 rounded-full text-xs">
                  <Sparkles size={12} className="mr-1" />
                  您还有 {freeGenerations} 次免费创作机会
                </div>
              )}
            </div>
            
            {/* 模式选择按钮组 */}
            <div className="space-y-3">
              {/* 描绘瞬间模式 - 更清晰地说明会包含角色 */}
              <button
                onClick={() => {
                  setSelectedMode('scene');
                  setStyle('custom');
                  setCurrentStep('prompt-creation');
                }}
                className="w-full bg-white rounded-lg p-4 shadow-sm flex items-center hover:shadow-md transition-shadow"
              >
                <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                  <ImageIcon size={22} className="text-indigo-600" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-semibold text-gray-800">描绘 {character.name} 的瞬间</h3>
                  <p className="text-xs text-gray-500 mt-1">创作各种场景中的 {character.name}，捕捉精彩时刻</p>
                </div>
                <ChevronLeft size={18} className="transform rotate-180 text-gray-400" />
              </button>
              
              {/* 自由创作模式 */}
              <button
                onClick={() => {
                  setSelectedMode('freestyle');
                  setStyle('custom');
                  setCurrentStep('prompt-creation');
                }}
                className="w-full bg-white rounded-lg p-4 shadow-sm flex items-center hover:shadow-md transition-shadow"
              >
                <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center mr-4">
                  <PenTool size={22} className="text-pink-600" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-semibold text-gray-800">自由创作</h3>
                  <p className="text-xs text-gray-500 mt-1">完全释放想象力，创作不受限的画面</p>
                </div>
                <ChevronLeft size={18} className="transform rotate-180 text-gray-400" />
              </button>
              
              {/* 记忆画像模式 */}
              <button
                onClick={() => {
                  setSelectedMode('portrait');
                  setStyle('random');
                  setCurrentStep('prompt-creation');
                }}
                className="w-full bg-white rounded-lg p-4 shadow-sm flex items-center hover:shadow-md transition-shadow"
              >
                <div className="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center mr-4">
                  <User size={22} className="text-teal-600" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-semibold text-gray-800">记忆画像</h3>
                  <p className="text-xs text-gray-500 mt-1">让 {character.name} 根据聊天记忆为您绘制专属画像</p>
                </div>
                <ChevronLeft size={18} className="transform rotate-180 text-gray-400" />
              </button>
            </div>
            
            <div className="bg-gray-100 rounded-lg p-3 flex items-start">
              <Zap size={18} className="text-amber-500 mt-0.5 mr-2 flex-shrink-0" />
              <p className="text-xs text-gray-600">
                每一幅画作都是 {character.name} 为您独家创作的，您可以分享、保存并在社交媒体上展示！
              </p>
            </div>
          </div>
        )}
        
        {currentStep === 'prompt-creation' && selectedMode === 'scene' && (
          <div className="p-4 space-y-5">
            {/* 角色头像和提示信息 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center">
                <CharacterAvatar size="lg" className="mr-4" />
                <div>
                  <h2 className="font-bold text-gray-800 flex items-center mb-1">
                    <ImageIcon size={16} className="mr-2 text-indigo-600" />
                    描绘 {character.name} 的瞬间
                  </h2>
                  <p className="text-sm text-gray-600">将使用 {character.name} 作为场景主角创建画面</p>
                </div>
              </div>
              
              
              {/* 风格选择 */}
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">选择风格：</p>
                <div className="grid grid-cols-4 gap-2">
                  {styles.map(styleOption => (
                    <div 
                      key={styleOption.id}
                      onClick={() => setStyle(styleOption.id)}
                      className={`flex flex-col items-center p-2 rounded-lg cursor-pointer ${style === styleOption.id ? 'bg-indigo-100 ring-2 ring-indigo-500' : 'bg-gray-50'}`}
                    >
                      <div className={`w-12 h-12 rounded-full overflow-hidden mb-1 ${styleOption.color}`}>
                      </div>
                      <span className="text-xs font-medium">{styleOption.name}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* 包含用户选项 */}
              <div className="mt-4 bg-indigo-50 rounded-lg p-3">
                <div className="flex items-start">
                  <div 
                    className={`w-5 h-5 rounded-md mr-2 mt-0.5 flex-shrink-0 cursor-pointer ${includeUser ? 'bg-indigo-600' : 'bg-white border border-gray-300'} flex items-center justify-center`} 
                    onClick={() => setIncludeUser(!includeUser)}
                  >
                    {includeUser && <CheckCircle size={14} className="text-white" />}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-indigo-800">在场景中与 {character.name} 合影</p>
                    <p className="text-xs text-indigo-600 mt-1">选中此项后，画面中将包含您与角色的互动</p>
                  </div>
                </div>
              </div>
              
              {/* 自拍上传选项 */}
              {includeUser && (
                <div className="mt-4 bg-white border border-indigo-200 rounded-lg p-3">
                  <div className="flex justify-between items-center mb-2">
                    <p className="text-sm font-medium text-gray-700 flex items-center">
                      <Camera size={16} className="text-indigo-500 mr-1" />
                      上传自拍（可选）
                    </p>
                    <button 
                      onClick={handleSelfieUploadToggle}
                      className="text-xs text-indigo-600 flex items-center"
                    >
                      {showSelfieUpload ? '取消' : '上传'}
                    </button>
                  </div>
                  
                  {showSelfieUpload && (
                    <div className="mt-2">
                      <div className="border-2 border-dashed border-indigo-200 rounded-lg p-4 flex flex-col items-center justify-center bg-indigo-50">
                        <Upload size={24} className="text-indigo-400 mb-2" />
                        <p className="text-xs text-indigo-600 text-center">
                          点击上传自拍照片<br />或拖拽文件至此处
                        </p>
                        <button className="mt-3 px-3 py-1.5 bg-indigo-600 text-white text-xs rounded-lg flex items-center">
                          <Camera size={12} className="mr-1" />
                          选择照片
                        </button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1 text-center">
                        照片仅用于此次创作，不会被保存
                      </p>
                    </div>
                  )}
                </div>
              )}
              
              {/* 场景标签 */}
              <div className="mt-4">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm text-gray-600">场景标签：</p>
                  <span className="text-xs text-gray-500">非必选</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {sceneTags.map((tag) => (
                    <span
                      key={tag}
                      onClick={() => setSceneTag(tag)}
                      className={`px-3 py-1.5 rounded-full text-xs cursor-pointer ${
                        sceneTag === tag 
                          ? 'bg-indigo-100 text-indigo-700 ring-1 ring-indigo-300' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
              
              {/* 氛围标签 */}
              <div className="mt-4">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm text-gray-600">氛围标签：</p>
                  <span className="text-xs text-gray-500">非必选</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {moodTags.map((tag) => (
                    <span
                      key={tag}
                      onClick={() => setMoodTag(tag)}
                      className={`px-3 py-1.5 rounded-full text-xs cursor-pointer ${
                        moodTag === tag 
                          ? 'bg-indigo-100 text-indigo-700 ring-1 ring-indigo-300' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
              
              {/* 提示词输入区域 */}
              <div className="mt-4">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm text-gray-600">描述词：</p>
                  <div className="flex items-center">
                    <button 
                      onClick={generateAIPrompt}
                      disabled={isGeneratingPrompt}
                      className={`px-2 py-1 rounded text-xs mr-2 flex items-center ${
                        isGeneratingPrompt ? 'bg-gray-200 text-gray-500' : 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white'
                      }`}
                    >
                      {isGeneratingPrompt ? (
                        <>
                          <div className="mr-1 w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                          生成中
                        </>
                      ) : (
                        <>
                          <Wand2 size={12} className="mr-1" />
                          一键生成
                        </>
                      )}
                    </button>
                    
                    {promptText && (
                      <button 
                        onClick={enhancePrompt}
                        disabled={isGeneratingPrompt}
                        className={`px-2 py-1 rounded text-xs flex items-center ${
                          isGeneratingPrompt ? 'bg-gray-200 text-gray-500' : 'bg-purple-100 text-purple-700'
                        }`}
                      >
                        {isGeneratingPrompt ? (
                          <>
                            <div className="mr-1 w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                            处理中
                          </>
                        ) : (
                          <>
                            <Sparkles size={12} className="mr-1" />
                            润色
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
                
                <textarea
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  className="w-full p-3 border border-gray-200 rounded-lg text-sm min-h-24"
                  placeholder={`描述${character.name}在场景中的状态和动作...${includeUser ? '\n例如：角色在樱花树下与我合影，微笑着...' : '\n例如：角色在雨夜的街道上撑伞行走...'}`}
                />
                
                {promptText && (
                  <div className="flex justify-end mt-1">
                    <button 
                      onClick={() => {
                        navigator.clipboard.writeText(promptText);
                        alert('已复制到剪贴板');
                      }}
                      className="text-xs text-gray-500 flex items-center"
                    >
                      <Copy size={12} className="mr-1" />
                      复制
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            {/* 小技巧提示框 */}
            <div className="bg-indigo-50 rounded-lg p-3">
              <h3 className="text-sm font-medium text-indigo-700 flex items-center mb-1">
                <Zap size={16} className="mr-1" />
                创作小技巧
              </h3>
              <p className="text-xs text-indigo-600">
                {includeUser 
                  ? `描述${character.name}与您互动的场景、表情和动作，能让合影更生动自然！` 
                  : `描述${character.name}的动作、表情和周围环境，会让场景更富有故事性！`}
              </p>
            </div>
            
            {/* 创作按钮区域 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-center mb-3">
                <div className="text-sm text-gray-700">创作方式：</div>
                <div className="flex items-center">
                  {freeGenerations > 0 ? (
                    <div className="text-sm text-amber-600 font-semibold flex items-center">
                      <Sparkles size={14} className="mr-1" />
                      免费创作 ({freeGenerations}次)
                    </div>
                  ) : (
                    <div className="text-sm text-purple-600 font-semibold flex items-center">
                      <PlayCircle size={14} className="mr-1" />
                      观看视频解锁
                    </div>
                  )}
                </div>
              </div>
              
              <button
                onClick={startGeneration}
                disabled={!promptText}
                className={`w-full py-3 rounded-lg font-medium flex items-center justify-center ${
                  promptText 
                    ? freeGenerations > 0 
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white' 
                      : 'bg-gradient-to-r from-amber-500 to-orange-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                }`}
              >
                {freeGenerations > 0 ? (
                  <>
                    <Wand2 size={18} className="mr-2" />
                    开始创作
                  </>
                ) : (
                  <>
                    <PlayCircle size={18} className="mr-2" />
                    观看视频解锁创作
                  </>
                )}
              </button>
              
              <div className="mt-3 text-center">
                <button 
                  onClick={startOver}
                  className="text-xs text-gray-500 flex items-center justify-center mx-auto"
                >
                  <RotateCcw size={12} className="mr-1" />
                  重新选择创作模式
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 自由创作模式界面 */}
        {currentStep === 'prompt-creation' && selectedMode === 'freestyle' && (
          <div className="p-4 space-y-5">
            {/* 角色头像和提示信息 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center">
                <CharacterAvatar size="lg" className="mr-4" />
                <div>
                  <h2 className="font-bold text-gray-800 flex items-center mb-1">
                    <PenTool size={16} className="mr-2 text-pink-600" />
                    自由创作
                  </h2>
                  <p className="text-sm text-gray-600">完全释放想象力，创作不受限的画面</p>
                </div>
              </div>
              
              {/* 风格选择 */}
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">选择风格：</p>
                <div className="grid grid-cols-4 gap-2">
                  {styles.map(styleOption => (
                    <div 
                      key={styleOption.id}
                      onClick={() => setStyle(styleOption.id)}
                      className={`flex flex-col items-center p-2 rounded-lg cursor-pointer ${style === styleOption.id ? 'bg-indigo-100 ring-2 ring-indigo-500' : 'bg-gray-50'}`}
                    >
                      <div className={`w-12 h-12 rounded-full overflow-hidden mb-1 ${styleOption.color}`}>
                      </div>
                      <span className="text-xs font-medium">{styleOption.name}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* 提示词输入区域 */}
              <div className="mt-4">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm text-gray-600">描述词：</p>
                  <div className="flex items-center">
                    <button 
                      onClick={generateAIPrompt}
                      disabled={isGeneratingPrompt}
                      className={`px-2 py-1 rounded text-xs mr-2 flex items-center ${
                        isGeneratingPrompt ? 'bg-gray-200 text-gray-500' : 'bg-gradient-to-r from-pink-500 to-purple-500 text-white'
                      }`}
                    >
                      {isGeneratingPrompt ? (
                        <>
                          <div className="mr-1 w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                          生成中
                        </>
                      ) : (
                        <>
                          <Wand2 size={12} className="mr-1" />
                          一键生成
                        </>
                      )}
                    </button>
                    
                    {promptText && (
                      <button 
                        onClick={enhancePrompt}
                        disabled={isGeneratingPrompt}
                        className={`px-2 py-1 rounded text-xs flex items-center ${
                          isGeneratingPrompt ? 'bg-gray-200 text-gray-500' : 'bg-purple-100 text-purple-700'
                        }`}
                      >
                        {isGeneratingPrompt ? (
                          <>
                            <div className="mr-1 w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                            处理中
                          </>
                        ) : (
                          <>
                            <Sparkles size={12} className="mr-1" />
                            润色
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
                
                <textarea
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  className="w-full p-3 border border-gray-200 rounded-lg text-sm min-h-24"
                  placeholder={`描述您想创作的画面...\n例如：未来科技感的城市景观，高楼林立，霓虹灯闪烁...`}
                />
                
                {promptText && (
                  <div className="flex justify-end mt-1">
                    <button 
                      onClick={() => {
                        navigator.clipboard.writeText(promptText);
                        alert('已复制到剪贴板');
                      }}
                      className="text-xs text-gray-500 flex items-center"
                    >
                      <Copy size={12} className="mr-1" />
                      复制
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            {/* 小技巧提示框 */}
            <div className="bg-pink-50 rounded-lg p-3">
              <h3 className="text-sm font-medium text-pink-700 flex items-center mb-1">
                <Zap size={16} className="mr-1" />
                创作小技巧
              </h3>
              <p className="text-xs text-pink-600">
                尝试描述场景的色彩、光线和氛围，会让创作效果更加出色！您也可以在描述中加入 {character.name} 角色哦。
              </p>
            </div>
            
            {/* 创作按钮区域 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-center mb-3">
                <div className="text-sm text-gray-700">创作方式：</div>
                <div className="flex items-center">
                  {freeGenerations > 0 ? (
                    <div className="text-sm text-amber-600 font-semibold flex items-center">
                      <Sparkles size={14} className="mr-1" />
                      免费创作 ({freeGenerations}次)
                    </div>
                  ) : (
                    <div className="text-sm text-purple-600 font-semibold flex items-center">
                      <PlayCircle size={14} className="mr-1" />
                      观看视频解锁
                    </div>
                  )}
                </div>
              </div>
              
              <button
                onClick={startGeneration}
                disabled={!promptText}
                className={`w-full py-3 rounded-lg font-medium flex items-center justify-center ${
                  promptText 
                    ? freeGenerations > 0 
                      ? 'bg-gradient-to-r from-pink-600 to-purple-600 text-white' 
                      : 'bg-gradient-to-r from-amber-500 to-orange-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                }`}
              >
                {freeGenerations > 0 ? (
                  <>
                    <Wand2 size={18} className="mr-2" />
                    开始创作
                  </>
                ) : (
                  <>
                    <PlayCircle size={18} className="mr-2" />
                    观看视频解锁创作
                  </>
                )}
              </button>
              
              <div className="mt-3 text-center">
                <button 
                  onClick={startOver}
                  className="text-xs text-gray-500 flex items-center justify-center mx-auto"
                >
                  <RotateCcw size={12} className="mr-1" />
                  重新选择创作模式
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 记忆画像模式界面 */}
        {currentStep === 'prompt-creation' && selectedMode === 'portrait' && (
          <div className="p-4 space-y-5">
            {/* 角色头像和提示信息 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center">
                <CharacterAvatar size="lg" className="mr-4" />
                <div>
                  <h2 className="font-bold text-gray-800 flex items-center mb-1">
                    <User size={16} className="mr-2 text-teal-600" />
                    记忆画像
                  </h2>
                  <p className="text-sm text-gray-600">让 {character.name} 根据聊天记忆为您绘制专属画像</p>
                </div>
              </div>
              
              {/* 风格选择 */}
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">选择画像风格：</p>
                <div className="grid grid-cols-4 gap-2">
                  {styles.filter(s => s.id !== 'custom').map(styleOption => (
                    <div 
                      key={styleOption.id}
                      onClick={() => setStyle(styleOption.id)}
                      className={`flex flex-col items-center p-2 rounded-lg cursor-pointer ${style === styleOption.id ? 'bg-indigo-100 ring-2 ring-indigo-500' : 'bg-gray-50'}`}
                    >
                      <div className={`w-12 h-12 rounded-full overflow-hidden mb-1 ${styleOption.color}`}>
                      </div>
                      <span className="text-xs font-medium">{styleOption.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* 记忆提示框 */}
            <div className="bg-teal-50 rounded-lg p-3">
              <h3 className="text-sm font-medium text-teal-700 flex items-center mb-1">
                <Zap size={16} className="mr-1" />
                关于记忆画像
              </h3>
              <p className="text-xs text-teal-600">
                {character.name} 将根据与您的聊天记忆，为您创作专属画像。如果您从未描述过自己的外貌，可以在补充描述中添加。
              </p>
            </div>
            
            {/* 创作按钮区域 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-center mb-3">
                <div className="text-sm text-gray-700">创作方式：</div>
                <div className="flex items-center">
                  {freeGenerations > 0 ? (
                    <div className="text-sm text-amber-600 font-semibold flex items-center">
                      <Sparkles size={14} className="mr-1" />
                      免费创作 ({freeGenerations}次)
                    </div>
                  ) : (
                    <div className="text-sm text-purple-600 font-semibold flex items-center">
                      <PlayCircle size={14} className="mr-1" />
                      观看视频解锁
                    </div>
                  )}
                </div>
              </div>
              
              <button
                onClick={startGeneration}
                className={`w-full py-3 rounded-lg font-medium flex items-center justify-center ${
                  freeGenerations > 0 
                    ? 'bg-gradient-to-r from-teal-600 to-blue-600 text-white' 
                    : 'bg-gradient-to-r from-amber-500 to-orange-500 text-white'
                }`}
              >
                {freeGenerations > 0 ? (
                  <>
                    <Wand2 size={18} className="mr-2" />
                    开始创作画像
                  </>
                ) : (
                  <>
                    <PlayCircle size={18} className="mr-2" />
                    观看视频解锁创作
                  </>
                )}
              </button>
              
              <div className="mt-3 text-center">
                <button 
                  onClick={startOver}
                  className="text-xs text-gray-500 flex items-center justify-center mx-auto"
                >
                  <RotateCcw size={12} className="mr-1" />
                  重新选择创作模式
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* 生成中状态 */}
        {currentStep === 'generating' && (
          <div className="p-4 h-full flex flex-col items-center justify-center">
            <div className="bg-white rounded-lg p-6 shadow-md w-full max-w-xs text-center">
              <div className="w-20 h-20 mx-auto bg-indigo-100 rounded-full flex items-center justify-center mb-4 relative">
                <div className="absolute inset-0 rounded-full border-4 border-indigo-200 border-opacity-50"></div>
                <div 
                  className="absolute inset-0 rounded-full border-4 border-indigo-600 border-t-transparent animate-spin"
                  style={{ 
                    clipPath: `polygon(0 0, 100% 0, 100% ${loadingProgress}%, 0 ${loadingProgress}%)` 
                  }}
                ></div>
                <Palette size={28} className="text-indigo-600" />
              </div>
              
              <h3 className="text-lg font-bold text-gray-800 mb-1">创作进行中...</h3>
              <p className="text-sm text-gray-600 mb-4">
                {character.name} 正在用心为您绘制，请稍候...
              </p>
              
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div 
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 h-2 rounded-full"
                  style={{ width: `${loadingProgress}%` }}
                ></div>
              </div>
              <div className="text-xs text-gray-500 flex items-center justify-center">
                <Clock size={12} className="mr-1" />
                预计剩余时间: {Math.max(0, 10 - Math.floor(loadingProgress / 10))} 秒
              </div>
            </div>
            
            {/* 创作动画效果 */}
            <div className="relative w-full max-w-xs h-40 mt-8">
              {/* 彩色动画粒子 */}
              {[...Array(20)].map((_, i) => (
                <div 
                  key={i}
                  className="absolute rounded-full opacity-70"
                  style={{
                    width: `${Math.random() * 8 + 4}px`,
                    height: `${Math.random() * 8 + 4}px`,
                    backgroundColor: `hsl(${Math.random() * 360}, 80%, 70%)`,
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                    animation: `float ${Math.random() * 3 + 2}s ease-in-out infinite`,
                    animationDelay: `${Math.random() * 2}s`
                  }}
                />
              ))}
            </div>
          </div>
        )}
        
        {/* 结果页面 */}
        {currentStep === 'result' && (
          <div className="p-4 space-y-5">
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-center mb-3">
                <h2 className="font-bold text-gray-800 flex items-center">
                  <Palette size={18} className="mr-2 text-purple-600" />
                  创作完成！
                </h2>
                <div className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full flex items-center">
                  <CheckCircle size={12} className="mr-1" />
                  高品质作品
                </div>
              </div>
              
              {/* 主图展示 */}
              <div className="relative rounded-lg overflow-hidden bg-gray-100 mb-3">
                <PlaceholderImage 
                  aspectRatio="3/4" 
                  gradientColor={`${
                    style === 'anime' ? 'from-blue-400 to-indigo-600' : 
                    style === 'cute' ? 'from-green-400 to-teal-600' : 
                    style === 'realistic' ? 'from-yellow-400 to-orange-600' : 
                    'from-purple-400 to-pink-600'
                  }`}
                  className="w-full h-96"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-white text-opacity-90 text-center">
                    <Sparkles size={36} className="mx-auto mb-2" />
                    <p className="text-lg font-bold">AI创作完成</p>
                    <p className="text-sm">由{character.name}为您倾心创作</p>
                  </div>
                </div>
                <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  由 {character.name} 创作
                </div>
              </div>
              
              {/* 其他图片变体 */}
              {imageOptions.length > 1 && (
                <div>
                  <p className="text-xs text-gray-600 mb-2">其他版本：</p>
                  <div className="flex gap-2 overflow-x-auto pb-2">
                    {imageOptions.map((option, index) => (
                      <div 
                        key={index}
                        onClick={() => selectImage(index)}
                        className={`w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 cursor-pointer ${
                          selectedImageIndex === index ? 'ring-2 ring-purple-500' : ''
                        }`}
                      >
                        <PlaceholderImage 
                          gradientColor={`${
                            style === 'anime' ? 'from-blue-400 to-indigo-600' : 
                            style === 'cute' ? 'from-green-400 to-teal-600' : 
                            style === 'realistic' ? 'from-yellow-400 to-orange-600' : 
                            'from-purple-400 to-pink-600'
                          }`}
                          className="w-full h-full"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* 提示词显示 */}
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-start">
                  <div className="text-xs text-gray-500">创作提示词：</div>
                  <button 
                    onClick={() => {
                      navigator.clipboard.writeText(promptText);
                      alert('已复制到剪贴板');
                    }}
                    className="text-xs text-purple-600 flex items-center"
                  >
                    <Copy size={12} className="mr-1" />
                    复制
                  </button>
                </div>
                <p className="text-sm text-gray-700 mt-1">
                  {promptText || selectedMode === 'portrait' ? 
                    (selectedMode === 'portrait' ? 
                      `根据聊天记忆中用户的描述，绘制一幅用户的肖像，风格符合${character.name}的审美，背景简约但有个性，${style === 'random' ? '随机风格' : style === 'anime' ? '动漫风格' : style === 'cute' ? 'Q版可爱风格' : style === 'realistic' ? '写实风格' : '自定义风格'}` 
                      : promptText) 
                    : '自由创作内容'}
                </p>
              </div>
            </div>
            
            {/* 操作按钮 */}
            <div className="grid grid-cols-2 gap-3 mb-3">
              <button
                onClick={shareImage}
                className="py-3 rounded-lg font-medium bg-gradient-to-r from-indigo-600 to-purple-600 text-white flex items-center justify-center"
              >
                <Share size={18} className="mr-2" />
                分享
              </button>
              
              <button
                onClick={saveImage}
                className="py-3 rounded-lg font-medium bg-purple-100 text-purple-700 flex items-center justify-center"
              >
                <Download size={18} className="mr-2" />
                保存
              </button>
            </div>
            
            {/* 设为聊天背景按钮 */}
            <button
              onClick={() => {
                alert('已将图片设置为聊天背景');
              }}
              className="w-full py-3 rounded-lg mb-3 font-medium bg-indigo-50 text-indigo-700 border border-indigo-200 flex items-center justify-center"
            >
              <MessageCircle size={16} className="mr-2" />
              设为聊天背景
            </button>
            
            {/* 反馈区域 */}
            {!feedbackGiven && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h3 className="text-sm font-medium text-gray-700 mb-2">您喜欢这个作品吗？</h3>
                <div className="flex gap-3">
                  <button
                    onClick={() => setFeedbackGiven(true)}
                    className="flex-1 py-2 rounded-lg bg-green-100 text-green-700 flex items-center justify-center"
                  >
                    <ThumbsUp size={16} className="mr-1" />
                    喜欢
                  </button>
                  
                  <button
                    onClick={() => setFeedbackGiven(true)}
                    className="flex-1 py-2 rounded-lg bg-gray-100 text-gray-700 flex items-center justify-center"
                  >
                    <ThumbsDown size={16} className="mr-1" />
                    不喜欢
                  </button>
                </div>
              </div>
            )}
            
            {/* 再试一次按钮 */}
            <div className="bg-white rounded-lg p-4 shadow-sm space-y-3">
              <button
                onClick={tryAgain}
                className="w-full py-3 rounded-lg font-medium bg-white border border-purple-300 text-purple-700 flex items-center justify-center"
              >
                <RefreshCw size={16} className="mr-2" />
                修改后重试
              </button>
              
              <button
                onClick={startOver}
                className="w-full py-3 rounded-lg font-medium bg-gray-100 text-gray-700 flex items-center justify-center"
              >
                <RotateCcw size={16} className="mr-2" />
                重新选择模式
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* 视频错误弹窗 */}
      {showVideoModal && videoError && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl p-4 w-full max-w-xs">
            <div className="text-center mb-4">
              <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center mb-3">
                <X size={24} className="text-red-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-800">视频加载失败</h3>
              <p className="text-sm text-gray-600 mt-2">
                无法加载激励视频，请检查网络连接并重试。
              </p>
            </div>
            
            <div className="space-y-3">
              <button
                onClick={retryVideo}
                className="w-full py-3 rounded-lg font-medium bg-gradient-to-r from-indigo-600 to-purple-600 text-white flex items-center justify-center"
              >
                <RefreshCw size={18} className="mr-2" />
                重新加载视频
              </button>
              
              <button
                onClick={() => setShowVideoModal(false)}
                className="w-full py-3 rounded-lg font-medium bg-gray-100 text-gray-700"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* 添加浮动动画关键帧 */}
      <style jsx>{`
        @keyframes float {
          0% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
          100% {
            transform: translateY(0px);
          }
        }
      `}</style>
    </div>
  );
};

export default OptimizedDimensionBrush;