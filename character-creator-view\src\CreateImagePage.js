import React, { useState, useEffect, useRef } from 'react';
import { 
  ChevronLeft, Image, Upload, RefreshCw, <PERSON>rkles, 
  Trash2, X, Check, AlertTriangle, Loader, 
  MessageCircle, Clock, ArrowLeft, Camera, Wand2,
  Paintbrush, RotateCw, ImagePlus, Eye, Copy, Plus, Download,
  PenLine, FileImage, ChevronDown, CheckCircle, Settings,Coins, DollarSign, PlayCircle
} from 'lucide-react';

const CreateImagePage = ({ onBack, onImageCreated, characterData, existingAssets = [],userBalance = { partyCoins: 150, starlightCoins: 50 }, 
  preferredPaymentMethod = 'partyCoins' }) => {
  // States for the creation process
  const [step, setStep] = useState('edit'); // 'edit', 'processing', 'complete'
  const [imageDescription, setImageDescription] = useState('');
  const [originalDescription, setOriginalDescription] = useState('');
  const [selectedReferenceImages, setSelectedReferenceImages] = useState([]);
  const [showReferenceSelector, setShowReferenceSelector] = useState(false);
  const [showLibrarySelector, setShowLibrarySelector] = useState(false);
  const [isEnhancingPrompt, setIsEnhancingPrompt] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('waiting'); // 'waiting', 'processing', 'error'
  const [processingProgress, setProcessingProgress] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState('约1分钟');
  const [resultImage, setResultImage] = useState(null);
  
  // New states for enhancement modal
  const [showEnhanceModal, setShowEnhanceModal] = useState(false);
  const [selectedEnhanceStyle, setSelectedEnhanceStyle] = useState(null);
  const [customEnhanceText, setCustomEnhanceText] = useState('');
  const [imageTitle, setImageTitle] = useState('');
  
  // For demo purposes - to simulate processing
  const processingTimer = useRef(null);
  
  // Reference to the description textarea for focusing
  const descriptionRef = useRef(null);
  
  // Enhancement style options
  const enhanceStyles = [
    { id: 'more_detailed', name: '更详细', description: '添加更多细节和描述', icon: <Sparkles size={16} className="text-amber-500" /> },
    { id: 'more_concise', name: '更精简', description: '精炼语言，保持关键内容', icon: <Check size={16} className="text-green-500" /> },
    { id: 'more_dramatic', name: '更戏剧化', description: '增加情感色彩和戏剧元素', icon: <Wand2 size={16} className="text-purple-500" /> },
    { id: 'more_professional', name: '更专业', description: '使用更专业的术语和描述', icon: <CheckCircle size={16} className="text-blue-500" /> },
    { id: 'custom', name: '自定义', description: '按照自定义指令润色', icon: <Settings size={16} className="text-gray-500" /> }
  ];
  
  // Sample inspirations for demo
  const inspirations = [
    "赛博侦探K在繁忙的霓虹街头查看线索，雨水反射着五彩灯光",
    "侦探K在高级咖啡厅悠闲地品尝咖啡，阳光透过窗户洒在桌面",
    "赛博侦探K在城市高楼天台上眺望夜景，风衣随风飘动",
    "侦探K在科技展览会观察未来科技，表情专注而神秘",
    "赛博侦探K在虚拟现实空间中和全息投影交互，周围数据流动",
    "侦探K在赛博朋克风格的办公室整理案件资料，桌上摆满全息屏幕",
    "赛博侦探K骑着未来感十足的悬浮摩托穿越城市高速",
    "侦探K在都市公园散步，周围是樱花盛开的美丽景色"
  ];
  
  // Style preferences options
  const styleOptions = [
    { id: 'realistic', name: '写实风格', description: '真实感强，适合日常场景' },
    { id: 'anime', name: '动漫风格', description: '二次元风格，可爱生动' },
    { id: 'cyberpunk', name: '赛博朋克', description: '未来科技感，霓虹色调' },
    { id: 'watercolor', name: '水彩画风', description: '柔和艺术感，温馨浪漫' },
    { id: '3d', name: '3D渲染', description: '立体感强，科技感十足' }
  ];
  
  const [selectedStyle, setSelectedStyle] = useState(styleOptions[2]); // Default to cyberpunk style
  const [showStyleOptions, setShowStyleOptions] = useState(false);

  // Payment method states
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(preferredPaymentMethod);
  const [tempPaymentMethod, setTempPaymentMethod] = useState(selectedPaymentMethod);
  
  // Payment costs (fixed price for image creation)
  const paymentOptions = {
    partyCoins: { id: 'partyCoins', name: '派对币', icon: <Coins size={16} className="text-yellow-500" />, amount: 50 },
    starlightCoins: { id: 'starlightCoins', name: '星光币', icon: <DollarSign size={16} className="text-blue-500" />, amount: 10 },
    watchVideo: { id: 'watchVideo', name: '观看激励视频', icon: <PlayCircle size={16} className="text-green-500" />, description: '观看30秒广告视频免费创作' }
  };

  // 添加支付方法相关的效果
  useEffect(() => {
    if (showPaymentMethods) {
      setTempPaymentMethod(selectedPaymentMethod);
    }
  }, [showPaymentMethods, selectedPaymentMethod]);

  // Determine the best default payment method based on user's balance
  const determineDefaultPaymentMethod = () => {
    const partyCoinsNeeded = paymentOptions.partyCoins.amount;
    const starlightCoinsNeeded = paymentOptions.starlightCoins.amount;
    
    // If user has preferred method with sufficient balance, use that
    if (preferredPaymentMethod === 'partyCoins' && userBalance.partyCoins >= partyCoinsNeeded) {
      return 'partyCoins';
    } else if (preferredPaymentMethod === 'starlightCoins' && userBalance.starlightCoins >= starlightCoinsNeeded) {
      return 'starlightCoins';
    }
    
    // Otherwise, choose based on available balance
    if (userBalance.partyCoins >= partyCoinsNeeded) {
      return 'partyCoins';
    } else if (userBalance.starlightCoins >= starlightCoinsNeeded) {
      return 'starlightCoins';
    } else {
      return 'watchVideo';
    }
  };

  // Handler for selecting payment method
  const handleSelectPaymentMethod = (methodId) => {
    setSelectedPaymentMethod(methodId);
    setShowPaymentMethods(false);
  };

  // Handler for proceeding with the selected payment method
  const handleWatchVideo = () => {
    // This would integrate with ad SDK in a real app
    alert('调用广告SDK，播放30秒激励视频');
    // Simulate successful video watch
    setTimeout(() => {
      handleCreateImage();
    }, 1500);
  };

  // Check if selected payment method is valid (sufficient balance)
  const isSelectedPaymentValid = () => {
    if (!selectedPaymentMethod) return false;
    
    if (selectedPaymentMethod === 'watchVideo') return true;
    
    const required = paymentOptions[selectedPaymentMethod].amount;
    const available = userBalance[selectedPaymentMethod];
    return available >= required;
  };

  // Get color class based on balance sufficiency
  const getBalanceColorClass = (coinType, required) => {
    const balance = userBalance[coinType] || 0;
    return balance >= required ? "text-green-600" : "text-red-500";
  };

  // Effect to auto resize textarea
  useEffect(() => {
    if (descriptionRef.current) {
      descriptionRef.current.style.height = 'auto';
      descriptionRef.current.style.height = `${descriptionRef.current.scrollHeight}px`;
    }
  }, [imageDescription]);

  // Simulate processing progress when in processing state
  useEffect(() => {
    if (step === 'processing') {
      let progress = 0;
      setProcessingProgress(0);
      setProcessingStatus('waiting');
      
      // 将等待时间从1000ms减少到300ms
      setTimeout(() => {
        setProcessingStatus('processing');
        
        processingTimer.current = setInterval(() => {
          // 将进度增量从Math.random() * 3增加到Math.random() * 15
          progress += Math.random() * 15;
          if (progress >= 100) {
            progress = 100;
            clearInterval(processingTimer.current);
            
            // 将最终等待时间从500ms减少到200ms
            setTimeout(() => {
              setStep('complete');
              setResultImage('/api/placeholder/400/400'); // In real app, this would be the actual generated image
            }, 200);
          }
          setProcessingProgress(progress);
        }, 300); // 将更新间隔从500ms减少到300ms
      }, 300);
    }
    
    return () => {
      if (processingTimer.current) {
        clearInterval(processingTimer.current);
      }
    };
  }, [step]);

  useEffect(() => {
    if (step === 'complete') {
      // 根据描述生成默认标题
      const defaultTitle = imageDescription
        .split('，')[0]  // 取第一个逗号前的内容
        .split('。')[0]  // 或第一个句号前的内容
        .slice(0, 20);   // 最多20个字符
        
      setImageTitle(defaultTitle + (defaultTitle.length >= 20 ? '...' : ''));
    }
  }, [step, imageDescription]);

  // Get appropriate processing status text
  const getProcessingStatusText = () => {
    if (processingStatus === 'waiting') return '排队中';
    if (processingStatus === 'processing') {
      if (processingProgress < 25) return '正在分析参考图片';
      if (processingProgress < 50) return '构建场景结构';
      if (processingProgress < 75) return '优化图像细节';
      if (processingProgress < 95) return '最终润色中';
      return '即将完成';
    }
    if (processingStatus === 'error') return '处理出错';
    return '处理中';
  };

  // Handler for starting image creation
  // Handler for starting image creation
  const handleCreateImage = () => {
    if (!imageDescription.trim()) {
      alert('请输入图片描述');
      return;
    }
    
    if (!selectedPaymentMethod) {
      setShowPaymentMethods(true);
      return;
    }

    // Check if user has enough coins
    if (selectedPaymentMethod === 'partyCoins' && userBalance.partyCoins < paymentOptions.partyCoins.amount) {
      alert(`派对币余额不足，您当前有 ${userBalance.partyCoins} 派对币，需要 ${paymentOptions.partyCoins.amount} 派对币`);
      return;
    }
    if (selectedPaymentMethod === 'starlightCoins' && userBalance.starlightCoins < paymentOptions.starlightCoins.amount) {
      alert(`星光币余额不足，您当前有 ${userBalance.starlightCoins} 星光币，需要 ${paymentOptions.starlightCoins.amount} 星光币`);
      return;
    }

    // Handle watch video payment method
    if (selectedPaymentMethod === 'watchVideo') {
      handleWatchVideo();
      return;
    }

    console.log('Starting image creation with payment method:', selectedPaymentMethod);
    setStep('processing');
  };

  // Handler for canceling creation
  const handleCancelCreation = () => {
    if (window.confirm('确定要取消当前创建吗？所有进度将丢失。')) {
      if (processingTimer.current) {
        clearInterval(processingTimer.current);
      }
      setStep('edit');
      setProcessingProgress(0);
    }
  };

  // Handler for showing enhancement options modal
  const handleShowEnhanceOptions = () => {
    if (!imageDescription.trim()) {
      alert('请先输入一些基础描述');
      return;
    }
    
    setSelectedEnhanceStyle(null);
    setCustomEnhanceText('');
    setShowEnhanceModal(true);
  };

  // Handler for enhancing the description with AI
  const handleEnhanceDescription = async () => {
    if (!selectedEnhanceStyle) {
      setShowEnhanceModal(false);
      return;
    }
    
    setShowEnhanceModal(false);
    setIsEnhancingPrompt(true);
    
    // Save original to allow reverting
    if (!originalDescription) {
      setOriginalDescription(imageDescription);
    }
    
    // Simulate AI enhancement (this would call the actual API in production)
    setTimeout(() => {
      const baseText = imageDescription;
      let enhanced = baseText;
      
      // Apply different enhancements based on selected style
      switch (selectedEnhanceStyle.id) {
        case 'more_detailed':
          enhanced = `${baseText}，场景中光线明暗对比强烈，远处霓虹灯将角色轮廓勾勒出科技感十足的蓝紫色光晕，特写镜头捕捉角色眼神中的坚定与神秘，画面质感精细，细节丰富，背景环境的复杂纹理清晰可见。`;
          break;
        case 'more_concise':
          enhanced = baseText.length > 100 
            ? baseText.slice(0, 100).split('，')[0] + '，简洁有力，突出角色特征与关键场景元素。' 
            : baseText + '，精炼有力，重点突出。';
          break;
        case 'more_dramatic':
          enhanced = `${baseText}，戏剧性的光影效果营造出紧张氛围，角色表情凝重而富有张力，姿态充满动感，周围环境似乎也在呼应主角的情绪变化，整体画面具有强烈的情感冲击力。`;
          break;
        case 'more_professional':
          enhanced = `${baseText}，运用专业摄影构图原则，遵循三分法则排布主体，画面色彩采用互补色系，主体与背景形成对比度分离，景深适中，确保细节清晰度与整体氛围并重，光源布局科学合理。`;
          break;
        case 'custom':
          enhanced = customEnhanceText 
            ? `${baseText}，${customEnhanceText}` 
            : baseText;
          break;
        default:
          enhanced = `${baseText}，场景中光线明暗对比强烈，远处霓虹灯将角色轮廓勾勒出科技感十足的蓝紫色光晕，特写镜头捕捉角色眼神中的坚定与神秘。`;
      }
      
      setImageDescription(enhanced);
      setIsEnhancingPrompt(false);
    }, 1500);
  };

  // Handler for rewriting the description completely
  const handleRewriteDescription = async () => {
    if (!imageDescription.trim()) {
      alert('请先输入一些基础描述');
      return;
    }
    
    setIsEnhancingPrompt(true);
    
    // Save original to allow reverting
    if (!originalDescription) {
      setOriginalDescription(imageDescription);
    }
    
    // Simulate AI rewriting (this would call the actual API in production)
    setTimeout(() => {
      const theme = imageDescription.includes('雨') ? '雨夜' : 
                     imageDescription.includes('咖啡') ? '咖啡厅' : 
                     imageDescription.includes('高楼') ? '高楼' : '城市';
      
      const rewritten = theme === '雨夜' ? 
        '赛博侦探K站在雨水淋漓的城市街头，身穿一件防水风衣，肩部有着未来科技元素的荧光点缀。背景是模糊的霓虹灯广告牌，光线在湿漉漉的地面上形成梦幻般的反射。雨滴在他的帽檐上汇聚，他的目光穿透雨幕，手中全息设备投射出案件关键线索。' :
        theme === '咖啡厅' ?
        '赛博侦探K在一家复古未来主义的咖啡馆里小憩，面前的咖啡杯冒着热气。阳光透过几何形状的彩色玻璃窗照射进来，在他的桌面和面部投下多彩的光斑。他身旁的全息屏幕显示着案件进展，而咖啡馆的其他顾客则是模糊的背景。' :
        theme === '高楼' ?
        '赛博侦探K站在摩天大楼的边缘，俯瞰着下方的城市全景。他的风衣在高空的强风中猎猎作响，身后是璀璨的城市夜景和无数高楼的全息广告。月光和城市的霓虹灯共同照亮了他若有所思的侧脸，远处的飞行器在楼宇间穿梭。' :
        '赛博侦探K穿行在未来城市的繁忙街道上，周围是来往的行人和悬浮交通工具。他身着融合了复古与未来元素的专业装扮，目光敏锐地观察着周围。街道两旁是各式全息投影广告和霓虹招牌，远处高楼的玻璃幕墙反射着日落的金色光芒。';
      
      setImageDescription(rewritten);
      setIsEnhancingPrompt(false);
    }, 1500);
  };

  // Handler for getting AI inspiration
  const handleGetInspiration = () => {
    // Randomly select an inspiration
    const randomInspiration = inspirations[Math.floor(Math.random() * inspirations.length)];
    setImageDescription(randomInspiration);
    
    // Focus the textarea after setting inspiration
    if (descriptionRef.current) {
      descriptionRef.current.focus();
    }
  };

  // Handler for clearing the description
  const handleClearDescription = () => {
    if (imageDescription.trim() && !window.confirm('确定要清空当前描述吗？')) {
      return;
    }
    setImageDescription('');
    setOriginalDescription('');
    
    // Focus the textarea after clearing
    if (descriptionRef.current) {
      descriptionRef.current.focus();
    }
  };

  // Handler for undoing AI enhancement
  const handleUndoEnhancement = () => {
    if (originalDescription) {
      setImageDescription(originalDescription);
      setOriginalDescription('');
    }
  };

  // Handler for selecting reference images
  const handleAddReferenceImage = (source) => {
    if (source === 'library') {
      setShowLibrarySelector(true);
      setShowReferenceSelector(false);
    } else if (source === 'camera') {
      // This would integrate with device camera in a real app
      alert('调用相机功能');
      setShowReferenceSelector(false);
    } else if (source === 'gallery') {
      // This would integrate with device gallery in a real app
      alert('调用相册功能');
      setShowReferenceSelector(false);
    }
  };

  // Handler for selecting an asset from library
  const handleSelectAsset = (asset) => {
    // Add to selected references if not already included
    if (!selectedReferenceImages.some(img => img.id === asset.id)) {
      setSelectedReferenceImages([...selectedReferenceImages, asset]);
    }
    setShowLibrarySelector(false);
  };

  // Handler for removing a reference image
  const handleRemoveReferenceImage = (id) => {
    setSelectedReferenceImages(selectedReferenceImages.filter(img => img.id !== id));
  };

  // 修改handleCompleteCreation函数，使用自定义标题
  const handleCompleteCreation = () => {
    // In a real app, this would add the image to the asset library
    if (onImageCreated) {
      onImageCreated({
        id: Date.now(),
        type: 'image',
        url: resultImage,
        title: imageTitle || imageDescription.slice(0, 20) + (imageDescription.length > 20 ? '...' : ''),
        createdAt: '刚刚',
        usedCount: 0,
        likes: 0,
        isHD: true,
      });
    }
    
    // Return to previous screen
    if (onBack) {
      onBack();
    }
  };

  // Filter image assets from all assets
  const imageAssets = existingAssets.filter(asset => asset.type === 'image');

  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white sticky top-0 z-10">
        <div className="flex items-center">
          <button 
            onClick={onBack} 
            className="p-2 mr-2 rounded-full hover:bg-white hover:bg-opacity-20"
          >
            <ChevronLeft size={20} />
          </button>
          <h1 className="text-lg font-bold">创建图片</h1>
        </div>
        
        {step === 'complete' && (
          <button 
            onClick={handleCompleteCreation}
            className="px-4 py-1.5 rounded-lg bg-green-500 text-white text-sm font-medium"
          >
            保存到素材库
          </button>
        )}
      </div>
      
      {/* Main content */}
      <div className="flex-1 p-4 overflow-auto">
        {step === 'edit' && (
          <>
            {/* Character info reminder */}
            {characterData && (
              <div className="mb-4 p-3 bg-purple-50 rounded-lg border border-purple-100 flex items-center">
                <div className="w-10 h-10 bg-purple-200 rounded-full overflow-hidden flex-shrink-0 mr-3">
                  {characterData.profileImage && (
                    <img 
                      src={characterData.profileImage} 
                      alt={characterData.name}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-purple-800">正在为 {characterData.name} 创建图片</h3>
                  <p className="text-xs text-purple-600">创建的图片将添加到角色素材库</p>
                </div>
              </div>
            )}
            
            {/* Reference image selection */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <FileImage size={16} className="mr-1 text-purple-600" />
                  参考图片 <span className="text-xs text-gray-500 ml-1">(可选)</span>
                </label>
                <button 
                  onClick={() => setShowReferenceSelector(true)}
                  className="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full flex items-center"
                >
                  <Plus size={14} className="mr-1" />
                  添加参考
                </button>
              </div>
              
              {selectedReferenceImages.length > 0 ? (
                <div className="flex gap-2 overflow-x-auto pb-2 hide-scrollbar">
                  {selectedReferenceImages.map((img) => (
                    <div key={img.id} className="relative flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border border-gray-200 bg-white">
                      <img 
                        src={img.url} 
                        alt={img.title}
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => handleRemoveReferenceImage(img.id)}
                        className="absolute top-1 right-1 w-5 h-5 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-dashed border-gray-300 p-3 text-center">
                  <p className="text-xs text-gray-500">
                    添加参考图片可以让AI更好地理解你想要的风格和场景
                  </p>
                </div>
              )}
            </div>
            
            {/* Style selection */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Paintbrush size={16} className="mr-1 text-purple-600" />
                  图片风格
                </label>
              </div>
              
              <div className="relative">
                <button
                  onClick={() => setShowStyleOptions(!showStyleOptions)}
                  className="w-full p-3 bg-white rounded-lg border border-gray-200 flex justify-between items-center"
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-2">
                      <Wand2 size={16} className="text-purple-600" />
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium">{selectedStyle.name}</div>
                      <div className="text-xs text-gray-500">{selectedStyle.description}</div>
                    </div>
                  </div>
                  <ChevronDown size={18} className="text-gray-400" />
                </button>
                
                {showStyleOptions && (
                  <div className="absolute z-20 top-full left-0 right-0 mt-1 bg-white rounded-lg border border-gray-200 shadow-lg max-h-64 overflow-y-auto">
                    {styleOptions.map((style) => (
                      <button
                        key={style.id}
                        onClick={() => {
                          setSelectedStyle(style);
                          setShowStyleOptions(false);
                        }}
                        className={`w-full p-3 flex items-center border-b border-gray-100 last:border-b-0 ${
                          selectedStyle.id === style.id ? 'bg-purple-50' : 'hover:bg-gray-50'
                        }`}
                      >
                        <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-2">
                          <Wand2 size={16} className="text-purple-600" />
                        </div>
                        <div className="text-left flex-1">
                          <div className="text-sm font-medium">{style.name}</div>
                          <div className="text-xs text-gray-500">{style.description}</div>
                        </div>
                        {selectedStyle.id === style.id && (
                          <CheckCircle size={18} className="text-purple-600 ml-2" />
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            {/* Description input */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label htmlFor="imageDescription" className="text-sm font-medium text-gray-700 flex items-center">
                  <PenLine size={16} className="mr-1 text-purple-600" />
                  图片描述
                </label>
                <button 
                  onClick={handleGetInspiration}
                  className="text-xs px-2 py-1 bg-amber-100 text-amber-700 rounded-full flex items-center"
                >
                  <Sparkles size={14} className="mr-1" />
                  给我灵感
                </button>
              </div>
              
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <textarea
                  ref={descriptionRef}
                  id="imageDescription"
                  value={imageDescription}
                  onChange={(e) => setImageDescription(e.target.value)}
                  placeholder="描述你想要的图片场景、风格和元素，例如：赛博侦探K在霓虹灯闪烁的雨夜街头查案..."
                  className="w-full p-3 min-h-[120px] text-sm border-none focus:ring-0 focus:outline-none resize-none"
                  disabled={isEnhancingPrompt}
                ></textarea>
                
                <div className="flex border-t border-gray-100">
                  <button
                    onClick={handleShowEnhanceOptions}
                    disabled={!imageDescription.trim() || isEnhancingPrompt}
                    className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                      !imageDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-purple-600'
                    }`}
                  >
                    {isEnhancingPrompt ? <Loader size={14} className="mr-1 animate-spin" /> : <Wand2 size={14} className="mr-1" />}
                    润色
                  </button>
                  <div className="w-px h-8 bg-gray-100 my-auto"></div>
                  <button
                    onClick={handleRewriteDescription}
                    disabled={!imageDescription.trim() || isEnhancingPrompt}
                    className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                      !imageDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-indigo-600'
                    }`}
                  >
                    {isEnhancingPrompt ? <Loader size={14} className="mr-1 animate-spin" /> : <RefreshCw size={14} className="mr-1" />}
                    重写
                  </button>
                  <div className="w-px h-8 bg-gray-100 my-auto"></div>
                  <button
                    onClick={handleClearDescription}
                    disabled={!imageDescription.trim() || isEnhancingPrompt}
                    className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                      !imageDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-red-600'
                    }`}
                  >
                    <Trash2 size={14} className="mr-1" />
                    清空
                  </button>
                </div>
              </div>
              
              {originalDescription && (
                <div className="mt-2 flex justify-end">
                  <button
                    onClick={handleUndoEnhancement}
                    className="text-xs text-gray-600 flex items-center"
                  >
                    <ArrowLeft size={12} className="mr-1" />
                    恢复原始描述
                  </button>
                </div>
              )}
            </div>
            
            {/* Helpful tips */}
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
              <h3 className="text-sm font-medium text-blue-800 flex items-center mb-1">
                <Sparkles size={16} className="mr-1 text-blue-500" />
                创作提示
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  添加详细的场景描述和光线信息可以提高生成质量
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  选择参考图片有助于保持角色风格的一致性
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 flex-shrink-0" />
                  选择与角色性格相符的场景和动作更能丰富角色形象
                </li>
              </ul>
            </div>

            {/* Bottom fixed create button */}
            <div className="h-16"></div> {/* Spacer for fixed button */}
          </>
        )}
        
        {step === 'processing' && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="w-full max-w-sm mb-8">
              <div className="flex justify-between items-center mb-2">
                <div className="text-sm font-medium text-gray-700 flex items-center">
                  <RotateCw size={16} className={`mr-1 text-blue-600 ${processingStatus === 'processing' ? 'animate-spin' : ''}`} />
                  {getProcessingStatusText()}
                </div>
                <div className="text-xs text-gray-500">
                  {Math.round(processingProgress)}%
                </div>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2 mb-1 overflow-hidden">
                <div 
                  className="h-full bg-blue-600 transition-all duration-300 ease-out"
                  style={{ width: `${processingProgress}%` }}
                ></div>
              </div>
              
              <div className="text-xs text-gray-500 flex justify-between">
                <span>预计等待时间: {estimatedTime}</span>
                {/* <span>请勿关闭页面</span> */}
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-4 shadow-md w-full max-w-sm">
              <div className="bg-gray-100 rounded-lg overflow-hidden aspect-square mb-4 flex items-center justify-center relative">
                <div className="w-full h-full relative overflow-hidden loading-animation">
                  {/* This is a placeholder showing a loading animation */}
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-purple-600 bg-white bg-opacity-80 px-3 py-2 rounded-lg shadow-sm">
                    <Loader size={24} className="animate-spin mx-auto mb-2" />
                    <p className="text-xs text-center">生成图像中...</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-blue-50 rounded-lg p-3 text-xs text-blue-800">
                <div className="flex items-center mb-2">
                  <MessageCircle size={14} className="mr-1 text-blue-600" />
                  <span className="font-medium">有用提示</span>
                </div>
                <p>您可以放心离开此页面继续其他操作，图片生成完成后我们会通知您。</p>
              </div>
            </div>
          </div>
        )}
        
        {step === 'complete' && (
          <div className="flex flex-col items-center py-4">
            <div className="bg-white rounded-xl p-4 shadow-md w-full mb-4">
              <div className="rounded-lg overflow-hidden mb-4">
                <img 
                  src={resultImage} 
                  alt="生成的图片"
                  className="w-full aspect-square object-cover"
                />
              </div>
              
              <div className="flex justify-between mb-3">
                <button className="flex-1 mr-1 py-2 rounded-lg bg-purple-100 text-purple-700 text-sm font-medium flex items-center justify-center">
                  <Eye size={16} className="mr-1" />
                  预览
                </button>
                <button className="flex-1 ml-1 py-2 rounded-lg bg-gray-100 text-gray-700 text-sm font-medium flex items-center justify-center">
                  <Download size={16} className="mr-1" />
                  下载
                </button>
              </div>
              <div className="mb-3">
              <h3 className="text-sm font-medium text-gray-700 mb-1">图片标题</h3>
              <div className="flex items-center">
                <input
                  type="text"
                  value={imageTitle}
                  onChange={(e) => setImageTitle(e.target.value)}
                  placeholder="给图片起个标题"
                  className="flex-1 text-sm border border-gray-200 rounded-lg p-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
                <button 
                  onClick={() => {
                    // 重新生成默认标题
                    const defaultTitle = imageDescription
                      .split('，')[0]
                      .split('。')[0]
                      .slice(0, 20);
                    setImageTitle(defaultTitle + (defaultTitle.length >= 20 ? '...' : ''));
                  }}
                  className="ml-2 p-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200"
                >
                  <RefreshCw size={16} />
                </button>
              </div>
            </div>
              <div className="space-y-3">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">图片描述</h3>
                  <div className="text-xs text-gray-600 bg-gray-50 rounded-lg p-3">
                    {imageDescription}
                    <button className="ml-2 text-purple-600 inline-flex items-center">
                      <Copy size={12} />
                    </button>
                  </div>
                </div>
                
                <div className="flex justify-between text-xs text-gray-500">
                  <span>图片风格: {selectedStyle.name}</span>
                </div>
              </div>
            </div>
            
            <div className="w-full bg-green-50 rounded-lg p-4 border border-green-100 mb-4">
              <div className="flex items-start">
                <CheckCircle size={18} className="text-green-600 mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-green-800 mb-1">图片已成功生成</h3>
                  <p className="text-xs text-green-700">点击「用它发动态」将图片用于发布动态，或保存到素材库中备用。</p>
                </div>
              </div>
            </div>
            
            <div className="w-full">
              <div className="flex justify-between mb-3">
                <button 
                  onClick={() => setStep('edit')}
                  className="flex-1 mr-1 py-3 rounded-lg border border-purple-500 text-purple-600 text-sm font-medium flex items-center justify-center"
                >
                  <RefreshCw size={16} className="mr-1" />
                  修改重新生成
                </button>
                <button 
                  onClick={handleCompleteCreation}
                  className="flex-1 ml-1 py-3 rounded-lg bg-green-500 text-white text-sm font-medium flex items-center justify-center"
                >
                  <Check size={16} className="mr-1" />
                  用它发动态
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Payment method selection modal */}
      {showPaymentMethods && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center p-4">
          <div className="bg-white rounded-t-xl w-full max-w-md">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-bold">选择支付方式</h2>
              <button 
                onClick={() => setShowPaymentMethods(false)}
                className="text-gray-500 p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4">
              <div className="grid grid-cols-1 gap-3">
                {/* Party Coins Option */}
                <div className={`p-4 rounded-lg text-left border ${
                  tempPaymentMethod === 'partyCoins' 
                    ? 'bg-yellow-50 border-yellow-300' 
                    : 'bg-white border-gray-200'
                }`}>
                  <button 
                    onClick={() => setTempPaymentMethod('partyCoins')}
                    className="w-full flex items-center"
                  >
                    <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                      <Coins size={20} className="text-yellow-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium">派对币</h3>
                        <span className={`text-sm font-medium ${getBalanceColorClass('partyCoins', paymentOptions.partyCoins.amount)}`}>
                          余额: {userBalance.partyCoins}
                        </span>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-gray-500">用派对币支付图片生成费用</p>
                        <span className="text-xs font-bold text-yellow-600">
                          -  {paymentOptions.partyCoins.amount}
                        </span>
                      </div>
                    </div>
                    {tempPaymentMethod === 'partyCoins' && (
                      <CheckCircle size={18} className="text-yellow-600 ml-2" />
                    )}
                  </button>
                  
                  {/* Add Get Party Coins button */}
                    <div className="mt-3 pl-13">
                      <button 
                        onClick={() => alert('跳转到派对币获取页面')}
                        className="ml-12 px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium flex items-center justify-center"
                      >
                        <Plus size={14} className="mr-1" />
                        获取派对币
                      </button>
                    </div>
                </div>
                
                {/* Starlight Coins Option */}
                <div className={`p-4 rounded-lg text-left border ${
                  tempPaymentMethod === 'starlightCoins' 
                    ? 'bg-blue-50 border-blue-300' 
                    : 'bg-white border-gray-200'
                }`}>
                  <button 
                    onClick={() => setTempPaymentMethod('starlightCoins')}
                    className="w-full flex items-center"
                  >
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <DollarSign size={20} className="text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium">星光币</h3>
                        <span className={`text-sm font-medium ${getBalanceColorClass('starlightCoins', paymentOptions.starlightCoins.amount)}`}>
                          余额: {userBalance.starlightCoins}
                        </span>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-gray-500">用星光币支付图片生成费用</p>
                        <span className="text-xs font-bold text-blue-600">
                          - {paymentOptions.starlightCoins.amount}
                        </span>
                      </div>
                    </div>
                    {tempPaymentMethod === 'starlightCoins' && (
                      <CheckCircle size={18} className="text-blue-600 ml-2" />
                    )}
                  </button>
                  
                  {/* Add Recharge Starlight Coins button */}
                    <div className="mt-3 pl-13">
                      <button 
                        onClick={() => alert('跳转到星光币充值页面')}
                        className="ml-12 px-3 py-1.5 bg-blue-100 text-blue-700 rounded-full text-xs font-medium flex items-center justify-center"
                      >
                        <Plus size={14} className="mr-1" />
                        充值星光币
                      </button>
                    </div>
                </div>
                
                {/* Watch Video Option */}
                <button 
                  onClick={() => setTempPaymentMethod('watchVideo')}
                  className={`p-4 rounded-lg text-left flex items-center border ${
                    tempPaymentMethod === 'watchVideo' 
                      ? 'bg-green-50 border-green-300' 
                      : 'bg-white border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <PlayCircle size={20} className="text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">观看激励视频</h3>
                    <p className="text-xs text-gray-500 mt-1">观看短视频广告以免费创作</p>
                  </div>
                  {tempPaymentMethod === 'watchVideo' && (
                    <CheckCircle size={18} className="text-green-600 ml-2" />
                  )}
                </button>
              </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <button 
                onClick={() => {
                  setSelectedPaymentMethod(tempPaymentMethod);
                  setShowPaymentMethods(false);
                }}
                className="w-full py-3 bg-purple-600 rounded-lg text-white font-medium"
              >
                确认支付方式
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Fixed bottom buttons */}
      {step === 'edit' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          {/* Payment Method Selection */}
          <div className="mb-3 p-2 bg-gray-50 rounded-lg flex items-center justify-between">
            <div className="flex items-center">
              {selectedPaymentMethod && paymentOptions[selectedPaymentMethod].icon}
              <span className="ml-2 text-sm font-medium">
                {selectedPaymentMethod ? paymentOptions[selectedPaymentMethod].name : '选择支付方式'}
              </span>
              {selectedPaymentMethod && (selectedPaymentMethod === 'partyCoins' || selectedPaymentMethod === 'starlightCoins') && (
                <span className="ml-2 text-sm">
                  需消耗 <span className="font-bold">{paymentOptions[selectedPaymentMethod].amount}</span>
                  <span className={`ml-1 text-xs ${getBalanceColorClass(selectedPaymentMethod, paymentOptions[selectedPaymentMethod].amount)}`}>
                    (余额: {userBalance[selectedPaymentMethod]})
                  </span>
                </span>
              )}
              {selectedPaymentMethod && selectedPaymentMethod === 'watchVideo' && (
                <span className="ml-2 text-xs text-green-600">
                  观看30秒视频
                </span>
              )}
            </div>
            <button 
              onClick={() => setShowPaymentMethods(true)}
              className="text-xs text-purple-600 border border-purple-200 rounded-lg px-2 py-1"
            >
              {selectedPaymentMethod ? '更换' : '选择'}
            </button>
          </div>
          <button 
            onClick={handleCreateImage}
            disabled={!imageDescription.trim() || !isSelectedPaymentValid()}
            className={`w-full py-3 rounded-lg text-sm font-medium ${
              imageDescription.trim() && isSelectedPaymentValid()
                ? 'bg-green-500 text-white' 
                : 'bg-gray-300 text-gray-500'
            }`}
          >
            创建图片
          </button>
        </div>
      )}
      
      {step === 'processing' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          <button 
            onClick={handleCancelCreation}
            className="w-full py-3 rounded-lg bg-red-500 text-white text-sm font-medium"
          >
            取消创建
          </button>
        </div>
      )}
      
      {/* Enhancement options modal */}
      {showEnhanceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl w-full max-w-md overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-bold">选择润色方式</h2>
              <button 
                onClick={() => setShowEnhanceModal(false)}
                className="text-gray-500 p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4 max-h-[50vh] overflow-y-auto">
              <div className="space-y-3">
                {enhanceStyles.map((style) => (
                  <button
                    key={style.id}
                    onClick={() => {
                      setSelectedEnhanceStyle(style);
                      if (style.id !== 'custom') {
                        setCustomEnhanceText('');
                      }
                    }}
                    className={`w-full p-3 border rounded-lg text-left flex items-center transition-colors ${
                      selectedEnhanceStyle?.id === style.id 
                        ? 'border-purple-500 bg-purple-50' 
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                      selectedEnhanceStyle?.id === style.id 
                        ? 'bg-purple-100' 
                        : 'bg-gray-100'
                    }`}>
                      {style.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium">{style.name}</h3>
                      <p className="text-xs text-gray-500">{style.description}</p>
                    </div>
                    {selectedEnhanceStyle?.id === style.id && (
                      <CheckCircle size={18} className="text-purple-600 ml-2" />
                    )}
                  </button>
                ))}
              </div>
              
              {selectedEnhanceStyle?.id === 'custom' && (
                <div className="mt-4">
                  <label className="text-sm font-medium text-gray-700 block mb-2">
                    自定义润色指令
                  </label>
                  <textarea
                    value={customEnhanceText}
                    onChange={(e) => setCustomEnhanceText(e.target.value)}
                    placeholder="例如：添加更多关于场景光线的描述，强调角色的表情..."
                    className="w-full p-3 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[80px] resize-none"
                  ></textarea>
                </div>
              )}
              
              <div className="mt-4 bg-blue-50 rounded-lg p-3 border border-blue-100">
                <h3 className="text-xs font-medium text-blue-800 flex items-center mb-1">
                  <Sparkles size={14} className="mr-1 text-blue-500" />
                  润色效果预览
                </h3>
                <p className="text-xs text-blue-700">
                  {selectedEnhanceStyle ? (
                    selectedEnhanceStyle.id === 'more_detailed' ? 
                      "原始描述将添加更丰富的环境、光线和细节描述。" :
                    selectedEnhanceStyle.id === 'more_concise' ?
                      "将保留关键内容，使描述更加精炼和有力。" :
                    selectedEnhanceStyle.id === 'more_dramatic' ?
                      "将添加更多情感和氛围元素，增强画面的戏剧性。" :
                    selectedEnhanceStyle.id === 'more_professional' ?
                      "将使用更专业的摄影和艺术术语，提升描述的专业性。" :
                    selectedEnhanceStyle.id === 'custom' ?
                      "将根据您的自定义指令进行润色。" :
                      "请选择一种润色方式。"
                  ) : "请选择一种润色方式。"}
                </p>
              </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <div className="flex gap-2">
                <button 
                  onClick={() => setShowEnhanceModal(false)}
                  className="flex-1 py-3 bg-gray-100 rounded-lg text-gray-700 font-medium"
                >
                  取消
                </button>
                <button 
                  onClick={handleEnhanceDescription}
                  disabled={!selectedEnhanceStyle || (selectedEnhanceStyle?.id === 'custom' && !customEnhanceText.trim())}
                  className={`flex-1 py-3 rounded-lg font-medium ${
                    (!selectedEnhanceStyle || (selectedEnhanceStyle?.id === 'custom' && !customEnhanceText.trim())) 
                      ? 'bg-gray-300 text-gray-500' 
                      : 'bg-purple-600 text-white'
                  }`}
                >
                  应用润色
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Reference image selection modal */}
      {showReferenceSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center p-4">
          <div className="bg-white rounded-t-xl w-full max-w-md">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-bold">添加参考图片</h2>
            </div>
            
            <div className="p-4">
              <div className="grid grid-cols-1 gap-3">
                <button 
                  onClick={() => handleAddReferenceImage('library')}
                  className="p-4 bg-purple-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                    <Image size={20} className="text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">从素材库选择</h3>
                    <p className="text-xs text-gray-500">使用已有的角色图片作为参考</p>
                  </div>
                </button>
                
                <button 
                  onClick={() => handleAddReferenceImage('camera')}
                  className="p-4 bg-blue-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <Camera size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">拍摄照片</h3>
                    <p className="text-xs text-gray-500">使用相机拍摄新照片作为参考</p>
                  </div>
                </button>
                
                <button 
                  onClick={() => handleAddReferenceImage('gallery')}
                  className="p-4 bg-green-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <Upload size={20} className="text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">从相册选择</h3>
                    <p className="text-xs text-gray-500">选择本地图片作为参考</p>
                  </div>
                </button>
              </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <button 
                onClick={() => setShowReferenceSelector(false)}
                className="w-full py-3 bg-gray-100 rounded-lg text-gray-700 font-medium"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Asset library selection modal */}
      {showLibrarySelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex flex-col">
          <div className="bg-white p-4 flex items-center justify-between border-b border-gray-200">
            <h2 className="text-lg font-bold flex items-center">
              <ChevronLeft 
                size={20} 
                className="mr-2 cursor-pointer" 
                onClick={() => {
                  setShowLibrarySelector(false);
                  setShowReferenceSelector(true);
                }}
              />
              素材库图片
            </h2>
            <button 
              onClick={() => setShowLibrarySelector(false)}
              className="text-gray-500"
            >
              <X size={20} />
            </button>
          </div>
          
          <div className="flex-1 bg-gray-50 p-4 overflow-auto">
            {imageAssets.length > 0 ? (
              <div className="grid grid-cols-2 gap-3">
                {imageAssets.map((asset) => (
                  <div 
                    key={asset.id} 
                    onClick={() => handleSelectAsset(asset)}
                    className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                  >
                    <div className="relative aspect-[4/3] bg-gray-100">
                      <img 
                        src={asset.url} 
                        alt={asset.title}
                        className="w-full h-full object-cover"
                      />
                      {selectedReferenceImages.some(img => img.id === asset.id) && (
                        <div className="absolute inset-0 bg-purple-500 bg-opacity-30 flex items-center justify-center">
                          <div className="bg-white rounded-full p-1">
                            <Check size={18} className="text-purple-600" />
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="p-2">
                      <p className="text-sm font-medium truncate">{asset.title}</p>
                      <p className="text-xs text-gray-500">{asset.createdAt}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Image size={24} className="text-gray-400" />
                </div>
                <p className="text-gray-500">暂无图片素材</p>
                <p className="text-xs text-gray-400 mt-1">请先创建一些图片素材</p>
              </div>
            )}
          </div>
          
          <div className="bg-white p-4 border-t border-gray-200">
            <button 
              onClick={() => setShowLibrarySelector(false)}
              className="w-full py-3 bg-purple-600 rounded-lg text-white font-medium"
            >
              完成选择
            </button>
          </div>
        </div>
      )}
      
      {/* Style for animations and special effects */}
      <style jsx>{`
        .loading-animation {
          background: linear-gradient(-45deg, #e6e6e6, #f0f0f0, #ebebeb, #f5f5f5);
          background-size: 400% 400%;
          animation: gradient 2s ease infinite;
        }
        
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  );
};

export default CreateImagePage;