import React, { useState, useRef, useEffect } from 'react';
import { 
  ChevronLeft, Image, Search, CheckCircle, Clock, Plus, Filter,
  Eye, Heart, Award, Sparkles, Calendar, Check, Upload, Camera, 
  X, Loader, Wand2, RefreshCw, AlertTriangle, ImagePlus, Flame,
  Bookmark, Trash2, PanelRight, MessageCircle, ChevronDown, Users,
  Send, ArrowRight, Hand
} from 'lucide-react';

const ImagePublishPage = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('select'); // select, create, preview
  const [selectedImage, setSelectedImage] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all'); // all, unused, used, favorite
  const [sortType, setSortType] = useState('newest'); // newest, popular, oldest
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  
  // 创建图片相关状态
  const [promptText, setPromptText] = useState('');
  const [isPolishing, setIsPolishing] = useState(false);
  const [isRewriting, setIsRewriting] = useState(false);
  const [selectedCharacterPhoto, setSelectedCharacterPhoto] = useState(null); 
  const [imageCount, setImageCount] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);

  // 发布相关状态
  const [publishTitle, setPublishTitle] = useState('');
  const [publishContent, setPublishContent] = useState('');
  
  // 模拟生成中的任务
  const [generationTasks, setGenerationTasks] = useState([
    {
      id: 'task-001',
      startTime: '10:27',
      progress: 68,
      estimatedTime: '1分钟',
      prompt: '角色站在雨中的街道上，霓虹灯光映照...',
      imageCount: 3
    }
  ]);
  
  // 模拟已有图片数据
  const existingImages = [
    {
      id: 1,
      url: '/api/placeholder/200/300',
      createdAt: '今天 09:42',
      used: true,
      usedCount: 2,
      statistics: {
        views: 5621,
        likes: 327
      },
      isHD: true,
      isFavorite: true
    },
    {
      id: 2,
      url: '/api/placeholder/200/300',
      createdAt: '今天 08:15',
      used: false,
      usedCount: 0,
      statistics: {
        views: 0,
        likes: 0
      },
      isHD: true,
      isFavorite: false
    },
    {
      id: 3,
      url: '/api/placeholder/200/300',
      createdAt: '昨天 15:32',
      used: true,
      usedCount: 1,
      statistics: {
        views: 3207,
        likes: 156
      },
      isHD: false,
      isFavorite: true
    },
    {
      id: 4,
      url: '/api/placeholder/200/300',
      createdAt: '昨天 11:20',
      used: false,
      usedCount: 0,
      statistics: {
        views: 0,
        likes: 0
      },
      isHD: true,
      isFavorite: false
    },
    {
      id: 5,
      url: '/api/placeholder/200/300',
      createdAt: '3天前',
      used: true,
      usedCount: 3,
      statistics: {
        views: 8943,
        likes: 562
      },
      isHD: true,
      isFavorite: true
    }
  ];

  const characterPhotos = [
    { id: 1, url: '/api/placeholder/100/100', name: "侦探风格" },
    { id: 2, url: '/api/placeholder/100/100', name: "休闲风格" },
    { id: 3, url: '/api/placeholder/100/100', name: "未来科技" },
    { id: 4, url: '/api/placeholder/100/100', name: "赛博朋克" }
  ];

  const suggestedPrompts = [
    "角色站在雨夜的霓虹街头，表情坚定，手中拿着一个科技感的设备",
    "角色在高楼天台俯瞰未来城市，暮色中的城市灯光映照在脸上",
    "角色在科技感的实验室中研究一组全息投影数据，面部表情专注",
    "角色在赛博朋克风格的咖啡厅中休息，窗外是忙碌的未来城市"
  ];
  
  // 过滤和搜索图片
  const filteredImages = existingImages
    .filter(img => {
      if (searchQuery && !`图片 ${img.id}`.includes(searchQuery)) return false;
      
      if (filterType === 'unused') return !img.used;
      if (filterType === 'used') return img.used;
      if (filterType === 'favorite') return img.isFavorite;
      
      return true;
    })
    .sort((a, b) => {
      if (sortType === 'newest') return b.id - a.id;
      if (sortType === 'oldest') return a.id - b.id;
      if (sortType === 'popular') {
        const aPopularity = a.statistics.views + (a.statistics.likes * 10);
        const bPopularity = b.statistics.views + (b.statistics.likes * 10);
        return bPopularity - aPopularity;
      }
      return 0;
    });
  
  // 模拟AI润色功能
  const polishPrompt = () => {
    setIsPolishing(true);
    setTimeout(() => {
      setPromptText(promptText + "（画面色调偏冷，背景使用霓虹灯和雨水元素，角色表情坚毅，整体赛博朋克风格）");
      setIsPolishing(false);
    }, 1500);
  };
  
  // 模拟AI重写功能
  const rewritePrompt = () => {
    setIsRewriting(true);
    setTimeout(() => {
      setPromptText("角色站在高耸的摩天大楼天台边缘，俯瞰被霓虹灯装点的未来城市。雨水顺着他的脸颊滑落，湿透的风衣在夜风中飘动。远处的城市灯光映照在他坚毅的脸上，营造出强烈的光影对比。整体色调为蓝色和紫色，充满赛博朋克风格。");
      setIsRewriting(false);
    }, 1500);
  };
  
  // 基于选中图片进入创建页面
  const createFromSelected = () => {
    if (selectedImage) {
      const selectedImg = existingImages.find(img => img.id === selectedImage);
      setSelectedCharacterPhoto(selectedImg);
      setActiveTab('create');
    }
  };
  
  // 模拟创建新图片
  const generateNewImages = () => {
    setIsGenerating(true);
    // 在实际应用中，这里会调用API开始生成图片任务
    setTimeout(() => {
      setIsGenerating(false);
      
      // 添加新的生成任务
      const newTask = {
        id: `task-${Date.now()}`,
        startTime: new Date().getHours() + ':' + (new Date().getMinutes() < 10 ? '0' : '') + new Date().getMinutes(),
        progress: 0,
        estimatedTime: `${Math.round(imageCount * 1.5)}分钟`,
        prompt: promptText.substring(0, 50) + (promptText.length > 50 ? '...' : ''),
        imageCount: imageCount
      };
      
      setGenerationTasks([newTask, ...generationTasks]);
      
      // 模拟任务进度更新
      const interval = setInterval(() => {
        setGenerationTasks(prev => {
          const updated = [...prev];
          const taskIndex = updated.findIndex(t => t.id === newTask.id);
          
          if (taskIndex !== -1) {
            updated[taskIndex] = {
              ...updated[taskIndex],
              progress: updated[taskIndex].progress + 5
            };
            
            // 当进度达到100%时，清除定时器
            if (updated[taskIndex].progress >= 100) {
              clearInterval(interval);
              
              // 模拟生成完成后添加新图片
              const newImage = {
                id: existingImages.length + 1,
                url: '/api/placeholder/200/300',
                createdAt: '刚刚',
                used: false,
                usedCount: 0,
                statistics: {
                  views: 0,
                  likes: 0
                },
                isHD: true,
                isFavorite: false
              };
              
              // 在实际应用中，这里会更新图片列表
              // setExistingImages([newImage, ...existingImages]);
            }
          }
          
          return updated;
        });
      }, 3000);
      
      // 重置表单并返回选择页面
      setPromptText('');
      setActiveTab('select');
    }, 1500);
  };
  
  // 切换到预览模式
  const previewSelected = () => {
    if (selectedImage) {
      setPublishTitle('');
      setPublishContent('');
      setActiveTab('preview');
    }
  };
  
  // 格式化数字(例如: 1000 -> 1K)
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'W';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num;
  };

  // 计算生成图片需要的币
  const calculateCost = () => {
    return imageCount * 3; // 每张图3个星光币
  };

  return (
    <div className="fixed inset-0 flex justify-center items-start bg-gray-100 overflow-auto">
      <div className="w-full max-w-md bg-gray-50 min-h-screen flex flex-col relative" style={{height: '100%'}}>
        {/* 头部导航 */}
        <div className="sticky top-0 z-20 w-full bg-purple-700 px-4 py-3 flex items-center justify-between text-white">
          <div className="flex items-center">
            <button 
              className="p-1 mr-2"
              onClick={() => {
                if (activeTab === 'create' || activeTab === 'preview') {
                  setActiveTab('select');
                }
              }}
            >
              <ChevronLeft size={20} className="text-white" />
            </button>
            <h1 className="text-lg font-bold">
              {activeTab === 'preview' ? '发布图文' : 
               activeTab === 'create' ? '创建新图片' : '发布图文'}
            </h1>
          </div>
          <div className="flex items-center">
            {activeTab === 'select' && (
              <>
                {selectedImage ? (
                  // 如果选中了图片，显示两个按钮
                  <div className="flex space-x-2">
                    <button 
                      onClick={createFromSelected}
                      className="bg-white text-purple-700 px-3 py-1.5 rounded-full text-sm font-medium flex items-center"
                    >
                      <ImagePlus size={16} className="mr-1" />
                      基于图片创建
                    </button>
                    <button 
                      onClick={previewSelected}
                      className="bg-purple-600 text-white px-3 py-1.5 rounded-full text-sm font-medium flex items-center"
                    >
                      <Send size={16} className="mr-1" />
                      发布图文
                    </button>
                  </div>
                ) : (
                  // 如果未选中图片，只显示创建按钮
                  <button 
                    onClick={() => setActiveTab('create')}
                    className="bg-white text-purple-700 px-3 py-1.5 rounded-full text-sm font-medium flex items-center"
                  >
                    <Plus size={16} className="mr-1" />
                    创建图片
                  </button>
                )}
              </>
            )}
          </div>
        </div>
        
        {/* 主内容区域，使用条件渲染而不是替换整个内容 */}
        <div className="flex-1 w-full overflow-auto">
          {/* 图片选择区域 */}
          {activeTab === 'select' && (
            <>
              {/* 搜索和筛选 - 移除多余的上边距 */}
              <div className="bg-white sticky top-16 z-10 shadow-sm w-full pt-1 pb-2 px-3">
                <div className="flex mb-2 mt-1">
                  <div className="relative flex-1">
                    <input 
                      type="text" 
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full border border-gray-300 rounded-full pl-10 pr-3 py-2 text-sm"
                      placeholder="搜索图片..."
                    />
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  <button 
                    onClick={() => setShowFilters(!showFilters)}
                    className="ml-2 p-2 border border-gray-300 rounded-full bg-white text-gray-600"
                  >
                    <Filter size={18} />
                  </button>
                </div>
                
                {showFilters && (
                  <div className="bg-gray-50 rounded-lg p-3 mb-3">
                    <div className="mb-2">
                      <h3 className="text-sm font-medium mb-1.5 text-gray-700">筛选方式</h3>
                      <div className="flex flex-wrap gap-2">
                        <button 
                          onClick={() => setFilterType('all')}
                          className={`px-3 py-1 rounded-full text-xs ${filterType === 'all' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                        >
                          全部图片
                        </button>
                        <button 
                          onClick={() => setFilterType('unused')}
                          className={`px-3 py-1 rounded-full text-xs ${filterType === 'unused' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                        >
                          未使用
                        </button>
                        <button 
                          onClick={() => setFilterType('used')}
                          className={`px-3 py-1 rounded-full text-xs ${filterType === 'used' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                        >
                          已使用
                        </button>
                        <button 
                          onClick={() => setFilterType('favorite')}
                          className={`px-3 py-1 rounded-full text-xs ${filterType === 'favorite' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                        >
                          收藏图片
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium mb-1.5 text-gray-700">排序方式</h3>
                      <div className="flex flex-wrap gap-2">
                        <button 
                          onClick={() => setSortType('newest')}
                          className={`px-3 py-1 rounded-full text-xs ${sortType === 'newest' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                        >
                          最新创建
                        </button>
                        <button 
                          onClick={() => setSortType('popular')}
                          className={`px-3 py-1 rounded-full text-xs ${sortType === 'popular' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                        >
                          最受欢迎
                        </button>
                        <button 
                          onClick={() => setSortType('oldest')}
                          className={`px-3 py-1 rounded-full text-xs ${sortType === 'oldest' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                        >
                          最早创建
                        </button>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* 任务进度展示 - 减少上下边距 */}
                {generationTasks.length > 0 && generationTasks.some(task => task.progress < 100) && (
                  <div className="mb-2 bg-indigo-50 rounded-lg p-2">
                    <div className="flex justify-between items-center mb-1.5">
                      <h3 className="text-sm font-medium text-indigo-700 flex items-center">
                        <Clock size={16} className="mr-1.5" />
                        图片生成中
                      </h3>
                      <span className="text-xs text-indigo-600">开始于 {generationTasks[0].startTime}</span>
                    </div>
                    
                    <div className="w-full bg-indigo-100 rounded-full h-2.5">
                      <div 
                        className="bg-indigo-600 h-2.5 rounded-full transition-all duration-500"
                        style={{ width: `${generationTasks[0].progress}%` }}
                      ></div>
                    </div>
                    
                    <div className="flex justify-between items-center mt-1.5 text-xs text-indigo-600">
                      <span>{generationTasks[0].progress}% 完成</span>
                      <span>预计剩余时间: {generationTasks[0].estimatedTime}</span>
                    </div>
                    
                    <div className="mt-1.5 text-xs text-gray-500 flex items-center">
                      <span className="truncate">{generationTasks[0].prompt}</span>
                      <span className="ml-1 bg-indigo-100 text-indigo-700 px-1.5 py-0.5 rounded-full">
                        {generationTasks[0].imageCount}张
                      </span>
                    </div>
                  </div>
                )}
              </div>
              
              {/* 图片列表容器 - 减少上边距 */}
              <div className="px-3 pb-3 pt-1 w-full">
                {/* 选择提示 - 更紧凑的样式 */}
                <div className="text-center text-sm font-medium text-gray-700 py-2 px-4 mb-3 bg-blue-50 border border-blue-100 rounded-lg flex items-center justify-center shadow-sm z-10 relative">
                  <Hand size={16} className="mr-2 text-blue-500" />
                  <span>请点击选择图片</span>
                </div>
                
                {/* 图片网格 */}
                <div className="grid grid-cols-2 gap-3">
                  {filteredImages.map((image) => (
                    <div 
                      key={image.id}
                      onClick={() => setSelectedImage(image.id === selectedImage ? null : image.id)}
                      className={`relative rounded-lg overflow-hidden border-2 ${
                        image.id === selectedImage 
                          ? 'border-purple-500 shadow-md' 
                          : 'border-transparent'
                      }`}
                    >
                      <div className="image-placeholder" style={{aspectRatio: '3/4'}}></div>
                      
                      {/* 选中标记 */}
                      {image.id === selectedImage && (
                        <div className="absolute top-2 right-2 bg-purple-600 text-white p-1 rounded-full">
                          <Check size={16} />
                        </div>
                      )}
                      
                      {/* 图片标签 */}
                      <div className="absolute top-2 left-2 flex space-x-1">
                        {image.isHD && (
                          <div className="bg-indigo-600 text-white text-xs px-1.5 py-0.5 rounded">
                            HD
                          </div>
                        )}
                        {image.isFavorite && (
                          <div className="bg-amber-500 text-white text-xs px-1.5 py-0.5 rounded flex items-center">
                            <Bookmark size={10} className="mr-0.5" />
                            收藏
                          </div>
                        )}
                      </div>
                      
                      {/* 使用状态 */}
                      {image.used && (
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs py-1 px-2 flex justify-between items-center">
                          <span className="flex items-center">
                            <CheckCircle size={12} className="mr-1 text-green-400" />
                            已使用
                          </span>
                          <span>{image.usedCount}次</span>
                        </div>
                      )}
                      
                      {/* 统计信息 */}
                      <div className="bg-white p-2 border-t border-gray-100">
                        <div className="flex justify-between items-center text-xs">
                          <div className="text-gray-500">{image.createdAt}</div>
                          <div className="flex items-center space-x-2">
                            {(image.statistics.views > 0 || image.statistics.likes > 0) && (
                              <>
                                <span className="flex items-center text-gray-500">
                                  <Eye size={12} className="mr-0.5" />
                                  {formatNumber(image.statistics.views)}
                                </span>
                                <span className="flex items-center text-gray-500">
                                  <Heart size={12} className="mr-0.5" />
                                  {formatNumber(image.statistics.likes)}
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                {filteredImages.length === 0 && (
                  <div className="flex flex-col items-center justify-center py-10">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-3">
                      <Image size={24} className="text-gray-400" />
                    </div>
                    <p className="text-gray-500 text-sm">暂无符合条件的图片</p>
                    <button 
                      onClick={() => {
                        setSearchQuery('');
                        setFilterType('all');
                      }}
                      className="mt-2 text-purple-600 text-sm"
                    >
                      清除筛选条件
                    </button>
                  </div>
                )}
              </div>
            </>
          )}

          {/* 创建新图片页面 */}
          {activeTab === 'create' && (
            <div className="bg-white w-full">
              <div className="p-4">
                {/* 角色照片展示 - 仅当从图片选择页面带入时展示 */}
                {selectedCharacterPhoto && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      基于角色图片
                    </label>
                    <div className="flex items-center bg-gray-50 p-3 rounded-lg">
                      <div className="w-16 h-16 rounded-lg overflow-hidden image-placeholder"></div>
                      <div className="ml-3">
                        <p className="text-sm text-gray-700">基于所选图片特征创建</p>
                        <p className="text-xs text-gray-500 mt-0.5">将保持角色特征与风格一致性</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* 描述输入 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    画面描述
                  </label>
                  <div className="relative">
                    <textarea
                      value={promptText}
                      onChange={(e) => setPromptText(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg p-3 h-32 text-sm"
                      placeholder="描述你想要的图片画面、角色动作、场景、氛围等..."
                    ></textarea>
                    <div className="absolute right-2 bottom-2 flex space-x-1">
                      <button 
                        onClick={polishPrompt}
                        disabled={!promptText || isPolishing}
                        className={`p-1.5 rounded-full ${!promptText || isPolishing ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                        title="AI润色"
                      >
                        {isPolishing ? <Loader size={16} className="animate-spin" /> : <Wand2 size={16} />}
                      </button>
                      <button 
                        onClick={rewritePrompt}
                        disabled={!promptText || isRewriting}
                        className={`p-1.5 rounded-full ${!promptText || isRewriting ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                        title="AI重写"
                      >
                        {isRewriting ? <Loader size={16} className="animate-spin" /> : <RefreshCw size={16} />}
                      </button>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1 flex justify-between">
                    <span>{promptText.length}/300 字符</span>
                    <span>描述越详细，生成结果越精准</span>
                  </div>
                </div>
                
                {/* 推荐描述 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    推荐描述
                  </label>
                  <div className="space-y-2">
                    {suggestedPrompts.map((prompt, index) => (
                      <div 
                        key={index}
                        onClick={() => setPromptText(prompt)}
                        className="border border-gray-200 rounded-lg p-2.5 text-xs cursor-pointer hover:border-purple-300 hover:bg-purple-50 transition-colors"
                      >
                        {prompt}
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* 生成张数 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    生成张数
                  </label>
                  <div className="flex items-center">
                    <button 
                      onClick={() => setImageCount(Math.max(1, imageCount - 1))}
                      className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-700 border border-gray-300 text-lg font-medium"
                    >
                      -
                    </button>
                    <div className="mx-6 text-center">
                      <div className="text-2xl font-bold text-gray-800">{imageCount}</div>
                      <div className="text-xs text-gray-500">每张3星光币</div>
                    </div>
                    <button 
                      onClick={() => setImageCount(Math.min(10, imageCount + 1))}
                      className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-700 border border-gray-300 text-lg font-medium"
                    >
                      +
                    </button>
                  </div>
                </div>
                
                {/* 消费提示和按钮 */}
                <div className="mt-6">
                  <div className="flex justify-between items-center mb-4">
                    <div className="text-sm font-medium text-gray-700">
                      需要消费: <span className="font-bold text-amber-600">{calculateCost()} 星光币</span>
                    </div>
                    <div className="text-xs text-amber-600 font-medium">
                      当前拥有: 128 星光币
                    </div>
                  </div>
                  
                  <button 
                    onClick={generateNewImages}
                    disabled={!promptText || isGenerating}
                    className={`w-full py-3 rounded-lg text-white font-medium flex items-center justify-center ${
                      !promptText || isGenerating ? 'bg-gray-400' : 'bg-gradient-to-r from-purple-600 to-indigo-600'
                    }`}
                  >
                    {isGenerating ? (
                      <>
                        <Loader size={18} className="animate-spin mr-2" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <ImagePlus size={18} className="mr-2" />
                        立即生成
                      </>
                    )}
                  </button>
                  
                  <div className="mt-2 text-xs text-gray-500 text-center">
                    创建后需等待约3分钟，可以离开此页面
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* 预览和发布区域 */}
          {activeTab === 'preview' && selectedImage && (
            <div className="bg-white w-full">
              <div className="p-4">
                {/* 预览图片 */}
                <div className="relative rounded-lg overflow-hidden mb-4">
                  <div className="image-placeholder" style={{height: '300px'}}></div>
                  
                  {/* 图片标签 */}
                  <div className="absolute top-2 left-2 flex space-x-1">
                    {existingImages.find(img => img.id === selectedImage)?.isHD && (
                      <div className="bg-indigo-600 text-white text-xs px-1.5 py-0.5 rounded">
                        HD
                      </div>
                    )}
                  </div>
                </div>
                
                {/* 创建内容表单 */}
                <div className="mb-4">
                  <div className="relative mb-3">
                    <input 
                      type="text"
                      value={publishTitle}
                      onChange={(e) => setPublishTitle(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg p-3 text-sm pr-20"
                      placeholder="添加标题（选填）"
                    />
                    <div className="absolute top-1/2 transform -translate-y-1/2 right-2 flex space-x-1">
                      <button
                        className="p-1.5 rounded-full text-purple-600 hover:bg-purple-50"
                        title="AI润色"
                      >
                        <Wand2 size={16} />
                      </button>
                      <button
                        className="p-1.5 rounded-full text-indigo-600 hover:bg-indigo-50"
                        title="AI重写"
                      >
                        <RefreshCw size={16} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="relative">
                    <textarea
                      value={publishContent}
                      onChange={(e) => setPublishContent(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg p-3 h-24 text-sm"
                      placeholder="分享你的想法..."
                    ></textarea>
                    <div className="absolute right-2 bottom-2 flex space-x-1">
                      <button 
                        className="p-1.5 rounded-full text-purple-600 hover:bg-purple-50"
                        title="AI润色"
                      >
                        <Wand2 size={16} />
                      </button>
                      <button 
                        className="p-1.5 rounded-full text-indigo-600 hover:bg-indigo-50"
                        title="AI重写"
                      >
                        <RefreshCw size={16} />
                      </button>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {publishContent.length}/500 字符
                  </div>
                </div>
                
                {/* 发布选项 */}
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <h3 className="text-sm font-medium mb-2">发布设置</h3>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Users size={16} className="text-gray-500 mr-2" />
                        <span className="text-sm text-gray-700">谁可以看到</span>
                      </div>
                      <button className="flex items-center text-sm text-gray-700">
                        <span>所有人</span>
                        <ChevronDown size={16} className="ml-1" />
                      </button>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <MessageCircle size={16} className="text-gray-500 mr-2" />
                        <span className="text-sm text-gray-700">允许评论</span>
                      </div>
                      <div className="relative inline-block w-10 h-6 rounded-full bg-purple-600">
                        <div className="absolute right-1 top-1 w-4 h-4 rounded-full bg-white transition-all"></div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Calendar size={16} className="text-gray-500 mr-2" />
                        <span className="text-sm text-gray-700">定时发布</span>
                      </div>
                      <div className="relative inline-block w-10 h-6 rounded-full bg-gray-300">
                        <div className="absolute left-1 top-1 w-4 h-4 rounded-full bg-white transition-all"></div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* 发布按钮 */}
                <div className="pt-2">
                  <button 
                    className="w-full py-3.5 rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-medium flex items-center justify-center"
                  >
                    <Send size={18} className="mr-2" />
                    发布动态
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      
        {/* 占位样式 */}
        <style jsx>{`
          .image-placeholder {
            background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
            background-size: 20px 20px;
            position: relative;
          }
          .image-placeholder::after {
            content: '图片预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: #666;
          }
        `}</style>
      </div>
    </div>
  );
};

export default ImagePublishPage;