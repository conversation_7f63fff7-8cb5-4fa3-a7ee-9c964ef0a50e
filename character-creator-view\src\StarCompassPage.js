import React, { useState, useEffect } from 'react';
import { ChevronLeft, TrendingUp, TrendingDown, Sparkles, Target, BarChart3, Star, RefreshCw, ArrowUp, ArrowDown, Calendar, Zap, Info, Eye, MessageCircle, AlertCircle, Shield, Heart } from 'lucide-react';

// 导入头像图片
import characterAvatar from './assets/沈婉.png';

const StarCompassPage = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [todayProfit] = useState(158);
  const [profitRate] = useState(1.26);
  const [showDetailView, setShowDetailView] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState('shanghai');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [followedStocks, setFollowedStocks] = useState([]);
  const [analyzingStocks, setAnalyzingStocks] = useState([]);
  const [showReport, setShowReport] = useState(false);
  const [reportStock, setReportStock] = useState(null);

  // 角色信息
  const character = {
    name: '沈婉',
    avatar: characterAvatar,
    investmentStyle: '稳健保守'
  };

  // 大盘数据
  const marketData = {
    shanghai: { name: '上证', value: 3340.69, change: -0.18, color: 'text-green-600' },
    shenzhen: { name: '深成', value: 10029.11, change: -0.61, color: 'text-green-600' },
    chinext: { name: '创业板', value: 1991.64, change: -0.68, color: 'text-green-600' }
  };

  // 处理查看分析报告
  const handleViewReport = (stock) => {
   setReportStock(stock);
   setShowReport(true);
  };

  // AI精选股票数据 - 最后一个股票增加心标状态
  const aiSelectedStocks = [
    {
      name: '招商银行',
      code: '600036',
      price: 32.56,
      change: -0.12,
      changePercent: -0.37,
      targetPrice: 35.80,
      suggestion: '买入',
      riskLevel: '低',
      aiComment: '大金融板块防御性强，估值处于历史低位，股息率诱人',
      hasReport: true,
      isBookmarked: false
    },
    {
      name: '比亚迪',
      code: '002594',
      price: 245.30,
      change: 1.85,
      changePercent: 0.76,
      targetPrice: 260.00,
      suggestion: '持有',
      riskLevel: '中',
      aiComment: '新能源龙头，政策利好预期，但短期涨幅较大建议持有待调整',
      hasReport: false,
      isBookmarked: false
    },
    {
      name: '贵州茅台',
      code: '600519',
      price: 1680.00,
      change: -5.20,
      changePercent: -0.31,
      targetPrice: 1750.00,
      suggestion: '观望',
      riskLevel: '低',
      aiComment: '消费白马，长期价值稳定，等待更好的介入时机',
      hasReport: false,
      isBookmarked: false
    },
    {
      name: '宁德时代',
      code: '300750',
      price: 186.50,
      change: 2.30,
      changePercent: 1.25,
      targetPrice: 195.00,
      suggestion: '买入',
      riskLevel: '中',
      aiComment: '新能源电池龙头，行业景气度高，技术优势明显',
      hasReport: false,
      isBookmarked: true  // 最后一个股票标记为已心标
    }
  ];

  // 今日操作记录
  const todayOperations = [
    { type: 'buy', stock: '科技未来', time: '10:30', reason: '顺势而为，AI判断上升趋势', amount: 50 },
    { type: 'sell', stock: '传统制造', time: '14:20', reason: '获利了结，规避风险', amount: 30 },
    { type: 'buy', stock: '新能源车', time: '15:45', reason: '政策利好，逢低吸纳', amount: 20 }
  ];

  // 下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  // 获取涨跌颜色
  const getChangeColor = (change) => {
    if (change > 0) return 'text-red-600';
    if (change < 0) return 'text-green-600';
    return 'text-gray-600';
  };

  // 市场情绪分析
  const getMarketSentiment = () => {
    const avgChange = (marketData.shanghai.change + marketData.shenzhen.change + marketData.chinext.change) / 3;
    if (avgChange > 0.5) return { text: '热情🔥', color: 'text-red-600', bg: 'bg-red-50' };
    if (avgChange > -0.5) return { text: '平稳😐', color: 'text-yellow-600', bg: 'bg-yellow-50' };
    return { text: '谨慎😟', color: 'text-green-600', bg: 'bg-green-50' };
  };

  const sentiment = getMarketSentiment();

  // 处理聊天按钮点击
  const handleChatClick = (context) => {
    console.log('Navigate to chat with context:', context);
  };

  // 显示Toast
  const showToastMessage = (message) => {
    setToastMessage(message);
    setShowToast(true);
    setTimeout(() => {
      setShowToast(false);
    }, 3000);
  };

  // 处理关注股票
  const handleFollowStock = (code) => {
    if (!followedStocks.includes(code)) {
      setFollowedStocks([...followedStocks, code]);
      showToastMessage('好的，已加入关注对象，如果今天有好的买入机会就会下手。');
    }
  };

  // 处理分析股票
  const handleAnalyzeStock = (code) => {
    setAnalyzingStocks([...analyzingStocks, code]);
    showToastMessage('嗯呢，我会搜索资料好好分析一下，待会把分析报告给你哦。');
    
    // 模拟分析完成
    setTimeout(() => {
      setAnalyzingStocks(analyzingStocks.filter(c => c !== code));
    }, 2000);
  };

  // 获取建议标签样式
  const getSuggestionStyle = (suggestion) => {
    switch (suggestion) {
      case '买入':
        return 'bg-red-50 text-red-600 border-red-200';
      case '持有':
        return 'bg-blue-50 text-blue-600 border-blue-200';
      case '观望':
        return 'bg-yellow-50 text-yellow-600 border-yellow-200';
      case '卖出':
        return 'bg-green-50 text-green-600 border-green-200';
      default:
        return 'bg-gray-50 text-gray-600 border-gray-200';
    }
  };

  // 获取风险等级样式
  const getRiskLevelStyle = (level) => {
    switch (level) {
      case '低':
        return { color: 'text-green-600', bg: 'bg-green-50', icon: '🛡️' };
      case '中':
        return { color: 'text-yellow-600', bg: 'bg-yellow-50', icon: '⚡' };
      case '高':
        return { color: 'text-red-600', bg: 'bg-red-50', icon: '🔥' };
      default:
        return { color: 'text-gray-600', bg: 'bg-gray-50', icon: '❓' };
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-50 overflow-hidden">
      {/* Toast通知 */}
      {showToast && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 animate-slide-down">
          <div className="bg-white rounded-full shadow-lg px-4 py-3 flex items-center gap-3">
            <img src={character.avatar} alt="" className="w-8 h-8 rounded-full object-cover" />
            <p className="text-sm text-gray-700">{toastMessage}</p>
          </div>
        </div>
      )}

      {/* 手机端容器 */}
      <div className="relative w-full max-w-[390px] h-full mx-auto bg-white shadow-xl">
        {/* 顶部导航栏 */}
        <div className="fixed top-0 left-0 right-0 max-w-[390px] mx-auto bg-white z-30 border-b border-gray-100">
          <div className="flex items-center justify-between px-4 py-3">
            <button className="p-2 -ml-2">
              <ChevronLeft className="w-6 h-6" />
            </button>
            <h1 className="font-semibold text-lg flex items-center gap-2">
              <Target className="w-5 h-5 text-purple-500" />
              星语罗盘
            </h1>
            <button 
              onClick={handleRefresh}
              className={`p-2 -mr-2 ${refreshing ? 'animate-spin' : ''}`}
            >
              <RefreshCw className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="pt-16 pb-4 h-full overflow-y-auto">
          
          {/* 大盘指数 - 最重要信息置顶 */}
          <div className="px-4 py-4">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-5">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-bold text-gray-800">今日大盘</h2>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${sentiment.bg} ${sentiment.color}`}>
                  市场情绪：{sentiment.text}
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-3">
                {Object.entries(marketData).map(([key, data]) => (
                  <div key={key} className="bg-white/60 backdrop-blur rounded-xl p-3 text-center">
                    <p className="text-sm text-gray-600 mb-1">{data.name}</p>
                    <p className="text-lg font-bold text-gray-800">{data.value}</p>
                    <p className={`text-sm font-medium ${getChangeColor(data.change)}`}>
                      {data.change > 0 ? '+' : ''}{data.change}%
                    </p>
                  </div>
                ))}
              </div>
              
              <button 
                onClick={() => setShowDetailView(true)}
                className="w-full mt-3 bg-white/40 backdrop-blur rounded-xl py-2 text-sm text-blue-700 font-medium flex items-center justify-center gap-1 active:bg-white/60"
              >
                <Eye className="w-4 h-4" />
                查看详细数据
              </button>
            </div>
          </div>

          {/* 个人收益概览 */}
          <div className="px-4 mb-4">
            <div className="bg-gradient-to-br from-purple-50 to-pink-100 rounded-3xl p-5">
              <h3 className="text-lg font-bold mb-3 flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-purple-600" />
                我的收益
              </h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-white/60 backdrop-blur rounded-xl p-3">
                  <p className="text-sm text-gray-600 mb-1">今日收益</p>
                  <p className={`text-xl font-bold ${getChangeColor(todayProfit)}`}>
                    {todayProfit > 0 ? '+' : ''}¥{todayProfit}
                  </p>
                </div>
                <div className="bg-white/60 backdrop-blur rounded-xl p-3">
                  <p className="text-sm text-gray-600 mb-1">收益率</p>
                  <p className={`text-xl font-bold ${getChangeColor(profitRate)}`}>
                    +{profitRate}%
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* AI角色的市场解读 */}
          <div className="px-4 mb-4">
            <div className="bg-gradient-to-br from-yellow-50 to-orange-100 rounded-3xl p-5">
              <div className="flex items-start gap-3 mb-4">
                <img src={character.avatar} alt="" className="w-12 h-12 rounded-full border-2 border-white shadow-md object-cover" />
                <div className="flex-1">
                  <p className="font-medium text-gray-800">{character.name}的市场解读</p>
                  <p className="text-xs text-gray-500">{new Date().toLocaleString()}</p>
                </div>
                <Sparkles className="w-5 h-5 text-yellow-500" />
              </div>
              
              <div className="bg-white/60 backdrop-blur rounded-xl p-4">
                <p className="text-sm text-gray-700 leading-relaxed">
                  "今天的市场就像秋日的午后，虽然三大指数都在小幅调整，但我觉得这更像是在积蓄力量呢！看到涨停榜上科技股依然坚挺，特别是中旗股份和格力博，它们就像秋日里最后绽放的花朵，格外耀眼✨ 
                  <br /><br />
                  主人，虽然大盘有些疲软，但我已经悄悄调整了仓位，减持了一些传统制造，增加了新能源的配置。相信明天会有惊喜哦~"
                </p>
              </div>

              {/* 关键指标 */}
              <div className="grid grid-cols-3 gap-2 mt-3">
                <div className="bg-white/40 backdrop-blur rounded-xl p-2 text-center">
                  <p className="text-xs text-gray-600">热门板块</p>
                  <p className="text-sm font-bold text-purple-600">科技🚀</p>
                </div>
                <div className="bg-white/40 backdrop-blur rounded-xl p-2 text-center">
                  <p className="text-xs text-gray-600">操作策略</p>
                  <p className="text-sm font-bold text-orange-600">调仓⚖️</p>
                </div>
                <div className="bg-white/40 backdrop-blur rounded-xl p-2 text-center">
                  <p className="text-xs text-gray-600">风险等级</p>
                  <p className="text-sm font-bold text-green-600">低风险🛡️</p>
                </div>
              </div>

              {/* 与AI对话按钮 */}
              <button
                onClick={() => handleChatClick('market-analysis')}
                className="w-full mt-4 bg-gradient-to-r from-orange-400 to-yellow-400 text-white rounded-2xl py-3 flex items-center justify-center gap-2 font-medium shadow-lg active:scale-95 transition-transform"
              >
                <MessageCircle className="w-5 h-5" />
                <span>与{character.name}聊聊</span>
                <span className="text-xs opacity-80 ml-1">💬</span>
              </button>
            </div>
          </div>

          {/* AI精选关注 - 替换原来的涨幅榜 */}
          <div className="px-4 mb-4">
            <div className="bg-white rounded-3xl shadow-sm">
              <div className="p-4 border-b border-gray-100">
                <h4 className="font-semibold flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-purple-500" />
                  今日精选关注
                  <span className="text-xs text-gray-500 ml-auto">AI智能推荐</span>
                </h4>
              </div>
              <div className="divide-y divide-gray-100">
                {aiSelectedStocks.map((stock, index) => (
                  <div key={index} className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium text-gray-800">{stock.name}</p>
                          <p className="text-xs text-gray-500">{stock.code}</p>
                          <span className={`text-xs px-2 py-0.5 rounded-full border ${getSuggestionStyle(stock.suggestion)}`}>
                            {stock.suggestion}
                          </span>
                          {/* 心标标识 */}
                          {stock.isBookmarked && (
                            <span className="bg-pink-100 text-pink-600 px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1">
                              <Heart className="w-3 h-3 fill-current" />
                              已心标
                            </span>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <span className="font-semibold text-gray-800">¥{stock.price}</span>
                          <span className={`font-medium ${getChangeColor(stock.change)}`}>
                            {stock.change > 0 ? '+' : ''}{stock.change} ({stock.changePercent > 0 ? '+' : ''}{stock.changePercent}%)
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-gray-500 mb-1">目标价</p>
                        <p className="text-sm font-semibold text-purple-600">¥{stock.targetPrice}</p>
                      </div>
                    </div>

                    {/* AI评语和风险等级 */}
                    <div className="bg-gray-50 rounded-xl p-3">
                      <p className="text-xs text-gray-700 leading-relaxed">{stock.aiComment}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className={`text-xs px-2 py-0.5 rounded-full ${getRiskLevelStyle(stock.riskLevel).bg} ${getRiskLevelStyle(stock.riskLevel).color}`}>
                          风险{stock.riskLevel} {getRiskLevelStyle(stock.riskLevel).icon}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 今日操作轨迹 */}
          <div className="px-4 mb-4">
            <div className="bg-white rounded-3xl shadow-sm">
              <div className="p-4 border-b border-gray-100">
                <h4 className="font-semibold flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-blue-500" />
                  今日操作轨迹
                </h4>
              </div>
              <div className="divide-y divide-gray-100">
                {todayOperations.map((op, index) => (
                  <div key={index} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          op.type === 'buy' ? 'bg-red-50' : 'bg-green-50'
                        }`}>
                          {op.type === 'buy' ? 
                            <ArrowUp className="w-4 h-4 text-red-500" /> : 
                            <ArrowDown className="w-4 h-4 text-green-500" />
                          }
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium text-gray-800">
                              {op.type === 'buy' ? '买入' : '卖出'} {op.stock}
                            </p>
                            <span className="text-xs text-gray-500">{op.time}</span>
                          </div>
                          <p className="text-sm text-gray-600">{op.reason}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-700">{op.amount}%</p>
                        <p className="text-xs text-gray-500">仓位</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 明日展望 */}
          <div className="px-4 mb-6">
            <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-3xl p-5">
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Star className="w-5 h-5 text-green-600" />
                明日展望
              </h4>
              <div className="bg-white/60 backdrop-blur rounded-xl p-4">
                <p className="text-sm text-gray-700 leading-relaxed">
                  "明天我想像勤劳的小蜜蜂一样，在新能源的花丛中多采些蜜！听说有政策春风要来，我已经准备好小篮子了🧺 
                  <br /><br />
                  科技股今天表现不错，但涨得有点快，像个调皮的孩子，明天我会找机会让它休息一下。还有啊，如果大盘继续调整，我可能会逢低增加一些优质白马股哦~"
                </p>
              </div>
              
              <div className="grid grid-cols-2 gap-3 mt-3">
                <div className="bg-white/40 backdrop-blur rounded-xl p-3 text-center">
                  <p className="text-xs text-gray-600 mb-1">明日重点</p>
                  <p className="text-sm font-bold text-green-600">新能源 + 白马股</p>
                </div>
                <div className="bg-white/40 backdrop-blur rounded-xl p-3 text-center">
                  <p className="text-xs text-gray-600 mb-1">操作倾向</p>
                  <p className="text-sm font-bold text-blue-600">逢低吸纳</p>
                </div>
              </div>

              {/* 与AI对话按钮 */}
              <button
                onClick={() => handleChatClick('tomorrow-outlook')}
                className="w-full mt-4 bg-gradient-to-r from-emerald-400 to-green-400 text-white rounded-2xl py-3 flex items-center justify-center gap-2 font-medium shadow-lg active:scale-95 transition-transform"
              >
                <MessageCircle className="w-5 h-5" />
                <span>与{character.name}聊聊</span>
                <span className="text-xs opacity-80 ml-1">🌟</span>
              </button>
            </div>
          </div>
        </div>

        {/* 详细数据弹窗 */}
        {showDetailView && (
          <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4" onClick={() => setShowDetailView(false)}>
            <div className="bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up max-h-[70vh] overflow-y-auto" onClick={e => e.stopPropagation()}>
              <h3 className="text-lg font-bold mb-4 text-center">大盘详细数据</h3>
              
              <div className="space-y-4">
                {Object.entries(marketData).map(([key, data]) => (
                  <div key={key} className="bg-gray-50 rounded-xl p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-semibold text-gray-800">{data.name}指数</h4>
                      <span className={`text-sm font-medium ${getChangeColor(data.change)}`}>
                        {data.change > 0 ? '+' : ''}{data.change}%
                      </span>
                    </div>
                    <p className="text-2xl font-bold text-gray-800 mb-2">{data.value}</p>
                    <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                      <div>今开: 3342.15</div>
                      <div>昨收: 3346.35</div>
                      <div>最高: 3355.82</div>
                      <div>最低: 3338.91</div>
                    </div>
                  </div>
                ))}
              </div>
              
              <button
                onClick={() => setShowDetailView(false)}
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold mt-6"
              >
                知道了
              </button>
            </div>
          </div>
        )}

        {/* 分析报告弹窗 */}
        {showReport && reportStock && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-end" onClick={() => setShowReport(false)}>
            <div 
            className="bg-white rounded-t-3xl w-full max-w-[390px] mx-auto animate-slide-up max-h-[85vh] overflow-hidden flex flex-col"
            onClick={e => e.stopPropagation()}
            >
            {/* 拖动指示器 */}
            <div className="py-2">
                <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto"></div>
            </div>
            
            {/* 报告内容 */}
            <div className="flex-1 overflow-y-auto px-4 pb-4">
                {/* 报告头部 */}
                <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                    <div>
                    <h3 className="text-xl font-bold">{reportStock.name}</h3>
                    <p className="text-sm text-gray-500">{reportStock.code}</p>
                    </div>
                    <div className="text-right">
                    <p className="text-sm text-gray-500">目标价</p>
                    <p className="text-xl font-bold text-purple-600">¥{reportStock.targetPrice}</p>
                    </div>
                </div>
                
                {/* 评级 */}
                <div className="flex items-center gap-2 mb-4">
                    <span className="bg-red-100 text-red-600 px-3 py-1 rounded-full text-sm font-medium">
                    强烈推荐
                    </span>
                    <span className="text-sm text-gray-500">2025年1月28日</span>
                </div>
                </div>

                {/* 核心观点 */}
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-4 mb-4">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Sparkles className="w-4 h-4 text-purple-500" />
                    核心观点
                </h4>
                <p className="text-sm text-gray-700 leading-relaxed">
                    招商银行作为股份制银行龙头，资产质量优异，零售业务护城河深厚。当前估值处于历史低位，股息率超过5%，具备较高的安全边际和投资价值。
                </p>
                </div>

                {/* 投资亮点 */}
                <div className="mb-4">
                <h4 className="font-semibold mb-3">投资亮点</h4>
                <div className="space-y-3">
                    <div className="flex gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-sm font-bold text-blue-600">1</span>
                    </div>
                    <div>
                        <p className="font-medium text-gray-800 mb-1">零售之王地位稳固</p>
                        <p className="text-sm text-gray-600">零售客户数突破1.8亿，私人银行客户AUM超3万亿，高净值客户基础雄厚</p>
                    </div>
                    </div>
                    
                    <div className="flex gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-sm font-bold text-blue-600">2</span>
                    </div>
                    <div>
                        <p className="font-medium text-gray-800 mb-1">资产质量行业领先</p>
                        <p className="text-sm text-gray-600">不良率仅0.94%，拨备覆盖率超过450%，风险抵御能力强</p>
                    </div>
                    </div>
                    
                    <div className="flex gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-sm font-bold text-blue-600">3</span>
                    </div>
                    <div>
                        <p className="font-medium text-gray-800 mb-1">高股息率防御属性</p>
                        <p className="text-sm text-gray-600">连续10年稳定分红，当前股息率5.2%，适合长期持有</p>
                    </div>
                    </div>
                </div>
                </div>

                {/* 关键数据 */}
                <div className="bg-gray-50 rounded-2xl p-4 mb-4">
                <h4 className="font-semibold mb-3">关键财务指标</h4>
                <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white rounded-xl p-3">
                    <p className="text-xs text-gray-500 mb-1">市盈率(PE)</p>
                    <p className="text-lg font-bold">4.8x</p>
                    <p className="text-xs text-green-600">低于行业均值</p>
                    </div>
                    <div className="bg-white rounded-xl p-3">
                    <p className="text-xs text-gray-500 mb-1">市净率(PB)</p>
                    <p className="text-lg font-bold">0.65x</p>
                    <p className="text-xs text-green-600">历史低位</p>
                    </div>
                    <div className="bg-white rounded-xl p-3">
                    <p className="text-xs text-gray-500 mb-1">ROE</p>
                    <p className="text-lg font-bold">13.8%</p>
                    <p className="text-xs text-blue-600">行业第一</p>
                    </div>
                    <div className="bg-white rounded-xl p-3">
                    <p className="text-xs text-gray-500 mb-1">净利润增速</p>
                    <p className="text-lg font-bold">+6.2%</p>
                    <p className="text-xs text-blue-600">稳健增长</p>
                    </div>
                </div>
                </div>

                {/* 技术面分析 */}
                <div className="mb-4">
                <h4 className="font-semibold mb-3">技术面分析</h4>
                <div className="bg-blue-50 rounded-xl p-4">
                    <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">当前价格</span>
                    <span className="font-semibold">¥32.56</span>
                    </div>
                    <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">20日均线</span>
                    <span className="font-semibold">¥32.20</span>
                    </div>
                    <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">支撑位</span>
                    <span className="font-semibold text-green-600">¥31.50</span>
                    </div>
                    <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">压力位</span>
                    <span className="font-semibold text-red-600">¥33.80</span>
                    </div>
                </div>
                </div>

                {/* 风险提示 */}
                <div className="bg-orange-50 rounded-2xl p-4 mb-4">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <AlertCircle className="w-4 h-4 text-orange-500" />
                    风险提示
                </h4>
                <ul className="text-sm text-gray-700 space-y-1">
                    <li>• 宏观经济下行可能影响资产质量</li>
                    <li>• 净息差收窄压力持续存在</li>
                    <li>• 房地产风险敞口需持续关注</li>
                </ul>
                </div>

                {/* AI点评 */}
                <div className="bg-gradient-to-br from-yellow-50 to-orange-100 rounded-2xl p-4 mb-4">
                <div className="flex items-start gap-3 mb-2">
                    <img src={character.avatar} alt="" className="w-10 h-10 rounded-full border-2 border-white shadow-md object-cover" />
                    <div className="flex-1">
                    <p className="font-medium text-gray-800">沈婉的点评</p>
                    <p className="text-xs text-gray-500">稳健投资者视角</p>
                    </div>
                </div>
                <p className="text-sm text-gray-700 leading-relaxed">
                    "招商银行就像一位稳重的大哥哥，虽然不会给你惊喜的暴涨，但每年稳稳的分红就像定期收租一样让人安心。现在的价格就像在打折的优质资产，我已经悄悄建仓了呢！对于咱们稳健型投资者来说，这是非常好的长期配置标的哦~ 💎"
                </p>
                </div>
            </div>

            {/* 底部操作按钮 */}
            <div className="border-t border-gray-200 p-4 bg-white">
                <div className="flex gap-3">
                <button
                    onClick={() => {
                    handleChatClick('stock-analysis');
                    setShowReport(false);
                    }}
                    className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-medium flex items-center justify-center gap-2 shadow-lg active:scale-95 transition-transform"
                >
                    <MessageCircle className="w-5 h-5" />
                    与{character.name}聊聊
                </button>
                
                <button
                    onClick={() => {
                    handleFollowStock(reportStock.code);
                    setShowReport(false);
                    }}
                    disabled={followedStocks.includes(reportStock.code)}
                    className={`flex-1 py-3 rounded-2xl font-medium flex items-center justify-center gap-2 transition-all ${
                    followedStocks.includes(reportStock.code)
                        ? 'bg-green-100 text-green-600'
                        : 'bg-gray-100 text-gray-700 active:bg-gray-200'
                    }`}
                >
                    {followedStocks.includes(reportStock.code) ? (
                    <>
                        <Star className="w-5 h-5 fill-current" />
                        已关注
                    </>
                    ) : (
                    <>
                        <Star className="w-5 h-5" />
                        重点关注
                    </>
                    )}
                </button>
                </div>
            </div>
            </div>
        </div>
        )}
      </div>

      <style jsx>{`
        @keyframes scale-up {
          from {
            transform: scale(0.9);
            opacity: 0;
          }
          to {
            transform: scale(1);
            opacity: 1;
          }
        }
        
        @keyframes slide-down {
          from {
            transform: translateX(-50%) translateY(-20px);
            opacity: 0;
          }
          to {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
          }
        }
        
        .animate-scale-up {
          animation: scale-up 0.3s ease-out;
        }
        
        .animate-slide-down {
          animation: slide-down 0.3s ease-out;
        }
        
        @keyframes slide-up {
        from {
            transform: translateY(100%);
        }
        to {
            transform: translateY(0);
        }
        }

        .animate-slide-up {
        animation: slide-up 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default StarCompassPage;