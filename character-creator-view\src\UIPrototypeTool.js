import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Trash2, Plus, ZoomIn, ZoomOut } from 'lucide-react';

// 流程图组件
const Flowchart = ({ data, setCurrentScreenIndex, screens }) => {
  const [draggingNode, setDraggingNode] = useState(null);
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 });
  const [nodes, setNodes] = useState(data.nodes);
  const svgRef = useRef(null);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);

  // 当数据变化时更新节点
  useEffect(() => {
    setNodes(data.nodes);
  }, [data]);

  // 开始拖动节点
  const handleNodeDragStart = (e, nodeId) => {
    e.stopPropagation();
    setDraggingNode(nodeId);
    setDragStartPos({ x: e.clientX, y: e.clientY });
  };

  // 拖动节点
  const handleMouseMove = (e) => {
    if (!draggingNode) return;

    const deltaX = e.clientX - dragStartPos.x;
    const deltaY = e.clientY - dragStartPos.y;

    setNodes(nodes.map(node => {
      if (node.id === draggingNode) {
        return { ...node, x: node.x + deltaX, y: node.y + deltaY };
      }
      return node;
    }));

    setDragStartPos({ x: e.clientX, y: e.clientY });
  };

  // 结束拖动节点
  const handleMouseUp = () => {
    setDraggingNode(null);
  };

  // 处理节点点击 - 跳转到对应屏幕
  const handleNodeClick = (nodeId) => {
    const screenIndex = screens.findIndex(screen => screen.id === nodeId);
    if (screenIndex !== -1) {
      setCurrentScreenIndex(screenIndex);
    }
  };

  // 处理SVG平移
  const handleSvgPan = (e) => {
    if (draggingNode) return;

    const svg = svgRef.current;
    if (!svg) return;

    const startX = e.clientX;
    const startY = e.clientY;
    const startPan = { ...pan };

    const handlePanMove = (moveEvent) => {
      const deltaX = (moveEvent.clientX - startX) / zoom;
      const deltaY = (moveEvent.clientY - startY) / zoom;

      setPan({
        x: startPan.x + deltaX,
        y: startPan.y + deltaY
      });
    };

    const handlePanEnd = () => {
      document.removeEventListener('mousemove', handlePanMove);
      document.removeEventListener('mouseup', handlePanEnd);
    };

    document.addEventListener('mousemove', handlePanMove);
    document.addEventListener('mouseup', handlePanEnd);
  };

  // 处理缩放
  const handleWheel = (e) => {
    e.preventDefault();

    const deltaZoom = e.deltaY < 0 ? 1.1 : 0.9;
    const newZoom = zoom * deltaZoom;

    if (newZoom > 0.1 && newZoom < 5) {
      setZoom(newZoom);
    }
  };

  useEffect(() => {
    // 添加全局事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    const svg = svgRef.current;
    if (svg) {
      svg.addEventListener('wheel', handleWheel, { passive: false });
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      if (svg) {
        svg.removeEventListener('wheel', handleWheel);
      }
    };
  }, [draggingNode, dragStartPos, zoom]);

  return (
    <div className="flex-1 bg-white overflow-hidden relative">
      <div className="absolute top-4 left-4 z-10 bg-white p-2 rounded shadow">
        <div>拖动节点可调整位置</div>
        <div>点击节点可跳转到对应UI设计稿</div>
        <div>滚轮可缩放流程图</div>
        <div>按住鼠标拖动可平移流程图</div>
      </div>

      <svg
        ref={svgRef}
        width="100%"
        height="100%"
        className="cursor-move"
        onMouseDown={handleSvgPan}
      >
        <g transform={`translate(${pan.x}, ${pan.y}) scale(${zoom})`}>
          {/* 连接线 */}
          {data.links.map((link, index) => {
            const sourceNode = nodes.find(node => node.id === link.source);
            const targetNode = nodes.find(node => node.id === link.target);

            if (!sourceNode || !targetNode) return null;

            const startX = sourceNode.x + 100; // 节点宽度的一半
            const startY = sourceNode.y + 40; // 节点高度的一半
            const endX = targetNode.x + 100;
            const endY = targetNode.y + 40;

            // 计算控制点，创建平滑曲线
            const controlX1 = startX + (endX - startX) * 0.4;
            const controlY1 = startY;
            const controlX2 = endX - (endX - startX) * 0.4;
            const controlY2 = endY;

            return (
              <g key={`link-${index}`}>
                <path
                  d={`M${startX},${startY} C${controlX1},${controlY1} ${controlX2},${controlY2} ${endX},${endY}`}
                  stroke="#666"
                  strokeWidth="2"
                  fill="none"
                  markerEnd="url(#arrowhead)"
                />
              </g>
            );
          })}

          {/* 箭头标记定义 */}
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
            </marker>
          </defs>

          {/* 节点 */}
          {nodes.map((node) => (
            <g
              key={node.id}
              transform={`translate(${node.x}, ${node.y})`}
              onMouseDown={(e) => handleNodeDragStart(e, node.id)}
              onClick={() => handleNodeClick(node.id)}
              className="cursor-pointer"
            >
              <rect
                width="200"
                height="80"
                rx="10"
                ry="10"
                fill="#fff"
                stroke={draggingNode === node.id ? "#3b82f6" : "#ccc"}
                strokeWidth="2"
              />

              <text
                x="100"
                y="30"
                textAnchor="middle"
                dominantBaseline="middle"
                fontWeight="bold"
                fontSize="14"
              >
                {node.name}
              </text>

              <text
                x="100"
                y="50"
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="12"
                fill="#666"
              >
                {(screens.find(screen => screen.id === node.id)?.hotspots || []).length} 个热区
              </text>

              {/* 拖动手柄 */}
              <circle
                cx="190"
                cy="10"
                r="6"
                fill="#3b82f6"
              />
              <line x1="186" y1="10" x2="194" y2="10" stroke="white" strokeWidth="2" />
              <line x1="190" y1="6" x2="190" y2="14" stroke="white" strokeWidth="2" />
            </g>
          ))}
        </g>
      </svg>
    </div>
  );
};

// 主应用组件 - 可导出组件
const UIPrototypeTool = forwardRef(({
  initialScreens = [],
  onScreensChange,
  onCurrentScreenChange,
  onModeChange,
  className = "",
  showToolbar = true,
  showSidebar = true,
  defaultMode = 'view'
}, ref) => {
  const [screens, setScreens] = useState([]);
  const [currentScreenIndex, setCurrentScreenIndex] = useState(null);
  const [mode, setMode] = useState('view'); // 'view', 'edit'
  const [addingHotspot, setAddingHotspot] = useState(false);
  const [isDraggingHotspot, setIsDraggingHotspot] = useState(false);
  const [draggedHotspotIndex, setDraggedHotspotIndex] = useState(null);
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 });
  const [showFlowchart, setShowFlowchart] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const imageContainerRef = useRef(null);

  // 处理图片上传
  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);

    if (files.length === 0) return;

    const newScreens = files.map(file => {
      const url = URL.createObjectURL(file);
      return {
        id: `screen-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name: file.name.replace(/\.[^/.]+$/, ""),
        imageUrl: url,
        hotspots: []
      };
    });

    setScreens([...screens, ...newScreens]);

    if (currentScreenIndex === null && newScreens.length > 0) {
      setCurrentScreenIndex(screens.length);
    }
  };

  // 在当前显示的屏幕上添加热区
  const handleAddHotspot = (e) => {
    if (!addingHotspot || currentScreenIndex === null) return;

    const container = imageContainerRef.current;
    const rect = container.getBoundingClientRect();
    const x = (e.clientX - rect.left) / zoomLevel;
    const y = (e.clientY - rect.top) / zoomLevel;

    const updatedScreens = [...screens];
    const newHotspot = {
      id: `hotspot-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      x,
      y,
      width: 100,
      height: 100,
      targetScreenId: null
    };

    updatedScreens[currentScreenIndex].hotspots.push(newHotspot);
    setScreens(updatedScreens);
    setAddingHotspot(false);
  };

  // 删除热区
  const handleDeleteHotspot = (hotspotIndex) => {
    const updatedScreens = [...screens];
    updatedScreens[currentScreenIndex].hotspots.splice(hotspotIndex, 1);
    setScreens(updatedScreens);
  };

  // 开始拖动热区
  const handleHotspotDragStart = (e, hotspotIndex) => {
    if (mode !== 'edit') return;

    e.stopPropagation();
    setIsDraggingHotspot(true);
    setDraggedHotspotIndex(hotspotIndex);
    setDragStartPos({ x: e.clientX, y: e.clientY });
  };

  // 拖动热区
  const handleMouseMove = (e) => {
    if (!isDraggingHotspot || draggedHotspotIndex === null) return;

    const deltaX = (e.clientX - dragStartPos.x) / zoomLevel;
    const deltaY = (e.clientY - dragStartPos.y) / zoomLevel;

    const updatedScreens = [...screens];
    const hotspot = updatedScreens[currentScreenIndex].hotspots[draggedHotspotIndex];

    hotspot.x += deltaX;
    hotspot.y += deltaY;

    setScreens(updatedScreens);
    setDragStartPos({ x: e.clientX, y: e.clientY });
  };

  // 结束拖动热区
  const handleMouseUp = () => {
    if (isDraggingHotspot) {
      setIsDraggingHotspot(false);
      setDraggedHotspotIndex(null);
    }
  };

  // 设置热区目标屏幕
  const handleSetHotspotTarget = (hotspotIndex, targetScreenId) => {
    const updatedScreens = [...screens];
    updatedScreens[currentScreenIndex].hotspots[hotspotIndex].targetScreenId = targetScreenId;
    setScreens(updatedScreens);
  };

  // 处理热区点击 - 在查看模式下跳转到目标屏幕
  const handleHotspotClick = (hotspot) => {
    if (mode === 'view' && hotspot.targetScreenId) {
      const targetIndex = screens.findIndex(screen => screen.id === hotspot.targetScreenId);
      if (targetIndex !== -1) {
        setCurrentScreenIndex(targetIndex);
      }
    }
  };

  // 调整热区大小
  const handleResizeHotspot = (hotspotIndex, width, height) => {
    const updatedScreens = [...screens];
    const hotspot = updatedScreens[currentScreenIndex].hotspots[hotspotIndex];

    hotspot.width = width;
    hotspot.height = height;

    setScreens(updatedScreens);
  };

  // 生成流程图数据
  const generateFlowchartData = () => {
    const nodes = screens.map((screen, index) => ({
      id: screen.id,
      name: screen.name,
      x: 150 + (index % 3) * 300,
      y: 100 + Math.floor(index / 3) * 200
    }));

    const links = [];

    screens.forEach(screen => {
      screen.hotspots.forEach(hotspot => {
        if (hotspot.targetScreenId) {
          links.push({
            source: screen.id,
            target: hotspot.targetScreenId
          });
        }
      });
    });

    return { nodes, links };
  };

  useEffect(() => {
    // 添加全局事件监听器
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDraggingHotspot, draggedHotspotIndex, dragStartPos, zoomLevel]);

  // 当前显示的屏幕
  const currentScreen = currentScreenIndex !== null ? screens[currentScreenIndex] : null;

  // 流程图数据
  const flowchartData = generateFlowchartData();

  // 导出给父组件的方法
  useImperativeHandle(ref, () => ({
    // 获取当前所有屏幕数据
    getScreens: () => screens,

    // 获取当前选中的屏幕
    getCurrentScreen: () => currentScreenIndex !== null ? screens[currentScreenIndex] : null,

    // 设置当前显示的屏幕索引
    setCurrentScreenIndex: (index) => {
      if (index >= 0 && index < screens.length) {
        setCurrentScreenIndex(index);
        return true;
      }
      return false;
    },

    // 添加新屏幕
    addScreen: (screenData) => {
      const newScreens = [...screens, screenData];
      setScreens(newScreens);
      return newScreens.length - 1; // 返回新添加屏幕的索引
    },

    // 删除屏幕
    removeScreen: (index) => {
      if (index >= 0 && index < screens.length) {
        const newScreens = [...screens];
        newScreens.splice(index, 1);
        setScreens(newScreens);

        // 如果删除的是当前显示的屏幕，重置当前显示索引
        if (currentScreenIndex === index) {
          setCurrentScreenIndex(newScreens.length > 0 ? 0 : null);
        } else if (currentScreenIndex > index) {
          setCurrentScreenIndex(currentScreenIndex - 1);
        }
        return true;
      }
      return false;
    },

    // 切换模式
    setMode: (newMode) => {
      if (newMode === 'view' || newMode === 'edit') {
        setMode(newMode);
        return true;
      }
      return false;
    },

    // 显示/隐藏流程图
    toggleFlowchart: (show = null) => {
      if (show !== null) {
        setShowFlowchart(show);
      } else {
        setShowFlowchart(!showFlowchart);
      }
      return showFlowchart;
    },

    // 导出所有数据为JSON
    exportData: () => {
      return JSON.stringify({
        screens,
        currentScreenIndex
      });
    },

    // 导入数据
    importData: (jsonData) => {
      try {
        const data = JSON.parse(jsonData);
        if (data.screens && Array.isArray(data.screens)) {
          setScreens(data.screens);
          setCurrentScreenIndex(data.currentScreenIndex || 0);
          return true;
        }
        return false;
      } catch (error) {
        console.error('导入数据格式错误:', error);
        return false;
      }
    }
  }));

  // 当屏幕或当前屏幕索引变化时，通知父组件
  useEffect(() => {
    if (onScreensChange) {
      onScreensChange(screens);
    }
  }, [screens, onScreensChange]);

  useEffect(() => {
    if (onCurrentScreenChange && currentScreenIndex !== null) {
      onCurrentScreenChange(screens[currentScreenIndex], currentScreenIndex);
    }
  }, [currentScreenIndex, screens, onCurrentScreenChange]);

  // 当模式变化时，通知父组件
  useEffect(() => {
    if (onModeChange) {
      onModeChange(mode);
    }
  }, [mode, onModeChange]);

  // 初始化组件
  useEffect(() => {
    if (initialScreens && initialScreens.length > 0) {
      setScreens(initialScreens);
      setCurrentScreenIndex(0);
    }

    setMode(defaultMode);
  }, []);

  return (
    <div className={`flex flex-col h-full bg-gray-100 ${className}`}>
      {/* 顶部工具栏 */}
      {showToolbar && (
        <div className="bg-gray-800 text-white p-4 flex justify-between items-center">
          <h1 className="text-xl font-bold">UI原型交互工具</h1>

          <div className="flex space-x-4">
            <label className="flex items-center px-4 py-2 bg-blue-600 rounded cursor-pointer hover:bg-blue-700">
              <span>上传UI图片</span>
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
            </label>

            <button
              className={`px-4 py-2 rounded ${mode === 'view' ? 'bg-green-600' : 'bg-gray-600'}`}
              onClick={() => setMode('view')}
            >
              查看模式
            </button>

            <button
              className={`px-4 py-2 rounded ${mode === 'edit' ? 'bg-yellow-600' : 'bg-gray-600'}`}
              onClick={() => setMode('edit')}
            >
              编辑模式
            </button>

            <button
              className={`px-4 py-2 rounded ${showFlowchart ? 'bg-purple-600' : 'bg-gray-600'}`}
              onClick={() => setShowFlowchart(!showFlowchart)}
            >
              {showFlowchart ? '隐藏流程图' : '显示流程图'}
            </button>
          </div>
        </div>
      )}

      {/* 主内容区 */}
      <div className="flex flex-1 overflow-hidden">
        {/* 屏幕缩略图侧边栏 */}
        {showSidebar && (
          <div className="w-64 bg-gray-200 p-4 overflow-y-auto">
            <h2 className="font-bold mb-4">UI设计稿列表</h2>

            {screens.map((screen, index) => (
              <div
                key={screen.id}
                className={`p-2 mb-2 rounded cursor-pointer flex items-center ${
                  index === currentScreenIndex ? 'bg-blue-200' : 'bg-white'
                }`}
                onClick={() => setCurrentScreenIndex(index)}
              >
                <div className="w-16 h-16 mr-2 bg-gray-300 overflow-hidden flex-shrink-0">
                  <img
                    src={screen.imageUrl}
                    alt={screen.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="overflow-hidden">
                  <div className="truncate">{screen.name}</div>
                  <div className="text-xs text-gray-500">
                    {screen.hotspots.length} 个热区
                  </div>
                </div>
              </div>
            ))}

            {screens.length === 0 && (
              <div className="text-gray-500 text-center">
                请上传UI设计稿图片
              </div>
            )}
          </div>
        )}

        {/* 主工作区 */}
        <div className="flex-1 p-4 overflow-hidden relative flex">
          {showFlowchart ? (
            <Flowchart data={flowchartData} setCurrentScreenIndex={setCurrentScreenIndex} screens={screens} />
          ) : (
            <div className="flex-1 flex flex-col">
              {/* 图片编辑工具 */}
              {mode === 'edit' && currentScreen && (
                <div className="mb-4 flex space-x-2">
                  <button
                    className={`px-3 py-1 rounded flex items-center ${
                      addingHotspot ? 'bg-green-600 text-white' : 'bg-gray-200'
                    }`}
                    onClick={() => setAddingHotspot(!addingHotspot)}
                  >
                    <Plus size={16} className="mr-1" />
                    添加热区
                  </button>

                  <button
                    className="px-3 py-1 rounded bg-gray-200 flex items-center"
                    onClick={() => setZoomLevel(zoomLevel + 0.1)}
                  >
                    <ZoomIn size={16} className="mr-1" />
                    放大
                  </button>

                  <button
                    className="px-3 py-1 rounded bg-gray-200 flex items-center"
                    onClick={() => setZoomLevel(Math.max(0.5, zoomLevel - 0.1))}
                  >
                    <ZoomOut size={16} className="mr-1" />
                    缩小
                  </button>

                  <div className="px-3 py-1 rounded bg-gray-100">
                    缩放: {Math.round(zoomLevel * 100)}%
                  </div>
                </div>
              )}

              {/* 图片显示区 */}
              <div
                className="flex-1 overflow-auto bg-gray-300 relative"
                onClick={handleAddHotspot}
                ref={imageContainerRef}
              >
                {currentScreen ? (
                  <div
                    style={{
                      transform: `scale(${zoomLevel})`,
                      transformOrigin: '0 0',
                      width: 'fit-content'
                    }}
                  >
                    <div className="relative">
                      <img
                        src={currentScreen.imageUrl}
                        alt={currentScreen.name}
                        className="max-w-none"
                      />

                      {/* 热区 */}
                      {currentScreen.hotspots.map((hotspot, index) => (
                        <div
                          key={hotspot.id}
                          className={`absolute border-2 ${
                            mode === 'edit'
                              ? 'border-red-500 bg-red-200 bg-opacity-50'
                              : 'border-transparent'
                          } cursor-pointer`}
                          style={{
                            left: `${hotspot.x}px`,
                            top: `${hotspot.y}px`,
                            width: `${hotspot.width}px`,
                            height: `${hotspot.height}px`
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleHotspotClick(hotspot);
                          }}
                          onMouseDown={(e) => handleHotspotDragStart(e, index)}
                        >
                          {mode === 'edit' && (
                            <>
                              <div className="absolute top-0 right-0 bg-red-500 text-white px-1">
                                {index + 1}
                              </div>

                              <div className="absolute -bottom-6 -right-2 w-4 h-4 bg-blue-500 cursor-se-resize"
                                onMouseDown={(e) => {
                                  e.stopPropagation();
                                  const startWidth = hotspot.width;
                                  const startHeight = hotspot.height;
                                  const startX = e.clientX;
                                  const startY = e.clientY;

                                  const handleResize = (moveEvent) => {
                                    const deltaX = (moveEvent.clientX - startX) / zoomLevel;
                                    const deltaY = (moveEvent.clientY - startY) / zoomLevel;
                                    handleResizeHotspot(
                                      index,
                                      Math.max(50, startWidth + deltaX),
                                      Math.max(50, startHeight + deltaY)
                                    );
                                  };

                                  const handleResizeEnd = () => {
                                    window.removeEventListener('mousemove', handleResize);
                                    window.removeEventListener('mouseup', handleResizeEnd);
                                  };

                                  window.addEventListener('mousemove', handleResize);
                                  window.addEventListener('mouseup', handleResizeEnd);
                                }}
                              />
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    {screens.length === 0
                      ? "请上传UI设计稿图片"
                      : "请从左侧选择一个UI设计稿"}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 属性面板 - 仅在编辑模式且选中屏幕时显示 */}
        {mode === 'edit' && currentScreen && !showFlowchart && (
          <div className="w-72 bg-gray-200 p-4 overflow-y-auto">
            <h2 className="font-bold mb-4">热区设置</h2>

            {currentScreen.hotspots.length === 0 ? (
              <div className="text-gray-500">
                点击"添加热区"按钮，然后点击图片添加热区
              </div>
            ) : (
              <div className="space-y-4">
                {currentScreen.hotspots.map((hotspot, index) => (
                  <div key={hotspot.id} className="bg-white p-3 rounded">
                    <div className="flex justify-between items-center mb-2">
                      <div className="font-medium">热区 #{index + 1}</div>
                      <button
                        className="text-red-500"
                        onClick={() => handleDeleteHotspot(index)}
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>

                    <div className="space-y-2">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>X: {Math.round(hotspot.x)}px</div>
                        <div>Y: {Math.round(hotspot.y)}px</div>
                        <div>宽: {Math.round(hotspot.width)}px</div>
                        <div>高: {Math.round(hotspot.height)}px</div>
                      </div>

                      <div>
                        <label className="block text-sm mb-1">
                          目标屏幕:
                        </label>
                        <select
                          className="w-full p-1 border rounded"
                          value={hotspot.targetScreenId || ''}
                          onChange={(e) => handleSetHotspotTarget(index, e.target.value)}
                        >
                          <option value="">-- 选择目标屏幕 --</option>
                          {screens.map((screen) => (
                            <option
                              key={screen.id}
                              value={screen.id}
                              disabled={screen.id === currentScreen.id}
                            >
                              {screen.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default UIPrototypeTool;

// 将UI的设计稿放进来做动态交互的功能
