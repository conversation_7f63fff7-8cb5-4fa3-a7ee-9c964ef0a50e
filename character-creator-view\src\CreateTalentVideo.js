import React, { useState, useEffect } from 'react';
import { 
  ChevronLeft, Play, Camera, Sparkles, 
  Zap, Star, Image, Edit, MessageCircle, 
  RefreshCw, Check, X, ArrowRight, Save, 
  Wand2, ThumbsUp, Trash2, PlusCircle, Loader
} from 'lucide-react';

const CreateTalentVideo = () => {
  // 状态管理
  const [step, setStep] = useState(1); // 1: 选择照片, 2: 创意描述, 3: 预览确认
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [description, setDescription] = useState('');
  const [suggestedDescriptions, setSuggestedDescriptions] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [videoTitle, setVideoTitle] = useState('');
  const [openingLine, setOpeningLine] = useState('');
  const [isPolishingTitle, setIsPolishingTitle] = useState(false);
  const [isRewritingTitle, setIsRewritingTitle] = useState(false);
  const [isPolishingOpening, setIsPolishingOpening] = useState(false);
  const [isRewritingOpening, setIsRewritingOpening] = useState(false);
  const [previewVideo, setPreviewVideo] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('热门');
  const [isPolishing, setIsPolishing] = useState(false);
  const [isRewriting, setIsRewriting] = useState(false);

  // 模拟AI润色标题
  const polishTitle = () => {
    setIsPolishingTitle(true);
    setTimeout(() => {
      setVideoTitle(videoTitle ? videoTitle + "（精致版）" : "雨夜侦探（精致版）");
      setIsPolishingTitle(false);
    }, 1500);
  };
  
  // 模拟AI重写标题
  const rewriteTitle = () => {
    setIsRewritingTitle(true);
    setTimeout(() => {
      setVideoTitle("夜幕下的赛博侦探");
      setIsRewritingTitle(false);
    }, 1500);
  };
  
  // 模拟AI润色开场白
  const polishOpeningLine = () => {
    setIsPolishingOpening(true);
    setTimeout(() => {
      setOpeningLine(openingLine ? openingLine + "，很高兴能与你交流，有什么可以帮助你的吗？" : "你看起来对侦探工作很感兴趣，我可以分享一些我的经验，你想了解哪方面？");
      setIsPolishingOpening(false);
    }, 1500);
  };
  
  // 模拟AI重写开场白
  const rewriteOpeningLine = () => {
    setIsRewritingOpening(true);
    setTimeout(() => {
      setOpeningLine("雨夜总是藏着很多秘密，就像这个城市一样。我是K，你看起来有故事要讲？");
      setIsRewritingOpening(false);
    }, 1500);
  };

  // 其余代码保持不变...
  // 创作者信息
  const creatorInfo = {
    name: "硬核创作者",
    avatar: "/api/placeholder/100/100",
    talentPoints: 2, // 剩余解锁才艺的机会
    characterLevel: 5,
    characterName: "赛博侦探 K",
    characterAvatar: "/api/placeholder/120/120"
  };
  
  // 角色照片数据
  const characterPhotos = [
    { id: 1, src: "/api/placeholder/150/200", name: "侦探风格", isHD: true },
    { id: 2, src: "/api/placeholder/150/200", name: "休闲风格", isHD: true },
    { id: 3, src: "/api/placeholder/150/200", name: "运动风格", isHD: false },
    { id: 4, src: "/api/placeholder/150/200", name: "正装风格", isHD: true },
    { id: 5, src: "/api/placeholder/150/200", name: "赛博朋克", isHD: true },
    { id: 6, src: "/api/placeholder/150/200", name: "夜景照明", isHD: false },
  ];
  
  // 才艺分类
  const talentCategories = [
    { id: 'hot', name: '热门' },
    { id: 'emotion', name: '情感达人' },
    { id: 'dance', name: '舞蹈达人' },
    { id: 'sports', name: '运动达人' },
    { id: 'daily', name: '日常生活' }
  ];
  
  // 预设描述模板
  const descriptionTemplates = {
    '热门': [
      "在霓虹灯闪烁的城市夜景中，K正站在高楼天台，俯瞰整个城市，神情坚定而冷静。",
      "K漫步在雨中的街道上，雨水顺着风衣滴落，霓虹灯的光芒映在雨水中形成绚丽的色彩。",
      "在拥挤的市场中，K敏锐地观察着周围的一切，寻找着可能的线索。"
    ],
    '情感达人': [
      "K站在落地窗前，望着窗外的城市夜景，表情若有所思，眼神中透露着对未来的期待。",
      "K轻抚桌上的相框，眼神中流露出一丝怀念，嘴角微微上扬，似乎回忆起了美好的过去。",
      "在咖啡厅的一角，K正专注地写着什么，时不时抬头看向窗外，眼神中充满感性的思考。"
    ],
    '舞蹈达人': [
      "K在霓虹灯闪烁的空间中，随着电子音乐的节奏展现出流畅的机械舞步，动作精准而充满力量。",
      "在空旷的工业风格室内，K展示着融合了街舞和现代舞的独特舞步，每个动作都充满韵律感。",
      "雨夜的街头，K在彩色霓虹灯下跳着即兴舞蹈，水花在脚下溅起，画面唯美而充满故事感。"
    ],
    '运动达人': [
      "K在城市的屋顶和墙壁间敏捷地跑酷，展示出惊人的身体控制力和平衡感。",
      "在拳击场上，K展示着精准的拳法和身法，每一个动作都充满力量和技巧。",
      "K在空旷的场地进行射击训练，专注的眼神和稳定的姿态展现出专业水准。"
    ],
    '日常生活': [
      "K在整理案件资料，专注地分析线索，桌面上散落着照片和笔记，一旁的咖啡冒着热气。",
      "清晨的公寓里，K站在厨房准备简单的早餐，窗外阳光洒进来，营造出温馨的氛围。",
      "K坐在窗边的沙发上阅读一本书，一只猫慵懒地趴在膝盖上，整个画面宁静而温暖。"
    ]
  };
  
  // 处理分类变更，更新推荐描述
  useEffect(() => {
    if (selectedCategory) {
      const category = selectedCategory === '热门' ? '热门' : 
                       selectedCategory === '情感达人' ? '情感达人' :
                       selectedCategory === '舞蹈达人' ? '舞蹈达人' :
                       selectedCategory === '运动达人' ? '运动达人' : '日常生活';
      setSuggestedDescriptions(descriptionTemplates[category] || []);
    }
  }, [selectedCategory]);
  
  // 模拟AI润色功能
  const polishDescription = () => {
    setIsPolishing(true);
    setTimeout(() => {
      setDescription(description + "（画面色调偏冷，以蓝色和紫色为主，打造未来感氛围。背景可加入霓虹灯和电子显示屏元素，增强科技感和赛博朋克风格。）");
      setIsPolishing(false);
    }, 1500);
  };
  
  // 模拟AI重写功能
  const rewriteDescription = () => {
    setIsRewriting(true);
    setTimeout(() => {
      setDescription("在雨夜的霓虹街头，K穿着标志性的风衣，站在一栋高楼的边缘俯瞰城市。背景中闪烁的霓虹灯映照出他坚毅的侧脸，雨水顺着他的脸颊滑落。画面通过蓝紫色调营造出深邃而神秘的氛围，远处的城市灯光与雾气交织，呈现出一种朦胧的赛博朋克风格。K的眼神中透露出坚定与思考，暗示着他正在解开一个复杂的谜题。");
      setIsRewriting(false);
    }, 2000);
  };
  
  // 模拟生成视频
  const generateVideo = () => {
    setIsGenerating(true);
    // 直接进入第三步，展示loading状态
    setStep(3);
    
    // 这里可以添加实际的视频生成API调用
    // 模拟3分钟后生成完成的回调
    setTimeout(() => {
      setPreviewVideo("/api/placeholder/320/480");
      setIsGenerating(false);
    }, 10000); // 实际项目中这个时间是约3分钟，这里用10秒演示
  };
  
  // 完成并保存才艺视频
  const saveVideo = () => {
    alert("才艺视频已成功保存！");
    // 这里可以添加保存逻辑和跳转到才艺列表页面
  };
  
  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg">
      {/* 头部导航 */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white">
        <div className="flex items-center">
          <button className="p-1 mr-2">
            <ChevronLeft size={20} className="text-white" />
          </button>
          <h1 className="text-lg font-bold">创建才艺视频</h1>
        </div>
        <div className="flex items-center">
          <div className="bg-purple-600 text-white text-xs px-3 py-1.5 rounded-full flex items-center">
            <Star size={14} className="mr-1.5" />
            消耗 {creatorInfo.talentPoints} 点才艺值
          </div>
        </div>
      </div>
      
      {/* 步骤指示器 - 改进版 */}
      <div className="bg-white p-5 border-b border-gray-200">
        <div className="flex justify-between">
          <div className="flex flex-col items-center flex-1">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-500'} mb-1`}>
              1
            </div>
            <div className="text-xs font-medium text-center">
              <span className={step === 1 ? 'text-purple-600' : 'text-gray-500'}>选择照片</span>
            </div>
          </div>
          
          <div className="flex items-center mt-3 px-1">
            <div className={`h-1 w-10 ${step >= 2 ? 'bg-purple-600' : 'bg-gray-200'}`}></div>
          </div>
          
          <div className="flex flex-col items-center flex-1">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-500'} mb-1`}>
              2
            </div>
            <div className="text-xs font-medium text-center">
              <span className={step === 2 ? 'text-purple-600' : 'text-gray-500'}>创意描述</span>
            </div>
          </div>
          
          <div className="flex items-center mt-3 px-1">
            <div className={`h-1 w-10 ${step >= 3 ? 'bg-purple-600' : 'bg-gray-200'}`}></div>
          </div>
          
          <div className="flex flex-col items-center flex-1">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-500'} mb-1`}>
              3
            </div>
            <div className="text-xs font-medium text-center">
              <span className={step === 3 ? 'text-purple-600' : 'text-gray-500'}>预览确认</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-auto p-4">
        {/* 将图片替换为CSS样式的占位符 */}
        <style jsx>{`
          .image-placeholder {
            background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
            background-size: 20px 20px;
            position: relative;
          }
          .image-placeholder::after {
            content: '照片预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 10px;
            color: #666;
          }
          .video-placeholder {
            background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
            background-size: 20px 20px;
            position: relative;
          }
          .video-placeholder::after {
            content: '视频预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: #666;
          }
        `}</style>
        
        {/* 步骤1: 选择照片 */}
        {step === 1 && (
          <div>
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <h3 className="text-md font-semibold mb-3 flex items-center">
                <Camera size={16} className="mr-2 text-purple-600" />
                选择角色照片
              </h3>
              <p className="text-xs text-gray-500 mb-4">
                选择一张风格照片作为视频的基础外观，会影响角色在视频中的服装和整体风格。
              </p>
              
              <div className="grid grid-cols-2 gap-3">
                {characterPhotos.map((photo) => (
                  <div 
                    key={photo.id} 
                    className={`border rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${selectedPhoto === photo.id ? 'border-purple-500 shadow-md' : 'border-gray-200'}`}
                    onClick={() => setSelectedPhoto(photo.id)}
                  >
                    <div className="relative">
                      <div className="h-48 image-placeholder"></div>
                      {photo.isHD && (
                        <div className="absolute top-2 right-2 bg-indigo-600 text-white text-xs px-1.5 py-0.5 rounded">
                          HD
                        </div>
                      )}
                      {selectedPhoto === photo.id && (
                        <div className="absolute inset-0 bg-purple-500 bg-opacity-20 flex items-center justify-center">
                          <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                            <Check size={16} className="text-white" />
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="p-2 bg-white">
                      <h4 className="text-sm font-medium">{photo.name}</h4>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-3 mb-4">
              <div className="flex items-start">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 mr-2 flex-shrink-0">
                  <Zap size={16} />
                </div>
                <div>
                  <div className="text-sm font-medium text-purple-700">提示</div>
                  <div className="text-xs text-purple-600 mt-1">
                    选择高清(HD)照片可以提升生成视频的清晰度和细节表现，创建更有吸引力的才艺视频。
                  </div>
                </div>
              </div>
            </div>
            
            <div className="sticky bottom-0 pt-2">
              <button 
                onClick={() => setStep(2)} 
                disabled={!selectedPhoto}
                className={`w-full py-3 rounded-lg text-white font-medium ${selectedPhoto ? 'bg-purple-600' : 'bg-gray-300'}`}
              >
                下一步: 创意描述
              </button>
            </div>
          </div>
        )}
        
        {/* 步骤2: 创意描述 */}
        {step === 2 && (
          <div>
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <h3 className="text-md font-semibold mb-3 flex items-center">
                <Edit size={16} className="mr-2 text-purple-600" />
                创意描述
              </h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">才艺分类</label>
                <div className="flex flex-wrap gap-2">
                  {talentCategories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.name)}
                      className={`px-3 py-1.5 rounded-full text-xs font-medium ${
                        selectedCategory === category.name 
                          ? 'bg-purple-600 text-white' 
                          : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* AI推荐描述 */}
              {suggestedDescriptions.length > 0 && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">推荐描述模板</label>
                  <div className="space-y-2">
                  {suggestedDescriptions.map((suggestion, index) => (
                      <div 
                        key={index}
                        onClick={() => setDescription(suggestion)}
                        className="border border-gray-200 rounded-lg p-3 text-xs cursor-pointer hover:border-purple-300 hover:bg-purple-50 transition-colors"
                      >
                        {suggestion}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">视频描述</label>
                <div className="relative">
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-3 h-32 text-sm"
                    placeholder="描述你想要的视频画面、角色动作、场景、氛围等..."
                  ></textarea>
                  <div className="absolute right-2 bottom-2 flex space-x-1">
                    <button 
                      onClick={polishDescription}
                      disabled={!description || isPolishing}
                      className={`p-1.5 rounded-full ${!description || isPolishing ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                      title="AI润色"
                    >
                      {isPolishing ? <Loader size={16} className="animate-spin" /> : <Wand2 size={16} />}
                    </button>
                    <button 
                      onClick={rewriteDescription}
                      disabled={!description || isRewriting}
                      className={`p-1.5 rounded-full ${!description || isRewriting ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                      title="AI重写"
                    >
                      {isRewriting ? <Loader size={16} className="animate-spin" /> : <RefreshCw size={16} />}
                    </button>
                    <button 
                      onClick={() => setDescription('')}
                      disabled={!description}
                      className={`p-1.5 rounded-full ${!description ? 'text-gray-300' : 'text-red-500 hover:bg-red-50'}`}
                      title="清空"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
                <div className="flex justify-between mt-1">
                  <div className="text-xs text-gray-500">
                    {description.length}/200 字符
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-3 mt-4">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 mr-2 flex-shrink-0">
                    <MessageCircle size={16} />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-purple-700">创作提示</div>
                    <div className="text-xs text-purple-600 mt-1">
                      详细描述场景、情感和动作能提高生成质量。可以提及角色表情、光线氛围和环境细节，让视频更生动。
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="sticky bottom-0 pt-2 flex space-x-3">
              <button 
                onClick={() => setStep(1)} 
                className="flex-1 py-3 rounded-lg bg-gray-100 text-gray-700 font-medium"
              >
                上一步
              </button>
              <button 
                onClick={generateVideo} 
                disabled={!description || isGenerating}
                className={`flex-1 py-3 rounded-lg text-white font-medium flex items-center justify-center ${description ? 'bg-purple-600' : 'bg-gray-300'}`}
              >
                {isGenerating ? (
                  <>
                    <Loader size={16} className="animate-spin mr-2" />
                    生成中...
                  </>
                ) : (
                  <>
                    立即生成
                  </>
                )}
              </button>
            </div>
          </div>
        )}
        
        {/* 步骤3: 预览确认 */}
        {step === 3 && (
          <div>
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <h3 className="text-md font-semibold mb-3 flex items-center">
                <Play size={16} className="mr-2 text-purple-600" />
                预览才艺视频
              </h3>
              
              <div className="mb-4 flex justify-center">
                {isGenerating ? (
                  <div className="relative w-64 h-96 rounded-lg overflow-hidden bg-gray-100 flex flex-col items-center justify-center">
                    <Loader size={40} className="text-purple-600 animate-spin mb-4" />
                    <p className="text-sm text-gray-600 text-center px-6">
                      视频生成中，您可以退出编辑或停留等待，生成成功后会有app通知
                    </p>
                  </div>
                ) : (
                  <div className="relative w-64 h-96 rounded-lg overflow-hidden video-placeholder">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                        <Play size={24} className="text-white ml-1" />
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <h4 className="text-sm font-medium mb-1">视频描述</h4>
                <p className="text-xs text-gray-600">{description}</p>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <h3 className="text-md font-semibold mb-3">为才艺视频命名</h3>
              <div className="relative">
                <input 
                  type="text" 
                  value={videoTitle}
                  onChange={(e) => setVideoTitle(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-3 text-sm"
                  placeholder="输入视频标题 (例如: 雨夜侦探)"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                  <button 
                    onClick={polishTitle}
                    disabled={!videoTitle || isPolishingTitle}
                    className={`p-1.5 rounded-full ${!videoTitle || isPolishingTitle ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                    title="AI润色"
                  >
                    {isPolishingTitle ? <Loader size={16} className="animate-spin" /> : <Wand2 size={16} />}
                  </button>
                  <button 
                    onClick={rewriteTitle}
                    disabled={!videoTitle || isRewritingTitle}
                    className={`p-1.5 rounded-full ${!videoTitle || isRewritingTitle ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                    title="AI重写"
                  >
                    {isRewritingTitle ? <Loader size={16} className="animate-spin" /> : <RefreshCw size={16} />}
                  </button>
                </div>
              </div>
              <div className="flex items-center mt-2">
                <input type="checkbox" id="pinVideo" className="mr-2" />
                <label htmlFor="pinVideo" className="text-xs text-gray-700">设为置顶视频</label>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
              <h3 className="text-md font-semibold mb-3">设置互动开场白</h3>
              <p className="text-xs text-gray-500 mb-3">
                当其他用户点击视频想与角色互动时，角色将以此开场白开启对话
              </p>
              <div className="relative">
                <textarea
                  value={openingLine}
                  onChange={(e) => setOpeningLine(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-3 h-24 text-sm"
                  placeholder="设置一个吸引人的开场白，引导用户与角色互动..."
                ></textarea>
                <div className="absolute right-2 bottom-2 flex space-x-1">
                  <button 
                    onClick={polishOpeningLine}
                    disabled={!openingLine || isPolishingOpening}
                    className={`p-1.5 rounded-full ${!openingLine || isPolishingOpening ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                    title="AI润色"
                  >
                    {isPolishingOpening ? <Loader size={16} className="animate-spin" /> : <Wand2 size={16} />}
                  </button>
                  <button 
                    onClick={rewriteOpeningLine}
                    disabled={!openingLine || isRewritingOpening}
                    className={`p-1.5 rounded-full ${!openingLine || isRewritingOpening ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                    title="AI重写"
                  >
                    {isRewritingOpening ? <Loader size={16} className="animate-spin" /> : <RefreshCw size={16} />}
                  </button>
                </div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {openingLine.length}/100 字符
              </div>
            </div>
            
            <div className="sticky bottom-0 pt-2 flex space-x-3">
              <button 
                onClick={() => setStep(2)} 
                className="flex-1 py-3 rounded-lg bg-gray-100 text-gray-700 font-medium"
                disabled={isGenerating}
              >
                返回修改
              </button>
              <button 
                onClick={saveVideo} 
                className="flex-1 py-3 rounded-lg bg-green-600 text-white font-medium flex items-center justify-center"
                disabled={isGenerating}
              >
                <Save size={16} className="mr-2" />
                保存才艺
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateTalentVideo;