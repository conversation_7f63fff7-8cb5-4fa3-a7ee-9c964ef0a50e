import React, { useState, useEffect } from 'react';
import { ChevronLeft, TrendingUp, TrendingDown, Info, Heart, MessageCircle, ShieldCheck, Zap, BarChart3, LineChart, Brain, Sparkles, AlertCircle, Clock, Target, ThumbsUp, ThumbsDown, Eye, EyeOff, HelpCircle } from 'lucide-react';

// 导入头像图片
import characterAvatar from './assets/沈婉.png';
// 导入K线图图片
import kLineImage from './assets/kLine.JPG';

const AIStockDetailPage = () => {
  // 状态管理
  const [currentTab, setCurrentTab] = useState('data'); // data | ai
  const [timeRange, setTimeRange] = useState('1D'); // 1D | 1W | 1M | 3M | 1Y
  const [showAdvancedData, setShowAdvancedData] = useState(false);
  const [userHolding, setUserHolding] = useState(200); // 用户持仓
  const [showBuySheet, setShowBuySheet] = useState(false);
  const [showSellSheet, setShowSellSheet] = useState(false);
  const [tradeAmount, setTradeAmount] = useState('');
  const [showMessage, setShowMessage] = useState(false);
  const [messageContent, setMessageContent] = useState('');
  const [aiMood, setAiMood] = useState('optimistic'); // optimistic | cautious | neutral
  const [userLevel] = useState('beginner'); // beginner | advanced
  const [aiLoading, setAiLoading] = useState(false); // AI解读加载状态
  const [aiGeneratedTime] = useState(new Date()); // AI解读生成时间
  
  // 股票数据
  const stockData = {
    name: '科技未来',
    code: '000001',
    currentPrice: 28.5,
    change: 2.82,
    changePercent: 11.01,
    high: 29.20,
    low: 27.84,
    open: 28.47,
    volume: '63.76万',
    turnover: '6.49%',
    pe: 15.63,
    marketCap: '132.99亿',
    aiScore: 85, // AI评分
    riskLevel: 'medium', // low | medium | high
    trend: 'bullish' // bullish | bearish | neutral
  };

  // AI角色信息
  const aiCharacter = {
    name: '沈婉',
    avatar: characterAvatar,
    currentAdvice: 'hold', // buy | sell | hold
    confidence: 78 // 信心指数
  };

  // AI解读内容
  const aiAnalysis = {
    summary: "主人，这支股票今天表现真不错呢！就像春天里的花朵一样绽放了~涨幅超过11%，我觉得它充满了活力！",
    technical: "从技术面看，股价突破了前期高点，成交量也在放大，这是个好兆头呢！MACD金叉，均线多头排列，短期趋势向好。",
    fundamental: "公司最近发布的财报超预期，新产品线也获得了市场认可。不过PE有点偏高，要注意估值风险哦~",
    prediction: "短期内我觉得还有上涨空间，但要注意28.8-29.2元的压力位。如果能突破30元，那就像火箭一样起飞啦！",
    risk: "不过主人要小心，涨得太快可能会有回调，建议分批操作，不要追高哦~"
  };

  // 关键指标 - 已移除，相关数据整合到顶部

  // 技术指标（高级用户）
  const technicalIndicators = [
    { name: 'MACD', value: '金叉', signal: 'buy' },
    { name: 'KDJ', value: '超买', signal: 'caution' },
    { name: 'RSI', value: '68.5', signal: 'neutral' },
    { name: 'BOLL', value: '上轨', signal: 'caution' }
  ];

  // 切换到AI标签时触发加载
  useEffect(() => {
    if (currentTab === 'ai') {
      setAiLoading(true);
      setTimeout(() => {
        setAiLoading(false);
      }, 2000);
    }
  }, [currentTab]);

  // 显示消息
  const showCharacterMessage = (message) => {
    setMessageContent(message);
    setShowMessage(true);
    setTimeout(() => setShowMessage(false), 3000);
  };

  // 处理买入
  const handleBuy = () => {
    const amount = parseInt(tradeAmount);
    if (!amount || amount <= 0) {
      showCharacterMessage("主人，请输入正确的数量哦~");
      return;
    }
    setUserHolding(prev => prev + amount);
    showCharacterMessage(`成功买入${amount}股！我会好好帮您照看的~`);
    setShowBuySheet(false);
    setTradeAmount('');
  };

  // 处理卖出
  const handleSell = () => {
    const amount = parseInt(tradeAmount);
    if (!amount || amount <= 0) {
      showCharacterMessage("主人，请输入正确的数量哦~");
      return;
    }
    if (amount > userHolding) {
      showCharacterMessage("主人的持仓不够了呢~");
      return;
    }
    setUserHolding(prev => prev - amount);
    showCharacterMessage(`成功卖出${amount}股！这次操作很棒哦~`);
    setShowSellSheet(false);
    setTradeAmount('');
  };

  // 买入卖出弹窗
  const TradeSheet = ({ type }) => {
    const isBuy = type === 'buy';
    return (
      <div className="fixed inset-0 bg-black/50 z-40 flex items-end justify-center" onClick={() => isBuy ? setShowBuySheet(false) : setShowSellSheet(false)}>
        <div className="w-full max-w-[390px] bg-white rounded-t-3xl p-6" onClick={e => e.stopPropagation()}>
          <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
          <h3 className="text-lg font-bold mb-4">{isBuy ? '买入' : '卖出'} {stockData.name}</h3>
          
          <div className="bg-gray-50 rounded-2xl p-4 mb-4">
            <div className="flex justify-between mb-2">
              <span className="text-gray-600">当前价格</span>
              <span className="font-semibold">¥{stockData.currentPrice}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">持仓数量</span>
              <span className="font-semibold">{userHolding}股</span>
            </div>
          </div>
          
          <input
            type="number"
            value={tradeAmount}
            onChange={(e) => setTradeAmount(e.target.value)}
            placeholder={`请输入${isBuy ? '买入' : '卖出'}数量`}
            className="w-full px-4 py-3 border border-gray-200 rounded-2xl mb-3"
          />
          
          <div className="grid grid-cols-4 gap-2 mb-4">
            {[100, 200, 500, 1000].map(amount => (
              <button
                key={amount}
                onClick={() => setTradeAmount(amount.toString())}
                className="py-2 bg-gray-100 rounded-xl text-sm font-medium"
              >
                {amount}股
              </button>
            ))}
          </div>
          
          {/* AI建议 */}
          <div className="bg-purple-50 rounded-2xl p-3 mb-4">
            <div className="flex items-start gap-2">
              <Sparkles className="w-4 h-4 text-purple-600 mt-0.5" />
              <p className="text-sm text-purple-800">
                {isBuy ? 
                  "主人，当前价格处于合理区间，可以考虑分批建仓哦~" : 
                  "已经有不错的收益了，适当止盈也是好策略呢！"
                }
              </p>
            </div>
          </div>
          
          <button
            onClick={isBuy ? handleBuy : handleSell}
            className={`w-full py-3 rounded-2xl font-semibold text-white ${
              isBuy ? 'bg-gradient-to-r from-red-500 to-pink-500' : 'bg-gradient-to-r from-green-500 to-emerald-500'
            }`}
          >
            确认{isBuy ? '买入' : '卖出'}
          </button>
        </div>
      </div>
    );
  };

  // AI加载组件
  const AILoadingContent = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-purple-100 to-pink-100 rounded-2xl p-4">
        <div className="flex items-start gap-3">
          <img src={aiCharacter.avatar} alt="" className="w-12 h-12 rounded-full" />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <p className="font-semibold">{aiCharacter.name}正在分析中...</p>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-white/60 rounded-full animate-pulse"></div>
              <div className="h-3 bg-white/60 rounded-full animate-pulse w-4/5"></div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-2xl shadow-sm p-4">
        <div className="h-4 bg-gray-200 rounded animate-pulse mb-3 w-24"></div>
        <div className="space-y-2">
          <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-3 bg-gray-200 rounded animate-pulse w-5/6"></div>
          <div className="h-3 bg-gray-200 rounded animate-pulse w-4/6"></div>
        </div>
      </div>
    </div>
  );

  // 格式化时间函数
  const formatGeneratedTime = (date) => {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    
    // 判断时间段
    let period = '';
    if (hours >= 5 && hours < 12) {
      period = '上午';
    } else if (hours >= 12 && hours < 18) {
      period = '下午';
    } else {
      period = '晚上';
    }
    
    return `${period} ${timeString}`;
  };

  return (
    <div className="fixed inset-0 bg-gray-50 overflow-hidden">
      <div className="relative w-full max-w-[390px] h-full mx-auto bg-white shadow-xl">
        {/* 顶部导航 */}
        <div className="fixed top-0 left-0 right-0 max-w-[390px] mx-auto bg-white z-30 border-b border-gray-100">
          <div className="flex items-center justify-between px-4 py-3">
            <button className="p-2 -ml-2">
              <ChevronLeft className="w-6 h-6" />
            </button>
            <div className="text-center">
              <h1 className="font-semibold text-base">{stockData.name}</h1>
              <p className="text-xs text-gray-500">{stockData.code}</p>
            </div>
            <button className="p-2 -mr-2">
              <Heart className="w-5 h-5 text-gray-400" />
            </button>
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="pt-16 pb-20 h-full overflow-y-auto">
          {/* 价格信息 - 优化布局 */}
          <div className="px-4 py-3 bg-gradient-to-br from-purple-50 to-pink-50">
            <div className="flex justify-between items-start">
              {/* 左侧：价格信息 */}
              <div>
                <div className="flex items-baseline gap-3 mb-1">
                  <span className="text-3xl font-bold text-red-600">{stockData.currentPrice}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-red-600 font-medium">+{stockData.change}</span>
                    <span className="text-red-600 font-medium">+{stockData.changePercent}%</span>
                  </div>
                </div>
                <span className="text-xs text-gray-500">{new Date().toLocaleTimeString()}</span>
              </div>
              
              {/* 右侧：AI评分和风险 */}
              <div className="flex flex-col items-end gap-1">
                <div className="flex items-center gap-1 text-xs bg-purple-100 px-2 py-0.5 rounded-full">
                  <span className="text-purple-700">AI评分 {stockData.aiScore}</span>
                </div>
                <div className="flex items-center gap-1 text-xs bg-yellow-100 px-2 py-0.5 rounded-full">
                  <span className="text-yellow-700">风险中等</span>
                </div>
              </div>
            </div>
          </div>

          {/* 核心数据展示 - 参考截图样式 */}
          <div className="px-4 py-3 bg-white border-b border-gray-100">
            <div className="grid grid-cols-3 gap-x-4 gap-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">今开</span>
                <span className="font-medium">{stockData.open}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">最高</span>
                <span className="font-medium text-red-600">{stockData.high}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">成交量</span>
                <span className="font-medium">{stockData.volume}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">昨收</span>
                <span className="font-medium">25.68</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">最低</span>
                <span className="font-medium text-green-600">{stockData.low}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">换手率</span>
                <span className="font-medium">{stockData.turnover}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">市盈率</span>
                <span className="font-medium">{stockData.pe}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">市值</span>
                <span className="font-medium">{stockData.marketCap}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">成交额</span>
                <span className="font-medium">1.82亿</span>
              </div>
            </div>
          </div>

          {/* 标签页 - 减少间距 */}
          <div className="px-4 mb-3 mt-2">
            <div className="flex bg-gray-100 rounded-xl p-1">
              <button
                onClick={() => setCurrentTab('data')}
                className={`flex-1 flex items-center justify-center gap-1 py-2 rounded-lg transition-colors ${
                  currentTab === 'data' ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600'
                }`}
              >
                <BarChart3 className="w-4 h-4" />
                <span className="text-sm font-medium">数据</span>
              </button>
              <button
                onClick={() => setCurrentTab('ai')}
                className={`flex-1 flex items-center justify-center gap-1 py-2 rounded-lg transition-colors ${
                  currentTab === 'ai' ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600'
                }`}
              >
                <Brain className="w-4 h-4" />
                <span className="text-sm font-medium">AI解读</span>
              </button>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="px-4">
            {currentTab === 'data' && (
              <div className="space-y-4">
                {/* 走势图 - 移到最前面，去掉标题 */}
                <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
                  {/* 时间选择 */}
                  <div className="p-4 pb-3">
                    <div className="flex gap-2 bg-gray-100 rounded-xl p-1">
                      {['1D', '1W', '1M', '3M', '1Y'].map(range => (
                        <button
                          key={range}
                          onClick={() => setTimeRange(range)}
                          className={`flex-1 py-1.5 text-sm font-medium rounded-lg transition-colors ${
                            timeRange === range ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600'
                          }`}
                        >
                          {range}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* K线图图片 */}
                  <div className="px-2 pb-4">
                    <img 
                      src={kLineImage} 
                      alt="K线图" 
                      className="w-full h-auto rounded-xl"
                    />
                  </div>
                </div>

                {/* AI图表解读 */}
                <div className="bg-purple-50 rounded-2xl p-3">
                  <div className="flex items-start gap-2">
                    <Sparkles className="w-4 h-4 text-purple-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-purple-900 mb-1">AI图表解读</p>
                      <p className="text-sm text-purple-700">
                        从图表看，股价在上升通道中运行良好，成交量配合理想。短期均线向上发散，显示多头力量较强。建议关注29.2元的压力位突破情况。
                      </p>
                    </div>
                  </div>
                </div>

                {/* 新手小贴士 */}
                {userLevel === 'beginner' && (
                  <div className="bg-yellow-50 rounded-2xl p-3">
                    <div className="flex items-start gap-2">
                      <HelpCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-yellow-900 mb-1">新手小贴士</p>
                        <p className="text-sm text-yellow-700">
                          市盈率(PE)表示投资者愿意为每1元利润支付的价格，一般15-25较为合理。换手率反映股票交易活跃程度。
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* 持仓信息 */}
                <div className="bg-white rounded-2xl shadow-sm p-4">
                  <h3 className="font-semibold mb-3">我的持仓</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">持仓数量</span>
                      <span className="text-sm font-medium">{userHolding}股</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">持仓成本</span>
                      <span className="text-sm font-medium">¥25.60</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">持仓市值</span>
                      <span className="text-sm font-medium">¥{(userHolding * stockData.currentPrice).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">盈亏金额</span>
                      <span className="text-sm font-medium text-red-600">+¥580</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">盈亏比例</span>
                      <span className="text-sm font-medium text-red-600">+11.33%</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentTab === 'ai' && (
              aiLoading ? (
                <AILoadingContent />
              ) : (
                <div className="space-y-4">
                  {/* 解读生成时间 - 新增 */}
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <Clock className="w-4 h-4" />
                    <span>解读生成于 {formatGeneratedTime(aiGeneratedTime)}</span>
                  </div>

                  {/* AI角色卡片 */}
                  <div className="bg-gradient-to-r from-purple-100 to-pink-100 rounded-2xl p-4">
                    <div className="flex items-start gap-3">
                      <img src={aiCharacter.avatar} alt="" className="w-12 h-12 rounded-full" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <p className="font-semibold">{aiCharacter.name}的解读</p>
                          <div className="flex items-center gap-1 text-xs bg-white/60 px-2 py-0.5 rounded-full">
                            <Zap className="w-3 h-3 text-yellow-500" />
                            <span>信心指数 {aiCharacter.confidence}%</span>
                          </div>
                        </div>
                        <p className="text-sm text-gray-700">{aiAnalysis.summary}</p>
                      </div>
                    </div>
                  </div>

                  {/* 投资建议 */}
                  <div className="bg-white rounded-2xl shadow-sm p-4">
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Target className="w-5 h-5 text-purple-500" />
                      投资建议
                    </h3>
                    <div className="grid grid-cols-3 gap-3 mb-4">
                      <button className="bg-red-50 rounded-xl p-3 text-center">
                        <TrendingUp className="w-6 h-6 text-red-500 mx-auto mb-1" />
                        <p className="text-sm font-medium">买入</p>
                        <p className="text-xs text-gray-500">28.0-28.5</p>
                      </button>
                      <button className="bg-green-50 rounded-xl p-3 text-center border-2 border-green-300">
                        <ShieldCheck className="w-6 h-6 text-green-500 mx-auto mb-1" />
                        <p className="text-sm font-medium">持有</p>
                        <p className="text-xs text-gray-500">当前建议</p>
                      </button>
                      <button className="bg-blue-50 rounded-xl p-3 text-center">
                        <TrendingDown className="w-6 h-6 text-blue-500 mx-auto mb-1" />
                        <p className="text-sm font-medium">卖出</p>
                        <p className="text-xs text-gray-500">29.5-30.0</p>
                      </button>
                    </div>
                    <div className="bg-yellow-50 rounded-xl p-3">
                      <p className="text-sm text-yellow-800">
                        <AlertCircle className="w-4 h-4 inline mr-1" />
                        建议：短期可持有，注意29.2元压力位
                      </p>
                    </div>
                  </div>

                  {/* 详细分析 */}
                  <div className="bg-white rounded-2xl shadow-sm p-4">
                    <h3 className="font-semibold mb-3">深度分析</h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-1">📊 技术面</p>
                        <p className="text-sm text-gray-600">{aiAnalysis.technical}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-1">💰 基本面</p>
                        <p className="text-sm text-gray-600">{aiAnalysis.fundamental}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-1">🔮 预测</p>
                        <p className="text-sm text-gray-600">{aiAnalysis.prediction}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-1">⚠️ 风险提示</p>
                        <p className="text-sm text-gray-600">{aiAnalysis.risk}</p>
                      </div>
                    </div>
                  </div>

                  {/* 高级数据开关 */}
                  <button
                    onClick={() => setShowAdvancedData(!showAdvancedData)}
                    className="w-full bg-purple-50 rounded-xl p-3 flex items-center justify-between"
                  >
                    <span className="text-sm font-medium text-purple-700">
                      {showAdvancedData ? '收起' : '展开'}高级数据
                    </span>
                    {showAdvancedData ? <EyeOff className="w-4 h-4 text-purple-600" /> : <Eye className="w-4 h-4 text-purple-600" />}
                  </button>

                  {/* 高级数据 */}
                  {showAdvancedData && (
                    <div className="bg-white rounded-2xl shadow-sm p-4">
                      <h3 className="font-semibold mb-3">技术指标</h3>
                      <div className="space-y-2">
                        {technicalIndicators.map((indicator, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">{indicator.name}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">{indicator.value}</span>
                              <span className={`text-xs px-2 py-0.5 rounded-full ${
                                indicator.signal === 'buy' ? 'bg-red-100 text-red-600' :
                                indicator.signal === 'sell' ? 'bg-green-100 text-green-600' :
                                indicator.signal === 'caution' ? 'bg-yellow-100 text-yellow-600' :
                                'bg-gray-100 text-gray-600'
                              }`}>
                                {indicator.signal === 'buy' ? '买入' :
                                 indicator.signal === 'sell' ? '卖出' :
                                 indicator.signal === 'caution' ? '警惕' : '中性'}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )
            )}
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="fixed bottom-0 left-0 right-0 max-w-[390px] mx-auto bg-white border-t border-gray-100 px-4 py-3">
          <div className="flex gap-3">
            <button
              onClick={() => setShowBuySheet(true)}
              className="flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white py-3 rounded-2xl font-semibold flex items-center justify-center gap-2"
            >
              <TrendingUp className="w-5 h-5" />
              买入
            </button>
            <button
              onClick={() => setShowSellSheet(true)}
              className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white py-3 rounded-2xl font-semibold flex items-center justify-center gap-2"
            >
              <TrendingDown className="w-5 h-5" />
              卖出
            </button>
            <button
              onClick={() => showCharacterMessage("我会持续关注这支股票，有异动会第一时间通知您！")}
              className="px-4 bg-purple-100 text-purple-600 rounded-2xl"
            >
              <MessageCircle className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* 买入弹窗 */}
        {showBuySheet && <TradeSheet type="buy" />}

        {/* 卖出弹窗 */}
        {showSellSheet && <TradeSheet type="sell" />}

        {/* 角色消息提示 */}
        {showMessage && (
          <div className="fixed top-20 left-4 right-4 max-w-[358px] mx-auto bg-white rounded-2xl shadow-lg p-4 z-50 animate-slide-down">
            <div className="flex items-start gap-3">
              <img src={aiCharacter.avatar} alt="" className="w-10 h-10 rounded-full" />
              <p className="text-sm flex-1">{messageContent}</p>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes slide-down {
          from {
            transform: translateY(-20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        
        .animate-slide-down {
          animation: slide-down 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default AIStockDetailPage;