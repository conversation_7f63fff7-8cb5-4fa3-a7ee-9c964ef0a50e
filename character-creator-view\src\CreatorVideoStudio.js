import React, { useState, useEffect, useRef } from 'react';
import {
  ChevronLeft, Film, Upload, RefreshCw, Sparkles,
  Trash2, X, Check, AlertTriangle, Loader,
  MessageCircle, Clock, ArrowLeft, Camera, Wand2,
  Paintbrush, RotateCw, ImagePlus, Eye, Copy, Plus, Download,
  PenLine, FileImage, ChevronDown, CheckCircle, Settings,
  Users, Play, Video, Volume2, SlidersHorizontal, Square, RectangleVertical,
  Share, PenTool, User, Coins, DollarSign, PlayCircle
} from 'lucide-react';

// Placeholder for a simple video player component (replace with actual implementation)
const VideoPlayerPlaceholder = ({ src, coverUrl }) => (
  <div className="w-full aspect-video bg-black rounded-lg overflow-hidden relative flex items-center justify-center">
    {coverUrl && <img src={coverUrl} alt="Video cover" className="absolute inset-0 w-full h-full object-cover opacity-50" />}
    <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
        <Play size={48} className="text-white text-opacity-80 drop-shadow-lg" />
    </div>
    <p className="absolute bottom-2 left-2 text-white text-xs bg-black bg-opacity-50 px-2 py-1 rounded">播放器占位</p>
  </div>
);

// Character Avatar component
const CharacterAvatar = ({ character, size = "lg", className = "" }) => {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12",
    lg: "w-16 h-16",
  };
  
  return (
    <div className={`rounded-full bg-purple-200 flex items-center justify-center ${sizeClasses[size]} ${className} overflow-hidden flex-shrink-0`}>
      {character?.profileImage ? (
        <img 
          src={character.profileImage} 
          alt={character.name}
          className="w-full h-full object-cover"
        />
      ) : (
        <span className="text-purple-700 font-bold text-lg">{character?.name?.charAt(0) || 'A'}</span>
      )}
    </div>
  );
};

const CreatorVideoStudio = ({ 
  onBack, 
  onVideoCreated, 
  characterData, 
  existingAssets = [], 
  userBalance = { partyCoins: 150, starlightCoins: 50 }, 
  preferredPaymentMethod = 'partyCoins' 
}) => {
  // States
  const [step, setStep] = useState('edit'); // 'edit', 'processing', 'complete'
  const [videoDescription, setVideoDescription] = useState('');
  const [videoTitle, setVideoTitle] = useState('');
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const titleInputRef = useRef(null); 
  const [originalDescription, setOriginalDescription] = useState('');
  const [selectedReferenceImages, setSelectedReferenceImages] = useState([]);
  const [showReferenceSelector, setShowReferenceSelector] = useState(false);
  const [showLibrarySelector, setShowLibrarySelector] = useState(false);
  const [isEnhancingPrompt, setIsEnhancingPrompt] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('waiting'); // 'waiting', 'processing', 'error'
  const [processingProgress, setProcessingProgress] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState('约1分钟');
  const [resultVideo, setResultVideo] = useState(null); // { url: '...', coverUrl: '...' }

  // Enhancement modal states
  const [showEnhanceModal, setShowEnhanceModal] = useState(false);
  const [selectedEnhanceStyle, setSelectedEnhanceStyle] = useState(null);
  const [customEnhanceText, setCustomEnhanceText] = useState('');

  // Video specific parameters
  const [selectedStyle, setSelectedStyle] = useState(null);
  const [showStyleOptions, setShowStyleOptions] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState(5); // Default 5 seconds
  const [motionIntensity, setMotionIntensity] = useState('medium'); // Default to medium intensity
  const [includeSound, setIncludeSound] = useState(true);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(true); // Default to showing advanced options for creators
  
  // Video orientation and format options
  const [videoOrientation, setVideoOrientation] = useState('portrait'); // Default to portrait/vertical
  const [videoResolution, setVideoResolution] = useState('720p'); // Default resolution

  // Payment method states
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(preferredPaymentMethod);
  
  // Payment costs based on video duration and style
  const paymentOptions = {
    partyCoins: { id: 'partyCoins', name: '派对币', icon: <Coins size={16} className="text-yellow-500" />, amount: selectedDuration === 5 ? 100 : 180 },
    starlightCoins: { id: 'starlightCoins', name: '星光币', icon: <DollarSign size={16} className="text-blue-500" />, amount: selectedDuration === 5 ? 20 : 35 },
    watchVideo: { id: 'watchVideo', name: '观看激励视频', icon: <PlayCircle size={16} className="text-green-500" />, description: '观看30秒广告视频免费创作' }
  };

  const generateVideoTitle = (description) => {
    // 模拟AI生成标题，在实际应用中可以调用AI服务
    let generatedTitle = '';
    
    if (description) {
      // 尝试获取第一句话或短语
      const firstSentence = description.split(/[.!?。？！]/)[0].trim();
      if (firstSentence.length <= 20) {
        generatedTitle = firstSentence;
      } else {
        // 如果第一句太长，取前20个字符加省略号
        generatedTitle = description.substring(0, 20) + '...';
      }
    } else {
      generatedTitle = '我的精彩视频';
    }
    
    setVideoTitle(generatedTitle);
  };

  // References
  const descriptionRef = useRef(null);
  const processingTimer = useRef(null);

  // --- Data Definitions ---

  // Video style options
  const videoStyles = [
    { id: 'cinematic', name: '电影感', description: '故事性强，氛围浓郁', iconColor: 'bg-gradient-to-br from-amber-400 to-orange-600' },
    { id: 'anime', name: '动漫风', description: '流畅的二次元动画风格', iconColor: 'bg-gradient-to-br from-blue-400 to-indigo-500' },
    { id: 'realistic', name: '写实', description: '接近真实世界的质感', iconColor: 'bg-gradient-to-br from-green-400 to-teal-500' },
    { id: 'dreamy', name: '梦幻', description: '柔和光线，唯美浪漫', iconColor: 'bg-gradient-to-br from-pink-400 to-purple-500' },
    { id: 'cyberpunk', name: '赛博朋克', description: '未来科技，霓虹光影', iconColor: 'bg-gradient-to-br from-cyan-400 to-blue-600' },
    { id: 'custom', name: '自定义', description: '可在描述说明里自定义', iconColor: 'bg-gradient-to-br from-gray-400 to-gray-600' }
  ];

  // Duration options - limited to 5s and 10s only
  const durationOptions = [5, 10]; // In seconds

  // Motion intensity options
  const motionOptions = [
    { id: 'low', name: '低', description: '细微动作，适合静态场景' },
    { id: 'medium', name: '中', description: '适度动感，自然流畅' },
    { id: 'high', name: '高', description: '剧烈动作，活力四射' }
  ];

  // Video orientation options
  const orientationOptions = [
    { id: 'portrait', name: '竖屏', description: '9:16 比例，适合手机浏览', icon: <RectangleVertical size={18} /> },
    { id: 'landscape', name: '横屏', description: '16:9 比例，适合电脑浏览', icon: <RectangleVertical size={18} className="transform rotate-90" /> },
    { id: 'square', name: '方形', description: '1:1 比例，通用格式', icon: <Square size={18} /> }
  ];

  // Resolution options
  const resolutionOptions = [
    { id: '480p', name: '标准清晰度', description: '体积小，生成快' },
    { id: '720p', name: '高清', description: '清晰度与性能平衡' },
    { id: '1080p', name: '全高清', description: '最佳视觉质量，体积较大' }
  ];

  // Professional inspirations for creators
  const inspirations = [
    `${characterData?.name || '角色'} 对镜头诉说人生感悟，背景渐变柔和，光线从侧面投射，营造思考氛围`,
    `${characterData?.name || '角色'} 在城市高楼间穿行，镜头跟随视角，展现都市活力与角色魅力`,
    `特写镜头缓慢靠近 ${characterData?.name || '角色'} 的双眼，捕捉瞳孔中闪烁的光芒和情感变化`,
    `${characterData?.name || '角色'} 在极简黑白空间中，随着节奏做出优美舞蹈动作，投影在地面形成艺术图案`,
    `从天空俯瞰视角，${characterData?.name || '角色'} 站在山巅，风吹动衣物和发丝，画面逐渐旋转上升`
  ];

  // Enhancement styles
  const enhanceStyles = [
    { id: 'more_detailed', name: '更详细', description: '添加更多细节和描述', icon: <Sparkles size={16} className="text-amber-500" /> },
    { id: 'more_concise', name: '更精简', description: '精炼语言，保持关键内容', icon: <Check size={16} className="text-green-500" /> },
    { id: 'more_dramatic', name: '更戏剧化', description: '增加情感色彩和戏剧元素', icon: <Wand2 size={16} className="text-purple-500" /> },
    { id: 'more_professional', name: '更专业', description: '使用更专业的术语和描述', icon: <CheckCircle size={16} className="text-blue-500" /> },
    { id: 'cinematic', name: '电影级', description: '添加电影级场景和镜头描述', icon: <Film size={16} className="text-indigo-500" /> },
    { id: 'custom', name: '自定义', description: '按照自定义指令润色', icon: <Settings size={16} className="text-gray-500" /> }
  ];

  // --- Effects ---

  // Auto resize textarea
  useEffect(() => {
    if (descriptionRef.current) {
      descriptionRef.current.style.height = 'auto';
      descriptionRef.current.style.height = `${descriptionRef.current.scrollHeight}px`;
    }
  }, [videoDescription]);

  // Update payment costs when duration changes
  useEffect(() => {
    // Update costs based on duration only
    let multiplier = 1;
    if (selectedDuration === 10) multiplier = 1.8;
    
    paymentOptions.partyCoins.amount = Math.round(100 * multiplier);
    paymentOptions.starlightCoins.amount = Math.round(20 * multiplier);
    
    // Update payment method if current one is insufficient
    const currentMethodId = selectedPaymentMethod;
    if (currentMethodId === 'partyCoins' || currentMethodId === 'starlightCoins') {
      const required = paymentOptions[currentMethodId].amount;
      const available = userBalance[currentMethodId];
      
      if (available < required) {
        // Automatically switch to best alternative
        setSelectedPaymentMethod(determineDefaultPaymentMethod());
      }
    }
  }, [selectedDuration]);

  // Simulate processing progress
  useEffect(() => {
    if (step === 'processing') {
      let progress = 0;
      setProcessingProgress(0);
      setProcessingStatus('waiting');
      
      // Realistic simulation of process with variable time based on video complexity
      const baseTime = 3000; // 3 seconds base
      const durationFactor = selectedDuration / 5; // Longer videos take more time
      const totalDuration = baseTime * durationFactor;
      
      setEstimatedTime(`约${Math.ceil(totalDuration/1000)}秒`);

      setTimeout(() => {
        setProcessingStatus('processing');
        const startTime = Date.now();

        processingTimer.current = setInterval(() => {
          const elapsedTime = Date.now() - startTime;
          progress = Math.min(100, Math.floor((elapsedTime / totalDuration) * 100));

          setProcessingProgress(progress);

          if (progress >= 100) {
            clearInterval(processingTimer.current);
            setTimeout(() => {
              setStep('complete');
              // Simulate result video URL and potentially cover art
              setResultVideo({
                url: '/api/placeholder/video.mp4',
                coverUrl: '/api/placeholder/400/225'
              });
              generateVideoTitle(videoDescription);
            }, 500);
          }
        }, 100);
      }, 500);
    }

    return () => {
      if (processingTimer.current) {
        clearInterval(processingTimer.current);
      }
    };
  }, [step, selectedDuration]);

  // --- Handlers ---
  
  // Determine the best default payment method based on user's balance
  const determineDefaultPaymentMethod = () => {
    const partyCoinsNeeded = paymentOptions.partyCoins.amount;
    const starlightCoinsNeeded = paymentOptions.starlightCoins.amount;
    
    // If user has preferred method with sufficient balance, use that
    if (preferredPaymentMethod === 'partyCoins' && userBalance.partyCoins >= partyCoinsNeeded) {
      return 'partyCoins';
    } else if (preferredPaymentMethod === 'starlightCoins' && userBalance.starlightCoins >= starlightCoinsNeeded) {
      return 'starlightCoins';
    }
    
    // Otherwise, choose based on available balance
    if (userBalance.partyCoins >= partyCoinsNeeded) {
      return 'partyCoins';
    } else if (userBalance.starlightCoins >= starlightCoinsNeeded) {
      return 'starlightCoins';
    } else {
      return 'watchVideo';
    }
  };

  // Handler for showing enhancement options modal
  const handleShowEnhanceOptions = () => {
    if (!videoDescription.trim()) {
      alert('请先输入一些基础描述');
      return;
    }
    
    setSelectedEnhanceStyle(null);
    setCustomEnhanceText('');
    setShowEnhanceModal(true);
  };

  // Handler for enhancing the description with AI
  const handleEnhanceDescription = async () => {
    if (!selectedEnhanceStyle) {
      setShowEnhanceModal(false);
      return;
    }
    
    setShowEnhanceModal(false);
    setIsEnhancingPrompt(true);
    
    // Save original to allow reverting
    if (!originalDescription) {
      setOriginalDescription(videoDescription);
    }
    
    // Simulate AI enhancement (this would call the actual API in production)
    setTimeout(() => {
      const baseText = videoDescription;
      let enhanced = baseText;
      
      // Apply different enhancements based on selected style
      switch (selectedEnhanceStyle.id) {
        case 'more_detailed':
          enhanced = `${baseText}，场景中光线明暗对比强烈，远处霓虹灯将角色轮廓勾勒出科技感十足的蓝紫色光晕，特写镜头捕捉角色眼神中的坚定与神秘，画面质感精细，细节丰富，背景环境的复杂纹理清晰可见。`;
          break;
        case 'more_concise':
          enhanced = baseText.length > 100 
            ? baseText.slice(0, 100).split('，')[0] + '，简洁有力，突出角色特征与关键场景元素。' 
            : baseText + '，精炼有力，重点突出。';
          break;
        case 'more_dramatic':
          enhanced = `${baseText}，戏剧性的光影效果营造出紧张氛围，角色表情凝重而富有张力，姿态充满动感，周围环境似乎也在呼应主角的情绪变化，整体画面具有强烈的情感冲击力。`;
          break;
        case 'more_professional':
          enhanced = `${baseText}，运用专业摄影构图原则，遵循三分法则排布主体，画面色彩采用互补色系，主体与背景形成对比度分离，景深适中，确保细节清晰度与整体氛围并重，光源布局科学合理。`;
          break;
        case 'cinematic':
          enhanced = `${baseText}，采用电影级镜头语言，从全景缓慢推进至特写，光圈适度虚化背景，画面比例调整为2.35:1宽银幕效果，色彩分级偏冷色调，配以轻微镜头抖动增强真实感，运用景深变化引导观众视线。`;
          break;
        case 'custom':
          enhanced = customEnhanceText 
            ? `${baseText}，${customEnhanceText}` 
            : baseText;
          break;
        default:
          enhanced = `${baseText}，场景中光线明暗对比强烈，远处霓虹灯将角色轮廓勾勒出科技感十足的蓝紫色光晕，特写镜头捕捉角色眼神中的坚定与神秘。`;
      }
      
      setVideoDescription(enhanced);
      setIsEnhancingPrompt(false);
    }, 1500);
  };

  // Handler for rewriting the description completely
  const handleRewriteDescription = async () => {
    if (!videoDescription.trim()) {
      alert('请先输入一些基础描述');
      return;
    }
    
    setIsEnhancingPrompt(true);
    
    // Save original to allow reverting
    if (!originalDescription) {
      setOriginalDescription(videoDescription);
    }
    
    // Simulate AI rewriting (this would call the actual API in production)
    setTimeout(() => {
      const theme = videoDescription.includes('雨') ? '雨夜' : 
                    videoDescription.includes('咖啡') ? '咖啡厅' : 
                    videoDescription.includes('海滩') ? '海滩' : '城市';
      
      const rewritten = theme === '雨夜' ? 
        `${characterData?.name || '角色'}站在雨水淋漓的城市街头，开头镜头从远处缓慢推进。角色身穿防水风衣，肩部有未来科技元素的荧光点缀。背景是模糊的霓虹灯广告牌，光线在湿漉漉的地面上形成梦幻般的反射。雨滴在角色帽檐上汇聚，镜头切换为特写，捕捉角色穿透雨幕的坚定目光，随后转向角色手中的全息设备，其上投射出关键信息。整个画面采用冷色调，偶尔闪烁的霓虹灯带来色彩对比。` :
        theme === '咖啡厅' ?
        `镜头从咖啡厅外部推入，展现${characterData?.name || '角色'}在一家复古未来主义咖啡馆里小憩的场景。角色面前的咖啡杯冒着热气，镜头环绕一周展示环境。阳光透过几何形状的彩色玻璃窗照射进来，在角色的桌面和面部投下多彩的光斑，形成流动的光影效果。角色身旁的全息屏幕显示着重要信息，周围的咖啡馆顾客则是散焦的模糊背景。随后镜头聚焦在角色若有所思的表情，最后定格在眼神的特写。整体色调温暖，充满怀旧与未来的混搭氛围。` :
        theme === '海滩' ?
        `航拍镜头从海面缓缓上升，展现金色沙滩全景，随后下降聚焦到站在海边的${characterData?.name || '角色'}。黄昏时分，夕阳的余晖给海面镀上一层金色，角色的侧脸被温暖的光线勾勒出柔和的轮廓。镜头环绕角色一周，捕捉微风吹拂发丝的动态，远处有几只海鸥掠过天际。视角从侧面转向正面，对焦角色面部表情的细微变化，最后镜头缓慢拉远，将角色、沙滩与海天一色的背景融为一体，构成一幅和谐画面。` :
        `空中俯瞰镜头展现未来城市全景，随后降低高度追踪${characterData?.name || '角色'}穿行在繁忙街道的身影。角色身着融合了复古与未来元素的服装，镜头从前方拍摄角色行走的坚定步伐，然后切换到特写捕捉角色敏锐的目光。街道两旁是各式全息投影广告和霓虹招牌，人来人往。镜头再次转换视角，展现角色穿过一片光影交错的区域，远处高楼的玻璃幕墙反射着日落的金色光芒。最后定格在角色转身凝视远方的画面，表情若有所思，背景虚化成斑斓的光点。`;
      
      setVideoDescription(rewritten);
      setIsEnhancingPrompt(false);
    }, 1500);
  };

  // Handler for getting AI inspiration
  const handleGetInspiration = () => {
    // Randomly select an inspiration
    const randomInspiration = inspirations[Math.floor(Math.random() * inspirations.length)];
    setVideoDescription(randomInspiration);
    
    // Focus the textarea after setting inspiration
    if (descriptionRef.current) {
      descriptionRef.current.focus();
    }
  };

  // Handler for clearing the description
  const handleClearDescription = () => {
    if (videoDescription.trim() && !window.confirm('确定要清空当前描述吗？')) {
      return;
    }
    setVideoDescription('');
    setOriginalDescription('');
    
    // Focus the textarea after clearing
    if (descriptionRef.current) {
      descriptionRef.current.focus();
    }
  };

  // Handler for undoing AI enhancement
  const handleUndoEnhancement = () => {
    if (originalDescription) {
      setVideoDescription(originalDescription);
      setOriginalDescription('');
    }
  };

  // Handler for selecting reference images
  const handleAddReferenceImage = (source) => {
    if (source === 'library') {
      setShowLibrarySelector(true);
      setShowReferenceSelector(false);
    } else if (source === 'camera') {
      // This would integrate with device camera in a real app
      alert('调用相机功能');
      setShowReferenceSelector(false);
    } else if (source === 'gallery') {
      // This would integrate with device gallery in a real app
      alert('调用相册功能');
      setShowReferenceSelector(false);
    }
  };

  // Handler for selecting an asset from library
  const handleSelectAsset = (asset) => {
    // Add to selected references if not already included
    if (!selectedReferenceImages.some(img => img.id === asset.id)) {
      setSelectedReferenceImages([...selectedReferenceImages, asset]);
    }
    setShowLibrarySelector(false);
  };

  // Handler for removing a reference image
  const handleRemoveReferenceImage = (id) => {
    setSelectedReferenceImages(selectedReferenceImages.filter(img => img.id !== id));
  };

  // Handler for selecting payment method
  const handleSelectPaymentMethod = (methodId) => {
    setSelectedPaymentMethod(methodId);
    setShowPaymentMethods(false);
  };
  
  const [tempPaymentMethod, setTempPaymentMethod] = useState(selectedPaymentMethod);

  useEffect(() => {
  if (showPaymentMethods) {
    setTempPaymentMethod(selectedPaymentMethod);
  }
}, [showPaymentMethods, selectedPaymentMethod]);

  // Handler for proceeding with the selected payment method
  const handleWatchVideo = () => {
    // This would integrate with ad SDK in a real app
    alert('调用广告SDK，播放30秒激励视频');
    // Simulate successful video watch
    setTimeout(() => {
      handleCreateVideo();
    }, 1500);
  };

      // Start video creation
  const handleCreateVideo = () => {
    if (!videoDescription.trim()) {
      alert('请输入视频描述'); 
      return;
    }
    if (!selectedStyle) {
      alert('请选择视频风格'); 
      return;
    }
    if (!selectedPaymentMethod) {
      setShowPaymentMethods(true);
      return;
    }

    // Check if user has enough coins
    if (selectedPaymentMethod === 'partyCoins' && userBalance.partyCoins < paymentOptions.partyCoins.amount) {
      alert(`派对币余额不足，您当前有 ${userBalance.partyCoins} 派对币，需要 ${paymentOptions.partyCoins.amount} 派对币`);
      return;
    }
    if (selectedPaymentMethod === 'starlightCoins' && userBalance.starlightCoins < paymentOptions.starlightCoins.amount) {
      alert(`星光币余额不足，您当前有 ${userBalance.starlightCoins} 星光币，需要 ${paymentOptions.starlightCoins.amount} 星光币`);
      return;
    }

    // Handle watch video payment method
    if (selectedPaymentMethod === 'watchVideo') {
      handleWatchVideo();
      return;
    }

    console.log('Starting video creation with:', {
      description: videoDescription,
      references: selectedReferenceImages.map(img => img.id),
      style: selectedStyle.id,
      duration: selectedDuration,
      orientation: videoOrientation,
      resolution: videoResolution,
      motion: motionIntensity,
      sound: includeSound,
      paymentMethod: selectedPaymentMethod
    });
    setStep('processing');
  };

  // Cancel creation
  const handleCancelCreation = () => {
    if (window.confirm('确定要取消当前创作吗？所有进度将丢失。')) {
      if (processingTimer.current) clearInterval(processingTimer.current);
      setStep('edit');
      setProcessingProgress(0);
    }
  };

  // 完成创建并保存
  const handleCompleteCreation = () => {
        if (onVideoCreated) {
        onVideoCreated({
            id: Date.now(),
            type: 'video',
            url: resultVideo.url,
            coverUrl: resultVideo.coverUrl,
            title: videoTitle, // 使用新的标题
            description: videoDescription, // 可以添加完整描述作为单独字段
            createdAt: '刚刚',
            duration: selectedDuration,
            orientation: videoOrientation,
            resolution: videoResolution,
            style: selectedStyle?.id,
        });
        }
        if (onBack) onBack();
  };

  // Filter image assets for reference selection
  const imageAssets = existingAssets.filter(asset => asset.type === 'image');
  
  // Check if selected payment method is valid (sufficient balance)
  const isSelectedPaymentValid = () => {
    if (!selectedPaymentMethod) return false;
    
    if (selectedPaymentMethod === 'watchVideo') return true;
    
    const required = paymentOptions[selectedPaymentMethod].amount;
    const available = userBalance[selectedPaymentMethod];
    return available >= required;
  };

  // Get processing status text for video
  const getProcessingStatusText = () => {
    if (processingStatus === 'waiting') return '排队等候处理';
    if (processingStatus === 'processing') {
      if (processingProgress < 20) return '分析视频描述';
      if (processingProgress < 40) return '构建视频基础';
      if (processingProgress < 60) return '生成关键帧';
      if (processingProgress < 80) return '渲染动态效果';
      if (processingProgress < 95) return '合成音轨 (如果选择)';
      return '视频即将完成';
    }
    if (processingStatus === 'error') return '处理遇到故障';
    return '视频生成中';
  };

  // Get color class based on balance sufficiency
  const getBalanceColorClass = (coinType, required) => {
    const balance = userBalance[coinType] || 0;
    return balance >= required ? "text-green-600" : "text-red-500";
  };

  // --- Render Logic ---

  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white sticky top-0 z-20">
        <div className="flex items-center">
          <button
            onClick={() => {
              if (step !== 'edit' && window.confirm('返回将丢失当前进度，确定吗？')) {
                setStep('edit');
              } else if (step === 'edit' && videoDescription.trim() && window.confirm('返回将丢失当前编辑内容，确定吗？')) {
                onBack();
              } else if (step === 'edit' && !videoDescription.trim()) {
                onBack();
              }
            }}
            className="p-2 mr-2 rounded-full hover:bg-white hover:bg-opacity-20"
          >
            <ChevronLeft size={20} />
          </button>
          <h1 className="text-lg font-bold flex items-center">
            <Film size={18} className="mr-2"/>
            创建视频素材
          </h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto relative">
        {/* Edit Step */}
        {step === 'edit' && (
          <div className="p-4 space-y-5">
            {/* Character info header */}
            {characterData && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <CharacterAvatar character={characterData} size="lg" className="mr-4" />
                  <div>
                    <h2 className="font-bold text-gray-800 flex items-center mb-1">
                      <Video size={16} className="mr-2 text-purple-600" />
                      为 {characterData?.name || '角色'} 创建视频素材
                    </h2>
                    <p className="text-sm text-gray-600">创建的视频将添加到角色素材库，可用于动态发布</p>
                  </div>
                </div>
              </div>
            )}
            
            {/* Reference Images */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                 <label className="text-sm font-medium text-gray-700 flex items-center">
                   <FileImage size={16} className="mr-1 text-purple-600" />
                   参考图片 <span className="text-xs text-gray-500 ml-1">(可选，用于风格/场景参考)</span>
                 </label>
                 <button 
                   onClick={() => setShowReferenceSelector(true)}
                   className="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full flex items-center"
                 >
                   <Plus size={14} className="mr-1" /> 添加参考
                 </button>
              </div>
              
              {selectedReferenceImages.length > 0 ? (
                <div className="flex gap-2 overflow-x-auto pb-2 hide-scrollbar">
                  {selectedReferenceImages.map((img) => (
                    <div key={img.id} className="relative flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border border-gray-200 bg-white">
                      <img 
                        src={img.url} 
                        alt={img.title}
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => handleRemoveReferenceImage(img.id)}
                        className="absolute top-1 right-1 w-5 h-5 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-dashed border-gray-300 p-3 text-center">
                  <p className="text-xs text-gray-500">
                    添加参考图片可以让AI更好地理解你想要的风格、场景和角色表现
                  </p>
                </div>
              )}
            </div>

            {/* Video Style Selection */}
             <div className="mb-4">
               <div className="flex justify-between items-center mb-2">
                 <label className="text-sm font-medium text-gray-700 flex items-center">
                   <Paintbrush size={16} className="mr-1 text-purple-600" />
                   视频风格 <span className="text-red-500 ml-1">*</span>
                 </label>
               </div>
               
               <div className="relative">
                  <button
                    onClick={() => setShowStyleOptions(!showStyleOptions)}
                    className={`w-full p-3 bg-white rounded-lg border ${selectedStyle ? 'border-gray-200' : 'border-red-300'} flex justify-between items-center`}
                  >
                    {selectedStyle ? (
                       <div className="flex items-center">
                         <div className={`w-8 h-8 rounded-full ${selectedStyle.iconColor} mr-2`}></div>
                         <div className="text-left">
                           <div className="text-sm font-medium">{selectedStyle.name}</div>
                           <div className="text-xs text-gray-500">{selectedStyle.description}</div>
                         </div>
                       </div>
                    ) : (
                       <span className="text-sm text-gray-500">请选择一个视频风格</span>
                    )}
                    <ChevronDown size={18} className="text-gray-400" />
                  </button>
                  
                  {showStyleOptions && (
                    <div className="absolute z-20 top-full left-0 right-0 mt-1 bg-white rounded-lg border border-gray-200 shadow-lg max-h-64 overflow-y-auto">
                       {videoStyles.map((style) => (
                         <button 
                           key={style.id} 
                           onClick={() => {
                             setSelectedStyle(style);
                             setShowStyleOptions(false);
                           }}
                           className={`w-full p-3 flex items-center border-b border-gray-100 last:border-b-0 ${
                             selectedStyle?.id === style.id ? 'bg-purple-50' : 'hover:bg-gray-50'
                           }`}
                         >
                            <div className={`w-8 h-8 rounded-full ${style.iconColor} mr-2`}></div>
                            <div className="text-left flex-1">
                              <div className="text-sm font-medium">{style.name}</div>
                              <div className="text-xs text-gray-500">{style.description}</div>
                            </div>
                            {selectedStyle?.id === style.id && (
                              <CheckCircle size={18} className="text-purple-600 ml-2" />
                            )}
                         </button>
                       ))}
                    </div>
                  )}
               </div>
             </div>

            {/* Description Input */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label htmlFor="videoDescription" className="text-sm font-medium text-gray-700 flex items-center">
                  <PenLine size={16} className="mr-1 text-purple-600" />
                  视频描述 <span className="text-red-500 ml-1">*</span>
                </label>
                <button 
                  onClick={handleGetInspiration}
                  className="text-xs px-2 py-1 bg-amber-100 text-amber-700 rounded-full flex items-center"
                >
                  <Sparkles size={14} className="mr-1" /> 灵感模板
                </button>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <textarea
                  ref={descriptionRef}
                  id="videoDescription"
                  value={videoDescription}
                  onChange={(e) => setVideoDescription(e.target.value)}
                  placeholder="详细描述视频内容、场景、动作、光线、镜头运动等元素..."
                  className="w-full p-3 min-h-[150px] text-sm border-none focus:ring-0 focus:outline-none resize-none"
                  disabled={isEnhancingPrompt}
                ></textarea>
                
                <div className="flex border-t border-gray-100">
                  <button
                    onClick={handleShowEnhanceOptions}
                    disabled={!videoDescription.trim() || isEnhancingPrompt}
                    className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                      !videoDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-purple-600'
                    }`}
                  >
                    {isEnhancingPrompt ? <Loader size={14} className="mr-1 animate-spin" /> : <Wand2 size={14} className="mr-1" />}
                    润色
                  </button>
                  <div className="w-px h-8 bg-gray-100 my-auto"></div>
                  <button
                    onClick={handleRewriteDescription}
                    disabled={!videoDescription.trim() || isEnhancingPrompt}
                    className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                      !videoDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-indigo-600'
                    }`}
                  >
                    {isEnhancingPrompt ? <Loader size={14} className="mr-1 animate-spin" /> : <RefreshCw size={14} className="mr-1" />}
                    重写
                  </button>
                  <div className="w-px h-8 bg-gray-100 my-auto"></div>
                  <button
                    onClick={handleClearDescription}
                    disabled={!videoDescription.trim() || isEnhancingPrompt}
                    className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                      !videoDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-red-600'
                    }`}
                  >
                    <Trash2 size={14} className="mr-1" />
                    清空
                  </button>
                </div>
              </div>
              
              {originalDescription && (
                <div className="mt-2 flex justify-end">
                  <button
                    onClick={handleUndoEnhancement}
                    className="text-xs text-gray-600 flex items-center"
                  >
                    <ArrowLeft size={12} className="mr-1" />
                    恢复原始描述
                  </button>
                </div>
              )}
            </div>

            {/* Configuration Section */}
            <div className="mb-4 bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium text-gray-700 flex items-center">
                  <Settings size={16} className="mr-1 text-purple-600" />
                  视频配置
                </h3>
              </div>
              
              {/* Duration selection */}
              <div className="mb-4">
                <label className="text-xs font-medium text-gray-700 block mb-2">
                  视频时长
                </label>
                <div className="grid grid-cols-4 gap-2">
                  {durationOptions.map(duration => (
                    <button 
                      key={duration}
                      onClick={() => setSelectedDuration(duration)} 
                      className={`py-2 rounded-lg text-center text-xs ${
                        selectedDuration === duration 
                          ? 'bg-purple-100 text-purple-700 border border-purple-300' 
                          : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      {duration}秒
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Motion intensity */}
              <div className="mb-4">
                <label className="text-xs font-medium text-gray-700 block mb-2">
                  动作强度
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {motionOptions.map(option => (
                    <button 
                      key={option.id}
                      onClick={() => setMotionIntensity(option.id)} 
                      className={`py-2 rounded-lg text-center ${
                        motionIntensity === option.id 
                          ? 'bg-purple-100 text-purple-700 border border-purple-300' 
                          : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      <div className="text-xs font-medium">{option.name}</div>
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Include Sound */}
              <div>
                <label className="text-xs font-medium text-gray-700 block mb-2">
                  背景音效
                </label>
                <div className="flex items-center space-x-4">
                  <button 
                    onClick={() => setIncludeSound(true)} 
                    className={`flex items-center space-x-2 p-2 rounded-lg ${includeSound ? 'text-purple-700' : 'text-gray-500'}`}
                  >
                    <div className={`w-4 h-4 rounded-full border flex items-center justify-center ${includeSound ? 'bg-purple-600 border-purple-600' : 'border-gray-300'}`}>
                      {includeSound && <div className="w-2 h-2 bg-white rounded-full"></div>}
                    </div>
                    <span className="text-xs">开启 (AI匹配)</span>
                  </button>
                  <button 
                    onClick={() => setIncludeSound(false)} 
                    className={`flex items-center space-x-2 p-2 rounded-lg ${!includeSound ? 'text-purple-700' : 'text-gray-500'}`}
                  >
                    <div className={`w-4 h-4 rounded-full border flex items-center justify-center ${!includeSound ? 'bg-purple-600 border-purple-600' : 'border-gray-300'}`}>
                      {!includeSound && <div className="w-2 h-2 bg-white rounded-full"></div>}
                    </div>
                    <span className="text-xs">无声</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Pro Tips */}
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
              <h3 className="text-sm font-medium text-blue-800 flex items-center mb-1">
                <Sparkles size={16} className="mr-1 text-blue-500" />
                创作提示
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 shrink-0" />
                  使用专业的镜头语言（如："特写镜头"、"缓慢推进"、"平移"等）能显著提升视频质量。
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 shrink-0" />
                  详细描述光线效果、场景氛围和角色表情可以让AI更好地理解你的创意意图。
                </li>
                <li className="flex items-start">
                  <Check size={12} className="mr-1 mt-0.5 shrink-0" />
                  高质量的参考图片可以帮助系统更精准地匹配你期望的风格和视觉效果。
                </li>
              </ul>
            </div>
            
            <div className="h-20"></div> {/* Spacer for fixed button */}
          </div>
        )}

        {/* Processing Step */}
        {step === 'processing' && (
           <div className="flex flex-col items-center justify-center py-8 px-4">
             <div className="w-full max-w-sm mb-8">
               <div className="flex justify-between items-center mb-2">
                 <div className="text-sm font-medium text-gray-700 flex items-center">
                   <RotateCw size={16} className={`mr-1 text-blue-600 ${processingStatus === 'processing' ? 'animate-spin' : ''}`} />
                   {getProcessingStatusText()}
                 </div>
                 <div className="text-xs text-gray-500">{Math.round(processingProgress)}%</div>
               </div>
               <div className="w-full bg-gray-200 rounded-full h-2 mb-1 overflow-hidden">
                 <div className="h-full bg-blue-600 transition-all duration-300 ease-out" style={{ width: `${processingProgress}%` }}></div>
               </div>
               <div className="text-xs text-gray-500 flex justify-between">
                 <span>预计等待: {estimatedTime}</span>
               </div>
             </div>
             <div className="bg-white rounded-xl p-4 shadow-md w-full max-w-sm">
                {/* Video placeholder animation */}
                <div className="bg-gray-100 rounded-lg overflow-hidden aspect-video mb-4 flex items-center justify-center relative loading-animation-video">
                    <div className="absolute inset-0 flex items-center justify-center">
                       <div className="text-purple-600 bg-white bg-opacity-80 px-3 py-2 rounded-lg shadow-sm">
                          <Loader size={24} className="animate-spin mx-auto mb-2" />
                          <p className="text-xs text-center">视频生成中...</p>
                       </div>
                    </div>
                </div>
               <div className="bg-blue-50 rounded-lg p-3 text-xs text-blue-800">
                 <div className="flex items-center mb-2">
                   <MessageCircle size={14} className="mr-1 text-blue-600" />
                   <span className="font-medium">友情提示</span>
                 </div>
                 <p>视频生成需要一点时间，复杂场景和高分辨率视频生成时间更长。您可以放心离开此页面，完成后会通知您。</p>
               </div>
             </div>
           </div>
        )}

        {/* Complete Step */}
        {step === 'complete' && resultVideo && (
           <div className="flex flex-col items-center py-4 px-4">
             <div className="bg-white rounded-xl p-4 shadow-md w-full mb-4">
                {/* Video Player Placeholder */}
                <VideoPlayerPlaceholder src={resultVideo.url} coverUrl={resultVideo.coverUrl} />

                <div className="flex justify-between mb-3 mt-4">
                  <button className="flex-1 mr-1 py-2 rounded-lg bg-purple-100 text-purple-700 text-sm font-medium flex items-center justify-center">
                    <Eye size={16} className="mr-1" />
                    预览
                  </button>
                  <button className="flex-1 ml-1 py-2 rounded-lg bg-gray-100 text-gray-700 text-sm font-medium flex items-center justify-center">
                    <Download size={16} className="mr-1" />
                    下载
                  </button>
                </div>

                <div className="mt-4 space-y-3">
                <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-1">视频标题</h3>
                    <div className="bg-gray-50 rounded-lg p-3 relative">
                        {isEditingTitle ? (
                        <form 
                            onSubmit={(e) => {
                            e.preventDefault();
                            setIsEditingTitle(false);
                            }}
                            className="flex"
                        >
                            <input
                            ref={titleInputRef}
                            type="text"
                            value={videoTitle}
                            onChange={(e) => setVideoTitle(e.target.value)}
                            className="flex-1 text-sm border-none bg-transparent focus:ring-0 focus:outline-none p-0"
                            autoFocus
                            />
                            <button 
                            type="submit" 
                            className="text-purple-600 ml-2 flex-shrink-0"
                            >
                            <Check size={16} />
                            </button>
                        </form>
                        ) : (
                        <>
                            <span className="text-sm font-medium">{videoTitle}</span>
                            <button 
                            onClick={() => {
                                setIsEditingTitle(true);
                                // 渲染后聚焦输入框
                                setTimeout(() => {
                                if (titleInputRef.current) {
                                    titleInputRef.current.focus();
                                }
                                }, 0);
                            }} 
                            className="absolute top-2 right-2 text-purple-600 opacity-50 hover:opacity-100"
                            >
                            <PenLine size={14} />
                            </button>
                        </>
                        )}
                    </div>
                    </div>
                   <div>
                     <h3 className="text-sm font-medium text-gray-700 mb-1">视频描述</h3>
                     <div className="text-xs text-gray-600 bg-gray-50 rounded-lg p-3 relative">
                       {videoDescription}
                       <button onClick={() => navigator.clipboard.writeText(videoDescription)} className="absolute top-2 right-2 text-purple-600 opacity-50 hover:opacity-100"> 
                         <Copy size={14} /> 
                       </button>
                     </div>
                   </div>
                   <div className="grid grid-cols-3 gap-2 text-xs text-gray-500">
                       <span>风格: {selectedStyle?.name || '未知'}</span>
                       <span>时长: {selectedDuration}秒</span>
                       <span>音效: {includeSound ? '开启' : '关闭'}</span>
                       <span>方向: 竖屏</span>
                       <span>分辨率: 720p</span>
                       <span>动作: {motionOptions.find(m => m.id === motionIntensity)?.name || '中'}</span>
                   </div>
                </div>
             </div>
             <div className="w-full bg-green-50 rounded-lg p-4 border border-green-100 mb-4">
                 <div className="flex items-start">
                   <CheckCircle size={18} className="text-green-600 mr-2 flex-shrink-0 mt-0.5" />
                   <div>
                     <h3 className="text-sm font-medium text-green-800 mb-1">视频已成功生成</h3>
                     <p className="text-xs text-green-700">点击「保存到素材库」将视频添加到角色的素材库中，可用于发布动态和其他互动。</p>
                   </div>
                 </div>
             </div>
             <div className="w-full">
                 <div className="grid grid-cols-2 gap-3 mb-3">
                   <button
                     onClick={() => alert('发布动态功能将在此实现')}
                     className="py-3 rounded-lg font-medium bg-gradient-to-r from-indigo-600 to-purple-600 text-white flex items-center justify-center"
                   >
                     <Share size={18} className="mr-2" />
                     直接发布动态
                   </button>
                   
                   <button
                     onClick={handleCompleteCreation}
                     className="py-3 rounded-lg font-medium bg-purple-100 text-purple-700 flex items-center justify-center"
                   >
                     <Download size={18} className="mr-2" />
                     保存到素材库
                   </button>
                 </div>
                 
                 <div className="space-y-3">
                   <button
                     onClick={() => setStep('edit')}
                     className="w-full py-3 rounded-lg font-medium bg-white border border-purple-300 text-purple-700 flex items-center justify-center"
                   >
                     <RefreshCw size={16} className="mr-2" />
                     修改后重试
                   </button>
                 </div>
             </div>
           </div>
        )}

      </div> {/* End Main Content */}

      {/* Fixed Bottom Buttons */}
      {step === 'edit' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          {/* Payment Method Selection */}
          <div className="mb-3 p-2 bg-gray-50 rounded-lg flex items-center justify-between">
            <div className="flex items-center">
              {selectedPaymentMethod && paymentOptions[selectedPaymentMethod].icon}
              <span className="ml-2 text-sm font-medium">
                {selectedPaymentMethod ? paymentOptions[selectedPaymentMethod].name : '选择支付方式'}
              </span>
              {selectedPaymentMethod && (selectedPaymentMethod === 'partyCoins' || selectedPaymentMethod === 'starlightCoins') && (
                <span className="ml-2 text-sm">
                  需消耗 <span className="font-bold">{paymentOptions[selectedPaymentMethod].amount}</span>
                  <span className={`ml-1 text-xs ${getBalanceColorClass(selectedPaymentMethod, paymentOptions[selectedPaymentMethod].amount)}`}>
                    (余额: {userBalance[selectedPaymentMethod]})
                  </span>
                </span>
              )}
              {selectedPaymentMethod && selectedPaymentMethod === 'watchVideo' && (
                <span className="ml-2 text-xs text-green-600">
                  观看30秒视频
                </span>
              )}
            </div>
            <button 
              onClick={() => setShowPaymentMethods(true)}
              className="text-xs text-purple-600 border border-purple-200 rounded-lg px-2 py-1"
            >
              {selectedPaymentMethod ? '更换' : '选择'}
            </button>
          </div>
          <button
            onClick={handleCreateVideo}
            disabled={!videoDescription.trim() || !selectedStyle || isEnhancingPrompt || !isSelectedPaymentValid()}
            className={`w-full py-3 rounded-lg text-sm font-medium ${
              videoDescription.trim() && selectedStyle && !isEnhancingPrompt && isSelectedPaymentValid()
                ? 'bg-green-500 text-white' 
                : 'bg-gray-300 text-gray-500'
            }`}
          >
            <Film size={18} className="mr-2 inline"/>
            创建视频
          </button>
        </div>
      )}

      {step === 'processing' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          <button 
            onClick={handleCancelCreation} 
            className="w-full py-3 rounded-lg bg-red-500 text-white text-sm font-medium"
          >
            取消创建
          </button>
        </div>
      )}

      {/* Payment method selection modal */}
{showPaymentMethods && (
  <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center p-4">
    <div className="bg-white rounded-t-xl w-full max-w-md">
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-lg font-bold">选择支付方式</h2>
        <button 
          onClick={() => setShowPaymentMethods(false)}
          className="text-gray-500 p-1 rounded-full hover:bg-gray-100"
        >
          <X size={20} />
        </button>
      </div>
      
      <div className="p-4">
        <div className="grid grid-cols-1 gap-3">
          {/* Party Coins Option */}
          <div className={`p-4 rounded-lg text-left border ${
            tempPaymentMethod === 'partyCoins' 
              ? 'bg-yellow-50 border-yellow-300' 
              : 'bg-white border-gray-200'
          }`}>
            <button 
              onClick={() => setTempPaymentMethod('partyCoins')}
              className="w-full flex items-center"
            >
              <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                <Coins size={20} className="text-yellow-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">派对币</h3>
                  <span className={`text-sm font-medium ${getBalanceColorClass('partyCoins', paymentOptions.partyCoins.amount)}`}>
                    余额: {userBalance.partyCoins}
                  </span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-gray-500">用派对币支付视频生成费用</p>
                  <span className="text-xs font-bold text-yellow-600">
                    -  {paymentOptions.partyCoins.amount}
                  </span>
                </div>
              </div>
              {tempPaymentMethod === 'partyCoins' && (
                <CheckCircle size={18} className="text-yellow-600 ml-2" />
              )}
            </button>
            
            {/* Add Get Party Coins button */}
              <div className="mt-3 pl-13">
                <button 
                  onClick={() => alert('跳转到派对币获取页面')}
                  className="ml-12 px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium flex items-center justify-center"
                >
                  <Plus size={14} className="mr-1" />
                  获取派对币
                </button>
              </div>
          </div>
          
          {/* Starlight Coins Option */}
          <div className={`p-4 rounded-lg text-left border ${
            tempPaymentMethod === 'starlightCoins' 
              ? 'bg-blue-50 border-blue-300' 
              : 'bg-white border-gray-200'
          }`}>
            <button 
              onClick={() => setTempPaymentMethod('starlightCoins')}
              className="w-full flex items-center"
            >
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <DollarSign size={20} className="text-blue-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">星光币</h3>
                  <span className={`text-sm font-medium ${getBalanceColorClass('starlightCoins', paymentOptions.starlightCoins.amount)}`}>
                    余额: {userBalance.starlightCoins}
                  </span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-gray-500">用星光币支付视频生成费用</p>
                  <span className="text-xs font-bold text-blue-600">
                    - {paymentOptions.starlightCoins.amount}
                  </span>
                </div>
              </div>
              {tempPaymentMethod === 'starlightCoins' && (
                <CheckCircle size={18} className="text-blue-600 ml-2" />
              )}
            </button>
            
            {/* Add Recharge Starlight Coins button */}
              <div className="mt-3 pl-13">
                <button 
                  onClick={() => alert('跳转到星光币充值页面')}
                  className="ml-12 px-3 py-1.5 bg-blue-100 text-blue-700 rounded-full text-xs font-medium flex items-center justify-center"
                >
                  <Plus size={14} className="mr-1" />
                  充值星光币
                </button>
              </div>
          </div>
          
          {/* Watch Video Option */}
          <button 
            onClick={() => setTempPaymentMethod('watchVideo')}
            className={`p-4 rounded-lg text-left flex items-center border ${
              tempPaymentMethod === 'watchVideo' 
                ? 'bg-green-50 border-green-300' 
                : 'bg-white border-gray-200 hover:bg-gray-50'
            }`}
          >
            <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
              <PlayCircle size={20} className="text-green-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium">观看激励视频</h3>
              <p className="text-xs text-gray-500 mt-1">观看短视频广告以免费创作</p>
            </div>
            {tempPaymentMethod === 'watchVideo' && (
              <CheckCircle size={18} className="text-green-600 ml-2" />
            )}
          </button>
        </div>
      </div>
      
      <div className="p-4 border-t border-gray-200">
        <button 
          onClick={() => {
            setSelectedPaymentMethod(tempPaymentMethod);
            setShowPaymentMethods(false);
          }}
          className="w-full py-3 bg-purple-600 rounded-lg text-white font-medium"
        >
          确认支付方式
        </button>
      </div>
    </div>
  </div>
)}

      {/* Enhancement options modal */}
      {showEnhanceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl w-full max-w-md overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-bold">选择润色方式</h2>
              <button 
                onClick={() => setShowEnhanceModal(false)}
                className="text-gray-500 p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4 max-h-[50vh] overflow-y-auto">
              <div className="space-y-3">
                {enhanceStyles.map((style) => (
                  <button
                    key={style.id}
                    onClick={() => {
                      setSelectedEnhanceStyle(style);
                      if (style.id !== 'custom') {
                        setCustomEnhanceText('');
                      }
                    }}
                    className={`w-full p-3 border rounded-lg text-left flex items-center transition-colors ${
                      selectedEnhanceStyle?.id === style.id 
                        ? 'border-purple-500 bg-purple-50' 
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                      selectedEnhanceStyle?.id === style.id 
                        ? 'bg-purple-100' 
                        : 'bg-gray-100'
                    }`}>
                      {style.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium">{style.name}</h3>
                      <p className="text-xs text-gray-500">{style.description}</p>
                    </div>
                    {selectedEnhanceStyle?.id === style.id && (
                      <CheckCircle size={18} className="text-purple-600 ml-2" />
                    )}
                  </button>
                ))}
              </div>
              
              {selectedEnhanceStyle?.id === 'custom' && (
                <div className="mt-4">
                  <label className="text-sm font-medium text-gray-700 block mb-2">
                    自定义润色指令
                  </label>
                  <textarea
                    value={customEnhanceText}
                    onChange={(e) => setCustomEnhanceText(e.target.value)}
                    placeholder="例如：添加更多关于场景光线的描述，强调角色的表情与动作细节..."
                    className="w-full p-3 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[80px] resize-none"
                  ></textarea>
                </div>
              )}
              
              <div className="mt-4 bg-blue-50 rounded-lg p-3 border border-blue-100">
                <h3 className="text-xs font-medium text-blue-800 flex items-center mb-1">
                  <Sparkles size={14} className="mr-1 text-blue-500" />
                  润色效果预览
                </h3>
                <p className="text-xs text-blue-700">
                  {selectedEnhanceStyle ? (
                    selectedEnhanceStyle.id === 'more_detailed' ? 
                      "原始描述将添加更丰富的环境、光线和细节描述。" :
                    selectedEnhanceStyle.id === 'more_concise' ?
                      "将保留关键内容，使描述更加精炼和有力。" :
                    selectedEnhanceStyle.id === 'more_dramatic' ?
                      "将添加更多情感和氛围元素，增强画面的戏剧性。" :
                    selectedEnhanceStyle.id === 'more_professional' ?
                      "将使用更专业的摄影和艺术术语，提升描述的专业性。" :
                    selectedEnhanceStyle.id === 'cinematic' ?
                      "将添加电影级的镜头语言和场景转换描述，提升视频的叙事感。" :
                    selectedEnhanceStyle.id === 'custom' ?
                      "将根据您的自定义指令进行润色。" :
                      "请选择一种润色方式。"
                  ) : "请选择一种润色方式。"}
                </p>
              </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <div className="flex gap-2">
                <button 
                  onClick={() => setShowEnhanceModal(false)}
                  className="flex-1 py-3 bg-gray-100 rounded-lg text-gray-700 font-medium"
                >
                  取消
                </button>
                <button 
                  onClick={handleEnhanceDescription}
                  disabled={!selectedEnhanceStyle || (selectedEnhanceStyle?.id === 'custom' && !customEnhanceText.trim())}
                  className={`flex-1 py-3 rounded-lg font-medium ${
                    (!selectedEnhanceStyle || (selectedEnhanceStyle?.id === 'custom' && !customEnhanceText.trim())) 
                      ? 'bg-gray-300 text-gray-500' 
                      : 'bg-purple-600 text-white'
                  }`}
                >
                  应用润色
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Reference image selection modal */}
      {showReferenceSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center p-4">
          <div className="bg-white rounded-t-xl w-full max-w-md">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-bold">添加参考图片</h2>
            </div>
            
            <div className="p-4">
              <div className="grid grid-cols-1 gap-3">
                <button 
                  onClick={() => handleAddReferenceImage('library')}
                  className="p-4 bg-purple-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                    <FileImage size={20} className="text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">从素材库选择</h3>
                    <p className="text-xs text-gray-500">使用已有的角色图片作为参考</p>
                  </div>
                </button>
                
                <button 
                  onClick={() => handleAddReferenceImage('camera')}
                  className="p-4 bg-blue-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <Camera size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">拍摄照片</h3>
                    <p className="text-xs text-gray-500">使用相机拍摄新照片作为参考</p>
                  </div>
                </button>
                
                <button 
                  onClick={() => handleAddReferenceImage('gallery')}
                  className="p-4 bg-green-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <Upload size={20} className="text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">从相册选择</h3>
                    <p className="text-xs text-gray-500">选择本地图片作为参考</p>
                  </div>
                </button>
              </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <button 
                onClick={() => setShowReferenceSelector(false)}
                className="w-full py-3 bg-gray-100 rounded-lg text-gray-700 font-medium"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Asset library selection modal */}
      {showLibrarySelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex flex-col">
          <div className="bg-white p-4 flex items-center justify-between border-b border-gray-200">
            <h2 className="text-lg font-bold flex items-center">
              <ChevronLeft 
                size={20} 
                className="mr-2 cursor-pointer" 
                onClick={() => {
                  setShowLibrarySelector(false);
                  setShowReferenceSelector(true);
                }}
              />
              素材库图片
            </h2>
            <button 
              onClick={() => setShowLibrarySelector(false)}
              className="text-gray-500"
            >
              <X size={20} />
            </button>
          </div>
          
          <div className="flex-1 bg-gray-50 p-4 overflow-auto">
            {imageAssets.length > 0 ? (
              <div className="grid grid-cols-2 gap-3">
                {imageAssets.map((asset) => (
                  <div 
                    key={asset.id} 
                    onClick={() => handleSelectAsset(asset)}
                    className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                  >
                    <div className="relative aspect-[4/3] bg-gray-100">
                      <img 
                        src={asset.url} 
                        alt={asset.title}
                        className="w-full h-full object-cover"
                      />
                      {selectedReferenceImages.some(img => img.id === asset.id) && (
                        <div className="absolute inset-0 bg-purple-500 bg-opacity-30 flex items-center justify-center">
                          <div className="bg-white rounded-full p-1">
                            <Check size={18} className="text-purple-600" />
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="p-2">
                      <p className="text-sm font-medium truncate">{asset.title}</p>
                      <p className="text-xs text-gray-500">{asset.createdAt}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                  <FileImage size={24} className="text-gray-400" />
                </div>
                <p className="text-gray-500">暂无图片素材</p>
                <p className="text-xs text-gray-400 mt-1">请先创建一些图片素材</p>
              </div>
            )}
          </div>
          
          <div className="bg-white p-4 border-t border-gray-200">
            <button 
              onClick={() => setShowLibrarySelector(false)}
              className="w-full py-3 bg-purple-600 rounded-lg text-white font-medium"
            >
              完成选择
            </button>
          </div>
        </div>
      )}

      {/* Styles */}
      <style jsx>{`
        .loading-animation-video {
          background: linear-gradient(-45deg, #e6e6e6, #f0f0f0, #ebebeb, #f5f5f5);
          background-size: 400% 400%;
          animation: gradient 2s ease infinite;
        }
        
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>
    </div>
  );
};

export default CreatorVideoStudio;