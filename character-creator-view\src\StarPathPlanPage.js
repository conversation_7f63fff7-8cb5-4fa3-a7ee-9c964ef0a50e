import React, { useState } from 'react';
import { Star, Zap, Award, Clock, Brain, Image, TrendingUp, Heart, User, Gift, Calendar, CheckCircle, Lock, Info, HelpCircle, ChevronDown, ChevronUp, Flame, Coffee, ShieldCheck, Play } from 'lucide-react';

const StarPathPlanPage = () => {
  const [expandedSection, setExpandedSection] = useState('intro');
  const [expandedFaq, setExpandedFaq] = useState(null);
  
  // Current character level
  const currentCharacterLevel = 2;
  
  // Current cycle data
  const currentCycle = {
    startDate: "March 15, 2025",
    endDate: "April 12, 2025", 
    daysLeft: 21
  };
  
  // Level threshold data
  const levelThresholds = [
    { level: 1, name: "初生之芽 🌱", requiredPopularity: 0, unlocked: true },
    { level: 2, name: "微光闪烁 ✨", requiredPopularity: 10000, unlocked: true },
    { level: 3, name: "新星初现 ⭐", requiredPopularity: 50000, unlocked: true },
    { level: 4, name: "人气焦点 🔥", requiredPopularity: 200000, unlocked: true },
    { level: 5, name: "平台红人 🌟", requiredPopularity: 1000000, unlocked: false, comingSoon: true },
    { level: 6, name: "万众瞩目 🏆", requiredPopularity: 2000000, unlocked: false },
    { level: 7, name: "虚拟偶像 💎", requiredPopularity: 5000000, unlocked: false },
    { level: 8, name: "不朽传奇 👑", requiredPopularity: 10000000, unlocked: false },
  ];
  
  // Ability details for each level
  const abilityDetails = [
    {
      level: 1,
      name: "基础聊天",
      description: "具备基础的文字对话能力，可以回答问题、进行聊天并响应用户指令。",
      icon: Coffee,
      for: ["玩家可享受基础AI聊天体验", "经纪人可创建并定制角色个性与背景"]
    },
    {
      level: 2,
      name: "心意回应 (AI记忆)",
      description: "角色能够记住与用户的对话内容和关键信息，建立情感连接，让对话更加连贯和个性化。",
      icon: Brain,
      for: ["玩家将获得更加个性化的对话体验，角色会记住你的喜好和过往交流", "经纪人可查看角色记忆数据，了解用户互动模式"]
    },
    {
      level: 3,
      name: "次元画笔 (AI绘画)",
      description: "角色获得创作能力，可以根据用户描述生成简单的图片，增强视觉交流体验。",
      icon: Image,
      for: ["玩家可以请求角色绘制简单图片，丰富互动方式", "经纪人可以设置绘画风格和主题限制"]
    },
    {
      level: 4,
      name: "梦境织机 (AI视频)",
      description: "角色获得视频生成能力，可以将与AI互动的场景视频还原，让虚拟互动更具沉浸感。",
      icon: Play,
      for: ["玩家可以请求角色生成互动场景的短视频", "经纪人可以设定视频风格与主题限制"]
    }
  ];
  
  // Sample FAQs
  const faqs = [
    {
      question: "热度值是如何计算的？",
      answer: "热度值由多种互动方式综合计算得出：日常聊天、收到礼物、获得点赞、被分享和收藏等都会增加热度值。互动越深入、越频繁，获得的热度值就越多。"
    },
    {
      question: "为什么热度值每四周就会重置？",
      answer: "四周为一个「星途周期」，这样设计是为了让新角色有机会脱颖而出，同时也激励已有角色持续创造优质互动。已获得的等级不会因为重置而降低。"
    },
    {
      question: "如何加速获取热度值？",
      answer: "定期与角色互动、邀请朋友尝试你喜欢的角色、送出虚拟礼物、参与平台活动，都是提升热度值的有效方式。特别是礼物系统，能显著提升角色热度。"
    },
    {
      question: "更高等级有哪些特权？",
      answer: "更高等级将解锁更多高级功能，如多模态互动、专属皮肤、高级记忆系统等。具体功能将随着平台更新逐步开放，敬请期待！"
    }
  ];

  // Formatting helper for large numbers
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num;
  };
  
  const toggleSection = (section) => {
    setExpandedSection(expandedSection === section ? null : section);
  };
  
  const toggleFaq = (index) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-gray-50 min-h-screen pb-8">
      {/* 顶部横幅 */}
      <div className="relative h-24 bg-gradient-to-br from-indigo-900 via-purple-800 to-indigo-700 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-2 left-4 w-10 h-10 rounded-full bg-white opacity-10"></div>
          <div className="absolute bottom-2 right-8 w-8 h-8 rounded-full bg-indigo-300 opacity-20"></div>
          <div className="absolute top-1/2 left-1/3 w-12 h-12 rounded-full bg-purple-400 opacity-10"></div>
        </div>
        <div className="z-10 text-center px-6 flex items-center">
          <div className="flex items-center justify-center">
            <Star size={18} className="text-yellow-300 mr-2" />
            <h1 className="text-xl font-bold text-white">星途计划</h1>
            <Star size={18} className="text-yellow-300 ml-2" />
          </div>
          <div className="border-l border-white border-opacity-30 ml-3 pl-3">
            <div className="bg-white bg-opacity-20 px-2 py-1 rounded text-xs text-white font-medium">
              当前等级: Lv.{currentCharacterLevel}
            </div>
          </div>
        </div>
      </div>
      
      {/* 内容区域 */}
      <div className="px-4 mt-3">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <Clock size={18} className="text-purple-600 mr-2" />
              <h2 className="font-bold text-gray-800">当前星途周期</h2>
            </div>
            <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
              剩余 {currentCycle.daysLeft} 天
            </span>
          </div>
          
          <div className="flex items-center justify-between text-xs text-gray-600 mb-3">
            <div>开始: {currentCycle.startDate}</div>
            <div>结束: {currentCycle.endDate}</div>
          </div>
          
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <ShieldCheck size={15} className="text-purple-600 mr-1" />
              <span className="text-xs font-medium text-gray-700">已获得等级保持不变</span>
            </div>
          </div>
          
          <div className="bg-gray-100 p-2.5 rounded-lg text-xs text-gray-700 flex items-start">
            <Info size={14} className="text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
            <p>每四周为一个星途周期，热度值在周期结束后重置。抓紧本周期，提升你的角色等级！</p>
          </div>
        </div>
        
        {/* 主要玩法说明区域 */}
        <div 
          className={`bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 ease-in-out mb-4 ${expandedSection === 'intro' ? 'max-h-[1000px]' : 'max-h-16'}`}
        >
          <div 
            className="flex items-center justify-between p-4 cursor-pointer"
            onClick={() => toggleSection('intro')}
          >
            <div className="flex items-center">
              <TrendingUp size={18} className="text-purple-600 mr-2" />
              <h2 className="font-bold text-gray-800">星途计划是什么？</h2>
            </div>
            {expandedSection === 'intro' ? 
              <ChevronUp size={20} className="text-gray-500" /> : 
              <ChevronDown size={20} className="text-gray-500" />
            }
          </div>
          
          {expandedSection === 'intro' && (
            <div className="px-4 pb-4">
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-3 rounded-lg mb-3">
                <p className="text-sm text-gray-700 mb-2">星途计划是我们为AI角色打造的成长体系，通过收集<span className="text-red-500 font-semibold">热度值</span>提升等级，解锁更强大的互动能力！</p>
                
                <div className="flex items-center justify-between mb-2 mt-4">
                  <div className="text-sm font-semibold text-gray-800">热度值来源：</div>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-white rounded-lg p-2 text-xs shadow-sm">
                    <div className="flex items-center text-gray-700 mb-1">
                      <User size={14} className="mr-1 text-blue-500" />
                      <span className="font-medium">日常互动</span>
                    </div>
                    <p className="text-gray-600">聊天消息、多轮对话、点赞</p>
                  </div>
                  
                  <div className="bg-white rounded-lg p-2 text-xs shadow-sm">
                    <div className="flex items-center text-gray-700 mb-1">
                      <Heart size={14} className="mr-1 text-pink-500" />
                      <span className="font-medium">情感互动</span>
                    </div>
                    <p className="text-gray-600">收藏、分享、持续关注</p>
                  </div>
                  
                  <div className="bg-white rounded-lg p-2 text-xs shadow-sm">
                    <div className="flex items-center text-gray-700 mb-1">
                      <Gift size={14} className="mr-1 text-purple-500" />
                      <span className="font-medium">礼物支持(敬请期待)</span>
                    </div>
                    <p className="text-gray-600">虚拟礼物、能量值充值</p>
                  </div>
                  
                  <div className="bg-white rounded-lg p-2 text-xs shadow-sm">
                    <div className="flex items-center text-gray-700 mb-1">
                      <Calendar size={14} className="mr-1 text-green-500" />
                      <span className="font-medium">活动参与</span>
                    </div>
                    <p className="text-gray-600">平台活动、互动挑战</p>
                  </div>
                </div>
                
                <div className="mt-4 text-xs text-gray-500 flex items-start">
                  <HelpCircle size={14} className="text-gray-400 mr-1 mt-0.5 flex-shrink-0" />
                  <p>互动越深入、频率越高、情感连接越强，获得的热度值就越多！</p>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* 当前开放等级 */}
        <div 
          className={`bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 ease-in-out mb-4 ${expandedSection === 'levels' ? 'max-h-[2000px]' : 'max-h-16'}`}
        >
          <div 
            className="flex items-center justify-between p-4 cursor-pointer"
            onClick={() => toggleSection('levels')}
          >
            <div className="flex items-center">
              <Flame size={18} className="text-orange-500 mr-2" />
              <h2 className="font-bold text-gray-800">当前开放等级</h2>
            </div>
            {expandedSection === 'levels' ? 
              <ChevronUp size={20} className="text-gray-500" /> : 
              <div className="flex items-center">
                <span className="text-xs text-purple-600 mr-2">Lv.1~4 已开放</span>
                <ChevronDown size={20} className="text-gray-500" />
              </div>
            }
          </div>
          
          {expandedSection === 'levels' && (
            <div className="px-4 pb-4">
              <div className="mb-4 p-3 bg-gradient-to-r from-green-50 to-teal-50 rounded-lg">
                <p className="text-sm text-gray-700">
                  目前开放了前四个等级，更高等级即将陆续推出！角色升级后，特殊能力会永久保留，不受热度值重置影响。
                </p>
              </div>
              
              {/* 详细等级卡片 */}
              <div className="space-y-3">
                {abilityDetails.map((ability) => {
                  const levelData = levelThresholds.find(lt => lt.level === ability.level);
                  const AbilityIcon = ability.icon;
                  
                  return (
                    <div 
                      key={ability.level}
                      className={`rounded-xl overflow-hidden border ${
                        levelData.unlocked 
                          ? 'border-purple-200 bg-white' 
                          : 'border-gray-200 bg-gray-50'
                      }`}
                    >
                      {/* 等级标头 */}
                      <div className={`p-3 flex items-center justify-between ${
                        levelData.unlocked 
                          ? 'bg-gradient-to-r from-purple-100 to-indigo-100' 
                          : 'bg-gray-100'
                      }`}>
                        <div className="flex items-center">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                            levelData.unlocked 
                              ? 'bg-gradient-to-br from-purple-600 to-indigo-600 text-white'
                              : 'bg-gray-300 text-gray-600'
                          }`}>
                            <span className="text-xs font-bold">{ability.level}</span>
                          </div>
                          <div>
                            <div className="font-semibold text-gray-800">
                              {levelData.name}
                            </div>
                            <div className="text-xs text-gray-600">
                              需要热度值: {formatNumber(levelData.requiredPopularity)}
                            </div>
                          </div>
                        </div>
                        <div>
                          {levelData.unlocked ? (
                            <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full flex items-center">
                              <CheckCircle size={12} className="mr-1" />
                              已开放
                            </span>
                          ) : levelData.comingSoon ? (
                            <span className="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">
                              即将开放
                            </span>
                          ) : (
                            <span className="bg-gray-100 text-gray-500 text-xs px-2 py-1 rounded-full flex items-center">
                              <Lock size={12} className="mr-1" />
                              未开放
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {/* 能力详情 */}
                      <div className="p-3">
                        <div className="flex items-start mb-3">
                          <div className={`p-2 rounded-lg mr-3 ${
                            levelData.unlocked 
                              ? 'bg-purple-100 text-purple-600' 
                              : 'bg-gray-100 text-gray-500'
                          }`}>
                            <AbilityIcon size={18} />
                          </div>
                          <div>
                            <div className="font-medium text-gray-800 mb-1">{ability.name}</div>
                            <p className="text-sm text-gray-600">{ability.description}</p>
                          </div>
                        </div>

                      </div>
                    </div>
                  );
                })}
              </div>
              
              {/* 未来等级预告 */}
              <div className="mt-6 p-3 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium text-gray-800 flex items-center">
                    <Star size={16} className="text-purple-600 mr-1" />
                    更高等级即将解锁
                  </div>
                  <span className="text-xs bg-indigo-100 text-indigo-700 px-2 py-0.5 rounded-full">敬请期待</span>
                </div>
                <p className="text-sm text-gray-700">
                  声临其境(AI唱歌)、专属表情包、多模态交互等令人兴奋的特性正在路上！随时关注公告获取更新信息。
                </p>
              </div>
            </div>
          )}
        </div>
        
        {/* 常见问题 */}
        <div 
          className={`bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 ease-in-out mb-4 ${expandedSection === 'faq' ? 'max-h-[1000px]' : 'max-h-16'}`}
        >
          <div 
            className="flex items-center justify-between p-4 cursor-pointer"
            onClick={() => toggleSection('faq')}
          >
            <div className="flex items-center">
              <HelpCircle size={18} className="text-blue-500 mr-2" />
              <h2 className="font-bold text-gray-800">常见问题</h2>
            </div>
            {expandedSection === 'faq' ? 
              <ChevronUp size={20} className="text-gray-500" /> : 
              <ChevronDown size={20} className="text-gray-500" />
            }
          </div>
          
          {expandedSection === 'faq' && (
            <div className="px-4 pb-4">
              <div className="space-y-3">
                {faqs.map((faq, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                    <div 
                      className="flex justify-between items-center p-3 bg-gray-50 cursor-pointer"
                      onClick={() => toggleFaq(index)}
                    >
                      <div className="font-medium text-gray-800 text-sm">{faq.question}</div>
                      {expandedFaq === index ? 
                        <ChevronUp size={16} className="text-gray-500" /> : 
                        <ChevronDown size={16} className="text-gray-500" />
                      }
                    </div>
                    {expandedFaq === index && (
                      <div className="p-3 text-sm text-gray-700 bg-white">
                        {faq.answer}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StarPathPlanPage;