/* App.css */
.home-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.home-page h1 {
  margin-bottom: 2rem;
  color: #333;
}

.nav-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.nav-button {
  display: block;
  padding: 1rem;
  background-color: #4285f4;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.nav-button:hover {
  background-color: #3367d6;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 添加一个返回首页的按钮样式，可以放在每个子页面 */
.back-home {
  position: fixed;
  top: 20px;
  left: 20px;
  padding: 0.5rem 1rem;
  background-color: #f1f1f1;
  color: #333;
  border-radius: 4px;
  text-decoration: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-home:hover {
  background-color: #e1e1e1;
}