import React, { useState } from 'react';
import { 
  Rocket, Sparkles, ShoppingCart, Palette, Users, ToyBrick, 
  Network, Telescope, ChevronDown, ChevronRight, Milestone,
  Zap, Gem, Film, Mic, Bot, Code, Gamepad, Wrench, Brain, Building
} from 'lucide-react';

// --- 数据定义 ---
const roadmapData = [
  {
    phase: 1,
    title: "星核点燃 (Foundation & Core Loop)",
    timeline: "Q1-Q2 202X",
    icon: Rocket,
    focus: [
      "上线核心升级体系，验证用户参与度",
      "建立基础商业闭环",
      "优化核心聊天体验"
    ],
    features: [
      { name: "AI角色「星途计划」上线", description: "人气值系统、星途等级体系 (Lv.1-5 基础权益)", type: "User & Creator Facing", icon: Zap },
      { name: "基础虚拟礼物系统", description: "上线小额礼物，与人气值挂钩", type: "Monetization", icon: ShoppingCart },
      { name: "角色详情页 V1", description: "展示等级、人气、基础信息", type: "User Facing", icon: Users },
      { name: "创作者控制台 V1", description: "查看角色状态、基础人气数据", type: "Creator Facing", icon: Palette },
      { name: "核心对话模型优化", description: "提升基础对话流畅度与上下文", type: "Platform", icon: Brain },
      { name: "激励广告接入 (初步)", description: "看广告得小额礼物/短暂加速", type: "Monetization", icon: Film },
    ]
  },
  {
    phase: 2,
    title: "星光闪耀 (Content Enrichment & Creator Tools)",
    timeline: "Q3 202X",
    icon: Sparkles,
    focus: [
      "引入深度内容形态，提升可玩性",
      "增强创作者赋能，提供创作工具",
      "拓展商业化手段"
    ],
    features: [
      { name: "「星光作品集」模块上线", description: "支持AI生成音乐、基础AI视频(图文+音)", type: "User & Creator Facing", icon: Gem },
      { name: "素材库 V1 & 基础AI生成", description: "支持图片、简单音频生成与管理", type: "Creator Facing", icon: Building },
      { name: "星途等级扩展 (Lv.6-7)", description: "解锁语音能力(TTS)、皮肤、技能槽(1个)", type: "User & Creator Facing", icon: Zap },
      { name: "创作者高级工具箱 (Beta)", description: "抢先体验、高级数据看板入口", type: "Creator Facing & Monetization", icon: Wrench },
      { name: "创作者「星途加速包」", description: "人气助推、曝光加油站 (付费)", type: "Monetization", icon: ShoppingCart },
      { name: "用户端「作品集」展示", description: "独立Tab展示音乐、视频作品", type: "User Facing", icon: Users },
       { name: "AI唱歌能力 (Beta)", description: "解锁Lv5可尝试AI生成简单歌曲", type: "User & Creator Facing", icon: Mic },
    ]
  },
  {
    phase: 3,
    title: "星云汇聚 (Ecosystem Ignition)",
    timeline: "Q4 202X",
    icon: Network,
    focus: [
      "启动开放平台，引入第三方力量",
      "丰富深度互动形态",
      "完善商业生态"
    ],
    features: [
      { name: "「星伴开放平台」(邀请制 Beta)", description: "上线核心API (互动代理、基础信息)、开发者平台 V1", type: "Platform", icon: Code },
      { name: "首批第三方应用上线", description: "扶持2-3个标杆应用 (如AI探案、心语树洞)", type: "User Facing", icon: Gamepad },
      { name: "互动剧本创作与体验", description: "上线作品集互动剧本功能 (可由第三方应用实现)", type: "User & Creator Facing", icon: Bot },
      { name: "星途等级扩展 (Lv.8)", description: "解锁高级多模态互动 (初步)", type: "User & Creator Facing", icon: Zap },
      { name: "平台/开发者/创作者三方分成体系", description: "建立开放平台收益分成机制", type: "Monetization", icon: ShoppingCart },
       { name: "AI绘画/视频能力增强", description: "提升作品集和素材库生成效果与控制力", type: "Platform", icon: Palette },
    ]
  },
  {
    phase: 4,
    title: "星河灿烂 (Platform Expansion & Refinement)",
    timeline: "202X+ Q1",
    icon: ToyBrick,
    focus: [
      "全面开放生态，扩大开发者规模",
      "优化平台核心能力与体验",
      "探索更多商业可能性"
    ],
    features: [
      { name: "「星伴开放平台」全面开放", description: "开放开发者注册，完善API与SDK", type: "Platform", icon: Code },
      { name: "开放平台应用市场", description: "创作者可浏览、启用/禁用应用", type: "Creator Facing", icon: Palette },
      { name: "用户端「互动中心」完善", description: "应用发现、管理与评价", type: "User Facing", icon: Users },
      { name: "高级创作者工具/特权", description: "如模型微调入口(Beta)、联名机会对接", type: "Creator Facing & Monetization", icon: Wrench },
      { name: "广告平台整合", description: "为第三方应用提供广告变现选项", type: "Monetization", icon: Film },
      { name: "社区功能增强", description: "围绕角色/作品/应用的讨论与互动", type: "User Facing", icon: Users },
    ]
  },
   {
    phase: 5,
    title: "未来之境 (Future Vision)",
    timeline: "Beyond",
    icon: Telescope,
    focus: [
      "探索前沿AI交互形态",
      "打破虚拟与现实边界"
    ],
    features: [
      { name: "硬件集成探索与合作", description: "与智能硬件厂商合作，实现物理交互", type: "Platform", icon: Code },
      { name: "AI自主意识与个性进化 (高级)", description: "探索更深层次的角色自主学习与演化", type: "Platform", icon: Brain },
      { name: "超个性化内容生成", description: "基于深度用户理解的定制内容服务", type: "User & Creator Facing", icon: Gem },
      { name: "去中心化角色身份/资产", description: "探索Web3相关技术结合 (如NFT)", type: "Platform", icon: Network },
    ]
  },
];

// --- 特性标签样式 ---
const featureTypeStyles = {
  "User Facing": "bg-blue-100 text-blue-700",
  "Creator Facing": "bg-purple-100 text-purple-700",
  "Platform": "bg-gray-100 text-gray-700",
  "Monetization": "bg-green-100 text-green-700",
  "User & Creator Facing": "bg-indigo-100 text-indigo-700",
};

// --- 单个阶段组件 ---
const PhaseCard = ({ phaseData, isExpanded, onToggle }) => {
  const PhaseIcon = phaseData.icon;
  
  return (
    <div className="mb-6 bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200 transition-all duration-300 ease-in-out">
      {/* 阶段头部 */}
      <div 
        className={`p-4 flex items-center justify-between cursor-pointer ${isExpanded ? 'bg-gradient-to-r from-purple-50 to-indigo-50' : 'bg-gray-50 hover:bg-gray-100'}`}
        onClick={onToggle}
      >
        <div className="flex items-center">
          <div className={`mr-4 p-3 rounded-full ${isExpanded ? 'bg-gradient-to-br from-purple-500 to-indigo-600 text-white shadow-md' : 'bg-gray-200 text-gray-600'}`}>
            <PhaseIcon size={24} />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-800">{`Phase ${phaseData.phase}: ${phaseData.title}`}</h2>
            <p className="text-sm text-gray-500">{phaseData.timeline}</p>
          </div>
        </div>
        <div className="text-purple-600 transition-transform duration-300">
          {isExpanded ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
        </div>
      </div>
      
      {/* 可展开内容 */}
      <div 
        className={`transition-all duration-500 ease-in-out overflow-hidden ${isExpanded ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'}`}
        style={{ transitionProperty: 'max-height, opacity' }}
      >
        <div className="p-5 border-t border-gray-200">
          <h3 className="text-md font-semibold mb-3 text-gray-700 flex items-center">
            <Milestone size={16} className="mr-2 text-purple-500" />
            主要目标:
          </h3>
          <ul className="list-disc list-inside space-y-1.5 text-sm text-gray-600 mb-6 pl-2">
            {phaseData.focus.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
          
          <h3 className="text-md font-semibold mb-4 text-gray-700">关键特性:</h3>
          <div className="space-y-3">
            {phaseData.features.map((feature, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                <div className="flex items-center justify-between mb-1">
                   <div className="flex items-center">
                     {feature.icon && <feature.icon size={16} className="mr-2 text-indigo-500 flex-shrink-0" />}
                     <span className="font-medium text-sm text-gray-800">{feature.name}</span>
                   </div>
                   <span className={`text-xs px-2 py-0.5 rounded-full whitespace-nowrap ${featureTypeStyles[feature.type] || 'bg-gray-100 text-gray-700'}`}>
                      {feature.type}
                   </span>
                </div>
                <p className="text-xs text-gray-600 pl-6">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// --- 主路线图组件 ---
const RoadmapVisualization = () => {
  const [expandedPhase, setExpandedPhase] = useState(1); // 默认展开第一阶段

  const handleToggle = (phaseNumber) => {
    setExpandedPhase(expandedPhase === phaseNumber ? null : phaseNumber);
  };

  return (
    <div className="p-4 md:p-8 bg-gradient-to-br from-gray-50 via-purple-50 to-indigo-100 min-h-screen">
      <h1 className="text-3xl font-bold text-center mb-4 text-gray-800">🚀 AI角色应用 - 迭代路线图 🚀</h1>
      <p className="text-center text-gray-600 mb-8 max-w-2xl mx-auto">
        这是一个宏伟的计划！我们将分阶段构建这个充满活力的AI角色生态系统。点击每个阶段标题可展开/折叠详细特性。
      </p>
      
      <div className="max-w-4xl mx-auto">
        {roadmapData.map((phase) => (
          <PhaseCard 
            key={phase.phase}
            phaseData={phase}
            isExpanded={expandedPhase === phase.phase}
            onToggle={() => handleToggle(phase.phase)}
          />
        ))}
      </div>
      
      <p className="text-center text-xs text-gray-500 mt-10">
        *注意: 时间线和特性优先级可能会根据实际情况调整。这是一个指导性计划。
      </p>
    </div>
  );
};

export default RoadmapVisualization;

// --- 如何在你的项目中使用 ---
// 1. 确保你安装了 react, lucide-react 和 tailwindcss。
// 2. 将此代码保存为一个 .jsx 文件 (例如 Roadmap.jsx)。
// 3. 在你的主应用文件中导入并使用它:
//    import RoadmapVisualization from './Roadmap';
//    function App() {
//      return <RoadmapVisualization />;
//    }
// 4. 确保你的 Tailwind CSS 配置能够扫描到这个文件中的类名。