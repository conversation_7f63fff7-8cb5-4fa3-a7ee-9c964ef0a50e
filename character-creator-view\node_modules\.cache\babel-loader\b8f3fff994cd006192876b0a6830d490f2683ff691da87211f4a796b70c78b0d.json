{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u89D2\\u8272\\u5347\\u7EA7\\u8BA1\\u5212\\\\code\\\\character-creator-view\\\\src\\\\AICharacterInvestmentPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ChevronRight, TrendingUp, TrendingDown, Sparkles, Heart, MessageCircle, Gift, AlertCircle, ChevronLeft, Coins, Trophy, Calendar, RefreshCw, Target, Zap, Star, X, Info, ArrowUp, BarChart3, Wallet, Battery } from 'lucide-react';\n\n// 导入头像图片\nimport characterAvatar from './assets/沈婉.png';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AICharacterInvestmentPage = () => {\n  _s();\n  // 状态管理\n  const [showIntro, setShowIntro] = useState(true);\n  const [showInfo, setShowInfo] = useState(false);\n  const [partyCoins, setPartyCoins] = useState(5280);\n  const [virtualFund, setVirtualFund] = useState(12580);\n  const [experienceFund] = useState(10000);\n  const [principalFund, setPrincipalFund] = useState(0);\n  const [totalProfit, setTotalProfit] = useState(2580);\n  const [characterMood, setCharacterMood] = useState('happy');\n  const [showBottomSheet, setShowBottomSheet] = useState(false);\n  const [bottomSheetType, setBottomSheetType] = useState('');\n  const [showMessage, setShowMessage] = useState(false);\n  const [messageContent, setMessageContent] = useState('');\n  const [exchangeAmount, setExchangeAmount] = useState('');\n  const [todayProfit, setTodayProfit] = useState(158);\n  const [profitRate, setProfitRate] = useState(25.8);\n  const [refreshing, setRefreshing] = useState(false);\n  const [lastDailyCheckIn, setLastDailyCheckIn] = useState(null);\n  const [lastInteractionTime, setLastInteractionTime] = useState({});\n  const [dailyCheckInDone, setDailyCheckInDone] = useState(false);\n  const [showVitalityTips, setShowVitalityTips] = useState(false);\n  const [lastAdviceCategory, setLastAdviceCategory] = useState(null);\n  const [lastAdviceOption, setLastAdviceOption] = useState(null);\n  const [currentAdviceCategory, setCurrentAdviceCategory] = useState('risk');\n\n  // 理财活力相关状态\n  const [vitalityPoints, setVitalityPoints] = useState(80); // 理财活力值\n  const [lastVitalityUpdate, setLastVitalityUpdate] = useState(Date.now());\n  const [isInvestmentActive, setIsInvestmentActive] = useState(true);\n\n  // 持仓信息\n  const [holdings, setHoldings] = useState([{\n    name: '科技未来',\n    code: '000001',\n    shares: 200,\n    profit: 580,\n    profitRate: 5.8,\n    price: 28.5\n  }, {\n    name: '消费龙头',\n    code: '000002',\n    shares: 300,\n    profit: -120,\n    profitRate: -2.1,\n    price: 45.2\n  }, {\n    name: '新能源车',\n    code: '000003',\n    shares: 100,\n    profit: 320,\n    profitRate: 8.2,\n    price: 156.8\n  }]);\n\n  // 角色信息\n  const character = {\n    name: '沈婉',\n    style: '稳健型',\n    description: '温柔体贴，偏好稳定增长',\n    avatar: characterAvatar,\n    investmentStyle: '稳健保守' // 投资风格\n  };\n\n  // 礼物列表（增加活力值）\n  const giftList = [{\n    id: 1,\n    name: '鲜花',\n    icon: '🌹',\n    price: 10,\n    vitality: 5,\n    message: '谢谢主人的鲜花！我会更努力的！'\n  }, {\n    id: 2,\n    name: '咖啡',\n    icon: '☕',\n    price: 20,\n    vitality: 10,\n    message: '咖啡的香味让我精神百倍！'\n  }, {\n    id: 3,\n    name: '蛋糕',\n    icon: '🎂',\n    price: 50,\n    vitality: 20,\n    message: '哇！蛋糕好甜，就像主人一样！'\n  }, {\n    id: 4,\n    name: '钻戒',\n    icon: '💍',\n    price: 100,\n    vitality: 40,\n    message: '这...这是给我的吗？我会珍藏一生的！'\n  }, {\n    id: 5,\n    name: '游艇',\n    icon: '🛥️',\n    price: 200,\n    vitality: 80,\n    message: '天哪！主人太大方了！我们一起去看海吧！'\n  }, {\n    id: 6,\n    name: '城堡',\n    icon: '🏰',\n    price: 500,\n    vitality: 200,\n    message: '我...我不知道说什么好了，谢谢主人！❤️'\n  }];\n\n  // 建议分类配置\n  const adviceCategories = {\n    risk: {\n      title: '风险偏好',\n      icon: '🛡️',\n      options: [{\n        id: 'conservative',\n        title: '保守稳健',\n        desc: '安全第一'\n      }, {\n        id: 'moderate',\n        title: '稳中求进',\n        desc: '平衡配置'\n      }, {\n        id: 'aggressive',\n        title: '激进冒险',\n        desc: '高风险高收益'\n      }]\n    },\n    timing: {\n      title: '操作节奏',\n      icon: '⏱️',\n      options: [{\n        id: 'quick',\n        title: '快进快出',\n        desc: '把握短期机会'\n      }, {\n        id: 'patient',\n        title: '耐心等待',\n        desc: '寻找最佳时机'\n      }, {\n        id: 'steady',\n        title: '稳定节奏',\n        desc: '按计划执行'\n      }]\n    },\n    focus: {\n      title: '关注重点',\n      icon: '🎯',\n      options: [{\n        id: 'value',\n        title: '价值为王',\n        desc: '寻找低估资产'\n      }, {\n        id: 'growth',\n        title: '成长优先',\n        desc: '追求高增长'\n      }, {\n        id: 'trend',\n        title: '趋势跟随',\n        desc: '顺势而为'\n      }]\n    }\n  };\n\n  // 活力值消耗（每小时消耗2点）\n  useEffect(() => {\n    const timer = setInterval(() => {\n      const now = Date.now();\n      const hoursPassed = (now - lastVitalityUpdate) / (1000 * 60 * 60);\n      const vitalityConsumed = Math.floor(hoursPassed * 2);\n      if (vitalityConsumed > 0) {\n        setVitalityPoints(prev => {\n          const newVitality = Math.max(0, prev - vitalityConsumed);\n\n          // 活力值低于30时，偶尔提醒\n          if (newVitality < 30 && newVitality > 0 && Math.random() < 0.3) {\n            const hints = [\"主人，我感觉有点累了呢...\", \"最近工作有点辛苦，需要补充能量了~\", \"如果能喝杯咖啡就好了...\"];\n            showCharacterMessage(hints[Math.floor(Math.random() * hints.length)]);\n          }\n\n          // 活力值为0，停止投资\n          if (newVitality === 0 && isInvestmentActive) {\n            setIsInvestmentActive(false);\n            showCharacterMessage(\"我太累了，需要休息一下...投资暂时停止了\");\n            // 自动结算\n            handleAutoSettle();\n          }\n          return newVitality;\n        });\n        setLastVitalityUpdate(now);\n      }\n    }, 60000); // 每分钟检查一次\n\n    return () => clearInterval(timer);\n  }, [lastVitalityUpdate, isInvestmentActive]);\n\n  // 在组件加载时检查是否需要每日签到奖励\n  useEffect(() => {\n    const checkDailyBonus = () => {\n      const today = new Date().toDateString();\n      const lastCheck = lastDailyCheckIn ? new Date(lastDailyCheckIn).toDateString() : null;\n      if (lastCheck !== today) {\n        // 每日首次查看，增加活力值\n        setVitalityPoints(prev => Math.min(100, prev + 10)); // 每日查看+10活力\n        setLastDailyCheckIn(Date.now());\n        setDailyCheckInDone(true);\n        showCharacterMessage(\"早上好主人！感谢您每天来看我，活力值+10！💕\");\n      }\n    };\n    checkDailyBonus();\n  }, []);\n\n  // 自动结算\n  const handleAutoSettle = () => {\n    const totalWithdrawable = principalFund + totalProfit;\n    const convertedCoins = Math.floor(totalWithdrawable / 100 * 0.95); // 扣除5%手续费\n    setPartyCoins(prev => prev + convertedCoins);\n    setPrincipalFund(0);\n    setTotalProfit(0);\n    setVirtualFund(experienceFund);\n  };\n\n  // 获取活力值颜色\n  const getVitalityColor = () => {\n    if (vitalityPoints > 60) return 'text-green-500';\n    if (vitalityPoints > 30) return 'text-yellow-500';\n    return 'text-red-500';\n  };\n\n  // 获取活力值图标\n  const getVitalityIcon = () => {\n    if (vitalityPoints > 60) return '⚡';\n    if (vitalityPoints > 30) return '🔋';\n    return '🪫';\n  };\n\n  // 显示角色消息\n  const showCharacterMessage = message => {\n    setMessageContent(message);\n    setShowMessage(true);\n    setTimeout(() => setShowMessage(false), 3000);\n  };\n\n  // 打开底部弹窗\n  const openBottomSheet = type => {\n    setBottomSheetType(type);\n    setShowBottomSheet(true);\n  };\n\n  // 关闭底部弹窗\n  const closeBottomSheet = () => {\n    setShowBottomSheet(false);\n    setExchangeAmount('');\n  };\n\n  // 处理兑换\n  const handleExchange = isIn => {\n    const amount = parseInt(exchangeAmount);\n    if (!amount || amount <= 0) {\n      showCharacterMessage(\"请输入正确的数量哦~\");\n      return;\n    }\n    if (isIn) {\n      // 投入资金\n      if (amount > partyCoins) {\n        showCharacterMessage(\"主人的派对币不够哦~\");\n        return;\n      }\n      setPartyCoins(prev => prev - amount);\n      setPrincipalFund(prev => prev + amount * 100); // 记录本金\n      setVirtualFund(prev => prev + amount * 100);\n      showCharacterMessage(`谢谢主人的信任！这${amount * 100}元就是我们的梦想启动金！`);\n    } else {\n      // 提取资产\n      const totalWithdrawable = principalFund + totalProfit; // 可提取总额\n      const maxExchange = Math.floor(totalWithdrawable / 100);\n      if (amount > maxExchange) {\n        showCharacterMessage(`主人，目前只能提取${maxExchange}个派对币哦~`);\n        return;\n      }\n      const withdrawAmount = amount * 100;\n      const fee = Math.floor(amount * 0.05);\n\n      // 优先从收益中扣除，不足再从本金扣\n      if (withdrawAmount <= totalProfit) {\n        setTotalProfit(prev => prev - withdrawAmount);\n      } else {\n        const fromProfit = totalProfit;\n        const fromPrincipal = withdrawAmount - fromProfit;\n        setTotalProfit(0);\n        setPrincipalFund(prev => prev - fromPrincipal);\n      }\n      setPartyCoins(prev => prev + amount - fee);\n      setVirtualFund(prev => prev - withdrawAmount);\n      showCharacterMessage(`成功提取${amount - fee}个派对币！手续费${fee}个`);\n    }\n    closeBottomSheet();\n  };\n\n  // 给角色建议\n  const giveAdvice = (category, option) => {\n    const lastAdvice = lastInteractionTime.advice || 0;\n    const timeSinceLastAdvice = Date.now() - lastAdvice;\n\n    // 生成动态回复\n    const generateResponse = () => {\n      const responses = {\n        conservative: \"明白了主人，我会更加谨慎，保护好我们的资产！\",\n        moderate: \"好的！我会平衡风险和收益，稳步前进！\",\n        aggressive: \"收到！让我们大胆一些，追求更高收益！\",\n        quick: \"了解！我会加快操作节奏，抓住短期机会！\",\n        patient: \"好的主人，我会耐心等待最佳时机！\",\n        steady: \"明白了，我会保持稳定的节奏操作！\",\n        value: \"收到！我会重点寻找被低估的优质资产！\",\n        growth: \"好的！我会关注高成长潜力的标的！\",\n        trend: \"明白了！我会顺势而为，跟随市场趋势！\"\n      };\n      return responses[option.id] || \"谢谢主人的建议，我会认真考虑的！\";\n    };\n    if (timeSinceLastAdvice > 1800000) {\n      // 30分钟冷却\n      setVitalityPoints(prev => Math.min(100, prev + 3));\n      setLastInteractionTime(prev => ({\n        ...prev,\n        advice: Date.now()\n      }));\n      showCharacterMessage(generateResponse() + \" (活力值+3)\");\n    } else {\n      const remainingTime = Math.ceil((1800000 - timeSinceLastAdvice) / 60000);\n      showCharacterMessage(generateResponse() + ` (${remainingTime}分钟后可再次获得活力值)`);\n    }\n\n    // 记录本次建议\n    setLastAdviceCategory(category);\n    setLastAdviceOption(option);\n    closeBottomSheet();\n  };\n\n  // 送礼物给角色\n  const sendGift = gift => {\n    if (partyCoins < gift.price) {\n      showCharacterMessage(\"主人的派对币不够了呢~\");\n      return;\n    }\n    setPartyCoins(prev => prev - gift.price);\n    setVitalityPoints(prev => Math.min(100, prev + gift.vitality)); // 最高100\n    showCharacterMessage(gift.message);\n\n    // 如果之前停止了投资，重新激活\n    if (!isInvestmentActive && vitalityPoints + gift.vitality > 0) {\n      setIsInvestmentActive(true);\n      showCharacterMessage(\"充满活力！我又可以继续为主人理财了！\");\n    }\n    closeBottomSheet();\n  };\n\n  // 下拉刷新\n  const handleRefresh = () => {\n    setRefreshing(true);\n    setTimeout(() => {\n      setRefreshing(false);\n      const newProfit = Math.floor(Math.random() * 500) - 100;\n      setTodayProfit(newProfit);\n      showCharacterMessage(\"数据已更新，主人看看我们今天的表现吧！\");\n    }, 1000);\n  };\n\n  // 新手引导弹窗\n  const IntroModal = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\",\n    onClick: () => setShowIntro(false),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mx-auto mb-4 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(Sparkles, {\n            className: \"w-10 h-10 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold mb-2\",\n          children: \"\\u661F\\u4F34\\u751F\\u8D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"\\u8BA9\", character.name, \"\\u5E2E\\u60A8\\u6253\\u7406\\u6D3E\\u5BF9\\u5E01\\uFF0C\\u4E00\\u8D77\\u521B\\u9020\\u8D22\\u5BCC\\uFF01\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-purple-600 font-bold\",\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: \"\\u667A\\u80FD\\u4EE3\\u7406\\u6295\\u8D44\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: [character.name, \"\\u4F1A\\u6839\\u636ETA\\u7684\\u6027\\u683C\\u7279\\u70B9\\uFF0C\\u5E2E\\u60A8\\u81EA\\u52A8\\u6295\\u8D44\\u7406\\u8D22\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-pink-600 font-bold\",\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: \"\\u60C5\\u611F\\u5316\\u4E92\\u52A8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"\\u6BCF\\u65E5\\u67E5\\u770B\\u6295\\u8D44\\u6C47\\u62A5\\uFF0C\\u7ED9TA\\u5EFA\\u8BAE\\uFF0C\\u4E00\\u8D77\\u7ECF\\u5386\\u8D22\\u5BCC\\u8D77\\u4F0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-600 font-bold\",\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: \"\\u6D3E\\u5BF9\\u5E01\\u589E\\u503C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"\\u6295\\u8D44\\u6536\\u76CA\\u53EF\\u968F\\u65F6\\u5151\\u6362\\u56DE\\u6D3E\\u5BF9\\u5E01\\uFF08\\u6536\\u53D65%\\u624B\\u7EED\\u8D39\\uFF09\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-yellow-50 rounded-2xl p-3 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-yellow-800\",\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            className: \"w-4 h-4 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), \"\\u521D\\u59CB10000\\u865A\\u62DF\\u8D44\\u91D1\\u4E3A\\u4F53\\u9A8C\\u91D1\\uFF0C\\u4E0D\\u53EF\\u63D0\\u53D6\\uFF0C\\u4EC5\\u6536\\u76CA\\u90E8\\u5206\\u53EF\\u5151\\u6362\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowIntro(false),\n        className: \"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold\",\n        children: \"\\u5F00\\u59CB\\u4F53\\u9A8C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 312,\n    columnNumber: 5\n  }, this);\n\n  // 功能说明弹窗\n  const InfoModal = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\",\n    onClick: () => setShowInfo(false),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up max-h-[80vh] overflow-y-auto\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-2\",\n          children: \"\\u529F\\u80FD\\u8BF4\\u660E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4 text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold mb-2 text-purple-600\",\n            children: \"\\uD83C\\uDFAF \\u600E\\u4E48\\u73A9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [\"1. \\u6295\\u5165\\u6D3E\\u5BF9\\u5E01\\u7ED9AI\\u89D2\\u8272\\u8FDB\\u884C\\u6295\\u8D44\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 32\n            }, this), \"2. AI\\u4F1A\\u6839\\u636E\\u5176\\u6027\\u683C\\u7279\\u70B9\\u81EA\\u52A8\\u64CD\\u4F5C\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 32\n            }, this), \"3. \\u67E5\\u770B\\u6BCF\\u65E5\\u6295\\u8D44\\u62A5\\u544A\\u4E86\\u89E3\\u6536\\u76CA\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 30\n            }, this), \"4. \\u7ED9AI\\u5EFA\\u8BAE\\u5F71\\u54CD\\u6295\\u8D44\\u7B56\\u7565\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 29\n            }, this), \"5. \\u9001\\u793C\\u7269\\u589E\\u52A0\\u7406\\u8D22\\u6D3B\\u529B\\u503C\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold mb-2 text-pink-600\",\n            children: \"\\uD83D\\uDCB0 \\u8D44\\u91D1\\u8BF4\\u660E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [\"\\u2022 \\u521D\\u59CB10000\\u5143\\u4E3A\\u4F53\\u9A8C\\u91D1\\uFF0C\\u4E0D\\u53EF\\u63D0\\u53D6\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 34\n            }, this), \"\\u2022 \\u60A8\\u6295\\u5165\\u7684\\u672C\\u91D1\\u53EF\\u968F\\u65F6\\u63D0\\u53D6\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 28\n            }, this), \"\\u2022 \\u6295\\u8D44\\u6536\\u76CA\\u53EF\\u968F\\u65F6\\u63D0\\u53D6\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 26\n            }, this), \"\\u2022 \\u63D0\\u53D6\\u65F6\\u6536\\u53D65%\\u624B\\u7EED\\u8D39\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 27\n            }, this), \"\\u2022 1\\u6D3E\\u5BF9\\u5E01 = 100\\u865A\\u62DF\\u8D44\\u91D1\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold mb-2 text-green-600\",\n            children: \"\\u26A1 \\u7406\\u8D22\\u6D3B\\u529B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [\"\\u2022 AI\\u9700\\u8981\\u7406\\u8D22\\u6D3B\\u529B\\u624D\\u80FD\\u6301\\u7EED\\u6295\\u8D44\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 31\n            }, this), \"\\u2022 \\u6D3B\\u529B\\u503C\\u6BCF\\u5C0F\\u65F6\\u6D88\\u80172\\u70B9\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 27\n            }, this), \"\\u2022 \\u6D3B\\u529B\\u503C\\u4E3A0\\u65F6\\u81EA\\u52A8\\u7ED3\\u7B97\\u8D44\\u4EA7\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 29\n            }, this), \"\\u2022 \\u9001\\u793C\\u7269\\u53EF\\u4EE5\\u589E\\u52A0\\u6D3B\\u529B\\u503C\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 27\n            }, this), \"\\u2022 \\u6BCF\\u65E5\\u67E5\\u770B\\u661F\\u4F34\\u751F\\u8D22\\u5C31\\u52A0\\u6D3B\\u529B\\u503C\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 30\n            }, this), \"\\u2022 \\u64CD\\u4F5C\\u8D44\\u4EA7\\u6295\\u53D6\\u4E5F\\u52A0\\u6D3B\\u529B\\u503C\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 28\n            }, this), \"\\u2022 \\u4E0E\\u865A\\u62DF\\u89D2\\u8272\\u8FDB\\u884C\\u6295\\u8D44\\u4E92\\u52A8\\u4E5F\\u52A0\\u6D3B\\u529B\\u503C\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 33\n            }, this), \"\\u2022 \\u521D\\u59CB\\u6D3B\\u529B\\u503C\\u4E3A100\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold mb-2 text-orange-600\",\n            children: \"\\u26A0\\uFE0F \\u6CE8\\u610F\\u4E8B\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [\"\\u2022 \\u8FD9\\u662F\\u865A\\u62DF\\u6295\\u8D44\\u6E38\\u620F\\uFF0C\\u975E\\u771F\\u5B9E\\u6295\\u8D44\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 31\n            }, this), \"\\u2022 \\u6295\\u8D44\\u6709\\u98CE\\u9669\\uFF0C\\u53EF\\u80FD\\u4EA7\\u751F\\u4E8F\\u635F\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 29\n            }, this), \"\\u2022 \\u5408\\u7406\\u5B89\\u6392\\u6D3E\\u5BF9\\u5E01\\uFF0C\\u4EAB\\u53D7\\u6E38\\u620F\\u4E50\\u8DA3\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 31\n            }, this), \"\\u2022 \\u591A\\u4E0EAI\\u4E92\\u52A8\\u63D0\\u5347\\u6295\\u8D44\\u6548\\u679C\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowInfo(false),\n        className: \"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold mt-6\",\n        children: \"\\u6211\\u77E5\\u9053\\u4E86\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 373,\n    columnNumber: 5\n  }, this);\n\n  // 底部弹出框\n  const BottomSheet = () => {\n    const sheetContent = () => {\n      switch (bottomSheetType) {\n        case 'exchange-in':\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-4\",\n              children: \"\\u6295\\u5165\\u6D3E\\u5BF9\\u5E01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-2xl p-4 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"\\u5F53\\u524D\\u6D3E\\u5BF9\\u5E01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: partyCoins\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: exchangeAmount,\n              onChange: e => setExchangeAmount(e.target.value),\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u6295\\u5165\\u6570\\u91CF\",\n              className: \"w-full px-4 py-3 border border-gray-200 rounded-2xl mb-3 text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 mb-4\",\n              children: \"1\\u6D3E\\u5BF9\\u5E01 = 100\\u865A\\u62DF\\u8D44\\u91D1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-4 gap-2 mb-6\",\n              children: [10, 50, 100, 200].map(amount => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setExchangeAmount(amount.toString()),\n                className: \"py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200\",\n                children: amount\n              }, amount, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleExchange(true),\n              className: \"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold\",\n              children: \"\\u786E\\u8BA4\\u6295\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true);\n        case 'exchange-out':\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-4\",\n              children: \"\\u63D0\\u53D6\\u8D44\\u4EA7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-2xl p-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"\\u4F53\\u9A8C\\u91D1\\uFF08\\u4E0D\\u53EF\\u63D0\\u53D6\\uFF09\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-400\",\n                  children: [\"\\xA5\", experienceFund]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"\\u6295\\u5165\\u672C\\u91D1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: [\"\\xA5\", principalFund]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"\\u6295\\u8D44\\u6536\\u76CA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-red-500\",\n                  children: [\"\\xA5\", totalProfit]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-2 mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-800 font-medium\",\n                    children: \"\\u53EF\\u63D0\\u53D6\\u603B\\u989D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-lg\",\n                    children: [\"\\xA5\", principalFund + totalProfit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u53EF\\u5151\\u6362\\u6D3E\\u5BF9\\u5E01\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: Math.floor((principalFund + totalProfit) / 100)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: exchangeAmount,\n              onChange: e => setExchangeAmount(e.target.value),\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u63D0\\u53D6\\u7684\\u6D3E\\u5BF9\\u5E01\\u6570\\u91CF\",\n              className: \"w-full px-4 py-3 border border-gray-200 rounded-2xl mb-3 text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 mb-4\",\n              children: \"\\u5C06\\u6536\\u53D65%\\u624B\\u7EED\\u8D39\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-2 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setExchangeAmount(Math.floor(totalProfit / 100).toString()),\n                className: \"py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200\",\n                disabled: totalProfit <= 0,\n                children: \"\\u4EC5\\u6536\\u76CA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setExchangeAmount(Math.floor(principalFund / 100).toString()),\n                className: \"py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200\",\n                disabled: principalFund <= 0,\n                children: \"\\u4EC5\\u672C\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setExchangeAmount(Math.floor((principalFund + totalProfit) / 100).toString()),\n                className: \"py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200\",\n                disabled: principalFund + totalProfit <= 0,\n                children: \"\\u5168\\u90E8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleExchange(false),\n              className: \"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold\",\n              children: \"\\u786E\\u8BA4\\u63D0\\u53D6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true);\n        case 'compass':\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-bold\",\n                children: \"\\u661F\\u8BED\\u7F57\\u76D8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: new Date().toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Target, {\n                  className: \"w-5 h-5 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-purple-900\",\n                    children: \"\\u4ECA\\u65E5\\u5927\\u76D8\\u6C14\\u8C61\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      // 跳转到大盘数据详情页面的逻辑\n                      console.log('跳转到大盘数据详情页面');\n                    },\n                    className: \"flex items-center gap-1 text-xs text-purple-600 bg-purple-100 px-2 py-0.5 rounded-full hover:bg-purple-200 active:scale-95 transition-all\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u8BE6\\u60C5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(ChevronRight, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(Sparkles, {\n                      className: \"w-5 h-5 text-purple-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-700 leading-relaxed\",\n                      children: \"\\\"\\u4ECA\\u5929\\u7684\\u5E02\\u573A\\u5C31\\u50CF\\u6625\\u5929\\u7684\\u82B1\\u56ED\\uFF0C\\u5904\\u5904\\u5145\\u6EE1\\u751F\\u673A\\uFF01\\u79D1\\u6280\\u677F\\u5757\\u5982\\u540C\\u5411\\u65E5\\u8475\\u822C\\u6602\\u9996\\u5411\\u9633\\uFF0C\\u65B0\\u80FD\\u6E90\\u50CF\\u6E05\\u6668\\u7684\\u9732\\u73E0\\u95EA\\u95EA\\u53D1\\u5149\\u3002\\u4E0D\\u8FC7\\u6D88\\u8D39\\u677F\\u5757\\u6709\\u70B9\\u50CF\\u5348\\u540E\\u6175\\u61D2\\u7684\\u732B\\u54AA\\uFF0C\\u9700\\u8981\\u65F6\\u95F4\\u6162\\u6162\\u82CF\\u9192\\u5462~\\\"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-3 gap-2 mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white/60 backdrop-blur rounded-xl p-2 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"\\u5E02\\u573A\\u60C5\\u7EEA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-bold text-green-600\",\n                      children: \"\\u4E50\\u89C2\\uD83D\\uDE0A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white/60 backdrop-blur rounded-xl p-2 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"\\u70ED\\u95E8\\u677F\\u5757\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-bold text-purple-600\",\n                      children: \"\\u79D1\\u6280\\uD83D\\uDE80\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white/60 backdrop-blur rounded-xl p-2 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"\\u98CE\\u9669\\u7B49\\u7EA7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 598,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-bold text-yellow-600\",\n                      children: \"\\u9002\\u4E2D\\u26A1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: character.avatar,\n                  alt: \"\",\n                  className: \"w-12 h-12 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium mb-1\",\n                    children: [character.name, \"\\u7684\\u6295\\u8D44\\u6C47\\u62A5\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: isInvestmentActive ? \"主人！跟随大盘的春风，我们今天收获满满！科技未来像火箭一样涨了5.8%，我觉得自己就像个小园丁，看着财富之花慢慢绽放~虽然消费龙头有点调皮，但我相信它只是在积蓄力量！\" : \"主人，我现在像一只需要充电的小机器人，暂时停下来休息了。如果您想让我继续在市场的花园里采蜜，可以送我一些能量礼物哦~\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-3 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-red-50 rounded-xl p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: \"\\u4ECA\\u65E5\\u6536\\u76CA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-red-600\",\n                  children: [\"+\\xA5\", todayProfit]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 rounded-xl p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: \"\\u4ECA\\u65E5\\u6536\\u76CA\\u7387\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-blue-600\",\n                  children: \"+1.26%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n                  className: \"w-4 h-4 text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this), \"\\u4ECA\\u65E5\\u64CD\\u4F5C\\u8F68\\u8FF9\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-xl p-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: \"\\u4E70\\u5165 \\u79D1\\u6280\\u672A\\u6765\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"10:30 \\u987A\\u52BF\\u800C\\u4E3A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 643,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TrendingUp, {\n                    className: \"w-5 h-5 text-red-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-xl p-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: \"\\u5356\\u51FA \\u4F20\\u7EDF\\u5236\\u9020\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 651,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"14:20 \\u83B7\\u5229\\u4E86\\u7ED3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TrendingDown, {\n                    className: \"w-5 h-5 text-green-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-50 rounded-xl p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium mb-2 flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Star, {\n                  className: \"w-4 h-4 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 21\n                }, this), \"\\u660E\\u65E5\\u5C55\\u671B\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"\\\"\\u660E\\u5929\\u6211\\u60F3\\u50CF\\u871C\\u8702\\u4E00\\u6837\\uFF0C\\u5728\\u65B0\\u80FD\\u6E90\\u7684\\u82B1\\u4E1B\\u4E2D\\u591A\\u91C7\\u4E9B\\u871C\\uFF01\\u542C\\u8BF4\\u6709\\u653F\\u7B56\\u6625\\u98CE\\u8981\\u6765\\uFF0C\\u6211\\u5DF2\\u7ECF\\u51C6\\u5907\\u597D\\u5C0F\\u7BEE\\u5B50\\u4E86\\u3002\\u79D1\\u6280\\u80A1\\u6DA8\\u5F97\\u592A\\u5FEB\\uFF0C\\u50CF\\u4E2A\\u8C03\\u76AE\\u7684\\u5B69\\u5B50\\uFF0C\\u6211\\u4F1A\\u627E\\u673A\\u4F1A\\u8BA9\\u5B83\\u4F11\\u606F\\u4E00\\u4E0B~\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true);\n        case 'advice':\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-4\",\n              children: [\"\\u7ED9\", character.name, \"\\u4E00\\u4E9B\\u5EFA\\u8BAE\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 15\n            }, this), lastAdviceOption && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-50 rounded-2xl p-3 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-purple-600 mb-1\",\n                children: \"\\u4E0A\\u6B21\\u5EFA\\u8BAE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-purple-900\",\n                    children: lastAdviceOption.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 683,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-purple-700\",\n                    children: adviceCategories[lastAdviceCategory].title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl\",\n                  children: adviceCategories[lastAdviceCategory].icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 mb-4\",\n              children: Object.entries(adviceCategories).map(([key, category]) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentAdviceCategory(key),\n                className: `flex-1 py-2 px-3 rounded-xl text-sm font-medium transition-colors ${currentAdviceCategory === key ? 'bg-purple-500 text-white' : 'bg-gray-100 text-gray-700'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: category.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 21\n                }, this), category.title]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: adviceCategories[currentAdviceCategory].options.map(option => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => giveAdvice(currentAdviceCategory, option),\n                className: `w-full p-4 rounded-2xl text-left transition-all ${(lastAdviceOption === null || lastAdviceOption === void 0 ? void 0 : lastAdviceOption.id) === option.id && lastAdviceCategory === currentAdviceCategory ? 'bg-purple-100 border-2 border-purple-300' : 'bg-gray-50 border-2 border-transparent hover:border-purple-200'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-900\",\n                  children: option.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: option.desc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 21\n                }, this), (lastAdviceOption === null || lastAdviceOption === void 0 ? void 0 : lastAdviceOption.id) === option.id && lastAdviceCategory === currentAdviceCategory && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-purple-600 mt-1\",\n                  children: \"\\u2713 \\u5F53\\u524D\\u5EFA\\u8BAE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 23\n                }, this)]\n              }, option.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: [\"\\u5EFA\\u8BAE\\u4F1A\\u5F71\\u54CD\", character.name, \"\\u7684\\u6295\\u8D44\\u7B56\\u7565\\u54E6~\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true);\n        case 'reward':\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-6\",\n              children: [\"\\u9001\\u793C\\u7269\\u7ED9\", character.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-2xl p-4 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"\\u5F53\\u524D\\u6D3E\\u5BF9\\u5E01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Coins, {\n                    className: \"w-4 h-4 text-yellow-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold\",\n                    children: partyCoins\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-3\",\n              children: giftList.map(gift => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => sendGift(gift),\n                className: \"bg-white border border-gray-200 rounded-2xl p-4 text-center active:scale-95 transition-transform hover:border-purple-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-3xl mb-2 block\",\n                  children: gift.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-sm\",\n                  children: gift.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 flex items-center justify-center gap-1 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Coins, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 23\n                  }, this), gift.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-green-500 mt-1\",\n                  children: [\"+\", gift.vitality, \"\\u6D3B\\u529B\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 21\n                }, this)]\n              }, gift.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"\\u9001\\u51FA\\u793C\\u7269\\u4F1A\\u589E\\u52A0\\u7406\\u8D22\\u6D3B\\u529B\\u503C\\uFF0C\\u8BA9\", character.name, \"\\u66F4\\u6709\\u52A8\\u529B\\uFF01\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true);\n        default:\n          return null;\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${showBottomSheet ? 'opacity-100 visible' : 'opacity-0 invisible'}`,\n      onClick: closeBottomSheet,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-end justify-center h-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-full max-w-[390px] bg-white rounded-t-3xl p-6 transition-transform duration-300 ${showBottomSheet ? 'translate-y-0' : 'translate-y-full'}`,\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-h-[70vh] overflow-y-auto\",\n            children: sheetContent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 778,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 主界面\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-50 overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full max-w-[390px] h-full mx-auto bg-white shadow-xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed top-0 left-0 right-0 max-w-[390px] mx-auto bg-white z-30 border-b border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between px-4 py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-2 -ml-2\",\n            children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"font-semibold text-lg\",\n            children: \"\\u661F\\u4F34\\u751F\\u8D22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 relative\",\n              onClick: () => {/* 跳转到排行榜 */},\n              children: [/*#__PURE__*/_jsxDEV(Trophy, {\n                className: \"w-5 h-5 text-purple-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 -mr-2\",\n              onClick: () => setShowInfo(true),\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                className: \"w-5 h-5 text-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-16 pb-20 h-full overflow-y-auto\",\n        onScroll: e => {\n          if (e.target.scrollTop === 0 && !refreshing) {\n            handleRefresh();\n          }\n        },\n        children: [refreshing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            className: \"w-5 h-5 text-purple-500 animate-spin mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 831,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: \"\\u66F4\\u65B0\\u6570\\u636E\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-purple-100 to-pink-100 rounded-3xl p-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: character.avatar,\n                  alt: character.name,\n                  className: \"w-14 h-14 rounded-full border-2 border-white shadow-md object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-lg\",\n                    children: character.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 843,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: character.investmentStyle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-1 mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Trophy, {\n                      className: \"w-3 h-3 text-yellow-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 847,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-yellow-600 font-medium\",\n                      children: \"\\u6392\\u884C\\u699C\\u7B2C12\\u540D\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                onClick: () => setShowVitalityTips(true),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1 mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl\",\n                    children: getVitalityIcon()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `font-bold text-lg ${getVitalityColor()}`,\n                    children: vitalityPoints\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Info, {\n                    className: \"w-3 h-3 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 19\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600\",\n                  children: \"\\u7406\\u8D22\\u6D3B\\u529B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/40 backdrop-blur rounded-xl px-3 py-2 mb-3 flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Coins, {\n                  className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: \"\\u5DF2\\u6295\\u6D3E\\u5BF9\\u5E01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-800\",\n                children: partyCoins\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/60 backdrop-blur rounded-2xl p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: \"\\u603B\\u8D44\\u4EA7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold\",\n                  children: [\"\\xA5\", virtualFund.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm ${totalProfit >= 0 ? 'text-red-600' : 'text-green-600'}`,\n                  children: [totalProfit >= 0 ? '+' : '', totalProfit, \" (\", profitRate, \"%)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white/60 backdrop-blur rounded-2xl p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: \"\\u603B\\u6536\\u76CA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xl font-bold ${todayProfit >= 0 ? 'text-red-600' : 'text-green-600'}`,\n                  children: [todayProfit >= 0 ? '+' : '', \"\\xA5\", todayProfit]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 882,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"1.26%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 15\n            }, this), vitalityPoints < 20 && vitalityPoints > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 bg-yellow-50 rounded-xl p-2 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-yellow-800\",\n                children: \"\\u6D3B\\u529B\\u503C\\u8F83\\u4F4E\\uFF0C\\u9001\\u4E2A\\u793C\\u7269\\u9F13\\u52B1\\u4E00\\u4E0B\\u5427~\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 17\n            }, this), vitalityPoints === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 bg-red-50 rounded-xl p-2 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-red-800\",\n                children: \"\\u6D3B\\u529B\\u8017\\u5C3D\\uFF0C\\u6295\\u8D44\\u5DF2\\u6682\\u505C\\uFF0C\\u9001\\u793C\\u7269\\u53EF\\u6062\\u590D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => openBottomSheet('exchange-in'),\n              className: \"bg-white rounded-2xl p-4 shadow-sm active:scale-95 transition-transform\",\n              children: [/*#__PURE__*/_jsxDEV(ArrowUp, {\n                className: \"w-6 h-6 text-purple-500 mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium\",\n                children: \"\\u6295\\u5165\\u8D44\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: \"\\u589E\\u52A0\\u672C\\u91D1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 912,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => openBottomSheet('exchange-out'),\n              className: \"bg-white rounded-2xl p-4 shadow-sm active:scale-95 transition-transform\",\n              children: [/*#__PURE__*/_jsxDEV(Wallet, {\n                className: \"w-6 h-6 text-green-500 mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium\",\n                children: \"\\u63D0\\u53D6\\u8D44\\u4EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: [\"\\u53EF\\u63D0\\xA5\", principalFund + totalProfit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 914,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 905,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 904,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-100\",\n              children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n                  className: \"w-5 h-5 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 19\n                }, this), \"\\u5F53\\u524D\\u6301\\u4ED3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divide-y divide-gray-100\",\n              children: holdings.map((stock, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 active:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: stock.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 939,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: stock.code\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 940,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 938,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `font-semibold ${stock.profit >= 0 ? 'text-red-600' : 'text-green-600'}`,\n                      children: [stock.profit >= 0 ? '+' : '', \"\\xA5\", stock.profit]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 943,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-sm ${stock.profitRate >= 0 ? 'text-red-600' : 'text-green-600'}`,\n                      children: [stock.profitRate >= 0 ? '+' : '', stock.profitRate, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 946,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [stock.shares, \"\\u80A1\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"\\xA5\", stock.price, \"/\\u80A1\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 953,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 max-w-[390px] mx-auto bg-white border-t border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-3 py-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => openBottomSheet('compass') // 改为 compass\n            ,\n            className: \"flex flex-col items-center gap-1 py-2 active:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(Target, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 13\n            }, this), \"  \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-600\",\n              children: \"\\u661F\\u8BED\\u7F57\\u76D8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => openBottomSheet('advice'),\n            className: \"flex flex-col items-center gap-1 py-2 active:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n              className: \"w-5 h-5 text-purple-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-purple-500\",\n              children: \"\\u7ED9\\u5EFA\\u8BAE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 972,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => openBottomSheet('reward'),\n            className: \"flex flex-col items-center gap-1 py-2 active:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(Gift, {\n              className: \"w-5 h-5 text-pink-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-pink-500\",\n              children: \"\\u5956\\u52B1\\u4E00\\u4E0B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 979,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 964,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 963,\n        columnNumber: 9\n      }, this), showIntro && /*#__PURE__*/_jsxDEV(IntroModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 990,\n        columnNumber: 23\n      }, this), showInfo && /*#__PURE__*/_jsxDEV(InfoModal, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 993,\n        columnNumber: 22\n      }, this), /*#__PURE__*/_jsxDEV(BottomSheet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 996,\n        columnNumber: 9\n      }, this), showMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed top-20 left-4 right-4 max-w-[358px] mx-auto bg-white rounded-2xl shadow-lg p-4 z-50 animate-slide-down\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: character.avatar,\n            alt: \"\",\n            className: \"w-10 h-10 rounded-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1002,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm flex-1\",\n            children: messageContent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1000,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes scale-up {\n          from {\n            transform: scale(0.9);\n            opacity: 0;\n          }\n          to {\n            transform: scale(1);\n            opacity: 1;\n          }\n        }\n        \n        @keyframes slide-down {\n          from {\n            transform: translateY(-20px);\n            opacity: 0;\n          }\n          to {\n            transform: translateY(0);\n            opacity: 1;\n          }\n        }\n        \n        .animate-scale-up {\n          animation: scale-up 0.3s ease-out;\n        }\n        \n        .animate-slide-down {\n          animation: slide-down 0.3s ease-out;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1009,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 798,\n    columnNumber: 5\n  }, this);\n};\n_s(AICharacterInvestmentPage, \"HK+N14t2LNLXJ4MA6O4Vy59nUhk=\");\n_c = AICharacterInvestmentPage;\nexport default AICharacterInvestmentPage;\nvar _c;\n$RefreshReg$(_c, \"AICharacterInvestmentPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ChevronRight", "TrendingUp", "TrendingDown", "<PERSON><PERSON><PERSON>", "Heart", "MessageCircle", "Gift", "AlertCircle", "ChevronLeft", "Coins", "Trophy", "Calendar", "RefreshCw", "Target", "Zap", "Star", "X", "Info", "ArrowUp", "BarChart3", "Wallet", "Battery", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AICharacterInvestmentPage", "_s", "showIntro", "setShowIntro", "showInfo", "setShowInfo", "partyCoins", "setPartyCoins", "virtualFund", "setVirtualFund", "experienceFund", "principalFund", "setPrincipalFund", "totalProfit", "setTotalProfit", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>Mood", "showBottomSheet", "setShowBottomSheet", "bottomSheetType", "setBottomSheetType", "showMessage", "setShowMessage", "messageContent", "setMessageContent", "exchangeAmount", "setExchangeAmount", "todayProfit", "setTodayProfit", "profitRate", "setProfitRate", "refreshing", "setRefreshing", "lastDailyCheckIn", "setLastDailyCheckIn", "lastInteractionTime", "setLastInteractionTime", "dailyCheckInDone", "setDailyCheckInDone", "showVitalityTips", "setShowVitalityTips", "lastAdviceCategory", "setLastAdviceCategory", "lastAdviceOption", "setLastAdviceOption", "currentAdviceCategory", "setCurrentAdviceCategory", "vitalityPoints", "setVitalityPoints", "lastVitalityUpdate", "setLastVitalityUpdate", "Date", "now", "isInvestmentActive", "setIsInvestmentActive", "holdings", "setHoldings", "name", "code", "shares", "profit", "price", "character", "style", "description", "avatar", "investmentStyle", "giftList", "id", "icon", "vitality", "message", "adviceCategories", "risk", "title", "options", "desc", "timing", "focus", "timer", "setInterval", "hoursPassed", "vitalityConsumed", "Math", "floor", "prev", "newVitality", "max", "random", "hints", "showCharacterMessage", "length", "handleAutoSettle", "clearInterval", "checkDailyBonus", "today", "toDateString", "<PERSON><PERSON><PERSON><PERSON>", "min", "totalWithdrawable", "convertedCoins", "getVitalityColor", "getVitalityIcon", "setTimeout", "openBottomSheet", "type", "closeBottomSheet", "handleExchange", "isIn", "amount", "parseInt", "maxExchange", "withdrawAmount", "fee", "fromProfit", "fromPrincipal", "giveAdvice", "category", "option", "lastAdvice", "advice", "timeSinceLastAdvice", "generateResponse", "responses", "conservative", "moderate", "aggressive", "quick", "patient", "steady", "value", "growth", "trend", "remainingTime", "ceil", "sendGift", "gift", "handleRefresh", "newProfit", "IntroModal", "className", "onClick", "children", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "InfoModal", "BottomSheet", "sheetContent", "onChange", "target", "placeholder", "map", "toString", "disabled", "toLocaleDateString", "console", "log", "src", "alt", "Object", "entries", "key", "onScroll", "scrollTop", "toLocaleString", "stock", "index", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/角色升级计划/code/character-creator-view/src/AICharacterInvestmentPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ChevronRight, TrendingUp, TrendingDown, Sparkles, Heart, MessageCircle, Gift, AlertCircle, ChevronLeft, Coins, Trophy, Calendar, RefreshCw, Target, Zap, Star, X, Info, ArrowUp, BarChart3, Wallet, Battery } from 'lucide-react';\r\n\r\n// 导入头像图片\r\nimport characterAvatar from './assets/沈婉.png';\r\n\r\nconst AICharacterInvestmentPage = () => {\r\n  // 状态管理\r\n  const [showIntro, setShowIntro] = useState(true);\r\n  const [showInfo, setShowInfo] = useState(false);\r\n  const [partyCoins, setPartyCoins] = useState(5280);\r\n  const [virtualFund, setVirtualFund] = useState(12580); \r\n  const [experienceFund] = useState(10000); \r\n  const [principalFund, setPrincipalFund] = useState(0); \r\n  const [totalProfit, setTotalProfit] = useState(2580); \r\n  const [characterMood, setCharacterMood] = useState('happy');\r\n  const [showBottomSheet, setShowBottomSheet] = useState(false);\r\n  const [bottomSheetType, setBottomSheetType] = useState('');\r\n  const [showMessage, setShowMessage] = useState(false);\r\n  const [messageContent, setMessageContent] = useState('');\r\n  const [exchangeAmount, setExchangeAmount] = useState('');\r\n  const [todayProfit, setTodayProfit] = useState(158);\r\n  const [profitRate, setProfitRate] = useState(25.8);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [lastDailyCheckIn, setLastDailyCheckIn] = useState(null); \r\n  const [lastInteractionTime, setLastInteractionTime] = useState({}); \r\n  const [dailyCheckInDone, setDailyCheckInDone] = useState(false); \r\n  const [showVitalityTips, setShowVitalityTips] = useState(false);\r\n  const [lastAdviceCategory, setLastAdviceCategory] = useState(null); \r\n  const [lastAdviceOption, setLastAdviceOption] = useState(null);\r\n  const [currentAdviceCategory, setCurrentAdviceCategory] = useState('risk');\r\n\r\n  \r\n  // 理财活力相关状态\r\n  const [vitalityPoints, setVitalityPoints] = useState(80); // 理财活力值\r\n  const [lastVitalityUpdate, setLastVitalityUpdate] = useState(Date.now());\r\n  const [isInvestmentActive, setIsInvestmentActive] = useState(true);\r\n  \r\n  // 持仓信息\r\n  const [holdings, setHoldings] = useState([\r\n    { name: '科技未来', code: '000001', shares: 200, profit: 580, profitRate: 5.8, price: 28.5 },\r\n    { name: '消费龙头', code: '000002', shares: 300, profit: -120, profitRate: -2.1, price: 45.2 },\r\n    { name: '新能源车', code: '000003', shares: 100, profit: 320, profitRate: 8.2, price: 156.8 }\r\n  ]);\r\n  \r\n  // 角色信息\r\n  const character = {\r\n    name: '沈婉',\r\n    style: '稳健型',\r\n    description: '温柔体贴，偏好稳定增长',\r\n    avatar: characterAvatar,\r\n    investmentStyle: '稳健保守' // 投资风格\r\n  };\r\n\r\n  // 礼物列表（增加活力值）\r\n  const giftList = [\r\n    { id: 1, name: '鲜花', icon: '🌹', price: 10, vitality: 5, message: '谢谢主人的鲜花！我会更努力的！' },\r\n    { id: 2, name: '咖啡', icon: '☕', price: 20, vitality: 10, message: '咖啡的香味让我精神百倍！' },\r\n    { id: 3, name: '蛋糕', icon: '🎂', price: 50, vitality: 20, message: '哇！蛋糕好甜，就像主人一样！' },\r\n    { id: 4, name: '钻戒', icon: '💍', price: 100, vitality: 40, message: '这...这是给我的吗？我会珍藏一生的！' },\r\n    { id: 5, name: '游艇', icon: '🛥️', price: 200, vitality: 80, message: '天哪！主人太大方了！我们一起去看海吧！' },\r\n    { id: 6, name: '城堡', icon: '🏰', price: 500, vitality: 200, message: '我...我不知道说什么好了，谢谢主人！❤️' }\r\n  ];\r\n\r\n  // 建议分类配置\r\nconst adviceCategories = {\r\n  risk: {\r\n    title: '风险偏好',\r\n    icon: '🛡️',\r\n    options: [\r\n      { id: 'conservative', title: '保守稳健', desc: '安全第一' },\r\n      { id: 'moderate', title: '稳中求进', desc: '平衡配置' },\r\n      { id: 'aggressive', title: '激进冒险', desc: '高风险高收益' }\r\n    ]\r\n  },\r\n  timing: {\r\n    title: '操作节奏',\r\n    icon: '⏱️',\r\n    options: [\r\n      { id: 'quick', title: '快进快出', desc: '把握短期机会' },\r\n      { id: 'patient', title: '耐心等待', desc: '寻找最佳时机' },\r\n      { id: 'steady', title: '稳定节奏', desc: '按计划执行' }\r\n    ]\r\n  },\r\n  focus: {\r\n    title: '关注重点',\r\n    icon: '🎯',\r\n    options: [\r\n      { id: 'value', title: '价值为王', desc: '寻找低估资产' },\r\n      { id: 'growth', title: '成长优先', desc: '追求高增长' },\r\n      { id: 'trend', title: '趋势跟随', desc: '顺势而为' }\r\n    ]\r\n  }\r\n};\r\n\r\n  // 活力值消耗（每小时消耗2点）\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      const now = Date.now();\r\n      const hoursPassed = (now - lastVitalityUpdate) / (1000 * 60 * 60);\r\n      const vitalityConsumed = Math.floor(hoursPassed * 2);\r\n      \r\n      if (vitalityConsumed > 0) {\r\n        setVitalityPoints(prev => {\r\n          const newVitality = Math.max(0, prev - vitalityConsumed);\r\n          \r\n          // 活力值低于30时，偶尔提醒\r\n          if (newVitality < 30 && newVitality > 0 && Math.random() < 0.3) {\r\n            const hints = [\r\n              \"主人，我感觉有点累了呢...\",\r\n              \"最近工作有点辛苦，需要补充能量了~\",\r\n              \"如果能喝杯咖啡就好了...\"\r\n            ];\r\n            showCharacterMessage(hints[Math.floor(Math.random() * hints.length)]);\r\n          }\r\n          \r\n          // 活力值为0，停止投资\r\n          if (newVitality === 0 && isInvestmentActive) {\r\n            setIsInvestmentActive(false);\r\n            showCharacterMessage(\"我太累了，需要休息一下...投资暂时停止了\");\r\n            // 自动结算\r\n            handleAutoSettle();\r\n          }\r\n          \r\n          return newVitality;\r\n        });\r\n        setLastVitalityUpdate(now);\r\n      }\r\n    }, 60000); // 每分钟检查一次\r\n\r\n    return () => clearInterval(timer);\r\n  }, [lastVitalityUpdate, isInvestmentActive]);\r\n\r\n  // 在组件加载时检查是否需要每日签到奖励\r\nuseEffect(() => {\r\n  const checkDailyBonus = () => {\r\n    const today = new Date().toDateString();\r\n    const lastCheck = lastDailyCheckIn ? new Date(lastDailyCheckIn).toDateString() : null;\r\n    \r\n    if (lastCheck !== today) {\r\n      // 每日首次查看，增加活力值\r\n      setVitalityPoints(prev => Math.min(100, prev + 10)); // 每日查看+10活力\r\n      setLastDailyCheckIn(Date.now());\r\n      setDailyCheckInDone(true);\r\n      showCharacterMessage(\"早上好主人！感谢您每天来看我，活力值+10！💕\");\r\n    }\r\n  };\r\n  \r\n  checkDailyBonus();\r\n}, []);\r\n\r\n  // 自动结算\r\n  const handleAutoSettle = () => {\r\n    const totalWithdrawable = principalFund + totalProfit;\r\n    const convertedCoins = Math.floor(totalWithdrawable / 100 * 0.95); // 扣除5%手续费\r\n    setPartyCoins(prev => prev + convertedCoins);\r\n    setPrincipalFund(0);\r\n    setTotalProfit(0);\r\n    setVirtualFund(experienceFund);\r\n  };\r\n\r\n  // 获取活力值颜色\r\n  const getVitalityColor = () => {\r\n    if (vitalityPoints > 60) return 'text-green-500';\r\n    if (vitalityPoints > 30) return 'text-yellow-500';\r\n    return 'text-red-500';\r\n  };\r\n\r\n  // 获取活力值图标\r\n  const getVitalityIcon = () => {\r\n    if (vitalityPoints > 60) return '⚡';\r\n    if (vitalityPoints > 30) return '🔋';\r\n    return '🪫';\r\n  };\r\n\r\n  // 显示角色消息\r\n  const showCharacterMessage = (message) => {\r\n    setMessageContent(message);\r\n    setShowMessage(true);\r\n    setTimeout(() => setShowMessage(false), 3000);\r\n  };\r\n\r\n  // 打开底部弹窗\r\n  const openBottomSheet = (type) => {\r\n    setBottomSheetType(type);\r\n    setShowBottomSheet(true);\r\n  };\r\n\r\n  // 关闭底部弹窗\r\n  const closeBottomSheet = () => {\r\n    setShowBottomSheet(false);\r\n    setExchangeAmount('');\r\n  };\r\n\r\n  // 处理兑换\r\n  const handleExchange = (isIn) => {\r\n    const amount = parseInt(exchangeAmount);\r\n    if (!amount || amount <= 0) {\r\n      showCharacterMessage(\"请输入正确的数量哦~\");\r\n      return;\r\n    }\r\n    \r\n    if (isIn) {\r\n      // 投入资金\r\n      if (amount > partyCoins) {\r\n        showCharacterMessage(\"主人的派对币不够哦~\");\r\n        return;\r\n      }\r\n      setPartyCoins(prev => prev - amount);\r\n      setPrincipalFund(prev => prev + amount * 100); // 记录本金\r\n      setVirtualFund(prev => prev + amount * 100);\r\n      showCharacterMessage(`谢谢主人的信任！这${amount * 100}元就是我们的梦想启动金！`);\r\n    } else {\r\n      // 提取资产\r\n      const totalWithdrawable = principalFund + totalProfit; // 可提取总额\r\n      const maxExchange = Math.floor(totalWithdrawable / 100);\r\n      \r\n      if (amount > maxExchange) {\r\n        showCharacterMessage(`主人，目前只能提取${maxExchange}个派对币哦~`);\r\n        return;\r\n      }\r\n      \r\n      const withdrawAmount = amount * 100;\r\n      const fee = Math.floor(amount * 0.05);\r\n      \r\n      // 优先从收益中扣除，不足再从本金扣\r\n      if (withdrawAmount <= totalProfit) {\r\n        setTotalProfit(prev => prev - withdrawAmount);\r\n      } else {\r\n        const fromProfit = totalProfit;\r\n        const fromPrincipal = withdrawAmount - fromProfit;\r\n        setTotalProfit(0);\r\n        setPrincipalFund(prev => prev - fromPrincipal);\r\n      }\r\n      \r\n      setPartyCoins(prev => prev + amount - fee);\r\n      setVirtualFund(prev => prev - withdrawAmount);\r\n      showCharacterMessage(`成功提取${amount - fee}个派对币！手续费${fee}个`);\r\n    }\r\n    closeBottomSheet();\r\n  };\r\n\r\n  // 给角色建议\r\n  const giveAdvice = (category, option) => {\r\n  const lastAdvice = lastInteractionTime.advice || 0;\r\n  const timeSinceLastAdvice = Date.now() - lastAdvice;\r\n  \r\n  // 生成动态回复\r\n  const generateResponse = () => {\r\n    const responses = {\r\n      conservative: \"明白了主人，我会更加谨慎，保护好我们的资产！\",\r\n      moderate: \"好的！我会平衡风险和收益，稳步前进！\",\r\n      aggressive: \"收到！让我们大胆一些，追求更高收益！\",\r\n      quick: \"了解！我会加快操作节奏，抓住短期机会！\",\r\n      patient: \"好的主人，我会耐心等待最佳时机！\",\r\n      steady: \"明白了，我会保持稳定的节奏操作！\",\r\n      value: \"收到！我会重点寻找被低估的优质资产！\",\r\n      growth: \"好的！我会关注高成长潜力的标的！\",\r\n      trend: \"明白了！我会顺势而为，跟随市场趋势！\"\r\n    };\r\n    return responses[option.id] || \"谢谢主人的建议，我会认真考虑的！\";\r\n  };\r\n  \r\n  if (timeSinceLastAdvice > 1800000) { // 30分钟冷却\r\n    setVitalityPoints(prev => Math.min(100, prev + 3));\r\n    setLastInteractionTime(prev => ({...prev, advice: Date.now()}));\r\n    showCharacterMessage(generateResponse() + \" (活力值+3)\");\r\n  } else {\r\n    const remainingTime = Math.ceil((1800000 - timeSinceLastAdvice) / 60000);\r\n    showCharacterMessage(generateResponse() + ` (${remainingTime}分钟后可再次获得活力值)`);\r\n  }\r\n  \r\n  // 记录本次建议\r\n  setLastAdviceCategory(category);\r\n  setLastAdviceOption(option);\r\n  \r\n  closeBottomSheet();\r\n};\r\n\r\n  // 送礼物给角色\r\n  const sendGift = (gift) => {\r\n    if (partyCoins < gift.price) {\r\n      showCharacterMessage(\"主人的派对币不够了呢~\");\r\n      return;\r\n    }\r\n    setPartyCoins(prev => prev - gift.price);\r\n    setVitalityPoints(prev => Math.min(100, prev + gift.vitality)); // 最高100\r\n    showCharacterMessage(gift.message);\r\n    \r\n    // 如果之前停止了投资，重新激活\r\n    if (!isInvestmentActive && vitalityPoints + gift.vitality > 0) {\r\n      setIsInvestmentActive(true);\r\n      showCharacterMessage(\"充满活力！我又可以继续为主人理财了！\");\r\n    }\r\n    \r\n    closeBottomSheet();\r\n  };\r\n\r\n  // 下拉刷新\r\n  const handleRefresh = () => {\r\n    setRefreshing(true);\r\n    setTimeout(() => {\r\n      setRefreshing(false);\r\n      const newProfit = Math.floor(Math.random() * 500) - 100;\r\n      setTodayProfit(newProfit);\r\n      showCharacterMessage(\"数据已更新，主人看看我们今天的表现吧！\");\r\n    }, 1000);\r\n  };\r\n\r\n  // 新手引导弹窗\r\n  const IntroModal = () => (\r\n    <div className=\"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\" onClick={() => setShowIntro(false)}>\r\n      <div className=\"bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up\" onClick={e => e.stopPropagation()}>\r\n        <div className=\"text-center mb-4\">\r\n          <div className=\"w-20 h-20 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mx-auto mb-4 flex items-center justify-center\">\r\n            <Sparkles className=\"w-10 h-10 text-white\" />\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold mb-2\">星伴生财</h2>\r\n          <p className=\"text-gray-600\">让{character.name}帮您打理派对币，一起创造财富！</p>\r\n        </div>\r\n        \r\n        <div className=\"space-y-3 mb-6\">\r\n          <div className=\"flex items-start gap-3\">\r\n            <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0\">\r\n              <span className=\"text-purple-600 font-bold\">1</span>\r\n            </div>\r\n            <div>\r\n              <p className=\"font-medium\">智能代理投资</p>\r\n              <p className=\"text-sm text-gray-500\">{character.name}会根据TA的性格特点，帮您自动投资理财</p>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"flex items-start gap-3\">\r\n            <div className=\"w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0\">\r\n              <span className=\"text-pink-600 font-bold\">2</span>\r\n            </div>\r\n            <div>\r\n              <p className=\"font-medium\">情感化互动</p>\r\n              <p className=\"text-sm text-gray-500\">每日查看投资汇报，给TA建议，一起经历财富起伏</p>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"flex items-start gap-3\">\r\n            <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0\">\r\n              <span className=\"text-green-600 font-bold\">3</span>\r\n            </div>\r\n            <div>\r\n              <p className=\"font-medium\">派对币增值</p>\r\n              <p className=\"text-sm text-gray-500\">投资收益可随时兑换回派对币（收取5%手续费）</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"bg-yellow-50 rounded-2xl p-3 mb-6\">\r\n          <p className=\"text-sm text-yellow-800\">\r\n            <AlertCircle className=\"w-4 h-4 inline mr-1\" />\r\n            初始10000虚拟资金为体验金，不可提取，仅收益部分可兑换\r\n          </p>\r\n        </div>\r\n        \r\n        <button\r\n          onClick={() => setShowIntro(false)}\r\n          className=\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold\"\r\n        >\r\n          开始体验\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // 功能说明弹窗\r\n  const InfoModal = () => (\r\n    <div className=\"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\" onClick={() => setShowInfo(false)}>\r\n      <div className=\"bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up max-h-[80vh] overflow-y-auto\" onClick={e => e.stopPropagation()}>\r\n        <div className=\"text-center mb-4\">\r\n          <h2 className=\"text-xl font-bold mb-2\">功能说明</h2>\r\n        </div>\r\n        \r\n        <div className=\"space-y-4 text-sm\">\r\n          <div>\r\n            <h3 className=\"font-semibold mb-2 text-purple-600\">🎯 怎么玩</h3>\r\n            <p className=\"text-gray-600\">\r\n              1. 投入派对币给AI角色进行投资<br/>\r\n              2. AI会根据其性格特点自动操作<br/>\r\n              3. 查看每日投资报告了解收益<br/>\r\n              4. 给AI建议影响投资策略<br/>\r\n              5. 送礼物增加理财活力值\r\n            </p>\r\n          </div>\r\n          \r\n          <div>\r\n            <h3 className=\"font-semibold mb-2 text-pink-600\">💰 资金说明</h3>\r\n            <p className=\"text-gray-600\">\r\n              • 初始10000元为体验金，不可提取<br/>\r\n              • 您投入的本金可随时提取<br/>\r\n              • 投资收益可随时提取<br/>\r\n              • 提取时收取5%手续费<br/>\r\n              • 1派对币 = 100虚拟资金\r\n            </p>\r\n          </div>\r\n          \r\n          <div>\r\n            <h3 className=\"font-semibold mb-2 text-green-600\">⚡ 理财活力</h3>\r\n            <p className=\"text-gray-600\">\r\n              • AI需要理财活力才能持续投资<br/>\r\n              • 活力值每小时消耗2点<br/>\r\n              • 活力值为0时自动结算资产<br/>\r\n              • 送礼物可以增加活力值<br/>\r\n              • 每日查看星伴生财就加活力值<br/>\r\n              • 操作资产投取也加活力值<br/>\r\n              • 与虚拟角色进行投资互动也加活力值<br/>\r\n              • 初始活力值为100\r\n            </p>\r\n          </div>\r\n          \r\n          <div>\r\n            <h3 className=\"font-semibold mb-2 text-orange-600\">⚠️ 注意事项</h3>\r\n            <p className=\"text-gray-600\">\r\n              • 这是虚拟投资游戏，非真实投资<br/>\r\n              • 投资有风险，可能产生亏损<br/>\r\n              • 合理安排派对币，享受游戏乐趣<br/>\r\n              • 多与AI互动提升投资效果\r\n            </p>\r\n          </div>\r\n        </div>\r\n        \r\n        <button\r\n          onClick={() => setShowInfo(false)}\r\n          className=\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold mt-6\"\r\n        >\r\n          我知道了\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // 底部弹出框\r\n  const BottomSheet = () => {\r\n    const sheetContent = () => {\r\n      switch (bottomSheetType) {\r\n        case 'exchange-in':\r\n          return (\r\n            <>\r\n              <h3 className=\"text-lg font-bold mb-4\">投入派对币</h3>\r\n              <div className=\"bg-gray-50 rounded-2xl p-4 mb-6\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span className=\"text-gray-600\">当前派对币</span>\r\n                  <span className=\"font-semibold\">{partyCoins}</span>\r\n                </div>\r\n              </div>\r\n              <input\r\n                type=\"number\"\r\n                value={exchangeAmount}\r\n                onChange={(e) => setExchangeAmount(e.target.value)}\r\n                placeholder=\"请输入投入数量\"\r\n                className=\"w-full px-4 py-3 border border-gray-200 rounded-2xl mb-3 text-lg\"\r\n              />\r\n              <p className=\"text-sm text-gray-500 mb-4\">1派对币 = 100虚拟资金</p>\r\n              <div className=\"grid grid-cols-4 gap-2 mb-6\">\r\n                {[10, 50, 100, 200].map(amount => (\r\n                  <button\r\n                    key={amount}\r\n                    onClick={() => setExchangeAmount(amount.toString())}\r\n                    className=\"py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200\"\r\n                  >\r\n                    {amount}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n              <button\r\n                onClick={() => handleExchange(true)}\r\n                className=\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold\"\r\n              >\r\n                确认投入\r\n              </button>\r\n            </>\r\n          );\r\n          \r\n        case 'exchange-out':\r\n          return (\r\n            <>\r\n              <h3 className=\"text-lg font-bold mb-4\">提取资产</h3>\r\n              <div className=\"bg-gray-50 rounded-2xl p-4 mb-4\">\r\n                <div className=\"flex justify-between items-center mb-2\">\r\n                  <span className=\"text-gray-600\">体验金（不可提取）</span>\r\n                  <span className=\"font-semibold text-gray-400\">¥{experienceFund}</span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center mb-2\">\r\n                  <span className=\"text-gray-600\">投入本金</span>\r\n                  <span className=\"font-semibold\">¥{principalFund}</span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center mb-2\">\r\n                  <span className=\"text-gray-600\">投资收益</span>\r\n                  <span className=\"font-semibold text-red-500\">¥{totalProfit}</span>\r\n                </div>\r\n                <div className=\"border-t pt-2 mt-2\">\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <span className=\"text-gray-800 font-medium\">可提取总额</span>\r\n                    <span className=\"font-bold text-lg\">¥{principalFund + totalProfit}</span>\r\n                  </div>\r\n                  <div className=\"flex justify-between items-center text-sm text-gray-500\">\r\n                    <span>可兑换派对币</span>\r\n                    <span>{Math.floor((principalFund + totalProfit) / 100)}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <input\r\n                type=\"number\"\r\n                value={exchangeAmount}\r\n                onChange={(e) => setExchangeAmount(e.target.value)}\r\n                placeholder=\"请输入提取的派对币数量\"\r\n                className=\"w-full px-4 py-3 border border-gray-200 rounded-2xl mb-3 text-lg\"\r\n              />\r\n              <p className=\"text-sm text-gray-500 mb-4\">将收取5%手续费</p>\r\n              \r\n              <div className=\"grid grid-cols-3 gap-2 mb-6\">\r\n                <button\r\n                  onClick={() => setExchangeAmount(Math.floor(totalProfit / 100).toString())}\r\n                  className=\"py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200\"\r\n                  disabled={totalProfit <= 0}\r\n                >\r\n                  仅收益\r\n                </button>\r\n                <button\r\n                  onClick={() => setExchangeAmount(Math.floor(principalFund / 100).toString())}\r\n                  className=\"py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200\"\r\n                  disabled={principalFund <= 0}\r\n                >\r\n                  仅本金\r\n                </button>\r\n                <button\r\n                  onClick={() => setExchangeAmount(Math.floor((principalFund + totalProfit) / 100).toString())}\r\n                  className=\"py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200\"\r\n                  disabled={principalFund + totalProfit <= 0}\r\n                >\r\n                  全部\r\n                </button>\r\n              </div>\r\n              \r\n              <button\r\n                onClick={() => handleExchange(false)}\r\n                className=\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold\"\r\n              >\r\n                确认提取\r\n              </button>\r\n            </>\r\n          );\r\n          \r\n        case 'compass':\r\n            return (\r\n                <>\r\n                <div className=\"flex items-center justify-between mb-6\">\r\n                    <h3 className=\"text-lg font-bold\">星语罗盘</h3>\r\n                    <span className=\"text-sm text-gray-500\">{new Date().toLocaleDateString()}</span>\r\n                </div>\r\n                \r\n                {/* 大盘气象解读 */}\r\n                <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-4 mb-6\">\r\n                    <div className=\"flex items-center gap-2 mb-3\">\r\n                    <Target className=\"w-5 h-5 text-purple-600\" />\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <p className=\"font-medium text-purple-900\">今日大盘气象</p>\r\n                        <button \r\n                            onClick={() => {\r\n                            // 跳转到大盘数据详情页面的逻辑\r\n                            console.log('跳转到大盘数据详情页面');\r\n                            }}\r\n                            className=\"flex items-center gap-1 text-xs text-purple-600 bg-purple-100 px-2 py-0.5 rounded-full hover:bg-purple-200 active:scale-95 transition-all\"\r\n                        >\r\n                            <span>详情</span>\r\n                            <ChevronRight className=\"w-3 h-3\" />\r\n                        </button>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"space-y-3\">\r\n                    <div className=\"flex items-start gap-3\">\r\n                        <div className=\"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                        <Sparkles className=\"w-5 h-5 text-purple-600\" />\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                        <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n                            \"今天的市场就像春天的花园，处处充满生机！科技板块如同向日葵般昂首向阳，新能源像清晨的露珠闪闪发光。不过消费板块有点像午后慵懒的猫咪，需要时间慢慢苏醒呢~\"\r\n                        </p>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div className=\"grid grid-cols-3 gap-2 mt-3\">\r\n                        <div className=\"bg-white/60 backdrop-blur rounded-xl p-2 text-center\">\r\n                        <p className=\"text-xs text-gray-600\">市场情绪</p>\r\n                        <p className=\"text-sm font-bold text-green-600\">乐观😊</p>\r\n                        </div>\r\n                        <div className=\"bg-white/60 backdrop-blur rounded-xl p-2 text-center\">\r\n                        <p className=\"text-xs text-gray-600\">热门板块</p>\r\n                        <p className=\"text-sm font-bold text-purple-600\">科技🚀</p>\r\n                        </div>\r\n                        <div className=\"bg-white/60 backdrop-blur rounded-xl p-2 text-center\">\r\n                        <p className=\"text-xs text-gray-600\">风险等级</p>\r\n                        <p className=\"text-sm font-bold text-yellow-600\">适中⚡</p>\r\n                        </div>\r\n                    </div>\r\n                    </div>\r\n                </div>\r\n                \r\n                {/* 个人投资汇报 */}\r\n                <div className=\"bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4 mb-6\">\r\n                    <div className=\"flex items-start gap-3\">\r\n                    <img src={character.avatar} alt=\"\" className=\"w-12 h-12 rounded-full\" />\r\n                    <div className=\"flex-1\">\r\n                        <p className=\"font-medium mb-1\">{character.name}的投资汇报</p>\r\n                        <p className=\"text-sm text-gray-600\">\r\n                        {isInvestmentActive \r\n                            ? \"主人！跟随大盘的春风，我们今天收获满满！科技未来像火箭一样涨了5.8%，我觉得自己就像个小园丁，看着财富之花慢慢绽放~虽然消费龙头有点调皮，但我相信它只是在积蓄力量！\"\r\n                            : \"主人，我现在像一只需要充电的小机器人，暂时停下来休息了。如果您想让我继续在市场的花园里采蜜，可以送我一些能量礼物哦~\"\r\n                        }\r\n                        </p>\r\n                    </div>\r\n                    </div>\r\n                </div>\r\n                \r\n                {/* 今日战绩 */}\r\n                <div className=\"grid grid-cols-2 gap-3 mb-6\">\r\n                    <div className=\"bg-red-50 rounded-xl p-3\">\r\n                    <p className=\"text-sm text-gray-600 mb-1\">今日收益</p>\r\n                    <p className=\"text-xl font-bold text-red-600\">+¥{todayProfit}</p>\r\n                    </div>\r\n                    <div className=\"bg-blue-50 rounded-xl p-3\">\r\n                    <p className=\"text-sm text-gray-600 mb-1\">今日收益率</p>\r\n                    <p className=\"text-xl font-bold text-blue-600\">+1.26%</p>\r\n                    </div>\r\n                </div>\r\n                \r\n                {/* 今日操作记录 */}\r\n                <div className=\"space-y-3 mb-6\">\r\n                    <p className=\"font-medium flex items-center gap-2\">\r\n                    <BarChart3 className=\"w-4 h-4 text-gray-600\" />\r\n                    今日操作轨迹\r\n                    </p>\r\n                    <div className=\"bg-gray-50 rounded-xl p-3\">\r\n                    <div className=\"flex justify-between items-center\">\r\n                        <div>\r\n                        <p className=\"font-medium\">买入 科技未来</p>\r\n                        <p className=\"text-sm text-gray-500\">10:30 顺势而为</p>\r\n                        </div>\r\n                        <TrendingUp className=\"w-5 h-5 text-red-500\" />\r\n                    </div>\r\n                    </div>\r\n                    <div className=\"bg-gray-50 rounded-xl p-3\">\r\n                    <div className=\"flex justify-between items-center\">\r\n                        <div>\r\n                        <p className=\"font-medium\">卖出 传统制造</p>\r\n                        <p className=\"text-sm text-gray-500\">14:20 获利了结</p>\r\n                        </div>\r\n                        <TrendingDown className=\"w-5 h-5 text-green-500\" />\r\n                    </div>\r\n                    </div>\r\n                </div>\r\n                \r\n                {/* 明日展望 */}\r\n                <div className=\"bg-purple-50 rounded-xl p-4\">\r\n                    <p className=\"font-medium mb-2 flex items-center gap-2\">\r\n                    <Star className=\"w-4 h-4 text-purple-600\" />\r\n                    明日展望\r\n                    </p>\r\n                    <p className=\"text-sm text-gray-600\">\r\n                    \"明天我想像蜜蜂一样，在新能源的花丛中多采些蜜！听说有政策春风要来，我已经准备好小篮子了。科技股涨得太快，像个调皮的孩子，我会找机会让它休息一下~\"\r\n                    </p>\r\n                </div>\r\n                </>\r\n            );\r\n          \r\n        case 'advice':\r\n          return (\r\n            <>\r\n              <h3 className=\"text-lg font-bold mb-4\">给{character.name}一些建议</h3>\r\n              \r\n              {/* 显示上次建议 */}\r\n              {lastAdviceOption && (\r\n                <div className=\"bg-purple-50 rounded-2xl p-3 mb-4\">\r\n                  <p className=\"text-sm text-purple-600 mb-1\">上次建议</p>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <p className=\"font-medium text-purple-900\">{lastAdviceOption.title}</p>\r\n                      <p className=\"text-xs text-purple-700\">{adviceCategories[lastAdviceCategory].title}</p>\r\n                    </div>\r\n                    <span className=\"text-2xl\">{adviceCategories[lastAdviceCategory].icon}</span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n              \r\n              {/* 分类标签 */}\r\n              <div className=\"flex gap-2 mb-4\">\r\n                {Object.entries(adviceCategories).map(([key, category]) => (\r\n                  <button\r\n                    key={key}\r\n                    onClick={() => setCurrentAdviceCategory(key)}\r\n                    className={`flex-1 py-2 px-3 rounded-xl text-sm font-medium transition-colors ${\r\n                      currentAdviceCategory === key \r\n                        ? 'bg-purple-500 text-white' \r\n                        : 'bg-gray-100 text-gray-700'\r\n                    }`}\r\n                  >\r\n                    <span className=\"mr-1\">{category.icon}</span>\r\n                    {category.title}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n              \r\n              {/* 建议选项 */}\r\n              <div className=\"space-y-3\">\r\n                {adviceCategories[currentAdviceCategory].options.map(option => (\r\n                  <button\r\n                    key={option.id}\r\n                    onClick={() => giveAdvice(currentAdviceCategory, option)}\r\n                    className={`w-full p-4 rounded-2xl text-left transition-all ${\r\n                      lastAdviceOption?.id === option.id && lastAdviceCategory === currentAdviceCategory\r\n                        ? 'bg-purple-100 border-2 border-purple-300'\r\n                        : 'bg-gray-50 border-2 border-transparent hover:border-purple-200'\r\n                    }`}\r\n                  >\r\n                    <p className=\"font-medium text-gray-900\">{option.title}</p>\r\n                    <p className=\"text-sm text-gray-600\">{option.desc}</p>\r\n                    {lastAdviceOption?.id === option.id && lastAdviceCategory === currentAdviceCategory && (\r\n                      <p className=\"text-xs text-purple-600 mt-1\">✓ 当前建议</p>\r\n                    )}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n              \r\n              <div className=\"mt-4 text-center\">\r\n                <p className=\"text-xs text-gray-500\">建议会影响{character.name}的投资策略哦~</p>\r\n              </div>\r\n            </>\r\n          );\r\n          \r\n        case 'reward':\r\n          return (\r\n            <>\r\n              <h3 className=\"text-lg font-bold mb-6\">送礼物给{character.name}</h3>\r\n              <div className=\"bg-gray-50 rounded-2xl p-4 mb-6\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span className=\"text-gray-600\">当前派对币</span>\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Coins className=\"w-4 h-4 text-yellow-500\" />\r\n                    <span className=\"font-semibold\">{partyCoins}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"grid grid-cols-3 gap-3\">\r\n                {giftList.map(gift => (\r\n                  <button\r\n                    key={gift.id}\r\n                    onClick={() => sendGift(gift)}\r\n                    className=\"bg-white border border-gray-200 rounded-2xl p-4 text-center active:scale-95 transition-transform hover:border-purple-300\"\r\n                  >\r\n                    <span className=\"text-3xl mb-2 block\">{gift.icon}</span>\r\n                    <p className=\"font-medium text-sm\">{gift.name}</p>\r\n                    <p className=\"text-xs text-gray-500 flex items-center justify-center gap-1 mt-1\">\r\n                      <Coins className=\"w-3 h-3\" />\r\n                      {gift.price}\r\n                    </p>\r\n                    <p className=\"text-xs text-green-500 mt-1\">+{gift.vitality}活力</p>\r\n                  </button>\r\n                ))}\r\n              </div>\r\n              <div className=\"mt-6 text-center\">\r\n                <p className=\"text-sm text-gray-500\">送出礼物会增加理财活力值，让{character.name}更有动力！</p>\r\n              </div>\r\n            </>\r\n          );\r\n          \r\n        default:\r\n          return null;\r\n      }\r\n    };\r\n\r\n    return (\r\n      <div className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${showBottomSheet ? 'opacity-100 visible' : 'opacity-0 invisible'}`} onClick={closeBottomSheet}>\r\n        <div className=\"flex items-end justify-center h-full\">\r\n          <div \r\n            className={`w-full max-w-[390px] bg-white rounded-t-3xl p-6 transition-transform duration-300 ${\r\n              showBottomSheet ? 'translate-y-0' : 'translate-y-full'\r\n            }`}\r\n            onClick={e => e.stopPropagation()}\r\n          >\r\n            <div className=\"w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4\"></div>\r\n            <div className=\"max-h-[70vh] overflow-y-auto\">\r\n              {sheetContent()}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 主界面\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-50 overflow-hidden\">\r\n      {/* 手机端容器 */}\r\n      <div className=\"relative w-full max-w-[390px] h-full mx-auto bg-white shadow-xl\">\r\n        {/* 顶部导航栏 */}\r\n        <div className=\"fixed top-0 left-0 right-0 max-w-[390px] mx-auto bg-white z-30 border-b border-gray-100\">\r\n        <div className=\"flex items-center justify-between px-4 py-3\">\r\n            <button className=\"p-2 -ml-2\">\r\n            <ChevronLeft className=\"w-6 h-6\" />\r\n            </button>\r\n            <h1 className=\"font-semibold text-lg\">星伴生财</h1>\r\n            <div className=\"flex items-center gap-2\">\r\n            {/* 新增：星耀排行榜入口 */}\r\n            <button className=\"p-2 relative\" onClick={() => {/* 跳转到排行榜 */}}>\r\n                <Trophy className=\"w-5 h-5 text-purple-500\" />\r\n                {/* 可选：添加小红点提示 */}\r\n                <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\r\n            </button>\r\n            <button className=\"p-2 -mr-2\" onClick={() => setShowInfo(true)}>\r\n                <Info className=\"w-5 h-5 text-gray-600\" />\r\n            </button>\r\n            </div>\r\n        </div>\r\n        </div>\r\n\r\n        {/* 主内容区域 */}\r\n        <div className=\"pt-16 pb-20 h-full overflow-y-auto\" onScroll={(e) => {\r\n          if (e.target.scrollTop === 0 && !refreshing) {\r\n            handleRefresh();\r\n          }\r\n        }}>\r\n          {/* 下拉刷新提示 */}\r\n          {refreshing && (\r\n            <div className=\"flex items-center justify-center py-4\">\r\n              <RefreshCw className=\"w-5 h-5 text-purple-500 animate-spin mr-2\" />\r\n              <span className=\"text-sm text-gray-600\">更新数据中...</span>\r\n            </div>\r\n          )}\r\n\r\n          {/* 角色卡片 */}\r\n          <div className=\"px-4 py-4\">\r\n            <div className=\"bg-gradient-to-br from-purple-100 to-pink-100 rounded-3xl p-5\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <img src={character.avatar} alt={character.name} className=\"w-14 h-14 rounded-full border-2 border-white shadow-md object-cover\" />\r\n                  <div>\r\n                    <h3 className=\"font-bold text-lg\">{character.name}</h3>\r\n                    <p className=\"text-sm text-gray-600\">{character.investmentStyle}</p>\r\n                    {/* 新增：显示当前排名 */}\r\n                    <div className=\"flex items-center gap-1 mt-1\">\r\n                    <Trophy className=\"w-3 h-3 text-yellow-500\" />\r\n                    <span className=\"text-xs text-yellow-600 font-medium\">排行榜第12名</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                {/* 理财活力值 */}\r\n                <div className=\"text-center\" onClick={() => setShowVitalityTips(true)}>\r\n                <div className=\"flex items-center gap-1 mb-1\">\r\n                  <span className=\"text-2xl\">{getVitalityIcon()}</span>\r\n                  <span className={`font-bold text-lg ${getVitalityColor()}`}>{vitalityPoints}</span>\r\n                  <Info className=\"w-3 h-3 text-gray-400\" /> {/* 添加提示图标 */}\r\n                </div>\r\n                <p className=\"text-xs text-gray-600\">理财活力</p>\r\n              </div>\r\n              </div>\r\n              \r\n              {/* 派对币显示 */}\r\n              <div className=\"bg-white/40 backdrop-blur rounded-xl px-3 py-2 mb-3 flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Coins className=\"w-4 h-4 text-yellow-500\" />\r\n                  <span className=\"text-sm text-gray-700\">已投派对币</span>\r\n                </div>\r\n                <span className=\"font-semibold text-gray-800\">{partyCoins}</span>\r\n              </div>\r\n              \r\n              <div className=\"grid grid-cols-2 gap-3\">\r\n                <div className=\"bg-white/60 backdrop-blur rounded-2xl p-3\">\r\n                  <p className=\"text-sm text-gray-600 mb-1\">总资产</p>\r\n                  <p className=\"text-xl font-bold\">¥{virtualFund.toLocaleString()}</p>\r\n                  <p className={`text-sm ${totalProfit >= 0 ? 'text-red-600' : 'text-green-600'}`}>\r\n                    {totalProfit >= 0 ? '+' : ''}{totalProfit} ({profitRate}%)\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-white/60 backdrop-blur rounded-2xl p-3\">\r\n                  <p className=\"text-sm text-gray-600 mb-1\">总收益</p>\r\n                  <p className={`text-xl font-bold ${todayProfit >= 0 ? 'text-red-600' : 'text-green-600'}`}>\r\n                    {todayProfit >= 0 ? '+' : ''}¥{todayProfit}\r\n                  </p>\r\n                  <p className=\"text-sm text-gray-500\">1.26%</p>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* 活力值不足提示 */}\r\n              {vitalityPoints < 20 && vitalityPoints > 0 && (\r\n                <div className=\"mt-3 bg-yellow-50 rounded-xl p-2 text-center\">\r\n                  <p className=\"text-xs text-yellow-800\">活力值较低，送个礼物鼓励一下吧~</p>\r\n                </div>\r\n              )}\r\n              {vitalityPoints === 0 && (\r\n                <div className=\"mt-3 bg-red-50 rounded-xl p-2 text-center\">\r\n                  <p className=\"text-xs text-red-800\">活力耗尽，投资已暂停，送礼物可恢复</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 快捷操作 */}\r\n          <div className=\"px-4 mb-4\">\r\n            <div className=\"grid grid-cols-2 gap-3\">\r\n              <button\r\n                onClick={() => openBottomSheet('exchange-in')}\r\n                className=\"bg-white rounded-2xl p-4 shadow-sm active:scale-95 transition-transform\"\r\n              >\r\n                <ArrowUp className=\"w-6 h-6 text-purple-500 mb-2\" />\r\n                <p className=\"font-medium\">投入资金</p>\r\n                <p className=\"text-xs text-gray-500\">增加本金</p>\r\n              </button>\r\n              <button\r\n                onClick={() => openBottomSheet('exchange-out')}\r\n                className=\"bg-white rounded-2xl p-4 shadow-sm active:scale-95 transition-transform\"\r\n              >\r\n                <Wallet className=\"w-6 h-6 text-green-500 mb-2\" />\r\n                <p className=\"font-medium\">提取资产</p>\r\n                <p className=\"text-xs text-gray-500\">可提¥{principalFund + totalProfit}</p>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 持仓列表 */}\r\n          <div className=\"px-4 mb-4\">\r\n            <div className=\"bg-white rounded-2xl shadow-sm\">\r\n              <div className=\"p-4 border-b border-gray-100\">\r\n                <h4 className=\"font-semibold flex items-center gap-2\">\r\n                  <BarChart3 className=\"w-5 h-5 text-purple-500\" />\r\n                  当前持仓\r\n                </h4>\r\n              </div>\r\n              <div className=\"divide-y divide-gray-100\">\r\n                {holdings.map((stock, index) => (\r\n                  <div key={index} className=\"p-4 active:bg-gray-50\">\r\n                    <div className=\"flex justify-between items-start mb-1\">\r\n                      <div>\r\n                        <p className=\"font-medium\">{stock.name}</p>\r\n                        <p className=\"text-sm text-gray-500\">{stock.code}</p>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <p className={`font-semibold ${stock.profit >= 0 ? 'text-red-600' : 'text-green-600'}`}>\r\n                          {stock.profit >= 0 ? '+' : ''}¥{stock.profit}\r\n                        </p>\r\n                        <p className={`text-sm ${stock.profitRate >= 0 ? 'text-red-600' : 'text-green-600'}`}>\r\n                          {stock.profitRate >= 0 ? '+' : ''}{stock.profitRate}%\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm text-gray-500\">\r\n                      <span>{stock.shares}股</span>\r\n                      <span>¥{stock.price}/股</span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 底部导航栏 */}\r\n        <div className=\"fixed bottom-0 left-0 right-0 max-w-[390px] mx-auto bg-white border-t border-gray-100\">\r\n        <div className=\"grid grid-cols-3 py-2\">\r\n            <button\r\n            onClick={() => openBottomSheet('compass')}  // 改为 compass\r\n            className=\"flex flex-col items-center gap-1 py-2 active:bg-gray-50\"\r\n            >\r\n            <Target className=\"w-5 h-5 text-gray-600\" />  {/* 改用罗盘图标 */}\r\n            <span className=\"text-xs text-gray-600\">星语罗盘</span>\r\n            </button>\r\n            <button\r\n            onClick={() => openBottomSheet('advice')}\r\n            className=\"flex flex-col items-center gap-1 py-2 active:bg-gray-50\"\r\n            >\r\n            <MessageCircle className=\"w-5 h-5 text-purple-500\" />\r\n            <span className=\"text-xs text-purple-500\">给建议</span>\r\n            </button>\r\n            <button \r\n            onClick={() => openBottomSheet('reward')}\r\n            className=\"flex flex-col items-center gap-1 py-2 active:bg-gray-50\"\r\n            >\r\n            <Gift className=\"w-5 h-5 text-pink-500\" />\r\n            <span className=\"text-xs text-pink-500\">奖励一下</span>\r\n            </button>\r\n        </div>\r\n        </div>\r\n\r\n        {/* 新手引导 */}\r\n        {showIntro && <IntroModal />}\r\n        \r\n        {/* 功能说明 */}\r\n        {showInfo && <InfoModal />}\r\n\r\n        {/* 底部弹窗 */}\r\n        <BottomSheet />\r\n\r\n        {/* 角色消息提示 */}\r\n        {showMessage && (\r\n          <div className=\"fixed top-20 left-4 right-4 max-w-[358px] mx-auto bg-white rounded-2xl shadow-lg p-4 z-50 animate-slide-down\">\r\n            <div className=\"flex items-start gap-3\">\r\n              <img src={character.avatar} alt=\"\" className=\"w-10 h-10 rounded-full object-cover\" />\r\n              <p className=\"text-sm flex-1\">{messageContent}</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <style jsx>{`\r\n        @keyframes scale-up {\r\n          from {\r\n            transform: scale(0.9);\r\n            opacity: 0;\r\n          }\r\n          to {\r\n            transform: scale(1);\r\n            opacity: 1;\r\n          }\r\n        }\r\n        \r\n        @keyframes slide-down {\r\n          from {\r\n            transform: translateY(-20px);\r\n            opacity: 0;\r\n          }\r\n          to {\r\n            transform: translateY(0);\r\n            opacity: 1;\r\n          }\r\n        }\r\n        \r\n        .animate-scale-up {\r\n          animation: scale-up 0.3s ease-out;\r\n        }\r\n        \r\n        .animate-slide-down {\r\n          animation: slide-down 0.3s ease-out;\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AICharacterInvestmentPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,aAAa,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,OAAO,QAAQ,cAAc;;AAE1O;AACA,OAAOC,eAAe,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACxC,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,OAAO,CAAC;EAC3D,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,GAAG,CAAC;EACnD,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACgE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClE,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3E,QAAQ,CAAC,MAAM,CAAC;;EAG1E;EACA,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC8E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/E,QAAQ,CAACgF,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EACxE,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;;EAElE;EACA,MAAM,CAACoF,QAAQ,EAAEC,WAAW,CAAC,GAAGrF,QAAQ,CAAC,CACvC;IAAEsF,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE,GAAG;IAAE/B,UAAU,EAAE,GAAG;IAAEgC,KAAK,EAAE;EAAK,CAAC,EACxF;IAAEJ,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE,CAAC,GAAG;IAAE/B,UAAU,EAAE,CAAC,GAAG;IAAEgC,KAAK,EAAE;EAAK,CAAC,EAC1F;IAAEJ,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE,GAAG;IAAE/B,UAAU,EAAE,GAAG;IAAEgC,KAAK,EAAE;EAAM,CAAC,CAC1F,CAAC;;EAEF;EACA,MAAMC,SAAS,GAAG;IAChBL,IAAI,EAAE,IAAI;IACVM,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,aAAa;IAC1BC,MAAM,EAAEtE,eAAe;IACvBuE,eAAe,EAAE,MAAM,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,CACf;IAAEC,EAAE,EAAE,CAAC;IAAEX,IAAI,EAAE,IAAI;IAAEY,IAAI,EAAE,IAAI;IAAER,KAAK,EAAE,EAAE;IAAES,QAAQ,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAkB,CAAC,EACrF;IAAEH,EAAE,EAAE,CAAC;IAAEX,IAAI,EAAE,IAAI;IAAEY,IAAI,EAAE,GAAG;IAAER,KAAK,EAAE,EAAE;IAAES,QAAQ,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAe,CAAC,EAClF;IAAEH,EAAE,EAAE,CAAC;IAAEX,IAAI,EAAE,IAAI;IAAEY,IAAI,EAAE,IAAI;IAAER,KAAK,EAAE,EAAE;IAAES,QAAQ,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAiB,CAAC,EACrF;IAAEH,EAAE,EAAE,CAAC;IAAEX,IAAI,EAAE,IAAI;IAAEY,IAAI,EAAE,IAAI;IAAER,KAAK,EAAE,GAAG;IAAES,QAAQ,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAsB,CAAC,EAC3F;IAAEH,EAAE,EAAE,CAAC;IAAEX,IAAI,EAAE,IAAI;IAAEY,IAAI,EAAE,KAAK;IAAER,KAAK,EAAE,GAAG;IAAES,QAAQ,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAsB,CAAC,EAC5F;IAAEH,EAAE,EAAE,CAAC;IAAEX,IAAI,EAAE,IAAI;IAAEY,IAAI,EAAE,IAAI;IAAER,KAAK,EAAE,GAAG;IAAES,QAAQ,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAwB,CAAC,CAC/F;;EAED;EACF,MAAMC,gBAAgB,GAAG;IACvBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbL,IAAI,EAAE,KAAK;MACXM,OAAO,EAAE,CACP;QAAEP,EAAE,EAAE,cAAc;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAO,CAAC,EACnD;QAAER,EAAE,EAAE,UAAU;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAO,CAAC,EAC/C;QAAER,EAAE,EAAE,YAAY;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAS,CAAC;IAEvD,CAAC;IACDC,MAAM,EAAE;MACNH,KAAK,EAAE,MAAM;MACbL,IAAI,EAAE,IAAI;MACVM,OAAO,EAAE,CACP;QAAEP,EAAE,EAAE,OAAO;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAS,CAAC,EAC9C;QAAER,EAAE,EAAE,SAAS;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAS,CAAC,EAChD;QAAER,EAAE,EAAE,QAAQ;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAQ,CAAC;IAElD,CAAC;IACDE,KAAK,EAAE;MACLJ,KAAK,EAAE,MAAM;MACbL,IAAI,EAAE,IAAI;MACVM,OAAO,EAAE,CACP;QAAEP,EAAE,EAAE,OAAO;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAS,CAAC,EAC9C;QAAER,EAAE,EAAE,QAAQ;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAQ,CAAC,EAC9C;QAAER,EAAE,EAAE,OAAO;QAAEM,KAAK,EAAE,MAAM;QAAEE,IAAI,EAAE;MAAO,CAAC;IAEhD;EACF,CAAC;;EAEC;EACAxG,SAAS,CAAC,MAAM;IACd,MAAM2G,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B,MAAM5B,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB,MAAM6B,WAAW,GAAG,CAAC7B,GAAG,GAAGH,kBAAkB,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MACjE,MAAMiC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,CAAC,CAAC;MAEpD,IAAIC,gBAAgB,GAAG,CAAC,EAAE;QACxBlC,iBAAiB,CAACqC,IAAI,IAAI;UACxB,MAAMC,WAAW,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEF,IAAI,GAAGH,gBAAgB,CAAC;;UAExD;UACA,IAAII,WAAW,GAAG,EAAE,IAAIA,WAAW,GAAG,CAAC,IAAIH,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE;YAC9D,MAAMC,KAAK,GAAG,CACZ,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,CAChB;YACDC,oBAAoB,CAACD,KAAK,CAACN,IAAI,CAACC,KAAK,CAACD,IAAI,CAACK,MAAM,CAAC,CAAC,GAAGC,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC;UACvE;;UAEA;UACA,IAAIL,WAAW,KAAK,CAAC,IAAIjC,kBAAkB,EAAE;YAC3CC,qBAAqB,CAAC,KAAK,CAAC;YAC5BoC,oBAAoB,CAAC,uBAAuB,CAAC;YAC7C;YACAE,gBAAgB,CAAC,CAAC;UACpB;UAEA,OAAON,WAAW;QACpB,CAAC,CAAC;QACFpC,qBAAqB,CAACE,GAAG,CAAC;MAC5B;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMyC,aAAa,CAACd,KAAK,CAAC;EACnC,CAAC,EAAE,CAAC9B,kBAAkB,EAAEI,kBAAkB,CAAC,CAAC;;EAE5C;EACFjF,SAAS,CAAC,MAAM;IACd,MAAM0H,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,KAAK,GAAG,IAAI5C,IAAI,CAAC,CAAC,CAAC6C,YAAY,CAAC,CAAC;MACvC,MAAMC,SAAS,GAAGhE,gBAAgB,GAAG,IAAIkB,IAAI,CAAClB,gBAAgB,CAAC,CAAC+D,YAAY,CAAC,CAAC,GAAG,IAAI;MAErF,IAAIC,SAAS,KAAKF,KAAK,EAAE;QACvB;QACA/C,iBAAiB,CAACqC,IAAI,IAAIF,IAAI,CAACe,GAAG,CAAC,GAAG,EAAEb,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACrDnD,mBAAmB,CAACiB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;QAC/Bd,mBAAmB,CAAC,IAAI,CAAC;QACzBoD,oBAAoB,CAAC,0BAA0B,CAAC;MAClD;IACF,CAAC;IAEDI,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEJ;EACA,MAAMF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMO,iBAAiB,GAAGxF,aAAa,GAAGE,WAAW;IACrD,MAAMuF,cAAc,GAAGjB,IAAI,CAACC,KAAK,CAACe,iBAAiB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;IACnE5F,aAAa,CAAC8E,IAAI,IAAIA,IAAI,GAAGe,cAAc,CAAC;IAC5CxF,gBAAgB,CAAC,CAAC,CAAC;IACnBE,cAAc,CAAC,CAAC,CAAC;IACjBL,cAAc,CAACC,cAAc,CAAC;EAChC,CAAC;;EAED;EACA,MAAM2F,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAItD,cAAc,GAAG,EAAE,EAAE,OAAO,gBAAgB;IAChD,IAAIA,cAAc,GAAG,EAAE,EAAE,OAAO,iBAAiB;IACjD,OAAO,cAAc;EACvB,CAAC;;EAED;EACA,MAAMuD,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIvD,cAAc,GAAG,EAAE,EAAE,OAAO,GAAG;IACnC,IAAIA,cAAc,GAAG,EAAE,EAAE,OAAO,IAAI;IACpC,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM2C,oBAAoB,GAAInB,OAAO,IAAK;IACxC/C,iBAAiB,CAAC+C,OAAO,CAAC;IAC1BjD,cAAc,CAAC,IAAI,CAAC;IACpBiF,UAAU,CAAC,MAAMjF,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMkF,eAAe,GAAIC,IAAI,IAAK;IAChCrF,kBAAkB,CAACqF,IAAI,CAAC;IACxBvF,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMwF,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxF,kBAAkB,CAAC,KAAK,CAAC;IACzBQ,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiF,cAAc,GAAIC,IAAI,IAAK;IAC/B,MAAMC,MAAM,GAAGC,QAAQ,CAACrF,cAAc,CAAC;IACvC,IAAI,CAACoF,MAAM,IAAIA,MAAM,IAAI,CAAC,EAAE;MAC1BnB,oBAAoB,CAAC,YAAY,CAAC;MAClC;IACF;IAEA,IAAIkB,IAAI,EAAE;MACR;MACA,IAAIC,MAAM,GAAGvG,UAAU,EAAE;QACvBoF,oBAAoB,CAAC,YAAY,CAAC;QAClC;MACF;MACAnF,aAAa,CAAC8E,IAAI,IAAIA,IAAI,GAAGwB,MAAM,CAAC;MACpCjG,gBAAgB,CAACyE,IAAI,IAAIA,IAAI,GAAGwB,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;MAC/CpG,cAAc,CAAC4E,IAAI,IAAIA,IAAI,GAAGwB,MAAM,GAAG,GAAG,CAAC;MAC3CnB,oBAAoB,CAAC,YAAYmB,MAAM,GAAG,GAAG,cAAc,CAAC;IAC9D,CAAC,MAAM;MACL;MACA,MAAMV,iBAAiB,GAAGxF,aAAa,GAAGE,WAAW,CAAC,CAAC;MACvD,MAAMkG,WAAW,GAAG5B,IAAI,CAACC,KAAK,CAACe,iBAAiB,GAAG,GAAG,CAAC;MAEvD,IAAIU,MAAM,GAAGE,WAAW,EAAE;QACxBrB,oBAAoB,CAAC,YAAYqB,WAAW,QAAQ,CAAC;QACrD;MACF;MAEA,MAAMC,cAAc,GAAGH,MAAM,GAAG,GAAG;MACnC,MAAMI,GAAG,GAAG9B,IAAI,CAACC,KAAK,CAACyB,MAAM,GAAG,IAAI,CAAC;;MAErC;MACA,IAAIG,cAAc,IAAInG,WAAW,EAAE;QACjCC,cAAc,CAACuE,IAAI,IAAIA,IAAI,GAAG2B,cAAc,CAAC;MAC/C,CAAC,MAAM;QACL,MAAME,UAAU,GAAGrG,WAAW;QAC9B,MAAMsG,aAAa,GAAGH,cAAc,GAAGE,UAAU;QACjDpG,cAAc,CAAC,CAAC,CAAC;QACjBF,gBAAgB,CAACyE,IAAI,IAAIA,IAAI,GAAG8B,aAAa,CAAC;MAChD;MAEA5G,aAAa,CAAC8E,IAAI,IAAIA,IAAI,GAAGwB,MAAM,GAAGI,GAAG,CAAC;MAC1CxG,cAAc,CAAC4E,IAAI,IAAIA,IAAI,GAAG2B,cAAc,CAAC;MAC7CtB,oBAAoB,CAAC,OAAOmB,MAAM,GAAGI,GAAG,WAAWA,GAAG,GAAG,CAAC;IAC5D;IACAP,gBAAgB,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMU,UAAU,GAAGA,CAACC,QAAQ,EAAEC,MAAM,KAAK;IACzC,MAAMC,UAAU,GAAGpF,mBAAmB,CAACqF,MAAM,IAAI,CAAC;IAClD,MAAMC,mBAAmB,GAAGtE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGmE,UAAU;;IAEnD;IACA,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,MAAMC,SAAS,GAAG;QAChBC,YAAY,EAAE,wBAAwB;QACtCC,QAAQ,EAAE,oBAAoB;QAC9BC,UAAU,EAAE,oBAAoB;QAChCC,KAAK,EAAE,qBAAqB;QAC5BC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,kBAAkB;QAC1BC,KAAK,EAAE,oBAAoB;QAC3BC,MAAM,EAAE,kBAAkB;QAC1BC,KAAK,EAAE;MACT,CAAC;MACD,OAAOT,SAAS,CAACL,MAAM,CAAClD,EAAE,CAAC,IAAI,kBAAkB;IACnD,CAAC;IAED,IAAIqD,mBAAmB,GAAG,OAAO,EAAE;MAAE;MACnCzE,iBAAiB,CAACqC,IAAI,IAAIF,IAAI,CAACe,GAAG,CAAC,GAAG,EAAEb,IAAI,GAAG,CAAC,CAAC,CAAC;MAClDjD,sBAAsB,CAACiD,IAAI,KAAK;QAAC,GAAGA,IAAI;QAAEmC,MAAM,EAAErE,IAAI,CAACC,GAAG,CAAC;MAAC,CAAC,CAAC,CAAC;MAC/DsC,oBAAoB,CAACgC,gBAAgB,CAAC,CAAC,GAAG,UAAU,CAAC;IACvD,CAAC,MAAM;MACL,MAAMW,aAAa,GAAGlD,IAAI,CAACmD,IAAI,CAAC,CAAC,OAAO,GAAGb,mBAAmB,IAAI,KAAK,CAAC;MACxE/B,oBAAoB,CAACgC,gBAAgB,CAAC,CAAC,GAAG,KAAKW,aAAa,cAAc,CAAC;IAC7E;;IAEA;IACA3F,qBAAqB,CAAC2E,QAAQ,CAAC;IAC/BzE,mBAAmB,CAAC0E,MAAM,CAAC;IAE3BZ,gBAAgB,CAAC,CAAC;EACpB,CAAC;;EAEC;EACA,MAAM6B,QAAQ,GAAIC,IAAI,IAAK;IACzB,IAAIlI,UAAU,GAAGkI,IAAI,CAAC3E,KAAK,EAAE;MAC3B6B,oBAAoB,CAAC,aAAa,CAAC;MACnC;IACF;IACAnF,aAAa,CAAC8E,IAAI,IAAIA,IAAI,GAAGmD,IAAI,CAAC3E,KAAK,CAAC;IACxCb,iBAAiB,CAACqC,IAAI,IAAIF,IAAI,CAACe,GAAG,CAAC,GAAG,EAAEb,IAAI,GAAGmD,IAAI,CAAClE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChEoB,oBAAoB,CAAC8C,IAAI,CAACjE,OAAO,CAAC;;IAElC;IACA,IAAI,CAAClB,kBAAkB,IAAIN,cAAc,GAAGyF,IAAI,CAAClE,QAAQ,GAAG,CAAC,EAAE;MAC7DhB,qBAAqB,CAAC,IAAI,CAAC;MAC3BoC,oBAAoB,CAAC,oBAAoB,CAAC;IAC5C;IAEAgB,gBAAgB,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAM+B,aAAa,GAAGA,CAAA,KAAM;IAC1BzG,aAAa,CAAC,IAAI,CAAC;IACnBuE,UAAU,CAAC,MAAM;MACfvE,aAAa,CAAC,KAAK,CAAC;MACpB,MAAM0G,SAAS,GAAGvD,IAAI,CAACC,KAAK,CAACD,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;MACvD5D,cAAc,CAAC8G,SAAS,CAAC;MACzBhD,oBAAoB,CAAC,qBAAqB,CAAC;IAC7C,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMiD,UAAU,GAAGA,CAAA,kBACjB9I,OAAA;IAAK+I,SAAS,EAAC,qEAAqE;IAACC,OAAO,EAAEA,CAAA,KAAM1I,YAAY,CAAC,KAAK,CAAE;IAAA2I,QAAA,eACtHjJ,OAAA;MAAK+I,SAAS,EAAC,2DAA2D;MAACC,OAAO,EAAEE,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAF,QAAA,gBAC3GjJ,OAAA;QAAK+I,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BjJ,OAAA;UAAK+I,SAAS,EAAC,mHAAmH;UAAAE,QAAA,eAChIjJ,OAAA,CAACrB,QAAQ;YAACoK,SAAS,EAAC;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNvJ,OAAA;UAAI+I,SAAS,EAAC,yBAAyB;UAAAE,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjDvJ,OAAA;UAAG+I,SAAS,EAAC,eAAe;UAAAE,QAAA,GAAC,QAAC,EAAChF,SAAS,CAACL,IAAI,EAAC,4FAAe;QAAA;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAENvJ,OAAA;QAAK+I,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BjJ,OAAA;UAAK+I,SAAS,EAAC,wBAAwB;UAAAE,QAAA,gBACrCjJ,OAAA;YAAK+I,SAAS,EAAC,mFAAmF;YAAAE,QAAA,eAChGjJ,OAAA;cAAM+I,SAAS,EAAC,2BAA2B;cAAAE,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNvJ,OAAA;YAAAiJ,QAAA,gBACEjJ,OAAA;cAAG+I,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrCvJ,OAAA;cAAG+I,SAAS,EAAC,uBAAuB;cAAAE,QAAA,GAAEhF,SAAS,CAACL,IAAI,EAAC,0GAAmB;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvJ,OAAA;UAAK+I,SAAS,EAAC,wBAAwB;UAAAE,QAAA,gBACrCjJ,OAAA;YAAK+I,SAAS,EAAC,iFAAiF;YAAAE,QAAA,eAC9FjJ,OAAA;cAAM+I,SAAS,EAAC,yBAAyB;cAAAE,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNvJ,OAAA;YAAAiJ,QAAA,gBACEjJ,OAAA;cAAG+I,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpCvJ,OAAA;cAAG+I,SAAS,EAAC,uBAAuB;cAAAE,QAAA,EAAC;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvJ,OAAA;UAAK+I,SAAS,EAAC,wBAAwB;UAAAE,QAAA,gBACrCjJ,OAAA;YAAK+I,SAAS,EAAC,kFAAkF;YAAAE,QAAA,eAC/FjJ,OAAA;cAAM+I,SAAS,EAAC,0BAA0B;cAAAE,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNvJ,OAAA;YAAAiJ,QAAA,gBACEjJ,OAAA;cAAG+I,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpCvJ,OAAA;cAAG+I,SAAS,EAAC,uBAAuB;cAAAE,QAAA,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvJ,OAAA;QAAK+I,SAAS,EAAC,mCAAmC;QAAAE,QAAA,eAChDjJ,OAAA;UAAG+I,SAAS,EAAC,yBAAyB;UAAAE,QAAA,gBACpCjJ,OAAA,CAACjB,WAAW;YAACgK,SAAS,EAAC;UAAqB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yJAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENvJ,OAAA;QACEgJ,OAAO,EAAEA,CAAA,KAAM1I,YAAY,CAAC,KAAK,CAAE;QACnCyI,SAAS,EAAC,+FAA+F;QAAAE,QAAA,EAC1G;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMC,SAAS,GAAGA,CAAA,kBAChBxJ,OAAA;IAAK+I,SAAS,EAAC,qEAAqE;IAACC,OAAO,EAAEA,CAAA,KAAMxI,WAAW,CAAC,KAAK,CAAE;IAAAyI,QAAA,eACrHjJ,OAAA;MAAK+I,SAAS,EAAC,wFAAwF;MAACC,OAAO,EAAEE,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAF,QAAA,gBACxIjJ,OAAA;QAAK+I,SAAS,EAAC,kBAAkB;QAAAE,QAAA,eAC/BjJ,OAAA;UAAI+I,SAAS,EAAC,wBAAwB;UAAAE,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAENvJ,OAAA;QAAK+I,SAAS,EAAC,mBAAmB;QAAAE,QAAA,gBAChCjJ,OAAA;UAAAiJ,QAAA,gBACEjJ,OAAA;YAAI+I,SAAS,EAAC,oCAAoC;YAAAE,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9DvJ,OAAA;YAAG+I,SAAS,EAAC,eAAe;YAAAE,QAAA,GAAC,+EACV,eAAAjJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,iFACL,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,+EACP,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,+DACN,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mEAErB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENvJ,OAAA;UAAAiJ,QAAA,gBACEjJ,OAAA;YAAI+I,SAAS,EAAC,kCAAkC;YAAAE,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DvJ,OAAA;YAAG+I,SAAS,EAAC,eAAe;YAAAE,QAAA,GAAC,sFACR,eAAAjJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,6EACX,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,iEACP,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,6DACJ,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,4DAEnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENvJ,OAAA;UAAAiJ,QAAA,gBACEjJ,OAAA;YAAI+I,SAAS,EAAC,mCAAmC;YAAAE,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DvJ,OAAA;YAAG+I,SAAS,EAAC,eAAe;YAAAE,QAAA,GAAC,mFACX,eAAAjJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,kEACT,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,8EACH,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,uEACP,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,yFACF,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,6EACP,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,2GACA,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,kDAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENvJ,OAAA;UAAAiJ,QAAA,gBACEjJ,OAAA;YAAI+I,SAAS,EAAC,oCAAoC;YAAAE,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DvJ,OAAA;YAAG+I,SAAS,EAAC,eAAe;YAAAE,QAAA,GAAC,6FACX,eAAAjJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mFACP,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,+FACH,eAAAvJ,OAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,yEAEvB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvJ,OAAA;QACEgJ,OAAO,EAAEA,CAAA,KAAMxI,WAAW,CAAC,KAAK,CAAE;QAClCuI,SAAS,EAAC,oGAAoG;QAAAE,QAAA,EAC/G;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,QAAQpI,eAAe;QACrB,KAAK,aAAa;UAChB,oBACEtB,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA;cAAI+I,SAAS,EAAC,wBAAwB;cAAAE,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDvJ,OAAA;cAAK+I,SAAS,EAAC,iCAAiC;cAAAE,QAAA,eAC9CjJ,OAAA;gBAAK+I,SAAS,EAAC,mCAAmC;gBAAAE,QAAA,gBAChDjJ,OAAA;kBAAM+I,SAAS,EAAC,eAAe;kBAAAE,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CvJ,OAAA;kBAAM+I,SAAS,EAAC,eAAe;kBAAAE,QAAA,EAAExI;gBAAU;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvJ,OAAA;cACE4G,IAAI,EAAC,QAAQ;cACbyB,KAAK,EAAEzG,cAAe;cACtB+H,QAAQ,EAAGT,CAAC,IAAKrH,iBAAiB,CAACqH,CAAC,CAACU,MAAM,CAACvB,KAAK,CAAE;cACnDwB,WAAW,EAAC,4CAAS;cACrBd,SAAS,EAAC;YAAkE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACFvJ,OAAA;cAAG+I,SAAS,EAAC,4BAA4B;cAAAE,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5DvJ,OAAA;cAAK+I,SAAS,EAAC,6BAA6B;cAAAE,QAAA,EACzC,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAACa,GAAG,CAAC9C,MAAM,iBAC5BhH,OAAA;gBAEEgJ,OAAO,EAAEA,CAAA,KAAMnH,iBAAiB,CAACmF,MAAM,CAAC+C,QAAQ,CAAC,CAAC,CAAE;gBACpDhB,SAAS,EAAC,oEAAoE;gBAAAE,QAAA,EAE7EjC;cAAM,GAJFA,MAAM;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKL,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvJ,OAAA;cACEgJ,OAAO,EAAEA,CAAA,KAAMlC,cAAc,CAAC,IAAI,CAAE;cACpCiC,SAAS,EAAC,+FAA+F;cAAAE,QAAA,EAC1G;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC;QAGP,KAAK,cAAc;UACjB,oBACEvJ,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA;cAAI+I,SAAS,EAAC,wBAAwB;cAAAE,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDvJ,OAAA;cAAK+I,SAAS,EAAC,iCAAiC;cAAAE,QAAA,gBAC9CjJ,OAAA;gBAAK+I,SAAS,EAAC,wCAAwC;gBAAAE,QAAA,gBACrDjJ,OAAA;kBAAM+I,SAAS,EAAC,eAAe;kBAAAE,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDvJ,OAAA;kBAAM+I,SAAS,EAAC,6BAA6B;kBAAAE,QAAA,GAAC,MAAC,EAACpI,cAAc;gBAAA;kBAAAuI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNvJ,OAAA;gBAAK+I,SAAS,EAAC,wCAAwC;gBAAAE,QAAA,gBACrDjJ,OAAA;kBAAM+I,SAAS,EAAC,eAAe;kBAAAE,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CvJ,OAAA;kBAAM+I,SAAS,EAAC,eAAe;kBAAAE,QAAA,GAAC,MAAC,EAACnI,aAAa;gBAAA;kBAAAsI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNvJ,OAAA;gBAAK+I,SAAS,EAAC,wCAAwC;gBAAAE,QAAA,gBACrDjJ,OAAA;kBAAM+I,SAAS,EAAC,eAAe;kBAAAE,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CvJ,OAAA;kBAAM+I,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,GAAC,MAAC,EAACjI,WAAW;gBAAA;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNvJ,OAAA;gBAAK+I,SAAS,EAAC,oBAAoB;gBAAAE,QAAA,gBACjCjJ,OAAA;kBAAK+I,SAAS,EAAC,mCAAmC;kBAAAE,QAAA,gBAChDjJ,OAAA;oBAAM+I,SAAS,EAAC,2BAA2B;oBAAAE,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxDvJ,OAAA;oBAAM+I,SAAS,EAAC,mBAAmB;oBAAAE,QAAA,GAAC,MAAC,EAACnI,aAAa,GAAGE,WAAW;kBAAA;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNvJ,OAAA;kBAAK+I,SAAS,EAAC,yDAAyD;kBAAAE,QAAA,gBACtEjJ,OAAA;oBAAAiJ,QAAA,EAAM;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBvJ,OAAA;oBAAAiJ,QAAA,EAAO3D,IAAI,CAACC,KAAK,CAAC,CAACzE,aAAa,GAAGE,WAAW,IAAI,GAAG;kBAAC;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvJ,OAAA;cACE4G,IAAI,EAAC,QAAQ;cACbyB,KAAK,EAAEzG,cAAe;cACtB+H,QAAQ,EAAGT,CAAC,IAAKrH,iBAAiB,CAACqH,CAAC,CAACU,MAAM,CAACvB,KAAK,CAAE;cACnDwB,WAAW,EAAC,oEAAa;cACzBd,SAAS,EAAC;YAAkE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACFvJ,OAAA;cAAG+I,SAAS,EAAC,4BAA4B;cAAAE,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEtDvJ,OAAA;cAAK+I,SAAS,EAAC,6BAA6B;cAAAE,QAAA,gBAC1CjJ,OAAA;gBACEgJ,OAAO,EAAEA,CAAA,KAAMnH,iBAAiB,CAACyD,IAAI,CAACC,KAAK,CAACvE,WAAW,GAAG,GAAG,CAAC,CAAC+I,QAAQ,CAAC,CAAC,CAAE;gBAC3EhB,SAAS,EAAC,oEAAoE;gBAC9EiB,QAAQ,EAAEhJ,WAAW,IAAI,CAAE;gBAAAiI,QAAA,EAC5B;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvJ,OAAA;gBACEgJ,OAAO,EAAEA,CAAA,KAAMnH,iBAAiB,CAACyD,IAAI,CAACC,KAAK,CAACzE,aAAa,GAAG,GAAG,CAAC,CAACiJ,QAAQ,CAAC,CAAC,CAAE;gBAC7EhB,SAAS,EAAC,oEAAoE;gBAC9EiB,QAAQ,EAAElJ,aAAa,IAAI,CAAE;gBAAAmI,QAAA,EAC9B;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvJ,OAAA;gBACEgJ,OAAO,EAAEA,CAAA,KAAMnH,iBAAiB,CAACyD,IAAI,CAACC,KAAK,CAAC,CAACzE,aAAa,GAAGE,WAAW,IAAI,GAAG,CAAC,CAAC+I,QAAQ,CAAC,CAAC,CAAE;gBAC7FhB,SAAS,EAAC,oEAAoE;gBAC9EiB,QAAQ,EAAElJ,aAAa,GAAGE,WAAW,IAAI,CAAE;gBAAAiI,QAAA,EAC5C;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvJ,OAAA;cACEgJ,OAAO,EAAEA,CAAA,KAAMlC,cAAc,CAAC,KAAK,CAAE;cACrCiC,SAAS,EAAC,+FAA+F;cAAAE,QAAA,EAC1G;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC;QAGP,KAAK,SAAS;UACV,oBACIvJ,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACAjJ,OAAA;cAAK+I,SAAS,EAAC,wCAAwC;cAAAE,QAAA,gBACnDjJ,OAAA;gBAAI+I,SAAS,EAAC,mBAAmB;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CvJ,OAAA;gBAAM+I,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,EAAE,IAAI3F,IAAI,CAAC,CAAC,CAAC2G,kBAAkB,CAAC;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eAGNvJ,OAAA;cAAK+I,SAAS,EAAC,mEAAmE;cAAAE,QAAA,gBAC9EjJ,OAAA;gBAAK+I,SAAS,EAAC,8BAA8B;gBAAAE,QAAA,gBAC7CjJ,OAAA,CAACX,MAAM;kBAAC0J,SAAS,EAAC;gBAAyB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9CvJ,OAAA;kBAAK+I,SAAS,EAAC,yBAAyB;kBAAAE,QAAA,gBACpCjJ,OAAA;oBAAG+I,SAAS,EAAC,6BAA6B;oBAAAE,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACrDvJ,OAAA;oBACIgJ,OAAO,EAAEA,CAAA,KAAM;sBACf;sBACAkB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;oBAC1B,CAAE;oBACFpB,SAAS,EAAC,2IAA2I;oBAAAE,QAAA,gBAErJjJ,OAAA;sBAAAiJ,QAAA,EAAM;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACfvJ,OAAA,CAACxB,YAAY;sBAACuK,SAAS,EAAC;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNvJ,OAAA;gBAAK+I,SAAS,EAAC,WAAW;gBAAAE,QAAA,gBAC1BjJ,OAAA;kBAAK+I,SAAS,EAAC,wBAAwB;kBAAAE,QAAA,gBACnCjJ,OAAA;oBAAK+I,SAAS,EAAC,qFAAqF;oBAAAE,QAAA,eACpGjJ,OAAA,CAACrB,QAAQ;sBAACoK,SAAS,EAAC;oBAAyB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNvJ,OAAA;oBAAK+I,SAAS,EAAC,QAAQ;oBAAAE,QAAA,eACvBjJ,OAAA;sBAAG+I,SAAS,EAAC,uCAAuC;sBAAAE,QAAA,EAAC;oBAErD;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENvJ,OAAA;kBAAK+I,SAAS,EAAC,6BAA6B;kBAAAE,QAAA,gBACxCjJ,OAAA;oBAAK+I,SAAS,EAAC,sDAAsD;oBAAAE,QAAA,gBACrEjJ,OAAA;sBAAG+I,SAAS,EAAC,uBAAuB;sBAAAE,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC7CvJ,OAAA;sBAAG+I,SAAS,EAAC,kCAAkC;sBAAAE,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNvJ,OAAA;oBAAK+I,SAAS,EAAC,sDAAsD;oBAAAE,QAAA,gBACrEjJ,OAAA;sBAAG+I,SAAS,EAAC,uBAAuB;sBAAAE,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC7CvJ,OAAA;sBAAG+I,SAAS,EAAC,mCAAmC;sBAAAE,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACNvJ,OAAA;oBAAK+I,SAAS,EAAC,sDAAsD;oBAAAE,QAAA,gBACrEjJ,OAAA;sBAAG+I,SAAS,EAAC,uBAAuB;sBAAAE,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC7CvJ,OAAA;sBAAG+I,SAAS,EAAC,mCAAmC;sBAAAE,QAAA,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNvJ,OAAA;cAAK+I,SAAS,EAAC,iEAAiE;cAAAE,QAAA,eAC5EjJ,OAAA;gBAAK+I,SAAS,EAAC,wBAAwB;gBAAAE,QAAA,gBACvCjJ,OAAA;kBAAKoK,GAAG,EAAEnG,SAAS,CAACG,MAAO;kBAACiG,GAAG,EAAC,EAAE;kBAACtB,SAAS,EAAC;gBAAwB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxEvJ,OAAA;kBAAK+I,SAAS,EAAC,QAAQ;kBAAAE,QAAA,gBACnBjJ,OAAA;oBAAG+I,SAAS,EAAC,kBAAkB;oBAAAE,QAAA,GAAEhF,SAAS,CAACL,IAAI,EAAC,gCAAK;kBAAA;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzDvJ,OAAA;oBAAG+I,SAAS,EAAC,uBAAuB;oBAAAE,QAAA,EACnCzF,kBAAkB,GACb,qFAAqF,GACrF;kBAA4D;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAE/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNvJ,OAAA;cAAK+I,SAAS,EAAC,6BAA6B;cAAAE,QAAA,gBACxCjJ,OAAA;gBAAK+I,SAAS,EAAC,0BAA0B;gBAAAE,QAAA,gBACzCjJ,OAAA;kBAAG+I,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClDvJ,OAAA;kBAAG+I,SAAS,EAAC,gCAAgC;kBAAAE,QAAA,GAAC,OAAE,EAACnH,WAAW;gBAAA;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACNvJ,OAAA;gBAAK+I,SAAS,EAAC,2BAA2B;gBAAAE,QAAA,gBAC1CjJ,OAAA;kBAAG+I,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnDvJ,OAAA;kBAAG+I,SAAS,EAAC,iCAAiC;kBAAAE,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNvJ,OAAA;cAAK+I,SAAS,EAAC,gBAAgB;cAAAE,QAAA,gBAC3BjJ,OAAA;gBAAG+I,SAAS,EAAC,qCAAqC;gBAAAE,QAAA,gBAClDjJ,OAAA,CAACL,SAAS;kBAACoJ,SAAS,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wCAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJvJ,OAAA;gBAAK+I,SAAS,EAAC,2BAA2B;gBAAAE,QAAA,eAC1CjJ,OAAA;kBAAK+I,SAAS,EAAC,mCAAmC;kBAAAE,QAAA,gBAC9CjJ,OAAA;oBAAAiJ,QAAA,gBACAjJ,OAAA;sBAAG+I,SAAS,EAAC,aAAa;sBAAAE,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACtCvJ,OAAA;sBAAG+I,SAAS,EAAC,uBAAuB;sBAAAE,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACNvJ,OAAA,CAACvB,UAAU;oBAACsK,SAAS,EAAC;kBAAsB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNvJ,OAAA;gBAAK+I,SAAS,EAAC,2BAA2B;gBAAAE,QAAA,eAC1CjJ,OAAA;kBAAK+I,SAAS,EAAC,mCAAmC;kBAAAE,QAAA,gBAC9CjJ,OAAA;oBAAAiJ,QAAA,gBACAjJ,OAAA;sBAAG+I,SAAS,EAAC,aAAa;sBAAAE,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACtCvJ,OAAA;sBAAG+I,SAAS,EAAC,uBAAuB;sBAAAE,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACNvJ,OAAA,CAACtB,YAAY;oBAACqK,SAAS,EAAC;kBAAwB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNvJ,OAAA;cAAK+I,SAAS,EAAC,6BAA6B;cAAAE,QAAA,gBACxCjJ,OAAA;gBAAG+I,SAAS,EAAC,0CAA0C;gBAAAE,QAAA,gBACvDjJ,OAAA,CAACT,IAAI;kBAACwJ,SAAS,EAAC;gBAAyB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJvJ,OAAA;gBAAG+I,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,EAAC;cAErC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACJ,CAAC;QAGX,KAAK,QAAQ;UACX,oBACEvJ,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA;cAAI+I,SAAS,EAAC,wBAAwB;cAAAE,QAAA,GAAC,QAAC,EAAChF,SAAS,CAACL,IAAI,EAAC,0BAAI;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAGhEzG,gBAAgB,iBACf9C,OAAA;cAAK+I,SAAS,EAAC,mCAAmC;cAAAE,QAAA,gBAChDjJ,OAAA;gBAAG+I,SAAS,EAAC,8BAA8B;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpDvJ,OAAA;gBAAK+I,SAAS,EAAC,mCAAmC;gBAAAE,QAAA,gBAChDjJ,OAAA;kBAAAiJ,QAAA,gBACEjJ,OAAA;oBAAG+I,SAAS,EAAC,6BAA6B;oBAAAE,QAAA,EAAEnG,gBAAgB,CAAC+B;kBAAK;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvEvJ,OAAA;oBAAG+I,SAAS,EAAC,yBAAyB;oBAAAE,QAAA,EAAEtE,gBAAgB,CAAC/B,kBAAkB,CAAC,CAACiC;kBAAK;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACNvJ,OAAA;kBAAM+I,SAAS,EAAC,UAAU;kBAAAE,QAAA,EAAEtE,gBAAgB,CAAC/B,kBAAkB,CAAC,CAAC4B;gBAAI;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAGDvJ,OAAA;cAAK+I,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAC7BqB,MAAM,CAACC,OAAO,CAAC5F,gBAAgB,CAAC,CAACmF,GAAG,CAAC,CAAC,CAACU,GAAG,EAAEhD,QAAQ,CAAC,kBACpDxH,OAAA;gBAEEgJ,OAAO,EAAEA,CAAA,KAAM/F,wBAAwB,CAACuH,GAAG,CAAE;gBAC7CzB,SAAS,EAAE,qEACT/F,qBAAqB,KAAKwH,GAAG,GACzB,0BAA0B,GAC1B,2BAA2B,EAC9B;gBAAAvB,QAAA,gBAEHjJ,OAAA;kBAAM+I,SAAS,EAAC,MAAM;kBAAAE,QAAA,EAAEzB,QAAQ,CAAChD;gBAAI;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC5C/B,QAAQ,CAAC3C,KAAK;cAAA,GATV2F,GAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUF,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNvJ,OAAA;cAAK+I,SAAS,EAAC,WAAW;cAAAE,QAAA,EACvBtE,gBAAgB,CAAC3B,qBAAqB,CAAC,CAAC8B,OAAO,CAACgF,GAAG,CAACrC,MAAM,iBACzDzH,OAAA;gBAEEgJ,OAAO,EAAEA,CAAA,KAAMzB,UAAU,CAACvE,qBAAqB,EAAEyE,MAAM,CAAE;gBACzDsB,SAAS,EAAE,mDACT,CAAAjG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyB,EAAE,MAAKkD,MAAM,CAAClD,EAAE,IAAI3B,kBAAkB,KAAKI,qBAAqB,GAC9E,0CAA0C,GAC1C,gEAAgE,EACnE;gBAAAiG,QAAA,gBAEHjJ,OAAA;kBAAG+I,SAAS,EAAC,2BAA2B;kBAAAE,QAAA,EAAExB,MAAM,CAAC5C;gBAAK;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3DvJ,OAAA;kBAAG+I,SAAS,EAAC,uBAAuB;kBAAAE,QAAA,EAAExB,MAAM,CAAC1C;gBAAI;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrD,CAAAzG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyB,EAAE,MAAKkD,MAAM,CAAClD,EAAE,IAAI3B,kBAAkB,KAAKI,qBAAqB,iBACjFhD,OAAA;kBAAG+I,SAAS,EAAC,8BAA8B;kBAAAE,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACtD;cAAA,GAZI9B,MAAM,CAAClD,EAAE;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaR,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvJ,OAAA;cAAK+I,SAAS,EAAC,kBAAkB;cAAAE,QAAA,eAC/BjJ,OAAA;gBAAG+I,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,GAAC,gCAAK,EAAChF,SAAS,CAACL,IAAI,EAAC,uCAAO;cAAA;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA,eACN,CAAC;QAGP,KAAK,QAAQ;UACX,oBACEvJ,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA;cAAI+I,SAAS,EAAC,wBAAwB;cAAAE,QAAA,GAAC,0BAAI,EAAChF,SAAS,CAACL,IAAI;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEvJ,OAAA;cAAK+I,SAAS,EAAC,iCAAiC;cAAAE,QAAA,eAC9CjJ,OAAA;gBAAK+I,SAAS,EAAC,mCAAmC;gBAAAE,QAAA,gBAChDjJ,OAAA;kBAAM+I,SAAS,EAAC,eAAe;kBAAAE,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CvJ,OAAA;kBAAK+I,SAAS,EAAC,yBAAyB;kBAAAE,QAAA,gBACtCjJ,OAAA,CAACf,KAAK;oBAAC8J,SAAS,EAAC;kBAAyB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7CvJ,OAAA;oBAAM+I,SAAS,EAAC,eAAe;oBAAAE,QAAA,EAAExI;kBAAU;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvJ,OAAA;cAAK+I,SAAS,EAAC,wBAAwB;cAAAE,QAAA,EACpC3E,QAAQ,CAACwF,GAAG,CAACnB,IAAI,iBAChB3I,OAAA;gBAEEgJ,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAACC,IAAI,CAAE;gBAC9BI,SAAS,EAAC,0HAA0H;gBAAAE,QAAA,gBAEpIjJ,OAAA;kBAAM+I,SAAS,EAAC,qBAAqB;kBAAAE,QAAA,EAAEN,IAAI,CAACnE;gBAAI;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDvJ,OAAA;kBAAG+I,SAAS,EAAC,qBAAqB;kBAAAE,QAAA,EAAEN,IAAI,CAAC/E;gBAAI;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClDvJ,OAAA;kBAAG+I,SAAS,EAAC,mEAAmE;kBAAAE,QAAA,gBAC9EjJ,OAAA,CAACf,KAAK;oBAAC8J,SAAS,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC5BZ,IAAI,CAAC3E,KAAK;gBAAA;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACJvJ,OAAA;kBAAG+I,SAAS,EAAC,6BAA6B;kBAAAE,QAAA,GAAC,GAAC,EAACN,IAAI,CAAClE,QAAQ,EAAC,cAAE;gBAAA;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA,GAV5DZ,IAAI,CAACpE,EAAE;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWN,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvJ,OAAA;cAAK+I,SAAS,EAAC,kBAAkB;cAAAE,QAAA,eAC/BjJ,OAAA;gBAAG+I,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,GAAC,sFAAc,EAAChF,SAAS,CAACL,IAAI,EAAC,gCAAK;cAAA;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA,eACN,CAAC;QAGP;UACE,OAAO,IAAI;MACf;IACF,CAAC;IAED,oBACEvJ,OAAA;MAAK+I,SAAS,EAAE,kEAAkE3H,eAAe,GAAG,qBAAqB,GAAG,qBAAqB,EAAG;MAAC4H,OAAO,EAAEnC,gBAAiB;MAAAoC,QAAA,eAC7KjJ,OAAA;QAAK+I,SAAS,EAAC,sCAAsC;QAAAE,QAAA,eACnDjJ,OAAA;UACE+I,SAAS,EAAE,qFACT3H,eAAe,GAAG,eAAe,GAAG,kBAAkB,EACrD;UACH4H,OAAO,EAAEE,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAE;UAAAF,QAAA,gBAElCjJ,OAAA;YAAK+I,SAAS,EAAC;UAAgD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtEvJ,OAAA;YAAK+I,SAAS,EAAC,8BAA8B;YAAAE,QAAA,EAC1CS,YAAY,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,oBACEvJ,OAAA;IAAK+I,SAAS,EAAC,0CAA0C;IAAAE,QAAA,gBAEvDjJ,OAAA;MAAK+I,SAAS,EAAC,iEAAiE;MAAAE,QAAA,gBAE9EjJ,OAAA;QAAK+I,SAAS,EAAC,yFAAyF;QAAAE,QAAA,eACxGjJ,OAAA;UAAK+I,SAAS,EAAC,6CAA6C;UAAAE,QAAA,gBACxDjJ,OAAA;YAAQ+I,SAAS,EAAC,WAAW;YAAAE,QAAA,eAC7BjJ,OAAA,CAAChB,WAAW;cAAC+J,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACTvJ,OAAA;YAAI+I,SAAS,EAAC,uBAAuB;YAAAE,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CvJ,OAAA;YAAK+I,SAAS,EAAC,yBAAyB;YAAAE,QAAA,gBAExCjJ,OAAA;cAAQ+I,SAAS,EAAC,cAAc;cAACC,OAAO,EAAEA,CAAA,KAAM,CAAC,aAAc;cAAAC,QAAA,gBAC3DjJ,OAAA,CAACd,MAAM;gBAAC6J,SAAS,EAAC;cAAyB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE9CvJ,OAAA;gBAAM+I,SAAS,EAAC;cAAwD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACTvJ,OAAA;cAAQ+I,SAAS,EAAC,WAAW;cAACC,OAAO,EAAEA,CAAA,KAAMxI,WAAW,CAAC,IAAI,CAAE;cAAAyI,QAAA,eAC3DjJ,OAAA,CAACP,IAAI;gBAACsJ,SAAS,EAAC;cAAuB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvJ,OAAA;QAAK+I,SAAS,EAAC,oCAAoC;QAAC0B,QAAQ,EAAGvB,CAAC,IAAK;UACnE,IAAIA,CAAC,CAACU,MAAM,CAACc,SAAS,KAAK,CAAC,IAAI,CAACxI,UAAU,EAAE;YAC3C0G,aAAa,CAAC,CAAC;UACjB;QACF,CAAE;QAAAK,QAAA,GAEC/G,UAAU,iBACTlC,OAAA;UAAK+I,SAAS,EAAC,uCAAuC;UAAAE,QAAA,gBACpDjJ,OAAA,CAACZ,SAAS;YAAC2J,SAAS,EAAC;UAA2C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnEvJ,OAAA;YAAM+I,SAAS,EAAC,uBAAuB;YAAAE,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACN,eAGDvJ,OAAA;UAAK+I,SAAS,EAAC,WAAW;UAAAE,QAAA,eACxBjJ,OAAA;YAAK+I,SAAS,EAAC,+DAA+D;YAAAE,QAAA,gBAC5EjJ,OAAA;cAAK+I,SAAS,EAAC,wCAAwC;cAAAE,QAAA,gBACrDjJ,OAAA;gBAAK+I,SAAS,EAAC,yBAAyB;gBAAAE,QAAA,gBACtCjJ,OAAA;kBAAKoK,GAAG,EAAEnG,SAAS,CAACG,MAAO;kBAACiG,GAAG,EAAEpG,SAAS,CAACL,IAAK;kBAACmF,SAAS,EAAC;gBAAqE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnIvJ,OAAA;kBAAAiJ,QAAA,gBACEjJ,OAAA;oBAAI+I,SAAS,EAAC,mBAAmB;oBAAAE,QAAA,EAAEhF,SAAS,CAACL;kBAAI;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvDvJ,OAAA;oBAAG+I,SAAS,EAAC,uBAAuB;oBAAAE,QAAA,EAAEhF,SAAS,CAACI;kBAAe;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAEpEvJ,OAAA;oBAAK+I,SAAS,EAAC,8BAA8B;oBAAAE,QAAA,gBAC7CjJ,OAAA,CAACd,MAAM;sBAAC6J,SAAS,EAAC;oBAAyB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9CvJ,OAAA;sBAAM+I,SAAS,EAAC,qCAAqC;sBAAAE,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvJ,OAAA;gBAAK+I,SAAS,EAAC,aAAa;gBAACC,OAAO,EAAEA,CAAA,KAAMrG,mBAAmB,CAAC,IAAI,CAAE;gBAAAsG,QAAA,gBACtEjJ,OAAA;kBAAK+I,SAAS,EAAC,8BAA8B;kBAAAE,QAAA,gBAC3CjJ,OAAA;oBAAM+I,SAAS,EAAC,UAAU;oBAAAE,QAAA,EAAExC,eAAe,CAAC;kBAAC;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrDvJ,OAAA;oBAAM+I,SAAS,EAAE,qBAAqBvC,gBAAgB,CAAC,CAAC,EAAG;oBAAAyC,QAAA,EAAE/F;kBAAc;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnFvJ,OAAA,CAACP,IAAI;oBAACsJ,SAAS,EAAC;kBAAuB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,KAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNvJ,OAAA;kBAAG+I,SAAS,EAAC,uBAAuB;kBAAAE,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNvJ,OAAA;cAAK+I,SAAS,EAAC,uFAAuF;cAAAE,QAAA,gBACpGjJ,OAAA;gBAAK+I,SAAS,EAAC,yBAAyB;gBAAAE,QAAA,gBACtCjJ,OAAA,CAACf,KAAK;kBAAC8J,SAAS,EAAC;gBAAyB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7CvJ,OAAA;kBAAM+I,SAAS,EAAC,uBAAuB;kBAAAE,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNvJ,OAAA;gBAAM+I,SAAS,EAAC,6BAA6B;gBAAAE,QAAA,EAAExI;cAAU;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eAENvJ,OAAA;cAAK+I,SAAS,EAAC,wBAAwB;cAAAE,QAAA,gBACrCjJ,OAAA;gBAAK+I,SAAS,EAAC,2CAA2C;gBAAAE,QAAA,gBACxDjJ,OAAA;kBAAG+I,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,EAAC;gBAAG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjDvJ,OAAA;kBAAG+I,SAAS,EAAC,mBAAmB;kBAAAE,QAAA,GAAC,MAAC,EAACtI,WAAW,CAACgK,cAAc,CAAC,CAAC;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEvJ,OAAA;kBAAG+I,SAAS,EAAE,WAAW/H,WAAW,IAAI,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;kBAAAiI,QAAA,GAC7EjI,WAAW,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,WAAW,EAAC,IAAE,EAACgB,UAAU,EAAC,IAC1D;gBAAA;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNvJ,OAAA;gBAAK+I,SAAS,EAAC,2CAA2C;gBAAAE,QAAA,gBACxDjJ,OAAA;kBAAG+I,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,EAAC;gBAAG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjDvJ,OAAA;kBAAG+I,SAAS,EAAE,qBAAqBjH,WAAW,IAAI,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;kBAAAmH,QAAA,GACvFnH,WAAW,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,MAAC,EAACA,WAAW;gBAAA;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACJvJ,OAAA;kBAAG+I,SAAS,EAAC,uBAAuB;kBAAAE,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLrG,cAAc,GAAG,EAAE,IAAIA,cAAc,GAAG,CAAC,iBACxClD,OAAA;cAAK+I,SAAS,EAAC,8CAA8C;cAAAE,QAAA,eAC3DjJ,OAAA;gBAAG+I,SAAS,EAAC,yBAAyB;gBAAAE,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACN,EACArG,cAAc,KAAK,CAAC,iBACnBlD,OAAA;cAAK+I,SAAS,EAAC,2CAA2C;cAAAE,QAAA,eACxDjJ,OAAA;gBAAG+I,SAAS,EAAC,sBAAsB;gBAAAE,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvJ,OAAA;UAAK+I,SAAS,EAAC,WAAW;UAAAE,QAAA,eACxBjJ,OAAA;YAAK+I,SAAS,EAAC,wBAAwB;YAAAE,QAAA,gBACrCjJ,OAAA;cACEgJ,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAAC,aAAa,CAAE;cAC9CoC,SAAS,EAAC,yEAAyE;cAAAE,QAAA,gBAEnFjJ,OAAA,CAACN,OAAO;gBAACqJ,SAAS,EAAC;cAA8B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDvJ,OAAA;gBAAG+I,SAAS,EAAC,aAAa;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnCvJ,OAAA;gBAAG+I,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACTvJ,OAAA;cACEgJ,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAAC,cAAc,CAAE;cAC/CoC,SAAS,EAAC,yEAAyE;cAAAE,QAAA,gBAEnFjJ,OAAA,CAACJ,MAAM;gBAACmJ,SAAS,EAAC;cAA6B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDvJ,OAAA;gBAAG+I,SAAS,EAAC,aAAa;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnCvJ,OAAA;gBAAG+I,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,GAAC,kBAAG,EAACnI,aAAa,GAAGE,WAAW;cAAA;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvJ,OAAA;UAAK+I,SAAS,EAAC,WAAW;UAAAE,QAAA,eACxBjJ,OAAA;YAAK+I,SAAS,EAAC,gCAAgC;YAAAE,QAAA,gBAC7CjJ,OAAA;cAAK+I,SAAS,EAAC,8BAA8B;cAAAE,QAAA,eAC3CjJ,OAAA;gBAAI+I,SAAS,EAAC,uCAAuC;gBAAAE,QAAA,gBACnDjJ,OAAA,CAACL,SAAS;kBAACoJ,SAAS,EAAC;gBAAyB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEnD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNvJ,OAAA;cAAK+I,SAAS,EAAC,0BAA0B;cAAAE,QAAA,EACtCvF,QAAQ,CAACoG,GAAG,CAAC,CAACc,KAAK,EAAEC,KAAK,kBACzB7K,OAAA;gBAAiB+I,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,gBAChDjJ,OAAA;kBAAK+I,SAAS,EAAC,uCAAuC;kBAAAE,QAAA,gBACpDjJ,OAAA;oBAAAiJ,QAAA,gBACEjJ,OAAA;sBAAG+I,SAAS,EAAC,aAAa;sBAAAE,QAAA,EAAE2B,KAAK,CAAChH;oBAAI;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3CvJ,OAAA;sBAAG+I,SAAS,EAAC,uBAAuB;sBAAAE,QAAA,EAAE2B,KAAK,CAAC/G;oBAAI;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNvJ,OAAA;oBAAK+I,SAAS,EAAC,YAAY;oBAAAE,QAAA,gBACzBjJ,OAAA;sBAAG+I,SAAS,EAAE,iBAAiB6B,KAAK,CAAC7G,MAAM,IAAI,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;sBAAAkF,QAAA,GACpF2B,KAAK,CAAC7G,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,MAAC,EAAC6G,KAAK,CAAC7G,MAAM;oBAAA;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACJvJ,OAAA;sBAAG+I,SAAS,EAAE,WAAW6B,KAAK,CAAC5I,UAAU,IAAI,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;sBAAAiH,QAAA,GAClF2B,KAAK,CAAC5I,UAAU,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE4I,KAAK,CAAC5I,UAAU,EAAC,GACtD;oBAAA;sBAAAoH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvJ,OAAA;kBAAK+I,SAAS,EAAC,4CAA4C;kBAAAE,QAAA,gBACzDjJ,OAAA;oBAAAiJ,QAAA,GAAO2B,KAAK,CAAC9G,MAAM,EAAC,QAAC;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5BvJ,OAAA;oBAAAiJ,QAAA,GAAM,MAAC,EAAC2B,KAAK,CAAC5G,KAAK,EAAC,SAAE;kBAAA;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA,GAlBEsB,KAAK;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvJ,OAAA;QAAK+I,SAAS,EAAC,uFAAuF;QAAAE,QAAA,eACtGjJ,OAAA;UAAK+I,SAAS,EAAC,uBAAuB;UAAAE,QAAA,gBAClCjJ,OAAA;YACAgJ,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAAC,SAAS,CAAE,CAAE;YAAA;YAC5CoC,SAAS,EAAC,yDAAyD;YAAAE,QAAA,gBAEnEjJ,OAAA,CAACX,MAAM;cAAC0J,SAAS,EAAC;YAAuB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,MAAE,eAC9CvJ,OAAA;cAAM+I,SAAS,EAAC,uBAAuB;cAAAE,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACTvJ,OAAA;YACAgJ,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAAC,QAAQ,CAAE;YACzCoC,SAAS,EAAC,yDAAyD;YAAAE,QAAA,gBAEnEjJ,OAAA,CAACnB,aAAa;cAACkK,SAAS,EAAC;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDvJ,OAAA;cAAM+I,SAAS,EAAC,yBAAyB;cAAAE,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACTvJ,OAAA;YACAgJ,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAAC,QAAQ,CAAE;YACzCoC,SAAS,EAAC,yDAAyD;YAAAE,QAAA,gBAEnEjJ,OAAA,CAAClB,IAAI;cAACiK,SAAS,EAAC;YAAuB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1CvJ,OAAA;cAAM+I,SAAS,EAAC,uBAAuB;cAAAE,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLlJ,SAAS,iBAAIL,OAAA,CAAC8I,UAAU;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAG3BhJ,QAAQ,iBAAIP,OAAA,CAACwJ,SAAS;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1BvJ,OAAA,CAACyJ,WAAW;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGd/H,WAAW,iBACVxB,OAAA;QAAK+I,SAAS,EAAC,8GAA8G;QAAAE,QAAA,eAC3HjJ,OAAA;UAAK+I,SAAS,EAAC,wBAAwB;UAAAE,QAAA,gBACrCjJ,OAAA;YAAKoK,GAAG,EAAEnG,SAAS,CAACG,MAAO;YAACiG,GAAG,EAAC,EAAE;YAACtB,SAAS,EAAC;UAAqC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrFvJ,OAAA;YAAG+I,SAAS,EAAC,gBAAgB;YAAAE,QAAA,EAAEvH;UAAc;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENvJ,OAAA;MAAO8K,GAAG;MAAA7B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnJ,EAAA,CA3gCID,yBAAyB;AAAA4K,EAAA,GAAzB5K,yBAAyB;AA6gC/B,eAAeA,yBAAyB;AAAC,IAAA4K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}