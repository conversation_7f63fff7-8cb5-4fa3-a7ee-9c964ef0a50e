import React, { useState } from 'react';
import { 
  ChevronLeft, Send, Wand2, RefreshCw, 
  Users, MessageCircle, Calendar, ChevronDown,
  PanelRight
} from 'lucide-react';

const PublishStatusPage = () => {
  // 状态管理
  const [selectedImage, setSelectedImage] = useState(null);
  const [publishTitle, setPublishTitle] = useState('');
  const [publishContent, setPublishContent] = useState('');
  const [openingLine, setOpeningLine] = useState('');
  const [isPolishingTitle, setIsPolishingTitle] = useState(false);
  const [isRewritingTitle, setIsRewritingTitle] = useState(false);
  const [isPolishingContent, setIsPolishingContent] = useState(false);
  const [isRewritingContent, setIsRewritingContent] = useState(false);
  const [isPolishingOpening, setIsPolishingOpening] = useState(false);
  const [isRewritingOpening, setIsRewritingOpening] = useState(false);
  
  // 发布设置
  const [visibility, setVisibility] = useState('所有人');
  const [allowComments, setAllowComments] = useState(true);
  const [scheduledPublish, setScheduledPublish] = useState(false);
  const [setAsCharacterStatus, setSetAsCharacterStatus] = useState(true);
  
  // 模拟已有图片数据
  const existingImages = [
    {
      id: 1,
      url: '/api/placeholder/400/500',
      createdAt: '今天 09:42',
      isHD: true,
      isFavorite: true
    }
  ];

  // 格式化数字(例如: 1000 -> 1K)
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'W';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num;
  };

  // 模拟AI润色功能
  const polishText = (type) => {
    if (type === 'title') {
      setIsPolishingTitle(true);
      setTimeout(() => {
        setPublishTitle('探索未来科技的奇妙旅程');
        setIsPolishingTitle(false);
      }, 1000);
    } else if (type === 'content') {
      setIsPolishingContent(true);
      setTimeout(() => {
        setPublishContent('今天我们探索了量子计算的前沿领域，发现了一些令人惊叹的新可能性。科技的无限潜力总是让我感到敬畏，你对未来科技有什么期待？');
        setIsPolishingContent(false);
      }, 1000);
    } else if (type === 'opening') {
      setIsPolishingOpening(true);
      setTimeout(() => {
        setOpeningLine('嘿，很高兴见到你！我刚刚发现了一些超酷的科技新闻，想和你分享一下...');
        setIsPolishingOpening(false);
      }, 1000);
    }
  };
  
  // 模拟AI重写功能
  const rewriteText = (type) => {
    if (type === 'title') {
      setIsRewritingTitle(true);
      setTimeout(() => {
        setPublishTitle('未来已来：科技前沿的惊人突破');
        setIsRewritingTitle(false);
      }, 1000);
    } else if (type === 'content') {
      setIsRewritingContent(true);
      setTimeout(() => {
        setPublishContent('刚刚参加了一个关于量子计算的研讨会，科学家们展示了如何利用量子纠缠解决传统计算机无法处理的问题。这些突破可能彻底改变我们的生活方式，你认为量子计算会在哪些领域带来革命性变化？');
        setIsRewritingContent(false);
      }, 1000);
    } else if (type === 'opening') {
      setIsRewritingOpening(true);
      setTimeout(() => {
        setOpeningLine('你好啊！我是你的科技向导。刚刚看到了一些令人震惊的量子计算突破，忍不住想和你聊聊...');
        setIsRewritingOpening(false);
      }, 1000);
    }
  };
  
  // 处理发布动态
  const handlePublish = () => {
    // 这里可以添加发布逻辑
    alert('动态发布成功！');
  };

  return (
    <div className="fixed inset-0 flex justify-center items-start bg-gray-100 overflow-auto">
      <div className="w-full max-w-md bg-gray-50 min-h-screen flex flex-col relative" style={{height: '100%'}}>
        {/* 头部导航 */}
        <div className="sticky top-0 z-20 w-full bg-purple-700 px-4 py-3 flex items-center justify-between text-white">
          <div className="flex items-center">
            <button className="p-1 mr-2">
              <ChevronLeft size={20} className="text-white" />
            </button>
            <h1 className="text-lg font-bold">发布动态</h1>
          </div>
        </div>
        
        {/* 主内容区域 */}
        <div className="flex-1 w-full overflow-auto">
          <div className="bg-white w-full">
            <div className="p-4">
              {/* 预览图片 */}
              <div className="relative rounded-lg overflow-hidden mb-4">
                <div className="image-placeholder" style={{height: '300px'}}></div>
                
                {/* 图片标签 */}
                <div className="absolute top-2 left-2 flex space-x-1">
                  {existingImages[0].isHD && (
                    <div className="bg-indigo-600 text-white text-xs px-1.5 py-0.5 rounded">
                      HD
                    </div>
                  )}
                </div>
              </div>
              
              {/* 创建内容表单 */}
              <div className="mb-4">
                <div className="relative mb-3">
                  <input 
                    type="text"
                    value={publishTitle}
                    onChange={(e) => setPublishTitle(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-3 text-sm pr-20"
                    placeholder="添加标题（选填）"
                  />
                  <div className="absolute top-1/2 transform -translate-y-1/2 right-2 flex space-x-1">
                    <button
                      onClick={() => polishText('title')}
                      disabled={isPolishingTitle}
                      className={`p-1.5 rounded-full ${isPolishingTitle ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                      title="AI润色"
                    >
                      {isPolishingTitle ? <div className="w-4 h-4 border-2 border-gray-300 border-t-purple-600 rounded-full animate-spin"></div> : <Wand2 size={16} />}
                    </button>
                    <button
                      onClick={() => rewriteText('title')}
                      disabled={isRewritingTitle}
                      className={`p-1.5 rounded-full ${isRewritingTitle ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                      title="AI重写"
                    >
                      {isRewritingTitle ? <div className="w-4 h-4 border-2 border-gray-300 border-t-indigo-600 rounded-full animate-spin"></div> : <RefreshCw size={16} />}
                    </button>
                  </div>
                </div>
                
                <div className="relative mb-3">
                  <textarea
                    value={publishContent}
                    onChange={(e) => setPublishContent(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-3 h-24 text-sm"
                    placeholder="分享你的想法..."
                  ></textarea>
                  <div className="absolute right-2 bottom-2 flex space-x-1">
                    <button 
                      onClick={() => polishText('content')}
                      disabled={isPolishingContent}
                      className={`p-1.5 rounded-full ${isPolishingContent ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                      title="AI润色"
                    >
                      {isPolishingContent ? <div className="w-4 h-4 border-2 border-gray-300 border-t-purple-600 rounded-full animate-spin"></div> : <Wand2 size={16} />}
                    </button>
                    <button 
                      onClick={() => rewriteText('content')}
                      disabled={isRewritingContent}
                      className={`p-1.5 rounded-full ${isRewritingContent ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                      title="AI重写"
                    >
                      {isRewritingContent ? <div className="w-4 h-4 border-2 border-gray-300 border-t-indigo-600 rounded-full animate-spin"></div> : <RefreshCw size={16} />}
                    </button>
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1 mb-3">
                  {publishContent.length}/500 字符
                </div>
                
                {/* 新增的开场白设置 */}
                <div className="relative mb-3">
                  <textarea
                    value={openingLine}
                    onChange={(e) => setOpeningLine(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-3 h-20 text-sm"
                    placeholder="AI角色与用户聊天的开场白"
                  ></textarea>
                  <div className="absolute right-2 bottom-2 flex space-x-1">
                    <button 
                      onClick={() => polishText('opening')}
                      disabled={isPolishingOpening}
                      className={`p-1.5 rounded-full ${isPolishingOpening ? 'text-gray-300' : 'text-purple-600 hover:bg-purple-50'}`}
                      title="AI润色"
                    >
                      {isPolishingOpening ? <div className="w-4 h-4 border-2 border-gray-300 border-t-purple-600 rounded-full animate-spin"></div> : <Wand2 size={16} />}
                    </button>
                    <button 
                      onClick={() => rewriteText('opening')}
                      disabled={isRewritingOpening}
                      className={`p-1.5 rounded-full ${isRewritingOpening ? 'text-gray-300' : 'text-indigo-600 hover:bg-indigo-50'}`}
                      title="AI重写"
                    >
                      {isRewritingOpening ? <div className="w-4 h-4 border-2 border-gray-300 border-t-indigo-600 rounded-full animate-spin"></div> : <RefreshCw size={16} />}
                    </button>
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {openingLine.length}/200 字符
                </div>
              </div>
              
              {/* 发布设置 */}
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <h3 className="text-sm font-medium mb-2">发布设置</h3>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Users size={16} className="text-gray-500 mr-2" />
                      <span className="text-sm text-gray-700">谁可以看到</span>
                    </div>
                    <button className="flex items-center text-sm text-gray-700">
                      <span>{visibility}</span>
                      <ChevronDown size={16} className="ml-1" />
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <MessageCircle size={16} className="text-gray-500 mr-2" />
                      <span className="text-sm text-gray-700">允许评论</span>
                    </div>
                    <div 
                      className={`relative inline-block w-10 h-6 rounded-full ${allowComments ? 'bg-purple-600' : 'bg-gray-300'}`}
                      onClick={() => setAllowComments(!allowComments)}
                    >
                      <div 
                        className={`absolute ${allowComments ? 'right-1' : 'left-1'} top-1 w-4 h-4 rounded-full bg-white transition-all duration-200`}
                      ></div>
                    </div>
                  </div>

                  {/* 新增的角色动态设置 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <PanelRight size={16} className="text-gray-500 mr-2" />
                      <span className="text-sm text-gray-700">同时配置为角色状态</span>
                    </div>
                    <div 
                      className={`relative inline-block w-10 h-6 rounded-full ${setAsCharacterStatus ? 'bg-purple-600' : 'bg-gray-300'}`}
                      onClick={() => setSetAsCharacterStatus(!setAsCharacterStatus)}
                    >
                      <div 
                        className={`absolute ${setAsCharacterStatus ? 'right-1' : 'left-1'} top-1 w-4 h-4 rounded-full bg-white transition-all duration-200`}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Calendar size={16} className="text-gray-500 mr-2" />
                      <span className="text-sm text-gray-700">定时发布</span>
                    </div>
                    <div 
                      className={`relative inline-block w-10 h-6 rounded-full ${scheduledPublish ? 'bg-purple-600' : 'bg-gray-300'}`}
                      onClick={() => setScheduledPublish(!scheduledPublish)}
                    >
                      <div 
                        className={`absolute ${scheduledPublish ? 'right-1' : 'left-1'} top-1 w-4 h-4 rounded-full bg-white transition-all duration-200`}
                      ></div>
                    </div>
                  </div>
                
                </div>
              </div>
              
              {/* 发布按钮 */}
              <div className="pt-2">
                <button 
                  onClick={handlePublish}
                  className="w-full py-3.5 rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-medium flex items-center justify-center shadow-md hover:shadow-lg transition-shadow"
                >
                  <Send size={18} className="mr-2" />
                  发布动态
                </button>
              </div>
            </div>
          </div>
        </div>
      
        {/* 占位样式 */}
        <style jsx>{`
          .image-placeholder {
            background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
            background-size: 20px 20px;
            position: relative;
          }
          .image-placeholder::after {
            content: '图片预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: #666;
          }
        `}</style>
      </div>
    </div>
  );
};

export default PublishStatusPage;


// 给你的附件里的代码PublishStatusPage，是用来发动态的页面（具体来说是AI创建人用AI生成的角色图片、视频等素材来给AI角色发动态）。这个发动态页面交互不够好，老板希望模仿给你的附件2截图，即soul app发动态的交互来做，具体来说就是：