{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u89D2\\u8272\\u5347\\u7EA7\\u8BA1\\u5212\\\\code\\\\character-creator-view\\\\src\\\\StockChatInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { ChevronLeft, Send, Mic, Plus, TrendingUp, TrendingDown, BarChart3, Star, MessageCircle, DollarSign, AlertCircle, Calendar, Target, Shield, Sparkles, ChevronRight, Eye, RefreshCw, PieChart, Activity, Info, Search, Volume2, Share2, Edit3, Phone, Heart, MoreHorizontal, Smile, X } from 'lucide-react';\n\n// 导入角色图片\nimport characterAvatar from './assets/沈婉.png';\nimport characterAvatarWholeBody from './assets/沈婉全身照.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StockChatInterface = () => {\n  _s();\n  const messagesEndRef = useRef(null);\n  const [inputValue, setInputValue] = useState('');\n  const [showQuickActions, setShowQuickActions] = useState(true);\n  const [waitingForStockInput, setWaitingForStockInput] = useState(false);\n\n  // 模拟不同场景的初始消息\n  const getInitialMessages = context => {\n    const baseMessages = [{\n      id: 'welcome',\n      sender: 'ai',\n      content: '你好呀主人！我是沈婉，你的专属投资小助手~ 今天市场表现不错，有什么想了解的吗？',\n      time: '14:30',\n      type: 'text'\n    }];\n\n    // 根据不同场景添加特定消息\n    if (context === 'stock-discussion') {\n      return [...baseMessages, {\n        id: 'stock-1',\n        sender: 'ai',\n        content: '你想跟我聊聊这只股票么？',\n        time: '14:31',\n        type: 'text'\n      }, {\n        id: 'stock-2',\n        sender: 'ai',\n        content: '股票概览',\n        time: '14:31',\n        type: 'stock-overview',\n        data: {\n          name: '科技未来',\n          code: '000001',\n          currentPrice: 28.5,\n          change: 2.82,\n          changePercent: 11.01,\n          volume: '63.76万',\n          turnover: '6.49%',\n          analysis: '这只股票今天表现真的很棒呢！涨幅超过11%，成交量也在放大，看起来很有活力~ 有什么想深入了解的吗？'\n        }\n      }];\n    }\n    return baseMessages;\n  };\n  const [messages, setMessages] = useState(getInitialMessages('stock-discussion'));\n\n  // 快捷操作选项\n  const quickActions = [{\n    icon: TrendingUp,\n    text: '今日收益',\n    color: 'text-cyan-400'\n  }, {\n    icon: PieChart,\n    text: '持仓分析',\n    color: 'text-blue-400'\n  }, {\n    icon: Activity,\n    text: '热门板块',\n    color: 'text-purple-400'\n  }, {\n    icon: Search,\n    text: '个股分析',\n    color: 'text-pink-400'\n  }];\n\n  // 处理发送消息\n  const handleSend = () => {\n    if (inputValue.trim() === '') return;\n    const newUserMessage = {\n      id: Date.now().toString(),\n      sender: 'user',\n      content: inputValue,\n      time: new Date().toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit'\n      }),\n      type: 'text'\n    };\n    setMessages([...messages, newUserMessage]);\n\n    // 检查是否在等待股票输入\n    if (waitingForStockInput) {\n      const stockName = extractStockName(inputValue);\n      setTimeout(() => {\n        const aiResponse = {\n          id: Date.now().toString(),\n          sender: 'ai',\n          content: `好的收到，我已经在后台建了分析${stockName}的任务，待会报告出来了会通知你哦，请给我点时间哦~ 🔍`,\n          time: new Date().toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          }),\n          type: 'text'\n        };\n        setMessages(prev => [...prev, aiResponse]);\n      }, 1000);\n      setWaitingForStockInput(false);\n    } else {\n      // 模拟AI回复\n      setTimeout(() => {\n        const aiResponse = generateAIResponse(inputValue);\n        setMessages(prev => [...prev, aiResponse]);\n      }, 1000);\n    }\n    setInputValue('');\n  };\n\n  // 提取股票名称或代码\n  const extractStockName = input => {\n    const cleaned = input.replace(/[请帮忙给出这只股票的分析报告：]/g, '').trim();\n    return cleaned || '该股票';\n  };\n\n  // 生成AI回复（根据用户输入）\n  const generateAIResponse = userInput => {\n    const input = userInput.toLowerCase();\n    const time = new Date().toLocaleTimeString('zh-CN', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n\n    // 检查是否包含股票分析相关的关键词\n    if (input.includes('分析') && (input.includes('股票') || input.includes('个股'))) {\n      const stockName = extractStockName(userInput);\n      return {\n        id: Date.now().toString(),\n        sender: 'ai',\n        content: `好的收到，我已经在后台建了分析${stockName}的任务，待会报告出来了会通知你哦，请给我点时间哦~ 🔍`,\n        time,\n        type: 'text'\n      };\n    } else if (input.includes('收益') || input.includes('赚') || input.includes('亏')) {\n      return {\n        id: Date.now().toString(),\n        sender: 'ai',\n        content: '收益报告',\n        time,\n        type: 'profit-report',\n        data: {\n          todayProfit: 158,\n          todayProfitRate: 1.26,\n          weekProfit: 520,\n          weekProfitRate: 2.85,\n          monthProfit: 1680,\n          monthProfitRate: 5.42,\n          holdings: [{\n            name: '招商银行',\n            profit: 280,\n            rate: 3.2\n          }, {\n            name: '比亚迪',\n            profit: -120,\n            rate: -1.5\n          }, {\n            name: '宁德时代',\n            profit: 450,\n            rate: 5.8\n          }]\n        }\n      };\n    } else if (input.includes('买什么') || input.includes('推荐')) {\n      return {\n        id: Date.now().toString(),\n        sender: 'ai',\n        content: '为你精选了几只优质股票~',\n        time,\n        type: 'stock-recommendation',\n        data: [{\n          name: '腾讯控股',\n          code: '00700',\n          price: 328.40,\n          change: 1.25,\n          reason: '互联网龙头，长期看好',\n          riskLevel: '中'\n        }, {\n          name: '贵州茅台',\n          code: '600519',\n          price: 1680.00,\n          change: -0.31,\n          reason: '消费白马，调整即机会',\n          riskLevel: '低'\n        }]\n      };\n    } else {\n      return {\n        id: Date.now().toString(),\n        sender: 'ai',\n        content: '嗯嗯，我明白了！投资路上有我陪着你，咱们一起稳稳地前进~ 有什么具体想了解的吗？',\n        time,\n        type: 'text'\n      };\n    }\n  };\n\n  // 处理快捷操作点击\n  const handleQuickAction = action => {\n    let aiResponse;\n    const time = new Date().toLocaleTimeString('zh-CN', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n    switch (action.text) {\n      case '今日收益':\n        aiResponse = generateAIResponse('今日收益');\n        break;\n      case '持仓分析':\n        aiResponse = {\n          id: Date.now().toString(),\n          sender: 'ai',\n          content: '持仓分析',\n          time,\n          type: 'portfolio-analysis',\n          data: {\n            totalValue: 125680,\n            distribution: [{\n              sector: '金融',\n              percentage: 35,\n              color: '#3B82F6'\n            }, {\n              sector: '科技',\n              percentage: 25,\n              color: '#8B5CF6'\n            }, {\n              sector: '新能源',\n              percentage: 20,\n              color: '#10B981'\n            }, {\n              sector: '消费',\n              percentage: 15,\n              color: '#F59E0B'\n            }, {\n              sector: '现金',\n              percentage: 5,\n              color: '#6B7280'\n            }],\n            suggestion: '当前持仓比较均衡，金融板块占比较高，建议适当增加新能源配置~'\n          }\n        };\n        break;\n      case '热门板块':\n        aiResponse = {\n          id: Date.now().toString(),\n          sender: 'ai',\n          content: '今日热门板块',\n          time,\n          type: 'hot-sectors',\n          data: [{\n            name: '人工智能',\n            change: 3.25,\n            stocks: ['科大讯飞', '商汤科技']\n          }, {\n            name: '新能源车',\n            change: 2.18,\n            stocks: ['比亚迪', '理想汽车']\n          }, {\n            name: '半导体',\n            change: -1.32,\n            stocks: ['中芯国际', '华虹半导体']\n          }]\n        };\n        break;\n      case '个股分析':\n        aiResponse = {\n          id: Date.now().toString(),\n          sender: 'ai',\n          content: '请帮忙给出这只股票的分析报告：（输入股票名称或代码）',\n          time,\n          type: 'text'\n        };\n        setWaitingForStockInput(true);\n        break;\n      default:\n        return;\n    }\n    setMessages(prev => [...prev, aiResponse]);\n  };\n\n  // 自动滚动到底部\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n\n  // 渲染消息内容\n  const renderMessageContent = message => {\n    switch (message.type) {\n      case 'text':\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm\",\n          children: message.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 16\n        }, this);\n      case 'stock-overview':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"w-4 h-4 text-purple-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-white\",\n              children: \"\\u80A1\\u7968\\u6982\\u89C8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/10 backdrop-blur-md rounded-xl p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-white text-lg\",\n                  children: message.data.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: message.data.code\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-white\",\n                  children: [\"\\xA5\", message.data.currentPrice]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm font-medium ${message.data.change > 0 ? 'text-red-400' : 'text-green-400'}`,\n                  children: [message.data.change > 0 ? '+' : '', message.data.change, \" (\", message.data.change > 0 ? '+' : '', message.data.changePercent, \"%)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-3 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"\\u6210\\u4EA4\\u91CF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-white\",\n                  children: message.data.volume\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"\\u6362\\u624B\\u7387\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-white\",\n                  children: message.data.turnover\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-500/20 rounded-lg p-2 flex items-start gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                className: \"w-4 h-4 text-purple-300 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-purple-200 leading-relaxed\",\n                children: message.data.analysis\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this);\n      case 'market-analysis':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"w-4 h-4 text-purple-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-white\",\n              children: \"\\u5E02\\u573A\\u89E3\\u8BFB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/10 backdrop-blur-md rounded-xl p-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-300\",\n                children: \"\\u5E02\\u573A\\u60C5\\u7EEA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-purple-300\",\n                children: [message.data.sentiment, \" \\uD83D\\uDE1F\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-2 mb-3\",\n              children: message.data.mainIndex.map((index, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: index.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-white\",\n                  children: index.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xs ${index.change < 0 ? 'text-green-400' : 'text-red-400'}`,\n                  children: [index.change > 0 ? '+' : '', index.change, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-1 mb-2\",\n              children: message.data.hotSectors.map((sector, i) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-purple-500/30 text-purple-300 text-xs px-2 py-1 rounded-full backdrop-blur-sm\",\n                children: sector\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-300 leading-relaxed\",\n              children: message.data.analysis\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this);\n      case 'stock-analysis':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Target, {\n              className: \"w-4 h-4 text-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-white\",\n              children: \"\\u80A1\\u7968\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/10 backdrop-blur-md rounded-xl p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-white\",\n                  children: message.data.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: message.data.code\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg font-bold text-white\",\n                  children: [\"\\xA5\", message.data.currentPrice]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xs ${message.data.change < 0 ? 'text-green-400' : 'text-red-400'}`,\n                  children: [message.data.change > 0 ? '+' : '', message.data.change, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-red-500/30 text-red-300 text-xs px-2 py-1 rounded-full font-medium\",\n                children: message.data.suggestion\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-green-500/30 text-green-300 text-xs px-2 py-1 rounded-full\",\n                children: [\"\\u98CE\\u9669\", message.data.riskLevel]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-purple-500/30 text-purple-300 text-xs px-2 py-1 rounded-full\",\n                children: [\"\\u76EE\\u6807\\u4EF7 \\xA5\", message.data.targetPrice]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-4 gap-2 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"\\u5E02\\u76C8\\u7387\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-white\",\n                  children: message.data.pe\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"\\u5E02\\u51C0\\u7387\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-white\",\n                  children: message.data.pb\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"ROE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-white\",\n                  children: message.data.roe\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"\\u80A1\\u606F\\u7387\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-white\",\n                  children: message.data.dividendYield\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-black/20 rounded-lg p-3 mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-300 leading-relaxed\",\n                children: message.data.analysis\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: message.data.keyPoints.map((point, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 text-xs text-gray-300\",\n                children: [/*#__PURE__*/_jsxDEV(Star, {\n                  className: \"w-3 h-3 text-yellow-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: point\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs py-2 rounded-lg font-medium\",\n                children: \"\\u52A0\\u5165\\u81EA\\u9009\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex-1 bg-white/10 text-white text-xs py-2 rounded-lg font-medium border border-white/20\",\n                children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this);\n      case 'profit-report':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n              className: \"w-4 h-4 text-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-white\",\n              children: \"\\u6536\\u76CA\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/10 backdrop-blur-md rounded-xl p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-2 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"\\u4ECA\\u65E5\\u6536\\u76CA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm font-bold ${message.data.todayProfit > 0 ? 'text-red-400' : 'text-green-400'}`,\n                  children: [message.data.todayProfit > 0 ? '+' : '', \"\\xA5\", message.data.todayProfit]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: [message.data.todayProfitRate > 0 ? '+' : '', message.data.todayProfitRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"\\u672C\\u5468\\u6536\\u76CA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm font-bold ${message.data.weekProfit > 0 ? 'text-red-400' : 'text-green-400'}`,\n                  children: [\"+\\xA5\", message.data.weekProfit]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: [\"+\", message.data.weekProfitRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-black/30 rounded-lg p-2 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: \"\\u672C\\u6708\\u6536\\u76CA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm font-bold ${message.data.monthProfit > 0 ? 'text-red-400' : 'text-green-400'}`,\n                  children: [\"+\\xA5\", message.data.monthProfit]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400\",\n                  children: [\"+\", message.data.monthProfitRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-300 font-medium\",\n                children: \"\\u6301\\u4ED3\\u6536\\u76CATOP3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this), message.data.holdings.map((stock, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center bg-black/20 rounded-lg p-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs font-medium text-gray-200\",\n                  children: stock.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-xs ${stock.profit > 0 ? 'text-red-400' : 'text-green-400'}`,\n                    children: [stock.profit > 0 ? '+' : '', \"\\xA5\", stock.profit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-xs ${stock.rate > 0 ? 'text-red-400' : 'text-green-400'}`,\n                    children: [\"(\", stock.rate > 0 ? '+' : '', stock.rate, \"%)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-300 mt-3 text-center\",\n              children: \"\\u4E3B\\u4EBA\\uFF0C\\u867D\\u7136\\u6709\\u4E9B\\u6CE2\\u52A8\\uFF0C\\u4F46\\u6574\\u4F53\\u6536\\u76CA\\u8FD8\\u4E0D\\u9519\\u54E6\\uFF01\\u7EE7\\u7EED\\u4FDD\\u6301~ \\uD83D\\uDCAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this);\n      case 'stock-recommendation':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-4 h-4 text-purple-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-white\",\n              children: \"\\u7CBE\\u9009\\u63A8\\u8350\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: message.data.map((stock, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-md rounded-xl p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-medium text-white\",\n                    children: stock.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-400\",\n                    children: stock.code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-bold text-white\",\n                    children: [\"\\xA5\", stock.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-xs ${stock.change > 0 ? 'text-red-400' : 'text-green-400'}`,\n                    children: [stock.change > 0 ? '+' : '', stock.change, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-300 mb-2\",\n                children: stock.reason\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs px-2 py-0.5 rounded-full ${stock.riskLevel === '低' ? 'bg-green-500/30 text-green-300' : stock.riskLevel === '中' ? 'bg-yellow-500/30 text-yellow-300' : 'bg-red-500/30 text-red-300'}`,\n                  children: [\"\\u98CE\\u9669\", stock.riskLevel]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"text-xs text-purple-300 font-medium\",\n                  children: \"\\u67E5\\u770B\\u8BE6\\u60C5 \\u2192\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this)]\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this);\n      case 'portfolio-analysis':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(PieChart, {\n              className: \"w-4 h-4 text-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-white\",\n              children: \"\\u6301\\u4ED3\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/10 backdrop-blur-md rounded-xl p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400\",\n                children: \"\\u603B\\u8D44\\u4EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl font-bold text-white\",\n                children: [\"\\xA5\", message.data.totalValue.toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 mb-3\",\n              children: message.data.distribution.map((item, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 rounded-full\",\n                  style: {\n                    backgroundColor: item.color\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-300 flex-1\",\n                  children: item.sector\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs font-medium text-white\",\n                  children: [item.percentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-20 h-2 bg-black/30 rounded-full overflow-hidden\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-full transition-all duration-500\",\n                    style: {\n                      width: `${item.percentage}%`,\n                      backgroundColor: item.color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 21\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-black/20 rounded-lg p-2\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-300\",\n                children: message.data.suggestion\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this);\n      case 'hot-sectors':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Activity, {\n              className: \"w-4 h-4 text-red-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-white\",\n              children: \"\\u70ED\\u95E8\\u677F\\u5757\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: message.data.map((sector, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-md rounded-xl p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-white\",\n                  children: sector.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-sm font-bold ${sector.change > 0 ? 'text-red-400' : 'text-green-400'}`,\n                  children: [sector.change > 0 ? '+' : '', sector.change, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-1 flex-wrap\",\n                children: sector.stocks.map((stock, j) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs bg-white/10 px-2 py-0.5 rounded text-gray-300\",\n                  children: stock\n                }, j, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 19\n              }, this)]\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm\",\n          children: message.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col max-w-md w-full mx-auto bg-black h-screen overflow-hidden relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 z-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: characterAvatarWholeBody,\n        alt: \"\\u6C88\\u5A49\\u5168\\u8EAB\\u7167\",\n        className: \"w-full h-full object-cover object-center opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-b from-purple-900/80 via-black/60 to-black/90\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex flex-col h-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-black/20 backdrop-blur-sm text-white px-4 py-1 flex justify-between items-center text-xs\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"17:54\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: \"5G\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-white/80 rounded-sm px-1 text-black font-medium\",\n            children: \"91\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-b from-purple-900/40 to-transparent backdrop-blur-sm text-white p-4 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 -ml-2\",\n          children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"font-medium text-lg\",\n            children: \"\\u6C88\\u5A49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-2\",\n            children: /*#__PURE__*/_jsxDEV(Volume2, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"p-2\",\n            children: /*#__PURE__*/_jsxDEV(Share2, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto p-4 space-y-3\",\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `max-w-[85%] ${message.sender === 'user' ? 'order-2' : 'order-1'}`,\n            children: [message.sender === 'ai' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start gap-2 mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: characterAvatar,\n                alt: \"\\u6C88\\u5A49\",\n                className: \"w-8 h-8 rounded-full border border-purple-400/50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-purple-300\",\n                children: \"\\u6C88\\u5A49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `rounded-2xl p-3 ${message.sender === 'user' ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white ml-12' : 'bg-black/40 backdrop-blur-md text-white border border-purple-500/20'}`,\n              children: [renderMessageContent(message), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs mt-1 ${message.sender === 'user' ? 'text-white/70' : 'text-gray-400'}`,\n                children: message.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this), showQuickActions && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-black/60 backdrop-blur-md border-t border-purple-500/20 p-3 relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowQuickActions(false),\n          className: \"absolute top-2 right-2 p-1 rounded-full bg-white/10 hover:bg-white/20 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"w-4 h-4 text-gray-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-4 gap-2\",\n          children: quickActions.map((action, i) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleQuickAction(action),\n            className: \"bg-purple-500/20 hover:bg-purple-500/30 rounded-xl p-3 flex flex-col items-center gap-1 transition-colors backdrop-blur-sm border border-purple-500/20\",\n            children: [/*#__PURE__*/_jsxDEV(action.icon, {\n              className: `w-5 h-5 ${action.color}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-200\",\n              children: action.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 19\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 702,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-black/80 backdrop-blur-md border-t border-purple-500/20\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 flex items-center justify-between border-b border-purple-500/20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: characterAvatar,\n              alt: \"\\u6C88\\u5A49\",\n              className: \"w-8 h-8 rounded-full border border-purple-400/50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-white font-medium\",\n                children: \"\\u6C88\\u5A49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-purple-300\",\n                children: \"@\\u5C0F\\u59D1\\u5A18\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 text-gray-400 hover:text-white\",\n              children: /*#__PURE__*/_jsxDEV(Edit3, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 text-gray-400 hover:text-white\",\n              children: /*#__PURE__*/_jsxDEV(Phone, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 text-gray-400 hover:text-white\",\n              children: /*#__PURE__*/_jsxDEV(Heart, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 text-gray-400 hover:text-white\",\n              children: /*#__PURE__*/_jsxDEV(MoreHorizontal, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 729,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3\",\n          children: [!showQuickActions && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowQuickActions(true),\n            className: \"w-full mb-2 bg-purple-500/20 hover:bg-purple-500/30 rounded-lg py-2 text-xs text-purple-300 font-medium transition-colors backdrop-blur-sm border border-purple-500/20 flex items-center justify-center gap-1\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this), \"\\u663E\\u793A\\u80A1\\u7968\\u5FEB\\u6377\\u64CD\\u4F5C\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 bg-gray-800/50 rounded-full px-4 py-2 flex items-center backdrop-blur-sm border border-purple-500/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: inputValue,\n                onChange: e => setInputValue(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && handleSend(),\n                placeholder: waitingForStockInput ? \"请输入股票名称或代码...\" : \"自由输入...\",\n                className: \"flex-1 bg-transparent outline-none text-sm text-white placeholder-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"ml-2 text-gray-400 hover:text-white\",\n                children: /*#__PURE__*/_jsxDEV(Smile, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 15\n            }, this), inputValue.trim() ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSend,\n              className: \"p-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg transition-all\",\n              children: /*#__PURE__*/_jsxDEV(Send, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 rounded-full bg-gray-800/50 text-gray-400 hover:text-white backdrop-blur-sm border border-purple-500/20\",\n              children: /*#__PURE__*/_jsxDEV(Mic, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 619,\n    columnNumber: 5\n  }, this);\n};\n_s(StockChatInterface, \"JbkP2sl4FPjbLgEebXl0uux2wHc=\");\n_c = StockChatInterface;\nexport default StockChatInterface;\nvar _c;\n$RefreshReg$(_c, \"StockChatInterface\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "ChevronLeft", "Send", "Mic", "Plus", "TrendingUp", "TrendingDown", "BarChart3", "Star", "MessageCircle", "DollarSign", "AlertCircle", "Calendar", "Target", "Shield", "<PERSON><PERSON><PERSON>", "ChevronRight", "Eye", "RefreshCw", "<PERSON><PERSON><PERSON>", "Activity", "Info", "Search", "Volume2", "Share2", "Edit3", "Phone", "Heart", "MoreHorizontal", "Smile", "X", "<PERSON><PERSON><PERSON><PERSON>", "characterAvatarWholeBody", "jsxDEV", "_jsxDEV", "StockChatInterface", "_s", "messagesEndRef", "inputValue", "setInputValue", "showQuickActions", "setShowQuickActions", "waitingForStockInput", "setWaitingForStockInput", "getInitialMessages", "context", "baseMessages", "id", "sender", "content", "time", "type", "data", "name", "code", "currentPrice", "change", "changePercent", "volume", "turnover", "analysis", "messages", "setMessages", "quickActions", "icon", "text", "color", "handleSend", "trim", "newUserMessage", "Date", "now", "toString", "toLocaleTimeString", "hour", "minute", "stockName", "extractStockName", "setTimeout", "aiResponse", "prev", "generateAIResponse", "input", "cleaned", "replace", "userInput", "toLowerCase", "includes", "todayProfit", "todayProfitRate", "weekProfit", "weekProfitRate", "monthProfit", "monthProfitRate", "holdings", "profit", "rate", "price", "reason", "riskLevel", "handleQuickAction", "action", "totalValue", "distribution", "sector", "percentage", "suggestion", "stocks", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "renderMessageContent", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sentiment", "mainIndex", "map", "index", "i", "value", "hotSectors", "targetPrice", "pe", "pb", "roe", "dividendYield", "keyPoints", "point", "stock", "toLocaleString", "item", "style", "backgroundColor", "width", "j", "src", "alt", "ref", "onClick", "onChange", "e", "target", "onKeyPress", "key", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/角色升级计划/code/character-creator-view/src/StockChatInterface.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { \r\n  ChevronLeft, Send, Mic, Plus, TrendingUp, TrendingDown, \r\n  BarChart3, Star, MessageCircle, DollarSign, AlertCircle,\r\n  Calendar, Target, Shield, Sparkles, ChevronRight, Eye,\r\n  RefreshCw, PieChart, Activity, Info, Search, Volume2, Share2,\r\n  Edit3, Phone, Heart, MoreHorizontal, Smile, X\r\n} from 'lucide-react';\r\n\r\n// 导入角色图片\r\nimport characterAvatar from './assets/沈婉.png';\r\nimport characterAvatarWholeBody from './assets/沈婉全身照.png';\r\n\r\nconst StockChatInterface = () => {\r\n  const messagesEndRef = useRef(null);\r\n  const [inputValue, setInputValue] = useState('');\r\n  const [showQuickActions, setShowQuickActions] = useState(true);\r\n  const [waitingForStockInput, setWaitingForStockInput] = useState(false);\r\n  \r\n  // 模拟不同场景的初始消息\r\n  const getInitialMessages = (context) => {\r\n    const baseMessages = [\r\n      {\r\n        id: 'welcome',\r\n        sender: 'ai',\r\n        content: '你好呀主人！我是沈婉，你的专属投资小助手~ 今天市场表现不错，有什么想了解的吗？',\r\n        time: '14:30',\r\n        type: 'text'\r\n      }\r\n    ];\r\n\r\n    // 根据不同场景添加特定消息\r\n    if (context === 'stock-discussion') {\r\n      return [...baseMessages, {\r\n        id: 'stock-1',\r\n        sender: 'ai',\r\n        content: '你想跟我聊聊这只股票么？',\r\n        time: '14:31',\r\n        type: 'text'\r\n      }, {\r\n        id: 'stock-2',\r\n        sender: 'ai',\r\n        content: '股票概览',\r\n        time: '14:31',\r\n        type: 'stock-overview',\r\n        data: {\r\n          name: '科技未来',\r\n          code: '000001',\r\n          currentPrice: 28.5,\r\n          change: 2.82,\r\n          changePercent: 11.01,\r\n          volume: '63.76万',\r\n          turnover: '6.49%',\r\n          analysis: '这只股票今天表现真的很棒呢！涨幅超过11%，成交量也在放大，看起来很有活力~ 有什么想深入了解的吗？'\r\n        }\r\n      }];\r\n    }\r\n\r\n    return baseMessages;\r\n  };\r\n\r\n  const [messages, setMessages] = useState(getInitialMessages('stock-discussion'));\r\n\r\n  // 快捷操作选项\r\n  const quickActions = [\r\n    { icon: TrendingUp, text: '今日收益', color: 'text-cyan-400' },\r\n    { icon: PieChart, text: '持仓分析', color: 'text-blue-400' },\r\n    { icon: Activity, text: '热门板块', color: 'text-purple-400' },\r\n    { icon: Search, text: '个股分析', color: 'text-pink-400' },\r\n  ];\r\n\r\n  // 处理发送消息\r\n  const handleSend = () => {\r\n    if (inputValue.trim() === '') return;\r\n    \r\n    const newUserMessage = {\r\n      id: Date.now().toString(),\r\n      sender: 'user',\r\n      content: inputValue,\r\n      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),\r\n      type: 'text'\r\n    };\r\n    \r\n    setMessages([...messages, newUserMessage]);\r\n    \r\n    // 检查是否在等待股票输入\r\n    if (waitingForStockInput) {\r\n      const stockName = extractStockName(inputValue);\r\n      setTimeout(() => {\r\n        const aiResponse = {\r\n          id: Date.now().toString(),\r\n          sender: 'ai',\r\n          content: `好的收到，我已经在后台建了分析${stockName}的任务，待会报告出来了会通知你哦，请给我点时间哦~ 🔍`,\r\n          time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),\r\n          type: 'text'\r\n        };\r\n        setMessages(prev => [...prev, aiResponse]);\r\n      }, 1000);\r\n      setWaitingForStockInput(false);\r\n    } else {\r\n      // 模拟AI回复\r\n      setTimeout(() => {\r\n        const aiResponse = generateAIResponse(inputValue);\r\n        setMessages(prev => [...prev, aiResponse]);\r\n      }, 1000);\r\n    }\r\n    \r\n    setInputValue('');\r\n  };\r\n\r\n  // 提取股票名称或代码\r\n  const extractStockName = (input) => {\r\n    const cleaned = input.replace(/[请帮忙给出这只股票的分析报告：]/g, '').trim();\r\n    return cleaned || '该股票';\r\n  };\r\n\r\n  // 生成AI回复（根据用户输入）\r\n  const generateAIResponse = (userInput) => {\r\n    const input = userInput.toLowerCase();\r\n    const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });\r\n    \r\n    // 检查是否包含股票分析相关的关键词\r\n    if (input.includes('分析') && (input.includes('股票') || input.includes('个股'))) {\r\n      const stockName = extractStockName(userInput);\r\n      return {\r\n        id: Date.now().toString(),\r\n        sender: 'ai',\r\n        content: `好的收到，我已经在后台建了分析${stockName}的任务，待会报告出来了会通知你哦，请给我点时间哦~ 🔍`,\r\n        time,\r\n        type: 'text'\r\n      };\r\n    } else if (input.includes('收益') || input.includes('赚') || input.includes('亏')) {\r\n      return {\r\n        id: Date.now().toString(),\r\n        sender: 'ai',\r\n        content: '收益报告',\r\n        time,\r\n        type: 'profit-report',\r\n        data: {\r\n          todayProfit: 158,\r\n          todayProfitRate: 1.26,\r\n          weekProfit: 520,\r\n          weekProfitRate: 2.85,\r\n          monthProfit: 1680,\r\n          monthProfitRate: 5.42,\r\n          holdings: [\r\n            { name: '招商银行', profit: 280, rate: 3.2 },\r\n            { name: '比亚迪', profit: -120, rate: -1.5 },\r\n            { name: '宁德时代', profit: 450, rate: 5.8 }\r\n          ]\r\n        }\r\n      };\r\n    } else if (input.includes('买什么') || input.includes('推荐')) {\r\n      return {\r\n        id: Date.now().toString(),\r\n        sender: 'ai',\r\n        content: '为你精选了几只优质股票~',\r\n        time,\r\n        type: 'stock-recommendation',\r\n        data: [\r\n          {\r\n            name: '腾讯控股',\r\n            code: '00700',\r\n            price: 328.40,\r\n            change: 1.25,\r\n            reason: '互联网龙头，长期看好',\r\n            riskLevel: '中'\r\n          },\r\n          {\r\n            name: '贵州茅台',\r\n            code: '600519',\r\n            price: 1680.00,\r\n            change: -0.31,\r\n            reason: '消费白马，调整即机会',\r\n            riskLevel: '低'\r\n          }\r\n        ]\r\n      };\r\n    } else {\r\n      return {\r\n        id: Date.now().toString(),\r\n        sender: 'ai',\r\n        content: '嗯嗯，我明白了！投资路上有我陪着你，咱们一起稳稳地前进~ 有什么具体想了解的吗？',\r\n        time,\r\n        type: 'text'\r\n      };\r\n    }\r\n  };\r\n\r\n  // 处理快捷操作点击\r\n  const handleQuickAction = (action) => {\r\n    let aiResponse;\r\n    const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });\r\n    \r\n    switch (action.text) {\r\n      case '今日收益':\r\n        aiResponse = generateAIResponse('今日收益');\r\n        break;\r\n      case '持仓分析':\r\n        aiResponse = {\r\n          id: Date.now().toString(),\r\n          sender: 'ai',\r\n          content: '持仓分析',\r\n          time,\r\n          type: 'portfolio-analysis',\r\n          data: {\r\n            totalValue: 125680,\r\n            distribution: [\r\n              { sector: '金融', percentage: 35, color: '#3B82F6' },\r\n              { sector: '科技', percentage: 25, color: '#8B5CF6' },\r\n              { sector: '新能源', percentage: 20, color: '#10B981' },\r\n              { sector: '消费', percentage: 15, color: '#F59E0B' },\r\n              { sector: '现金', percentage: 5, color: '#6B7280' }\r\n            ],\r\n            suggestion: '当前持仓比较均衡，金融板块占比较高，建议适当增加新能源配置~'\r\n          }\r\n        };\r\n        break;\r\n      case '热门板块':\r\n        aiResponse = {\r\n          id: Date.now().toString(),\r\n          sender: 'ai',\r\n          content: '今日热门板块',\r\n          time,\r\n          type: 'hot-sectors',\r\n          data: [\r\n            { name: '人工智能', change: 3.25, stocks: ['科大讯飞', '商汤科技'] },\r\n            { name: '新能源车', change: 2.18, stocks: ['比亚迪', '理想汽车'] },\r\n            { name: '半导体', change: -1.32, stocks: ['中芯国际', '华虹半导体'] }\r\n          ]\r\n        };\r\n        break;\r\n      case '个股分析':\r\n        aiResponse = {\r\n          id: Date.now().toString(),\r\n          sender: 'ai',\r\n          content: '请帮忙给出这只股票的分析报告：（输入股票名称或代码）',\r\n          time,\r\n          type: 'text'\r\n        };\r\n        setWaitingForStockInput(true);\r\n        break;\r\n      default:\r\n        return;\r\n    }\r\n    \r\n    setMessages(prev => [...prev, aiResponse]);\r\n  };\r\n\r\n  // 自动滚动到底部\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  // 渲染消息内容\r\n  const renderMessageContent = (message) => {\r\n    switch (message.type) {\r\n      case 'text':\r\n        return <p className=\"text-sm\">{message.content}</p>;\r\n      \r\n      case 'stock-overview':\r\n        return (\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <BarChart3 className=\"w-4 h-4 text-purple-400\" />\r\n              <span className=\"font-medium text-white\">股票概览</span>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-3\">\r\n              {/* 股票基本信息 */}\r\n              <div className=\"flex justify-between items-start mb-3\">\r\n                <div>\r\n                  <h4 className=\"font-bold text-white text-lg\">{message.data.name}</h4>\r\n                  <p className=\"text-xs text-gray-400\">{message.data.code}</p>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  <p className=\"text-xl font-bold text-white\">¥{message.data.currentPrice}</p>\r\n                  <p className={`text-sm font-medium ${message.data.change > 0 ? 'text-red-400' : 'text-green-400'}`}>\r\n                    {message.data.change > 0 ? '+' : ''}{message.data.change} ({message.data.change > 0 ? '+' : ''}{message.data.changePercent}%)\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* 关键指标 */}\r\n              <div className=\"grid grid-cols-2 gap-3 mb-3\">\r\n                <div className=\"bg-black/30 rounded-lg p-2\">\r\n                  <p className=\"text-xs text-gray-400\">成交量</p>\r\n                  <p className=\"text-sm font-bold text-white\">{message.data.volume}</p>\r\n                </div>\r\n                <div className=\"bg-black/30 rounded-lg p-2\">\r\n                  <p className=\"text-xs text-gray-400\">换手率</p>\r\n                  <p className=\"text-sm font-bold text-white\">{message.data.turnover}</p>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* AI点评 */}\r\n              <div className=\"bg-purple-500/20 rounded-lg p-2 flex items-start gap-2\">\r\n                <Sparkles className=\"w-4 h-4 text-purple-300 mt-0.5 flex-shrink-0\" />\r\n                <p className=\"text-xs text-purple-200 leading-relaxed\">\r\n                  {message.data.analysis}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n      \r\n      case 'market-analysis':\r\n        return (\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <BarChart3 className=\"w-4 h-4 text-purple-400\" />\r\n              <span className=\"font-medium text-white\">市场解读</span>\r\n            </div>\r\n            \r\n            {/* 市场情绪 */}\r\n            <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-3\">\r\n              <div className=\"flex justify-between items-center mb-2\">\r\n                <span className=\"text-sm text-gray-300\">市场情绪</span>\r\n                <span className=\"text-sm font-medium text-purple-300\">\r\n                  {message.data.sentiment} 😟\r\n                </span>\r\n              </div>\r\n              \r\n              {/* 主要指数 */}\r\n              <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n                {message.data.mainIndex.map((index, i) => (\r\n                  <div key={i} className=\"bg-black/30 rounded-lg p-2 text-center\">\r\n                    <p className=\"text-xs text-gray-400\">{index.name}</p>\r\n                    <p className=\"text-sm font-bold text-white\">{index.value}</p>\r\n                    <p className={`text-xs ${index.change < 0 ? 'text-green-400' : 'text-red-400'}`}>\r\n                      {index.change > 0 ? '+' : ''}{index.change}%\r\n                    </p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              \r\n              {/* 热门板块 */}\r\n              <div className=\"flex gap-1 mb-2\">\r\n                {message.data.hotSectors.map((sector, i) => (\r\n                  <span key={i} className=\"bg-purple-500/30 text-purple-300 text-xs px-2 py-1 rounded-full backdrop-blur-sm\">\r\n                    {sector}\r\n                  </span>\r\n                ))}\r\n              </div>\r\n              \r\n              {/* 分析文字 */}\r\n              <p className=\"text-xs text-gray-300 leading-relaxed\">\r\n                {message.data.analysis}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        );\r\n      \r\n      case 'stock-analysis':\r\n        return (\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <Target className=\"w-4 h-4 text-blue-400\" />\r\n              <span className=\"font-medium text-white\">股票分析报告</span>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4\">\r\n              {/* 股票基本信息 */}\r\n              <div className=\"flex justify-between items-start mb-3\">\r\n                <div>\r\n                  <h4 className=\"font-bold text-white\">{message.data.name}</h4>\r\n                  <p className=\"text-xs text-gray-400\">{message.data.code}</p>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  <p className=\"text-lg font-bold text-white\">¥{message.data.currentPrice}</p>\r\n                  <p className={`text-xs ${message.data.change < 0 ? 'text-green-400' : 'text-red-400'}`}>\r\n                    {message.data.change > 0 ? '+' : ''}{message.data.change}%\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* 建议和风险 */}\r\n              <div className=\"flex gap-2 mb-3\">\r\n                <span className=\"bg-red-500/30 text-red-300 text-xs px-2 py-1 rounded-full font-medium\">\r\n                  {message.data.suggestion}\r\n                </span>\r\n                <span className=\"bg-green-500/30 text-green-300 text-xs px-2 py-1 rounded-full\">\r\n                  风险{message.data.riskLevel}\r\n                </span>\r\n                <span className=\"bg-purple-500/30 text-purple-300 text-xs px-2 py-1 rounded-full\">\r\n                  目标价 ¥{message.data.targetPrice}\r\n                </span>\r\n              </div>\r\n              \r\n              {/* 关键指标 */}\r\n              <div className=\"grid grid-cols-4 gap-2 mb-3\">\r\n                <div className=\"bg-black/30 rounded-lg p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-400\">市盈率</p>\r\n                  <p className=\"text-sm font-bold text-white\">{message.data.pe}</p>\r\n                </div>\r\n                <div className=\"bg-black/30 rounded-lg p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-400\">市净率</p>\r\n                  <p className=\"text-sm font-bold text-white\">{message.data.pb}</p>\r\n                </div>\r\n                <div className=\"bg-black/30 rounded-lg p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-400\">ROE</p>\r\n                  <p className=\"text-sm font-bold text-white\">{message.data.roe}</p>\r\n                </div>\r\n                <div className=\"bg-black/30 rounded-lg p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-400\">股息率</p>\r\n                  <p className=\"text-sm font-bold text-white\">{message.data.dividendYield}</p>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* 分析说明 */}\r\n              <div className=\"bg-black/20 rounded-lg p-3 mb-3\">\r\n                <p className=\"text-xs text-gray-300 leading-relaxed\">{message.data.analysis}</p>\r\n              </div>\r\n              \r\n              {/* 关键点 */}\r\n              <div className=\"space-y-1\">\r\n                {message.data.keyPoints.map((point, i) => (\r\n                  <div key={i} className=\"flex items-center gap-2 text-xs text-gray-300\">\r\n                    <Star className=\"w-3 h-3 text-yellow-400\" />\r\n                    <span>{point}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              \r\n              {/* 操作按钮 */}\r\n              <div className=\"flex gap-2 mt-3\">\r\n                <button className=\"flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs py-2 rounded-lg font-medium\">\r\n                  加入自选\r\n                </button>\r\n                <button className=\"flex-1 bg-white/10 text-white text-xs py-2 rounded-lg font-medium border border-white/20\">\r\n                  查看详情\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n      \r\n      case 'profit-report':\r\n        return (\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <DollarSign className=\"w-4 h-4 text-green-400\" />\r\n              <span className=\"font-medium text-white\">收益报告</span>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4\">\r\n              {/* 收益概览 */}\r\n              <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n                <div className=\"bg-black/30 rounded-lg p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-400\">今日收益</p>\r\n                  <p className={`text-sm font-bold ${message.data.todayProfit > 0 ? 'text-red-400' : 'text-green-400'}`}>\r\n                    {message.data.todayProfit > 0 ? '+' : ''}¥{message.data.todayProfit}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-400\">\r\n                    {message.data.todayProfitRate > 0 ? '+' : ''}{message.data.todayProfitRate}%\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-black/30 rounded-lg p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-400\">本周收益</p>\r\n                  <p className={`text-sm font-bold ${message.data.weekProfit > 0 ? 'text-red-400' : 'text-green-400'}`}>\r\n                    +¥{message.data.weekProfit}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-400\">+{message.data.weekProfitRate}%</p>\r\n                </div>\r\n                <div className=\"bg-black/30 rounded-lg p-2 text-center\">\r\n                  <p className=\"text-xs text-gray-400\">本月收益</p>\r\n                  <p className={`text-sm font-bold ${message.data.monthProfit > 0 ? 'text-red-400' : 'text-green-400'}`}>\r\n                    +¥{message.data.monthProfit}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-400\">+{message.data.monthProfitRate}%</p>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* 持仓收益 */}\r\n              <div className=\"space-y-2\">\r\n                <p className=\"text-xs text-gray-300 font-medium\">持仓收益TOP3</p>\r\n                {message.data.holdings.map((stock, i) => (\r\n                  <div key={i} className=\"flex justify-between items-center bg-black/20 rounded-lg p-2\">\r\n                    <span className=\"text-xs font-medium text-gray-200\">{stock.name}</span>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <span className={`text-xs ${stock.profit > 0 ? 'text-red-400' : 'text-green-400'}`}>\r\n                        {stock.profit > 0 ? '+' : ''}¥{stock.profit}\r\n                      </span>\r\n                      <span className={`text-xs ${stock.rate > 0 ? 'text-red-400' : 'text-green-400'}`}>\r\n                        ({stock.rate > 0 ? '+' : ''}{stock.rate}%)\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              \r\n              <p className=\"text-xs text-gray-300 mt-3 text-center\">\r\n                主人，虽然有些波动，但整体收益还不错哦！继续保持~ 💪\r\n              </p>\r\n            </div>\r\n          </div>\r\n        );\r\n      \r\n      case 'stock-recommendation':\r\n        return (\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <Sparkles className=\"w-4 h-4 text-purple-400\" />\r\n              <span className=\"font-medium text-white\">精选推荐</span>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              {message.data.map((stock, i) => (\r\n                <div key={i} className=\"bg-white/10 backdrop-blur-md rounded-xl p-3\">\r\n                  <div className=\"flex justify-between items-start mb-2\">\r\n                    <div>\r\n                      <h5 className=\"font-medium text-white\">{stock.name}</h5>\r\n                      <p className=\"text-xs text-gray-400\">{stock.code}</p>\r\n                    </div>\r\n                    <div className=\"text-right\">\r\n                      <p className=\"text-sm font-bold text-white\">¥{stock.price}</p>\r\n                      <p className={`text-xs ${stock.change > 0 ? 'text-red-400' : 'text-green-400'}`}>\r\n                        {stock.change > 0 ? '+' : ''}{stock.change}%\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  <p className=\"text-xs text-gray-300 mb-2\">{stock.reason}</p>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <span className={`text-xs px-2 py-0.5 rounded-full ${\r\n                      stock.riskLevel === '低' ? 'bg-green-500/30 text-green-300' :\r\n                      stock.riskLevel === '中' ? 'bg-yellow-500/30 text-yellow-300' : \r\n                      'bg-red-500/30 text-red-300'\r\n                    }`}>\r\n                      风险{stock.riskLevel}\r\n                    </span>\r\n                    <button className=\"text-xs text-purple-300 font-medium\">查看详情 →</button>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        );\r\n      \r\n      case 'portfolio-analysis':\r\n        return (\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <PieChart className=\"w-4 h-4 text-blue-400\" />\r\n              <span className=\"font-medium text-white\">持仓分析</span>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/10 backdrop-blur-md rounded-xl p-4\">\r\n              <div className=\"mb-3\">\r\n                <p className=\"text-xs text-gray-400\">总资产</p>\r\n                <p className=\"text-xl font-bold text-white\">¥{message.data.totalValue.toLocaleString()}</p>\r\n              </div>\r\n              \r\n              {/* 持仓分布 */}\r\n              <div className=\"space-y-2 mb-3\">\r\n                {message.data.distribution.map((item, i) => (\r\n                  <div key={i} className=\"flex items-center gap-2\">\r\n                    <div className=\"w-3 h-3 rounded-full\" style={{ backgroundColor: item.color }}></div>\r\n                    <span className=\"text-xs text-gray-300 flex-1\">{item.sector}</span>\r\n                    <span className=\"text-xs font-medium text-white\">{item.percentage}%</span>\r\n                    <div className=\"w-20 h-2 bg-black/30 rounded-full overflow-hidden\">\r\n                      <div \r\n                        className=\"h-full transition-all duration-500\" \r\n                        style={{ width: `${item.percentage}%`, backgroundColor: item.color }}\r\n                      ></div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              \r\n              <div className=\"bg-black/20 rounded-lg p-2\">\r\n                <p className=\"text-xs text-gray-300\">{message.data.suggestion}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n      \r\n      case 'hot-sectors':\r\n        return (\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <Activity className=\"w-4 h-4 text-red-400\" />\r\n              <span className=\"font-medium text-white\">热门板块</span>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              {message.data.map((sector, i) => (\r\n                <div key={i} className=\"bg-white/10 backdrop-blur-md rounded-xl p-3\">\r\n                  <div className=\"flex justify-between items-center mb-2\">\r\n                    <span className=\"font-medium text-white\">{sector.name}</span>\r\n                    <span className={`text-sm font-bold ${\r\n                      sector.change > 0 ? 'text-red-400' : 'text-green-400'\r\n                    }`}>\r\n                      {sector.change > 0 ? '+' : ''}{sector.change}%\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex gap-1 flex-wrap\">\r\n                    {sector.stocks.map((stock, j) => (\r\n                      <span key={j} className=\"text-xs bg-white/10 px-2 py-0.5 rounded text-gray-300\">\r\n                        {stock}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        );\r\n      \r\n      default:\r\n        return <p className=\"text-sm\">{message.content}</p>;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col max-w-md w-full mx-auto bg-black h-screen overflow-hidden relative\">\r\n      {/* 背景图片 */}\r\n      <div className=\"absolute inset-0 z-0\">\r\n        <img \r\n          src={characterAvatarWholeBody}\r\n          alt=\"沈婉全身照\" \r\n          className=\"w-full h-full object-cover object-center opacity-50\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-gradient-to-b from-purple-900/80 via-black/60 to-black/90\"></div>\r\n      </div>\r\n\r\n      {/* 内容区域 */}\r\n      <div className=\"relative z-10 flex flex-col h-full\">\r\n        {/* 状态栏 */}\r\n        <div className=\"bg-black/20 backdrop-blur-sm text-white px-4 py-1 flex justify-between items-center text-xs\">\r\n          <span>17:54</span>\r\n          <div className=\"flex items-center gap-1\">\r\n            <span className=\"text-xs\">5G</span>\r\n            <span className=\"bg-white/80 rounded-sm px-1 text-black font-medium\">91</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 顶部导航 */}\r\n        <div className=\"bg-gradient-to-b from-purple-900/40 to-transparent backdrop-blur-sm text-white p-4 flex items-center\">\r\n          <button className=\"p-2 -ml-2\">\r\n            <ChevronLeft className=\"w-6 h-6\" />\r\n          </button>\r\n          \r\n          <div className=\"flex-1 text-center\">\r\n            <h1 className=\"font-medium text-lg\">沈婉</h1>\r\n          </div>\r\n          \r\n          <div className=\"flex items-center gap-2\">\r\n            <button className=\"p-2\">\r\n              <Volume2 className=\"w-5 h-5\" />\r\n            </button>\r\n            <button className=\"p-2\">\r\n              <Share2 className=\"w-5 h-5\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 消息区域 */}\r\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-3\">\r\n          {/* 消息列表 */}\r\n          {messages.map((message) => (\r\n            <div\r\n              key={message.id}\r\n              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}\r\n            >\r\n              <div className={`max-w-[85%] ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>\r\n                {message.sender === 'ai' && (\r\n                  <div className=\"flex items-start gap-2 mb-1\">\r\n                    <img \r\n                      src={characterAvatar}\r\n                      alt=\"沈婉\" \r\n                      className=\"w-8 h-8 rounded-full border border-purple-400/50\"\r\n                    />\r\n                    <span className=\"text-xs text-purple-300\">沈婉</span>\r\n                  </div>\r\n                )}\r\n                \r\n                <div className={`rounded-2xl p-3 ${\r\n                  message.sender === 'user' \r\n                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white ml-12' \r\n                    : 'bg-black/40 backdrop-blur-md text-white border border-purple-500/20'\r\n                }`}>\r\n                  {renderMessageContent(message)}\r\n                  \r\n                  <div className={`text-xs mt-1 ${\r\n                    message.sender === 'user' ? 'text-white/70' : 'text-gray-400'\r\n                  }`}>\r\n                    {message.time}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n          <div ref={messagesEndRef} />\r\n        </div>\r\n\r\n        {/* 快捷操作区 */}\r\n        {showQuickActions && (\r\n          <div className=\"bg-black/60 backdrop-blur-md border-t border-purple-500/20 p-3 relative\">\r\n            {/* 关闭按钮 */}\r\n            <button\r\n              onClick={() => setShowQuickActions(false)}\r\n              className=\"absolute top-2 right-2 p-1 rounded-full bg-white/10 hover:bg-white/20 transition-colors\"\r\n            >\r\n              <X className=\"w-4 h-4 text-gray-300\" />\r\n            </button>\r\n            \r\n            <div className=\"grid grid-cols-4 gap-2\">\r\n              {quickActions.map((action, i) => (\r\n                <button\r\n                  key={i}\r\n                  onClick={() => handleQuickAction(action)}\r\n                  className=\"bg-purple-500/20 hover:bg-purple-500/30 rounded-xl p-3 flex flex-col items-center gap-1 transition-colors backdrop-blur-sm border border-purple-500/20\"\r\n                >\r\n                  <action.icon className={`w-5 h-5 ${action.color}`} />\r\n                  <span className=\"text-xs text-gray-200\">{action.text}</span>\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* 底部区域 */}\r\n        <div className=\"bg-black/80 backdrop-blur-md border-t border-purple-500/20\">\r\n          {/* 角色信息和操作按钮 */}\r\n          <div className=\"p-3 flex items-center justify-between border-b border-purple-500/20\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <img \r\n                src={characterAvatar}\r\n                alt=\"沈婉\" \r\n                className=\"w-8 h-8 rounded-full border border-purple-400/50\"\r\n              />\r\n              <div>\r\n                <p className=\"text-sm text-white font-medium\">沈婉</p>\r\n                <p className=\"text-xs text-purple-300\">@小姑娘</p>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center gap-3\">\r\n              <button className=\"p-2 text-gray-400 hover:text-white\">\r\n                <Edit3 className=\"w-5 h-5\" />\r\n              </button>\r\n              <button className=\"p-2 text-gray-400 hover:text-white\">\r\n                <Phone className=\"w-5 h-5\" />\r\n              </button>\r\n              <button className=\"p-2 text-gray-400 hover:text-white\">\r\n                <Heart className=\"w-5 h-5\" />\r\n              </button>\r\n              <button className=\"p-2 text-gray-400 hover:text-white\">\r\n                <MoreHorizontal className=\"w-5 h-5\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 输入区域 */}\r\n          <div className=\"p-3\">\r\n            {/* 当快捷操作栏关闭时，显示打开按钮 */}\r\n            {!showQuickActions && (\r\n              <button\r\n                onClick={() => setShowQuickActions(true)}\r\n                className=\"w-full mb-2 bg-purple-500/20 hover:bg-purple-500/30 rounded-lg py-2 text-xs text-purple-300 font-medium transition-colors backdrop-blur-sm border border-purple-500/20 flex items-center justify-center gap-1\"\r\n              >\r\n                <BarChart3 className=\"w-4 h-4\" />\r\n                显示股票快捷操作\r\n              </button>\r\n            )}\r\n            \r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"flex-1 bg-gray-800/50 rounded-full px-4 py-2 flex items-center backdrop-blur-sm border border-purple-500/20\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={inputValue}\r\n                  onChange={(e) => setInputValue(e.target.value)}\r\n                  onKeyPress={(e) => e.key === 'Enter' && handleSend()}\r\n                  placeholder={waitingForStockInput ? \"请输入股票名称或代码...\" : \"自由输入...\"}\r\n                  className=\"flex-1 bg-transparent outline-none text-sm text-white placeholder-gray-400\"\r\n                />\r\n                <button className=\"ml-2 text-gray-400 hover:text-white\">\r\n                  <Smile className=\"w-5 h-5\" />\r\n                </button>\r\n              </div>\r\n              \r\n              {inputValue.trim() ? (\r\n                <button \r\n                  onClick={handleSend}\r\n                  className=\"p-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:shadow-lg transition-all\"\r\n                >\r\n                  <Send className=\"w-5 h-5\" />\r\n                </button>\r\n              ) : (\r\n                <button className=\"p-2 rounded-full bg-gray-800/50 text-gray-400 hover:text-white backdrop-blur-sm border border-purple-500/20\">\r\n                  <Mic className=\"w-5 h-5\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StockChatInterface;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,WAAW,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,YAAY,EACtDC,SAAS,EAAEC,IAAI,EAAEC,aAAa,EAAEC,UAAU,EAAEC,WAAW,EACvDC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,GAAG,EACrDC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAC5DC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,cAAc,EAAEC,KAAK,EAAEC,CAAC,QACxC,cAAc;;AAErB;AACA,OAAOC,eAAe,MAAM,iBAAiB;AAC7C,OAAOC,wBAAwB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,cAAc,GAAGtC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM8C,kBAAkB,GAAIC,OAAO,IAAK;IACtC,MAAMC,YAAY,GAAG,CACnB;MACEC,EAAE,EAAE,SAAS;MACbC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,0CAA0C;MACnDC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE;IACR,CAAC,CACF;;IAED;IACA,IAAIN,OAAO,KAAK,kBAAkB,EAAE;MAClC,OAAO,CAAC,GAAGC,YAAY,EAAE;QACvBC,EAAE,EAAE,SAAS;QACbC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,cAAc;QACvBC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC,EAAE;QACDJ,EAAE,EAAE,SAAS;QACbC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE;UACJC,IAAI,EAAE,MAAM;UACZC,IAAI,EAAE,QAAQ;UACdC,YAAY,EAAE,IAAI;UAClBC,MAAM,EAAE,IAAI;UACZC,aAAa,EAAE,KAAK;UACpBC,MAAM,EAAE,QAAQ;UAChBC,QAAQ,EAAE,OAAO;UACjBC,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;IACJ;IAEA,OAAOd,YAAY;EACrB,CAAC;EAED,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC8C,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;;EAEhF;EACA,MAAMmB,YAAY,GAAG,CACnB;IAAEC,IAAI,EAAE3D,UAAU;IAAE4D,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC1D;IAAEF,IAAI,EAAE7C,QAAQ;IAAE8C,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACxD;IAAEF,IAAI,EAAE5C,QAAQ;IAAE6C,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC1D;IAAEF,IAAI,EAAE1C,MAAM;IAAE2C,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,CACvD;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI7B,UAAU,CAAC8B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAE9B,MAAMC,cAAc,GAAG;MACrBtB,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBxB,MAAM,EAAE,MAAM;MACdC,OAAO,EAAEX,UAAU;MACnBY,IAAI,EAAE,IAAIoB,IAAI,CAAC,CAAC,CAACG,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;MACpFxB,IAAI,EAAE;IACR,CAAC;IAEDW,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEQ,cAAc,CAAC,CAAC;;IAE1C;IACA,IAAI3B,oBAAoB,EAAE;MACxB,MAAMkC,SAAS,GAAGC,gBAAgB,CAACvC,UAAU,CAAC;MAC9CwC,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAG;UACjBhC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACzBxB,MAAM,EAAE,IAAI;UACZC,OAAO,EAAE,kBAAkB2B,SAAS,8BAA8B;UAClE1B,IAAI,EAAE,IAAIoB,IAAI,CAAC,CAAC,CAACG,kBAAkB,CAAC,OAAO,EAAE;YAAEC,IAAI,EAAE,SAAS;YAAEC,MAAM,EAAE;UAAU,CAAC,CAAC;UACpFxB,IAAI,EAAE;QACR,CAAC;QACDW,WAAW,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,UAAU,CAAC,CAAC;MAC5C,CAAC,EAAE,IAAI,CAAC;MACRpC,uBAAuB,CAAC,KAAK,CAAC;IAChC,CAAC,MAAM;MACL;MACAmC,UAAU,CAAC,MAAM;QACf,MAAMC,UAAU,GAAGE,kBAAkB,CAAC3C,UAAU,CAAC;QACjDwB,WAAW,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,UAAU,CAAC,CAAC;MAC5C,CAAC,EAAE,IAAI,CAAC;IACV;IAEAxC,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIK,KAAK,IAAK;IAClC,MAAMC,OAAO,GAAGD,KAAK,CAACE,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAChB,IAAI,CAAC,CAAC;IAC9D,OAAOe,OAAO,IAAI,KAAK;EACzB,CAAC;;EAED;EACA,MAAMF,kBAAkB,GAAII,SAAS,IAAK;IACxC,MAAMH,KAAK,GAAGG,SAAS,CAACC,WAAW,CAAC,CAAC;IACrC,MAAMpC,IAAI,GAAG,IAAIoB,IAAI,CAAC,CAAC,CAACG,kBAAkB,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;;IAE3F;IACA,IAAIO,KAAK,CAACK,QAAQ,CAAC,IAAI,CAAC,KAAKL,KAAK,CAACK,QAAQ,CAAC,IAAI,CAAC,IAAIL,KAAK,CAACK,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE;MAC1E,MAAMX,SAAS,GAAGC,gBAAgB,CAACQ,SAAS,CAAC;MAC7C,OAAO;QACLtC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBxB,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,kBAAkB2B,SAAS,8BAA8B;QAClE1B,IAAI;QACJC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MAAM,IAAI+B,KAAK,CAACK,QAAQ,CAAC,IAAI,CAAC,IAAIL,KAAK,CAACK,QAAQ,CAAC,GAAG,CAAC,IAAIL,KAAK,CAACK,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7E,OAAO;QACLxC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBxB,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,IAAI;QACJC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE;UACJoC,WAAW,EAAE,GAAG;UAChBC,eAAe,EAAE,IAAI;UACrBC,UAAU,EAAE,GAAG;UACfC,cAAc,EAAE,IAAI;UACpBC,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE,IAAI;UACrBC,QAAQ,EAAE,CACR;YAAEzC,IAAI,EAAE,MAAM;YAAE0C,MAAM,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAI,CAAC,EACxC;YAAE3C,IAAI,EAAE,KAAK;YAAE0C,MAAM,EAAE,CAAC,GAAG;YAAEC,IAAI,EAAE,CAAC;UAAI,CAAC,EACzC;YAAE3C,IAAI,EAAE,MAAM;YAAE0C,MAAM,EAAE,GAAG;YAAEC,IAAI,EAAE;UAAI,CAAC;QAE5C;MACF,CAAC;IACH,CAAC,MAAM,IAAId,KAAK,CAACK,QAAQ,CAAC,KAAK,CAAC,IAAIL,KAAK,CAACK,QAAQ,CAAC,IAAI,CAAC,EAAE;MACxD,OAAO;QACLxC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBxB,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,cAAc;QACvBC,IAAI;QACJC,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE,CACJ;UACEC,IAAI,EAAE,MAAM;UACZC,IAAI,EAAE,OAAO;UACb2C,KAAK,EAAE,MAAM;UACbzC,MAAM,EAAE,IAAI;UACZ0C,MAAM,EAAE,YAAY;UACpBC,SAAS,EAAE;QACb,CAAC,EACD;UACE9C,IAAI,EAAE,MAAM;UACZC,IAAI,EAAE,QAAQ;UACd2C,KAAK,EAAE,OAAO;UACdzC,MAAM,EAAE,CAAC,IAAI;UACb0C,MAAM,EAAE,YAAY;UACpBC,SAAS,EAAE;QACb,CAAC;MAEL,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLpD,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBxB,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,0CAA0C;QACnDC,IAAI;QACJC,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAIC,MAAM,IAAK;IACpC,IAAItB,UAAU;IACd,MAAM7B,IAAI,GAAG,IAAIoB,IAAI,CAAC,CAAC,CAACG,kBAAkB,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;IAE3F,QAAQ0B,MAAM,CAACpC,IAAI;MACjB,KAAK,MAAM;QACTc,UAAU,GAAGE,kBAAkB,CAAC,MAAM,CAAC;QACvC;MACF,KAAK,MAAM;QACTF,UAAU,GAAG;UACXhC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACzBxB,MAAM,EAAE,IAAI;UACZC,OAAO,EAAE,MAAM;UACfC,IAAI;UACJC,IAAI,EAAE,oBAAoB;UAC1BC,IAAI,EAAE;YACJkD,UAAU,EAAE,MAAM;YAClBC,YAAY,EAAE,CACZ;cAAEC,MAAM,EAAE,IAAI;cAAEC,UAAU,EAAE,EAAE;cAAEvC,KAAK,EAAE;YAAU,CAAC,EAClD;cAAEsC,MAAM,EAAE,IAAI;cAAEC,UAAU,EAAE,EAAE;cAAEvC,KAAK,EAAE;YAAU,CAAC,EAClD;cAAEsC,MAAM,EAAE,KAAK;cAAEC,UAAU,EAAE,EAAE;cAAEvC,KAAK,EAAE;YAAU,CAAC,EACnD;cAAEsC,MAAM,EAAE,IAAI;cAAEC,UAAU,EAAE,EAAE;cAAEvC,KAAK,EAAE;YAAU,CAAC,EAClD;cAAEsC,MAAM,EAAE,IAAI;cAAEC,UAAU,EAAE,CAAC;cAAEvC,KAAK,EAAE;YAAU,CAAC,CAClD;YACDwC,UAAU,EAAE;UACd;QACF,CAAC;QACD;MACF,KAAK,MAAM;QACT3B,UAAU,GAAG;UACXhC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACzBxB,MAAM,EAAE,IAAI;UACZC,OAAO,EAAE,QAAQ;UACjBC,IAAI;UACJC,IAAI,EAAE,aAAa;UACnBC,IAAI,EAAE,CACJ;YAAEC,IAAI,EAAE,MAAM;YAAEG,MAAM,EAAE,IAAI;YAAEmD,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM;UAAE,CAAC,EACxD;YAAEtD,IAAI,EAAE,MAAM;YAAEG,MAAM,EAAE,IAAI;YAAEmD,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM;UAAE,CAAC,EACvD;YAAEtD,IAAI,EAAE,KAAK;YAAEG,MAAM,EAAE,CAAC,IAAI;YAAEmD,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO;UAAE,CAAC;QAE7D,CAAC;QACD;MACF,KAAK,MAAM;QACT5B,UAAU,GAAG;UACXhC,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACzBxB,MAAM,EAAE,IAAI;UACZC,OAAO,EAAE,4BAA4B;UACrCC,IAAI;UACJC,IAAI,EAAE;QACR,CAAC;QACDR,uBAAuB,CAAC,IAAI,CAAC;QAC7B;MACF;QACE;IACJ;IAEAmB,WAAW,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,UAAU,CAAC,CAAC;EAC5C,CAAC;;EAED;EACA/E,SAAS,CAAC,MAAM;IACd4G,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC/C,QAAQ,CAAC,CAAC;EAEd,MAAM+C,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAxE,cAAc,CAACyE,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIC,OAAO,IAAK;IACxC,QAAQA,OAAO,CAAC/D,IAAI;MAClB,KAAK,MAAM;QACT,oBAAOjB,OAAA;UAAGiF,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAEF,OAAO,CAACjE;QAAO;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAErD,KAAK,gBAAgB;QACnB,oBACEtF,OAAA;UAAKiF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlF,OAAA;YAAKiF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ClF,OAAA,CAAC3B,SAAS;cAAC4G,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDtF,OAAA;cAAMiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAENtF,OAAA;YAAKiF,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAE1DlF,OAAA;cAAKiF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlF,OAAA;gBAAAkF,QAAA,gBACElF,OAAA;kBAAIiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACC;gBAAI;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrEtF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACE;gBAAI;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,GAAC,MAAC,EAACF,OAAO,CAAC9D,IAAI,CAACG,YAAY;gBAAA;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5EtF,OAAA;kBAAGiF,SAAS,EAAE,uBAAuBD,OAAO,CAAC9D,IAAI,CAACI,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;kBAAA4D,QAAA,GAChGF,OAAO,CAAC9D,IAAI,CAACI,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE0D,OAAO,CAAC9D,IAAI,CAACI,MAAM,EAAC,IAAE,EAAC0D,OAAO,CAAC9D,IAAI,CAACI,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE0D,OAAO,CAAC9D,IAAI,CAACK,aAAa,EAAC,IAC7H;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClF,OAAA;gBAAKiF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzClF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5CtF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACM;gBAAM;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzClF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5CtF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACO;gBAAQ;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACrElF,OAAA,CAACnB,QAAQ;gBAACoG,SAAS,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrEtF,OAAA;gBAAGiF,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACnDF,OAAO,CAAC9D,IAAI,CAACQ;cAAQ;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,iBAAiB;QACpB,oBACEtF,OAAA;UAAKiF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlF,OAAA;YAAKiF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ClF,OAAA,CAAC3B,SAAS;cAAC4G,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDtF,OAAA;cAAMiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAGNtF,OAAA;YAAKiF,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAC1DlF,OAAA;cAAKiF,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDlF,OAAA;gBAAMiF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDtF,OAAA;gBAAMiF,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAClDF,OAAO,CAAC9D,IAAI,CAACqE,SAAS,EAAC,eAC1B;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EACzCF,OAAO,CAAC9D,IAAI,CAACsE,SAAS,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,kBACnC3F,OAAA;gBAAaiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBAC7DlF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEQ,KAAK,CAACvE;gBAAI;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrDtF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEQ,KAAK,CAACE;gBAAK;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7DtF,OAAA;kBAAGiF,SAAS,EAAE,WAAWS,KAAK,CAACpE,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;kBAAA4D,QAAA,GAC7EQ,KAAK,CAACpE,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEoE,KAAK,CAACpE,MAAM,EAAC,GAC7C;gBAAA;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA,GALIK,CAAC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7BF,OAAO,CAAC9D,IAAI,CAAC2E,UAAU,CAACJ,GAAG,CAAC,CAACnB,MAAM,EAAEqB,CAAC,kBACrC3F,OAAA;gBAAciF,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,EACvGZ;cAAM,GADEqB,CAAC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEN,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNtF,OAAA;cAAGiF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACjDF,OAAO,CAAC9D,IAAI,CAACQ;YAAQ;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,gBAAgB;QACnB,oBACEtF,OAAA;UAAKiF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlF,OAAA;YAAKiF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ClF,OAAA,CAACrB,MAAM;cAACsG,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5CtF,OAAA;cAAMiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAENtF,OAAA;YAAKiF,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAE1DlF,OAAA;cAAKiF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlF,OAAA;gBAAAkF,QAAA,gBACElF,OAAA;kBAAIiF,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACC;gBAAI;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7DtF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACE;gBAAI;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,GAAC,MAAC,EAACF,OAAO,CAAC9D,IAAI,CAACG,YAAY;gBAAA;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5EtF,OAAA;kBAAGiF,SAAS,EAAE,WAAWD,OAAO,CAAC9D,IAAI,CAACI,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;kBAAA4D,QAAA,GACpFF,OAAO,CAAC9D,IAAI,CAACI,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE0D,OAAO,CAAC9D,IAAI,CAACI,MAAM,EAAC,GAC3D;gBAAA;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BlF,OAAA;gBAAMiF,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,EACpFF,OAAO,CAAC9D,IAAI,CAACsD;cAAU;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACPtF,OAAA;gBAAMiF,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,GAAC,cAC5E,EAACF,OAAO,CAAC9D,IAAI,CAAC+C,SAAS;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACPtF,OAAA;gBAAMiF,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,GAAC,yBAC3E,EAACF,OAAO,CAAC9D,IAAI,CAAC4E,WAAW;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClF,OAAA;gBAAKiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDlF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5CtF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAAC6E;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDlF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5CtF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAAC8E;gBAAE;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDlF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5CtF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAAC+E;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDlF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5CtF,OAAA;kBAAGiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACgF;gBAAa;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,iCAAiC;cAAAC,QAAA,eAC9ClF,OAAA;gBAAGiF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACQ;cAAQ;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBF,OAAO,CAAC9D,IAAI,CAACiF,SAAS,CAACV,GAAG,CAAC,CAACW,KAAK,EAAET,CAAC,kBACnC3F,OAAA;gBAAaiF,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBACpElF,OAAA,CAAC1B,IAAI;kBAAC2G,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CtF,OAAA;kBAAAkF,QAAA,EAAOkB;gBAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFZK,CAAC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BlF,OAAA;gBAAQiF,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,EAAC;cAEvH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtF,OAAA;gBAAQiF,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,EAAC;cAE7G;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,eAAe;QAClB,oBACEtF,OAAA;UAAKiF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlF,OAAA;YAAKiF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ClF,OAAA,CAACxB,UAAU;cAACyG,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDtF,OAAA;cAAMiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAENtF,OAAA;YAAKiF,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAE1DlF,OAAA;cAAKiF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClF,OAAA;gBAAKiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDlF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7CtF,OAAA;kBAAGiF,SAAS,EAAE,qBAAqBD,OAAO,CAAC9D,IAAI,CAACoC,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;kBAAA4B,QAAA,GACnGF,OAAO,CAAC9D,IAAI,CAACoC,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,MAAC,EAAC0B,OAAO,CAAC9D,IAAI,CAACoC,WAAW;gBAAA;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACJtF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACjCF,OAAO,CAAC9D,IAAI,CAACqC,eAAe,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEyB,OAAO,CAAC9D,IAAI,CAACqC,eAAe,EAAC,GAC7E;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDlF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7CtF,OAAA;kBAAGiF,SAAS,EAAE,qBAAqBD,OAAO,CAAC9D,IAAI,CAACsC,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;kBAAA0B,QAAA,GAAC,OAClG,EAACF,OAAO,CAAC9D,IAAI,CAACsC,UAAU;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACJtF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,GAAC,EAACF,OAAO,CAAC9D,IAAI,CAACuC,cAAc,EAAC,GAAC;gBAAA;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDlF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7CtF,OAAA;kBAAGiF,SAAS,EAAE,qBAAqBD,OAAO,CAAC9D,IAAI,CAACwC,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;kBAAAwB,QAAA,GAAC,OACnG,EAACF,OAAO,CAAC9D,IAAI,CAACwC,WAAW;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACJtF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,GAAC,EAACF,OAAO,CAAC9D,IAAI,CAACyC,eAAe,EAAC,GAAC;gBAAA;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlF,OAAA;gBAAGiF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAC5DN,OAAO,CAAC9D,IAAI,CAAC0C,QAAQ,CAAC6B,GAAG,CAAC,CAACY,KAAK,EAAEV,CAAC,kBAClC3F,OAAA;gBAAaiF,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBACnFlF,OAAA;kBAAMiF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEmB,KAAK,CAAClF;gBAAI;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvEtF,OAAA;kBAAKiF,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtClF,OAAA;oBAAMiF,SAAS,EAAE,WAAWoB,KAAK,CAACxC,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;oBAAAqB,QAAA,GAChFmB,KAAK,CAACxC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,MAAC,EAACwC,KAAK,CAACxC,MAAM;kBAAA;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACPtF,OAAA;oBAAMiF,SAAS,EAAE,WAAWoB,KAAK,CAACvC,IAAI,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;oBAAAoB,QAAA,GAAC,GAC/E,EAACmB,KAAK,CAACvC,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEuC,KAAK,CAACvC,IAAI,EAAC,IAC1C;kBAAA;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GATEK,CAAC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUN,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtF,OAAA;cAAGiF,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,sBAAsB;QACzB,oBACEtF,OAAA;UAAKiF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlF,OAAA;YAAKiF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ClF,OAAA,CAACnB,QAAQ;cAACoG,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDtF,OAAA;cAAMiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAENtF,OAAA;YAAKiF,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBF,OAAO,CAAC9D,IAAI,CAACuE,GAAG,CAAC,CAACY,KAAK,EAAEV,CAAC,kBACzB3F,OAAA;cAAaiF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAClElF,OAAA;gBAAKiF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDlF,OAAA;kBAAAkF,QAAA,gBACElF,OAAA;oBAAIiF,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAEmB,KAAK,CAAClF;kBAAI;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDtF,OAAA;oBAAGiF,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEmB,KAAK,CAACjF;kBAAI;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACNtF,OAAA;kBAAKiF,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlF,OAAA;oBAAGiF,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,GAAC,MAAC,EAACmB,KAAK,CAACtC,KAAK;kBAAA;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DtF,OAAA;oBAAGiF,SAAS,EAAE,WAAWoB,KAAK,CAAC/E,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;oBAAA4D,QAAA,GAC7EmB,KAAK,CAAC/E,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE+E,KAAK,CAAC/E,MAAM,EAAC,GAC7C;kBAAA;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtF,OAAA;gBAAGiF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEmB,KAAK,CAACrC;cAAM;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DtF,OAAA;gBAAKiF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDlF,OAAA;kBAAMiF,SAAS,EAAE,oCACfoB,KAAK,CAACpC,SAAS,KAAK,GAAG,GAAG,gCAAgC,GAC1DoC,KAAK,CAACpC,SAAS,KAAK,GAAG,GAAG,kCAAkC,GAC5D,4BAA4B,EAC3B;kBAAAiB,QAAA,GAAC,cACA,EAACmB,KAAK,CAACpC,SAAS;gBAAA;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACPtF,OAAA;kBAAQiF,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA,GAvBEK,CAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBN,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,oBAAoB;QACvB,oBACEtF,OAAA;UAAKiF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlF,OAAA;YAAKiF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ClF,OAAA,CAACf,QAAQ;cAACgG,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CtF,OAAA;cAAMiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAENtF,OAAA;YAAKiF,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAC1DlF,OAAA;cAAKiF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlF,OAAA;gBAAGiF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5CtF,OAAA;gBAAGiF,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,GAAC,MAAC,EAACF,OAAO,CAAC9D,IAAI,CAACkD,UAAU,CAACkC,cAAc,CAAC,CAAC;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BF,OAAO,CAAC9D,IAAI,CAACmD,YAAY,CAACoB,GAAG,CAAC,CAACc,IAAI,EAAEZ,CAAC,kBACrC3F,OAAA;gBAAaiF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBAC9ClF,OAAA;kBAAKiF,SAAS,EAAC,sBAAsB;kBAACuB,KAAK,EAAE;oBAAEC,eAAe,EAAEF,IAAI,CAACvE;kBAAM;gBAAE;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpFtF,OAAA;kBAAMiF,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEqB,IAAI,CAACjC;gBAAM;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnEtF,OAAA;kBAAMiF,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,GAAEqB,IAAI,CAAChC,UAAU,EAAC,GAAC;gBAAA;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1EtF,OAAA;kBAAKiF,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChElF,OAAA;oBACEiF,SAAS,EAAC,oCAAoC;oBAC9CuB,KAAK,EAAE;sBAAEE,KAAK,EAAE,GAAGH,IAAI,CAAChC,UAAU,GAAG;sBAAEkC,eAAe,EAAEF,IAAI,CAACvE;oBAAM;kBAAE;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GATEK,CAAC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtF,OAAA;cAAKiF,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzClF,OAAA;gBAAGiF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEF,OAAO,CAAC9D,IAAI,CAACsD;cAAU;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,aAAa;QAChB,oBACEtF,OAAA;UAAKiF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlF,OAAA;YAAKiF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ClF,OAAA,CAACd,QAAQ;cAAC+F,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CtF,OAAA;cAAMiF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAENtF,OAAA;YAAKiF,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBF,OAAO,CAAC9D,IAAI,CAACuE,GAAG,CAAC,CAACnB,MAAM,EAAEqB,CAAC,kBAC1B3F,OAAA;cAAaiF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAClElF,OAAA;gBAAKiF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDlF,OAAA;kBAAMiF,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAEZ,MAAM,CAACnD;gBAAI;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7DtF,OAAA;kBAAMiF,SAAS,EAAE,qBACfX,MAAM,CAAChD,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EACpD;kBAAA4D,QAAA,GACAZ,MAAM,CAAChD,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEgD,MAAM,CAAChD,MAAM,EAAC,GAC/C;gBAAA;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNtF,OAAA;gBAAKiF,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAClCZ,MAAM,CAACG,MAAM,CAACgB,GAAG,CAAC,CAACY,KAAK,EAAEM,CAAC,kBAC1B3G,OAAA;kBAAciF,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,EAC5EmB;gBAAK,GADGM,CAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEN,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GAfEK,CAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBN,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBAAOtF,OAAA;UAAGiF,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAEF,OAAO,CAACjE;QAAO;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;IACvD;EACF,CAAC;EAED,oBACEtF,OAAA;IAAKiF,SAAS,EAAC,kFAAkF;IAAAC,QAAA,gBAE/FlF,OAAA;MAAKiF,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnClF,OAAA;QACE4G,GAAG,EAAE9G,wBAAyB;QAC9B+G,GAAG,EAAC,gCAAO;QACX5B,SAAS,EAAC;MAAqD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACFtF,OAAA;QAAKiF,SAAS,EAAC;MAA+E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClG,CAAC,eAGNtF,OAAA;MAAKiF,SAAS,EAAC,oCAAoC;MAAAC,QAAA,gBAEjDlF,OAAA;QAAKiF,SAAS,EAAC,6FAA6F;QAAAC,QAAA,gBAC1GlF,OAAA;UAAAkF,QAAA,EAAM;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBtF,OAAA;UAAKiF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtClF,OAAA;YAAMiF,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnCtF,OAAA;YAAMiF,SAAS,EAAC,oDAAoD;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,sGAAsG;QAAAC,QAAA,gBACnHlF,OAAA;UAAQiF,SAAS,EAAC,WAAW;UAAAC,QAAA,eAC3BlF,OAAA,CAACjC,WAAW;YAACkH,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAETtF,OAAA;UAAKiF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjClF,OAAA;YAAIiF,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eAENtF,OAAA;UAAKiF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtClF,OAAA;YAAQiF,SAAS,EAAC,KAAK;YAAAC,QAAA,eACrBlF,OAAA,CAACX,OAAO;cAAC4F,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACTtF,OAAA;YAAQiF,SAAS,EAAC,KAAK;YAAAC,QAAA,eACrBlF,OAAA,CAACV,MAAM;cAAC2F,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,sCAAsC;QAAAC,QAAA,GAElDvD,QAAQ,CAAC8D,GAAG,CAAET,OAAO,iBACpBhF,OAAA;UAEEiF,SAAS,EAAE,QAAQD,OAAO,CAAClE,MAAM,KAAK,MAAM,GAAG,aAAa,GAAG,eAAe,EAAG;UAAAoE,QAAA,eAEjFlF,OAAA;YAAKiF,SAAS,EAAE,eAAeD,OAAO,CAAClE,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,EAAG;YAAAoE,QAAA,GAChFF,OAAO,CAAClE,MAAM,KAAK,IAAI,iBACtBd,OAAA;cAAKiF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClF,OAAA;gBACE4G,GAAG,EAAE/G,eAAgB;gBACrBgH,GAAG,EAAC,cAAI;gBACR5B,SAAS,EAAC;cAAkD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACFtF,OAAA;gBAAMiF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACN,eAEDtF,OAAA;cAAKiF,SAAS,EAAE,mBACdD,OAAO,CAAClE,MAAM,KAAK,MAAM,GACrB,+DAA+D,GAC/D,qEAAqE,EACxE;cAAAoE,QAAA,GACAH,oBAAoB,CAACC,OAAO,CAAC,eAE9BhF,OAAA;gBAAKiF,SAAS,EAAE,gBACdD,OAAO,CAAClE,MAAM,KAAK,MAAM,GAAG,eAAe,GAAG,eAAe,EAC5D;gBAAAoE,QAAA,EACAF,OAAO,CAAChE;cAAI;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA5BDN,OAAO,CAACnE,EAAE;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BZ,CACN,CAAC,eACFtF,OAAA;UAAK8G,GAAG,EAAE3G;QAAe;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,EAGLhF,gBAAgB,iBACfN,OAAA;QAAKiF,SAAS,EAAC,yEAAyE;QAAAC,QAAA,gBAEtFlF,OAAA;UACE+G,OAAO,EAAEA,CAAA,KAAMxG,mBAAmB,CAAC,KAAK,CAAE;UAC1C0E,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eAEnGlF,OAAA,CAACJ,CAAC;YAACqF,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAETtF,OAAA;UAAKiF,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EACpCrD,YAAY,CAAC4D,GAAG,CAAC,CAACtB,MAAM,EAAEwB,CAAC,kBAC1B3F,OAAA;YAEE+G,OAAO,EAAEA,CAAA,KAAM7C,iBAAiB,CAACC,MAAM,CAAE;YACzCc,SAAS,EAAC,wJAAwJ;YAAAC,QAAA,gBAElKlF,OAAA,CAACmE,MAAM,CAACrC,IAAI;cAACmD,SAAS,EAAE,WAAWd,MAAM,CAACnC,KAAK;YAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDtF,OAAA;cAAMiF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEf,MAAM,CAACpC;YAAI;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GALvDK,CAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMA,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDtF,OAAA;QAAKiF,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBAEzElF,OAAA;UAAKiF,SAAS,EAAC,qEAAqE;UAAAC,QAAA,gBAClFlF,OAAA;YAAKiF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClF,OAAA;cACE4G,GAAG,EAAE/G,eAAgB;cACrBgH,GAAG,EAAC,cAAI;cACR5B,SAAS,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACFtF,OAAA;cAAAkF,QAAA,gBACElF,OAAA;gBAAGiF,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpDtF,OAAA;gBAAGiF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtF,OAAA;YAAKiF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClF,OAAA;cAAQiF,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACpDlF,OAAA,CAACT,KAAK;gBAAC0F,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACTtF,OAAA;cAAQiF,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACpDlF,OAAA,CAACR,KAAK;gBAACyF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACTtF,OAAA;cAAQiF,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACpDlF,OAAA,CAACP,KAAK;gBAACwF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACTtF,OAAA;cAAQiF,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACpDlF,OAAA,CAACN,cAAc;gBAACuF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtF,OAAA;UAAKiF,SAAS,EAAC,KAAK;UAAAC,QAAA,GAEjB,CAAC5E,gBAAgB,iBAChBN,OAAA;YACE+G,OAAO,EAAEA,CAAA,KAAMxG,mBAAmB,CAAC,IAAI,CAAE;YACzC0E,SAAS,EAAC,+MAA+M;YAAAC,QAAA,gBAEzNlF,OAAA,CAAC3B,SAAS;cAAC4G,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oDAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAEDtF,OAAA;YAAKiF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClF,OAAA;cAAKiF,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAC1HlF,OAAA;gBACEiB,IAAI,EAAC,MAAM;gBACX2E,KAAK,EAAExF,UAAW;gBAClB4G,QAAQ,EAAGC,CAAC,IAAK5G,aAAa,CAAC4G,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBAC/CuB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAInF,UAAU,CAAC,CAAE;gBACrDoF,WAAW,EAAE7G,oBAAoB,GAAG,eAAe,GAAG,SAAU;gBAChEyE,SAAS,EAAC;cAA4E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACFtF,OAAA;gBAAQiF,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eACrDlF,OAAA,CAACL,KAAK;kBAACsF,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELlF,UAAU,CAAC8B,IAAI,CAAC,CAAC,gBAChBlC,OAAA;cACE+G,OAAO,EAAE9E,UAAW;cACpBgD,SAAS,EAAC,yGAAyG;cAAAC,QAAA,eAEnHlF,OAAA,CAAChC,IAAI;gBAACiH,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,gBAETtF,OAAA;cAAQiF,SAAS,EAAC,6GAA6G;cAAAC,QAAA,eAC7HlF,OAAA,CAAC/B,GAAG;gBAACgH,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpF,EAAA,CAtxBID,kBAAkB;AAAAqH,EAAA,GAAlBrH,kBAAkB;AAwxBxB,eAAeA,kBAAkB;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}