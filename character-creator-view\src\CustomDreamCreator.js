import React, { useState, useEffect, useRef } from 'react';
import {
  ChevronLeft, Film, Upload, Sparkles, X, Check, ArrowRight,
  Loader, Clock, Camera, Wand2, Play, Download, Star,
  FileImage, User, DollarSign, CheckCircle, AlertTriangle,
  Video, Volume2, MessageCircle, Share, Copy, Eye, Info,
  Heart, ArrowUpRight, Lock, Gift, ArrowDown, Plus, RefreshCw
} from 'lucide-react';

// 视频播放器占位组件
const VideoPlayerPreview = ({ style = "", isPlaying = false }) => (
<div className="w-full aspect-video bg-gradient-to-br from-purple-900 to-indigo-800 rounded-lg overflow-hidden relative flex items-center justify-center">
  <div className={`absolute inset-0 ${style || 'bg-black bg-opacity-10'} pattern-grid`}></div>
  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
    {!isPlaying && <Play size={48} className="text-white text-opacity-80 drop-shadow-lg" />}
  </div>
  <div className="absolute bottom-2 right-2 bg-purple-600 text-white text-xs px-2 py-1 rounded-full">定制梦境</div>
</div>
);

// 角色头像组件
const CharacterAvatar = ({ character, size = "lg", className = "" }) => {
const sizeClasses = {
  sm: "w-8 h-8",
  md: "w-12 h-12",
  lg: "w-16 h-16",
};

return (
  <div className={`rounded-full bg-gradient-to-br from-purple-300 to-purple-500 flex items-center justify-center ${sizeClasses[size]} ${className} overflow-hidden flex-shrink-0`}>
    <span className="text-white font-bold text-lg">{character?.name?.charAt(0) || 'A'}</span>
  </div>
);
};

// 任务状态组件
const TaskItem = ({ task }) => {
const statusColors = {
  waiting: "bg-blue-100 text-blue-600",
  processing: "bg-yellow-100 text-yellow-600",
  completed: "bg-green-100 text-green-600",
  failed: "bg-red-100 text-red-600",
};

const statusText = {
  waiting: "排队中",
  processing: "处理中",
  completed: "已完成",
  failed: "失败",
};

const getProgress = () => {
  if (task.status === 'waiting') return 0;
  if (task.status === 'completed') return 100;
  if (task.status === 'failed') return 100;
  return task.progress || 30; // 默认进度
};

return (
  <div className="bg-white rounded-lg p-3 shadow-sm mb-2">
    <div className="flex justify-between items-center mb-2">
      <div className="flex items-center">
        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-2">
          <Film size={18} className="text-purple-600" />
        </div>
        <div>
          <p className="text-sm font-medium">{task.title || "定制梦境视频"}</p>
          <p className="text-xs text-gray-500">{task.createdAt}</p>
        </div>
      </div>
      <div className={`px-2 py-1 rounded-full text-xs ${statusColors[task.status]}`}>
        {statusText[task.status]}
      </div>
    </div>

    {(task.status === 'waiting' || task.status === 'processing') && (
      <div className="w-full bg-gray-200 rounded-full h-1.5 mb-1">
        <div
          className={`h-full rounded-full ${task.status === 'waiting' ? 'bg-blue-500' : 'bg-yellow-500'}`}
          style={{ width: `${getProgress()}%`, transition: 'width 0.5s ease-in-out' }}
        ></div>
      </div>
    )}

    <div className="flex justify-between items-center">
      <span className="text-xs text-gray-500">
        {task.status === 'waiting' ? '预计等待: 15分钟' :
         task.status === 'processing' ? '处理中: 约10分钟' :
         task.status === 'completed' ? '可查看和下载' : '请尝试重新创建'}
      </span>
      {task.status === 'completed' && (
        <button className="text-xs flex items-center text-purple-600 px-2 py-1 rounded-full bg-purple-50">
          <Eye size={12} className="mr-1" /> 查看
        </button>
      )}
    </div>
  </div>
);
};

// 视频案例组件
const VideoExampleItem = ({ example, onPlay }) => (
<div className="flex-shrink-0 w-64 mr-3 bg-white rounded-lg overflow-hidden shadow-sm">
  <div className="relative">
    <div
      className="w-full h-36 bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center pattern-dots"
      style={{backgroundSize: '10px 10px'}}
    >
      <Film size={24} className="text-white text-opacity-50" />
    </div>
    <button
      onClick={() => onPlay(example)}
      className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 hover:bg-opacity-30 transition-all"
    >
      <div className="w-10 h-10 rounded-full bg-white bg-opacity-80 flex items-center justify-center">
        <Play size={20} className="text-purple-700 ml-1" />
      </div>
    </button>
    {example.isNew && (
      <div className="absolute top-2 right-2 bg-pink-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
        NEW
      </div>
    )}
  </div>
  <div className="p-3">
    <h3 className="text-sm font-medium mb-1">{example.title}</h3>
    <p className="text-xs text-gray-500 line-clamp-2">{example.description}</p>
  </div>
</div>
);

// 主组件
const CustomDreamCreator = ({ onBack, characterData = { name: "苏晚晚", profileImage: null }, userBalance = { starlightCoins: 5000 } }) => {
// 状态管理
const [showTaskList, setShowTaskList] = useState(false);
const [step, setStep] = useState('intro'); // intro, create, processing, complete
const [dreamDescription, setDreamDescription] = useState('');
const [isLoadingPreview, setIsLoadingPreview] = useState(false);
const [selectedCharacter, setSelectedCharacter] = useState(characterData);
const [includeUser, setIncludeUser] = useState(false);
const [userImage, setUserImage] = useState(null);
const [showExampleVideo, setShowExampleVideo] = useState(null);
// const [isEnhancingPrompt, setIsEnhancingPrompt] = useState(false); // MODIFIED: Removed as per requirement 2
// MODIFIED: Removed unused state variables
// const [resultVideo, setResultVideo] = useState(null); 
// const [showPreview, setShowPreview] = useState(false); 
const [showPhotoUploadModal, setShowPhotoUploadModal] = useState(false);
const [currentVideoIndex, setCurrentVideoIndex] = useState(0);

const descriptionRef = useRef(null);

// 假数据 - 在实际应用中会从API获取
const tasks = [
  { id: 1, title: "浪漫海滨漫步", status: "processing", progress: 45, createdAt: "5分钟前" },
  { id: 2, title: "未来城市冒险", status: "waiting", progress: 0, createdAt: "刚刚" },
  { id: 3, title: "梦幻森林相遇", status: "completed", createdAt: "昨天 14:30" },
];

// 模拟的多个生成视频数据
const generatedVideos = [
  {
    id: 1,
    title: "浪漫海滩日落",
    thumbnail: "https://via.placeholder.com/400x720?text=视频1",
    videoUrl: "#",
    style: "from-orange-500 to-pink-600",
    length: "5.2秒"
  },
  {
    id: 2,
    title: "星空下的邂逅",
    thumbnail: "https://via.placeholder.com/400x720?text=视频2",
    videoUrl: "#",
    style: "from-indigo-500 to-purple-600",
    length: "4.8秒"
  },
  {
    id: 3,
    title: "樱花雨下的漫步",
    thumbnail: "https://via.placeholder.com/400x720?text=视频3",
    videoUrl: "#",
    style: "from-pink-400 to-rose-600",
    length: "5.5秒"
  }
];

const videoExamples = [
  {
    id: 1,
    title: "未来都市邂逅",
    description: "赛博朋克风格的城市夜景中，角色在霓虹灯闪烁下等待，光影交错勾勒出神秘氛围。",
    coverUrl: "/api/placeholder/400/225?text=未来都市",
    videoUrl: "/api/placeholder/video",
    isNew: true
  },
  {
    id: 2,
    title: "海滨浪漫漫步",
    description: "金色夕阳下的海滩，温柔海浪轻抚沙滩，角色与用户十指相扣漫步，留下清晰脚印。",
    coverUrl: "/api/placeholder/400/225?text=海滨浪漫",
    videoUrl: "/api/placeholder/video",
  },
  {
    id: 3,
    title: "魔法森林奇遇",
    description: "梦幻森林中，角色被萤火虫环绕，魔法光芒照亮周围，神秘而美丽的异世界场景。",
    coverUrl: "/api/placeholder/400/225?text=魔法森林",
    videoUrl: "/api/placeholder/video",
  },
  {
    id: 4,
    title: "甜蜜咖啡馆约会",
    description: "阳光透过窗户洒在角色身上，咖啡香气弥漫，音乐轻柔，温馨互动的约会氛围。",
    coverUrl: "/api/placeholder/400/225?text=咖啡馆约会",
    videoUrl: "/api/placeholder/video",
    isNew: true
  }
];

// 场景推荐列表
const sceneRecommendations = [
  "未来科技城市夜景，霓虹灯光照亮雨中的街道，角色撑伞漫步其中，全息广告在背景中闪烁",
  "浪漫海滩日落，金色阳光洒在海面上，角色与我并肩而坐，海浪轻拍沙滩，微风拂过发丝",
  "魔幻森林场景，光芒从树缝间洒下，角色被萤火虫环绕，周围的植物似乎会发光，如梦如幻",
  "豪华古典城堡内部，烛光照亮华丽的舞厅，角色穿着正式服装邀请我共舞一曲",
  "星空下的草地野餐，银河清晰可见，角色与我躺在野餐布上仰望星空，周围点缀着温暖的提灯"
];

// 效果 - 自动调整文本区域高度
useEffect(() => {
  if (descriptionRef.current) {
    descriptionRef.current.style.height = 'auto';
    descriptionRef.current.style.height = `${descriptionRef.current.scrollHeight}px`;
  }
}, [dreamDescription]);

// 处理器 - 获取创作灵感
const handleGetInspiration = () => {
  const randomScene = sceneRecommendations[Math.floor(Math.random() * sceneRecommendations.length)];
  setDreamDescription(randomScene);
};

// MODIFIED: Removed handleEnhanceDescription as per requirement 2
// // 处理器 - 增强描述
// const handleEnhanceDescription = () => { ... };

// 处理器 - 添加用户照片
const handleAddUserPhoto = (source) => {
  if (source === 'camera') {
    alert('调用相机功能');
    setUserImage("camera_placeholder_image.jpg"); // Placeholder, actual image data/URL would be set
  } else if (source === 'gallery') {
    alert('调用图库功能');
    setUserImage("gallery_placeholder_image.jpg"); // Placeholder
  }
  setIncludeUser(true);
  setShowPhotoUploadModal(false); 
};

// 处理器 - 切换照片上传弹窗显示状态
const togglePhotoUploadModal = () => {
  setShowPhotoUploadModal(!showPhotoUploadModal);
};

// 处理器 - 切换视频
const handleChangeVideo = (index) => {
  setCurrentVideoIndex(index);
};

// 处理器 - 下一个视频
const handleNextVideo = () => {
  setCurrentVideoIndex((prev) => (prev === generatedVideos.length - 1 ? 0 : prev + 1));
};

// 处理器 - 上一个视频
const handlePrevVideo = () => {
  setCurrentVideoIndex((prev) => (prev === 0 ? generatedVideos.length - 1 : prev - 1));
};

// MODIFIED: Removed unused handleShowPreview function
// const handleShowPreview = () => { ... };

// 处理器 - 按返回按钮
const handleBackButton = () => {
  if (step === 'create') {
    if (window.confirm('返回将丢失当前编辑内容，确定返回吗？')) {
      setStep('intro');
    }
  } else if (step === 'complete') {
    setStep('create');
  } else {
    onBack?.();
  }
};

// 处理器 - 创建视频
const handleStartCreate = () => {
  console.log("点击开始创建按钮");
  if (!dreamDescription.trim()) {
    alert('请输入梦境描述');
    return;
  }

  if (userBalance.starlightCoins < 990) {
    alert('星光币余额不足，请先充值');
    return;
  }

  setIsLoadingPreview(true);

  setTimeout(() => {
    console.log("创建完成，切换到结果页");
    setCurrentVideoIndex(0); // Reset to first video for new result
    setStep('complete');
    setIsLoadingPreview(false);
  }, 2000);
};

// 处理器 - 创建定制视频 (This seems to be the old "intro" page's main CTA action handler)
const handleCreateCustomDream = () => {
  if (userBalance.starlightCoins < 990) {
    alert('星光币余额不足，请先充值');
    return;
  }
  setStep('processing');
  setTimeout(() => {
    setStep('intro');
    setShowTaskList(true); 
  }, 3000);
};

// 处理器 - 播放示例视频
const handlePlayExample = (example) => {
  setShowExampleVideo(example);
};

// 处理器 - 充值功能
const handleRecharge = () => {
  alert('打开充值界面');
};

// UI渲染 - 示例视频弹窗
const renderExampleVideoModal = () => (
  <div className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4">
    <div className="w-full max-w-md bg-gray-900 rounded-xl overflow-hidden">
      <div className="relative">
        <VideoPlayerPreview
          src={showExampleVideo.videoUrl}
          // coverUrl={showExampleVideo.coverUrl} // coverUrl not used by VideoPlayerPreview
          isPlaying={true}
        />
        <button
          onClick={() => setShowExampleVideo(null)}
          className="absolute top-2 right-2 w-8 h-8 rounded-full bg-black bg-opacity-50 flex items-center justify-center"
        >
          <X size={20} className="text-white" />
        </button>
      </div>
      <div className="p-4">
        <h3 className="text-white font-bold mb-1">{showExampleVideo.title}</h3>
        <p className="text-gray-300 text-sm">{showExampleVideo.description}</p>
      </div>
    </div>
  </div>
);

return (
  <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen">
    {/* 头部区域 */}
    <div className="bg-gradient-to-r from-purple-700 to-indigo-800 p-4 flex items-center justify-between text-white sticky top-0 z-20">
      <div className="flex items-center">
        <button
          onClick={handleBackButton}
          className="p-2 mr-2 rounded-full hover:bg-white hover:bg-opacity-20"
        >
          <ChevronLeft size={20} />
        </button>
        <h1 className="text-lg font-bold flex items-center">
          <Sparkles size={18} className="mr-2"/>
          定制梦境 <span className="ml-1 px-1.5 py-0.5 bg-pink-500 text-white text-xs rounded-full">高级</span>
        </h1>
      </div>
      {tasks.length > 0 && (
        <button
          onClick={() => setShowTaskList(!showTaskList)}
          className="relative p-2 rounded-full hover:bg-white hover:bg-opacity-20"
        >
          <Clock size={20} />
          <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 rounded-full text-xs flex items-center justify-center">
            {tasks.length}
          </span>
        </button>
      )}
    </div>

    {/* 任务列表 */}
    {showTaskList && (
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex justify-between items-center mb-3">
          <h2 className="font-bold text-gray-800">任务列表</h2>
          <button
            onClick={() => setShowTaskList(false)}
            className="text-gray-500 p-1"
          >
            <X size={18} />
          </button>
        </div>

        <div className="space-y-2">
          {tasks.map(task => (
            <TaskItem key={task.id} task={task} />
          ))}
        </div>
      </div>
    )}

    {/* 介绍页 */}
    {step === 'intro' && (
      <div className="flex-1 overflow-auto">
        {/* 顶部宣传区 */}
        <div className="relative">
          <div className="w-full h-40 bg-gradient-to-b from-purple-900 to-indigo-900 flex items-center justify-center overflow-hidden">
            <div className="absolute inset-0 pattern-stars opacity-40 bg-cover bg-center"></div>
            <div className="relative z-10 text-center px-6">
              <h2 className="text-2xl font-bold text-white mb-2">定制专属梦境视频</h2>
              <p className="text-white text-opacity-90 text-sm">将你与AI角色的幻想场景变为现实</p>
            </div>
          </div>
        </div>

        {/* 视频案例区域 */}
        <div className="px-4 py-5">
          <h2 className="text-lg font-bold text-gray-800 mb-3 flex items-center">
            <Film size={18} className="mr-2 text-purple-600" />
            梦境案例展示
          </h2>

          <div className="flex overflow-x-auto pb-4 hide-scrollbar">
            {videoExamples.map(example => (
              <VideoExampleItem
                key={example.id}
                example={example}
                onPlay={handlePlayExample}
              />
            ))}
          </div>
        </div>

        {/* 功能特点介绍 */}
        <div className="px-4 py-3 bg-white">
          <h2 className="text-lg font-bold text-gray-800 mb-3 flex items-center">
            <Sparkles size={18} className="mr-2 text-purple-600" />
            定制梦境特色
          </h2>

          <div className="grid grid-cols-2 gap-3 mb-5">
            <div className="p-3 bg-purple-50 rounded-lg">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                <Wand2 size={18} className="text-purple-600" />
              </div>
              <h3 className="font-semibold text-sm mb-1">高级渲染</h3>
              <p className="text-xs text-gray-600">采用最新大模型，渲染效果更真实细腻</p>
            </div>

            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                <User size={18} className="text-blue-600" />
              </div>
              <h3 className="font-semibold text-sm mb-1">用户合成</h3>
              <p className="text-xs text-gray-600">可选添加用户照片，创建互动场景</p>
            </div>

            <div className="p-3 bg-pink-50 rounded-lg">
              <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mb-2">
                <Heart size={18} className="text-pink-600" />
              </div>
              <h3 className="font-semibold text-sm mb-1">极致浪漫</h3>
              <p className="text-xs text-gray-600">专为情感场景优化，捕捉完美瞬间</p>
            </div>

            <div className="p-3 bg-amber-50 rounded-lg">
              <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center mb-2">
                <Download size={18} className="text-amber-600" />
              </div>
              <h3 className="font-semibold text-sm mb-1">永久收藏</h3>
              <p className="text-xs text-gray-600">可下载保存，永久珍藏美好回忆</p>
            </div>
          </div>



          {/* 用户评价 */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
              <MessageCircle size={16} className="mr-1 text-purple-600" />
              用户评价
            </h3>

            <div className="bg-white border border-gray-200 rounded-lg p-3 mb-2">
              <div className="flex items-start mb-2">
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-2">
                  <span className="text-green-700 font-medium text-sm">M</span>
                </div>
                <div>
                  <div className="flex items-center">
                    <h4 className="text-sm font-medium">梦想家Mia</h4>
                    <div className="flex ml-2">
                      {Array(5).fill(0).map((_, i) => (
                        <Star key={i} size={12} className="text-amber-400" fill="#f59e0b" />
                      ))}
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    太神奇了！生成的视频超出我预期，完美捕捉了我描述的场景，每一个细节都像是从我脑海中直接呈现出来的！值得每一分星光币！
                  </p>
                </div>
              </div>
              <div className="flex justify-end">
                <span className="text-xs text-gray-400">2天前</span>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <div className="flex items-start mb-2">
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                  <span className="text-blue-700 font-medium text-sm">K</span>
                </div>
                <div>
                  <div className="flex items-center">
                    <h4 className="text-sm font-medium">科技控Kevin</h4>
                    <div className="flex ml-2">
                      {Array(5).fill(0).map((_, i) => (
                        <Star key={i} size={12} className={`${i < 4 ? 'text-amber-400' : 'text-gray-300'}`} fill={i < 4 ? '#f59e0b' : '#d1d5db'} />
                      ))}
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    画面质量令人惊叹，我和AI角色的互动场景看起来非常自然！虽然渲染时间有点长，但成品值得等待。推荐给所有想要特别回忆的人。
                  </p>
                </div>
              </div>
              <div className="flex justify-end">
                <span className="text-xs text-gray-400">1周前</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )}

    {/* 创作页 - 优化后的部分 */}
    {step === 'create' && (
      <div className="flex-1 overflow-auto p-4 space-y-5">
        {/* 浪漫头部背景 */}
        <div className="relative overflow-hidden rounded-xl mb-2">
          <div className="w-full h-32 bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500">
            <div className="absolute inset-0 pattern-hearts opacity-20"></div>
          </div>
          <div className="absolute inset-0 flex flex-col items-center justify-center text-white px-6">
            <h2 className="text-xl font-bold mb-1">与{selectedCharacter.name}共创美好梦境</h2>
            <p className="text-sm text-center text-white text-opacity-90">定制专属视频，留下永恒浪漫瞬间</p>
          </div>
        </div>
        
        {/* MODIFIED: Added "定制梦境介绍" button as per requirement 1 */}
        <button
          onClick={() => setStep('intro')}
          className="w-full py-2.5 mb-2 bg-white border border-purple-200 text-purple-600 rounded-lg text-sm font-medium flex items-center justify-center shadow-sm hover:bg-purple-50 transition-colors"
        >
          <Info size={16} className="mr-1.5" />
          定制梦境介绍
        </button>


        {/* 角色与用户关系展示 - 新的浪漫互动设计 */}
        <div className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-xl p-4 shadow-sm border border-pink-100">
          <h3 className="text-sm font-medium text-pink-700 flex items-center mb-3">
            <Heart size={16} className="mr-1 text-pink-600" />
            浪漫互动场景
          </h3>

          <div className="relative">
            {/* 人物关系区域 */}
            <div className="flex items-center justify-center relative pb-4">
              {/* AI角色区域 */}
              <div className="flex flex-col items-center">
                <div className="relative">
                  <div className="w-20 h-20 rounded-full bg-gradient-to-br from-purple-300 to-purple-600 flex items-center justify-center overflow-hidden shadow-lg border-2 border-white">
                    <span className="text-white font-bold text-2xl">{selectedCharacter?.name?.charAt(0) || 'A'}</span>
                  </div>
                  {!includeUser && (
                    <div className="absolute -bottom-1 -right-1 bg-pink-500 p-1 rounded-full shadow-md">
                      <Heart size={16} fill="#ffffff" className="text-white" />
                    </div>
                  )}
                </div>
                <p className="text-sm font-medium mt-2 text-purple-800">{selectedCharacter.name}</p>
              </div>

              {/* 中间连接部分 */}
              {includeUser && userImage ? (
                <div className="mx-1 relative">
                  <div className="w-16 h-px bg-gradient-to-r from-purple-300 to-pink-300 relative">
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
                      <Heart size={14} fill="#ec4899" className="text-pink-500" />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="relative mx-3 flex flex-col items-center">
                  <div className="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center border border-dashed border-pink-300">
                    <Plus size={16} className="text-pink-500" />
                  </div>
                  <div className="w-24 text-center mt-1">
                    <span className="text-xs text-pink-600 font-medium">添加您的照片</span>
                  </div>
                </div>
              )}

              {/* 用户照片区域 */}
              {includeUser && userImage ? (
                <div className="flex flex-col items-center">
                  <div className="relative">
                    <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-300 to-blue-500 flex items-center justify-center overflow-hidden shadow-lg border-2 border-white">
                      <User size={30} className="text-white" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 bg-pink-500 p-1 rounded-full shadow-md">
                      <Heart size={16} fill="#ffffff" className="text-white" />
                    </div>
                  </div>
                  <p className="text-sm font-medium mt-2 text-blue-800">您</p>
                </div>
              ) : null}
            </div>
            
            {/* MODIFIED: Removed "优秀案例" section as per requirement 1 */}
            {/* 
            <div className="mt-2 bg-gradient-to-r from-indigo-100 to-purple-100 p-3 rounded-lg border border-purple-200">
              ...优秀案例内容...
            </div>
            */}


            {/* 用户照片上传选项 - 改为单个按钮和上拉弹窗 */}
            <div className="mt-4 flex justify-center">
              {!userImage ? (
                <div className="w-full">
                  <button
                    onClick={togglePhotoUploadModal}
                    className="w-full py-2.5 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg text-white text-sm flex items-center justify-center shadow-sm"
                  >
                    <Camera size={16} className="mr-1.5" /> 添加您的照片
                  </button>
                  <div className="flex justify-center mt-2">
                    <span className="text-xs text-pink-700 bg-pink-50 px-3 py-1 rounded-full">
                      上传照片，与{selectedCharacter.name}共创浪漫梦境
                    </span>
                  </div>
                </div>
              ) : (
                <div className="w-full bg-purple-50 rounded-lg p-3 border border-purple-100">
                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center mr-3 border-2 border-white">
                      <User size={20} className="text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-purple-800">您的照片已添加</p>
                      <p className="text-xs text-gray-600">将与{selectedCharacter.name}共同出现在视频中</p>
                    </div>
                    <button
                      onClick={() => {
                        setUserImage(null);
                        setIncludeUser(false);
                      }}
                      className="p-2 text-gray-500 hover:text-red-500"
                    >
                      <X size={18} />
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 浪漫氛围提示 */}
        <div className="bg-pink-50 rounded-lg p-3 border border-pink-100">
          <h3 className="text-sm font-medium text-pink-800 flex items-center mb-2">
            <Heart size={16} className="mr-1 text-pink-600" /> 浪漫场景灵感
          </h3>
          <p className="text-xs text-pink-700 mb-2">
            让{selectedCharacter.name}{includeUser ? "与您" : ""}沉浸在以下浪漫场景中，每个瞬间都将成为珍贵回忆：
          </p>
          <div className="flex flex-wrap gap-2 mb-2">
            <button className="px-3 py-1.5 bg-pink-100 text-pink-700 rounded-full text-xs" onClick={() => setDreamDescription("浪漫海滩日落，金色阳光洒在海面上，角色与我并肩而坐，海浪轻拍沙滩，微风拂过发丝")}>海滩日落</button>
            <button className="px-3 py-1.5 bg-purple-100 text-purple-700 rounded-full text-xs" onClick={() => setDreamDescription("星空下的草地野餐，银河清晰可见，角色与我躺在野餐布上仰望星空，周围点缀着温暖的提灯")}>星空野餐</button>
            <button className="px-3 py-1.5 bg-indigo-100 text-indigo-700 rounded-full text-xs" onClick={() => setDreamDescription("雪花飘落的冬日街道，角色与我手牵手漫步，周围是圣诞灯饰，温暖的灯光映照在彼此脸上")}>冬日漫步</button>
            <button className="px-3 py-1.5 bg-blue-100 text-blue-700 rounded-full text-xs" onClick={() => setDreamDescription("古典音乐会大厅里，角色穿着优雅的礼服，与我相视而笑，周围烛光摇曳，大提琴声悠扬")}>优雅音乐会</button>
          </div>
        </div>

        {/* 梦境描述 */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <label htmlFor="dreamDescription" className="text-sm font-medium text-gray-700 flex items-center">
              <Sparkles size={16} className="mr-1 text-purple-600" />
              梦境描述 <span className="text-red-500 ml-1">*</span>
            </label>
            <button
              onClick={handleGetInspiration}
              className="text-xs px-2 py-1 bg-amber-100 text-amber-700 rounded-full flex items-center"
            >
              <Sparkles size={14} className="mr-1" />
              {/* MODIFIED: Renamed "随机灵感" to "AI帮写" as per requirement 2 */}
              AI帮写
            </button>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <textarea
              ref={descriptionRef}
              id="dreamDescription"
              value={dreamDescription}
              onChange={(e) => setDreamDescription(e.target.value)}
              placeholder={`描述你希望与${selectedCharacter.name}${includeUser ? "一起" : ""}在视频中呈现的场景、氛围、互动方式...`}
              className="w-full p-3 min-h-[120px] text-sm border-none focus:ring-0 focus:outline-none resize-none"
              // MODIFIED: Removed disabled={isEnhancingPrompt} as isEnhancingPrompt is removed
            ></textarea>

            {/* MODIFIED: Removed "AI增强描述" button and its container as per requirement 2 */}
            {/* 
            <div className="flex border-t border-gray-100">
              ... AI增强描述 button ...
            </div>
            */}
          </div>

          <div className="mt-2 text-xs text-gray-500 flex items-center">
            <Info size={12} className="mr-1" />
            描述越详细，AI生成的视频场景越符合期望
          </div>
        </div>

        {/* 注意事项与提示 */}
        <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
          <h3 className="text-sm font-medium text-blue-800 flex items-center mb-1">
            <Info size={16} className="mr-1 text-blue-500" /> 创作提示
          </h3>
          <ul className="text-xs text-blue-700 space-y-1">
            <li className="flex items-start"><Check size={12} className="mr-1 mt-0.5 shrink-0"/> 定制梦境视频创建过程需要10-20分钟，完成后会通知您</li>
            <li className="flex items-start"><Check size={12} className="mr-1 mt-0.5 shrink-0"/> 描述中可包含场景、情感、服装、光线、视角等元素</li>
            <li className="flex items-start"><Check size={12} className="mr-1 mt-0.5 shrink-0"/> 视频生成后支持下载，永久保存珍贵回忆</li>
            {/* MODIFIED: Added pricing details to tips as per requirement 3 */}
            <li className="flex items-start"><Check size={12} className="mr-1 mt-0.5 shrink-0"/> 视频特点：高级渲染、5秒视频、包含音效</li>
          </ul>
        </div>
        
        {/* MODIFIED: Removed "价格信息" (定制浪漫回忆) module as per requirement 3 */}
        {/*
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg p-4 border border-pink-100">
          ... price info ...
        </div>
        */}


        <div className="h-20"></div> {/* 底部按钮间距 */}
      </div>
    )}

    {/* 完成页面 - 带轮播的视频预览 */}
    {step === 'complete' && (
      <div className="flex-1 overflow-auto p-4 space-y-4">
        <div className="bg-white rounded-xl p-4 shadow-md">
          {/* MODIFIED: Added shared title for the video set */}
          <h2 className="text-xl font-semibold text-gray-800 mb-4 text-center">
            {generatedVideos[0]?.title || "您的专属梦境视频"}
          </h2>

          {/* 视频轮播区域 */}
          <div className="relative">
            <div
              className="w-full rounded-lg overflow-hidden relative flex items-center justify-center"
              style={{ 
                height: "482px", 
                backgroundImage: `linear-gradient(to bottom right, var(--tw-gradient-stops))`,
                '--tw-gradient-from': generatedVideos[currentVideoIndex]?.style.split(' ')[0] || 'from-purple-900', // Fallback
                '--tw-gradient-to': generatedVideos[currentVideoIndex]?.style.split(' ')[1] || 'to-indigo-800', // Fallback
              }}
            >
              <div className="absolute inset-0 bg-black bg-opacity-30 pattern-grid"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Play size={48} className="text-white text-opacity-80 drop-shadow-lg" />
              </div>

              {/* MODIFIED: Video info - top area: removed individual title, kept index/total */}
              <div className="absolute top-2 left-2 right-2 flex justify-end items-center">
                {/* Individual title removed from here */}
                <div className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">
                  {currentVideoIndex + 1}/{generatedVideos.length}
                </div>
              </div>

              {/* MODIFIED: Bottom area - removed "XX秒 · 定制梦境" text */}
              {/* Original content removed:
              <div className="absolute bottom-3 right-3">
                <div className="bg-black bg-opacity-40 backdrop-blur-sm px-3 py-1 rounded-full text-white text-xs flex items-center">
                  <span>{generatedVideos[currentVideoIndex].length}</span>
                  <span className="mx-1">·</span>
                  <span>定制梦境</span>
                </div>
              </div>
              */}
            </div>

            {/* 左右切换按钮 */}
            <button
              onClick={handlePrevVideo}
              className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center"
            >
              <ChevronLeft size={20} className="text-white" />
            </button>

            <button
              onClick={handleNextVideo}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center"
            >
              <ArrowRight size={20} className="text-white" />
            </button>
          </div>

          {/* 轮播指示器 */}
          <div className="flex justify-center mt-3 space-x-1.5">
            {generatedVideos.map((_, index) => (
              <button
                key={index}
                onClick={() => handleChangeVideo(index)}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentVideoIndex
                    ? 'bg-purple-600 w-4'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          <div className="mt-5">
            <h3 className="text-sm font-medium text-gray-700 mb-1">梦境描述</h3>
            <div className="text-xs text-gray-600 bg-gray-50 rounded-lg p-3 relative">
              {dreamDescription}
              <button
                onClick={() => {
                  navigator.clipboard.writeText(dreamDescription);
                  alert('已复制到剪贴板');
                }}
                className="absolute top-2 right-2 text-purple-600 opacity-50 hover:opacity-100"
              >
                <Copy size={14} />
              </button>
            </div>
          </div>

          {/* MODIFIED: "所用照片" section */}
          <div className="mt-4">
            <h3 className="text-base font-semibold text-gray-700 mb-3">所用照片：</h3>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              {/* AI Character */}
              <div className="flex items-center">
                <CharacterAvatar character={selectedCharacter} size="md" className="mr-3" />
                <div>
                  <p className="text-sm font-medium text-purple-800">{selectedCharacter.name}</p>
                  <p className="text-xs text-gray-500">AI 角色</p>
                </div>
              </div>

              {/* User Photo (if uploaded) */}
              {userImage && (
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-lg overflow-hidden bg-gradient-to-br from-blue-300 to-blue-500 flex items-center justify-center mr-3 shadow">
                    <User size={24} className="text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-800">您的照片</p>
                    <p className="text-xs text-gray-500">已用于视频合成</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Video details summary */}
          <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mt-4">
            <span className="flex items-center"><Film size={12} className="mr-1 text-purple-500" /> 时长: {generatedVideos[currentVideoIndex]?.length || "N/A"}</span>
            <span className="flex items-center"><Volume2 size={12} className="mr-1 text-purple-500" /> 音效: 已包含</span>
            <span className="flex items-center"><Eye size={12} className="mr-1 text-purple-500" /> 质量: 高清</span>
            <span className="flex items-center"><User size={12} className="mr-1 text-purple-500" /> 角色: {selectedCharacter?.name}</span>
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
          <h3 className="text-sm font-medium text-blue-800 flex items-center mb-1">
            <Info size={16} className="mr-1 text-blue-500" /> 提示
          </h3>
          <p className="text-xs text-blue-700">您可以将视频下载保存，或分享给朋友。感谢使用定制梦境功能！</p>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => alert('分享到社交媒体')}
            className="flex-1 py-3 bg-purple-100 text-purple-700 rounded-lg flex items-center justify-center font-medium"
          >
            <Share size={18} className="mr-2" />
            分享
          </button>

          <button
            onClick={() => {
              setDreamDescription('');
              setUserImage(null);
              setIncludeUser(false);
              setStep('create');
            }}
            className="flex-1 py-3 bg-gray-100 text-gray-700 rounded-lg flex items-center justify-center font-medium"
          >
            <RefreshCw size={18} className="mr-2" />
            再创作一个
          </button>
        </div>
      </div>
    )}

    {/* 处理中页面 */}
    {step === 'processing' && (
      <div className="flex flex-col items-center justify-center py-8 px-4 flex-1">
        <div className="w-full max-w-sm mb-8">
          <div className="flex justify-between items-center mb-2">
            <div className="text-sm font-medium text-gray-700 flex items-center">
              <Loader size={16} className="mr-1 text-blue-600 animate-spin" />
              正在提交梦境创建任务
            </div>
            <div className="text-xs text-gray-500">准备中</div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-1 overflow-hidden">
            <div className="h-full bg-blue-600 transition-all duration-300 ease-out w-3/4"></div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-4 shadow-md w-full max-w-sm">
      <div className="bg-gray-100 rounded-lg overflow-hidden aspect-video mb-4 flex items-center justify-center relative loading-animation">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-purple-600 bg-white bg-opacity-80 px-3 py-2 rounded-lg shadow-sm">
                <Loader size={24} className="animate-spin mx-auto mb-2" />
                <p className="text-xs text-center">提交中...</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-3 text-xs text-blue-800">
            <div className="flex items-center mb-2">
              <MessageCircle size={14} className="mr-1 text-blue-600" />
              <span className="font-medium">创建说明</span>
            </div>
            <p className="mb-2">您的定制梦境视频正在提交到云端服务器，创建过程需要10-20分钟。</p>
            <p>完成后将通过消息通知您，您可以随时在任务列表中查看进度。</p>
          </div>
        </div>
      </div>
    )}

    {/* 底部按钮 - 在intro页面展示简洁版，在create页面展示详细版 */}
    {step === 'intro' && (
      <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
        <button
          onClick={() => setStep('create')} 
          className="w-full py-3 rounded-lg font-medium flex items-center justify-center bg-gradient-to-r from-purple-600 to-indigo-600 text-white"
        >
          <Sparkles size={18} className="mr-1" />
          开始创建梦境视频
        </button>
      </div>
    )}

    {step === 'create' && (
      <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
        <div className="mb-2 flex items-center justify-between">
          <div className="flex items-center">
            <DollarSign size={16} className="text-pink-500 mr-1" />
            <span className="text-sm font-medium">990星光币</span>
          </div>
          <div className="flex items-center">
            <div className="text-xs text-gray-600 mr-2">
              余额: <span className={`ml-1 font-medium ${userBalance.starlightCoins >= 990 ? 'text-green-600' : 'text-red-500'}`}>{userBalance.starlightCoins}</span>
            </div>
            <button
              onClick={handleRecharge}
              className="text-xs bg-blue-500 text-white px-3 py-1 rounded-full flex items-center shadow-sm"
            >
              <Plus size={10} className="mr-0.5" /> 充值
            </button>
          </div>
        </div>

        <button
          onClick={handleStartCreate}
          disabled={!dreamDescription.trim() || isLoadingPreview}
          className={`w-full py-3 rounded-lg font-medium flex items-center justify-center ${
            (dreamDescription.trim() && !isLoadingPreview)
              ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
              : 'bg-gray-300 text-gray-500'
          }`}
        >
          {isLoadingPreview ?
            <Loader size={18} className="mr-1 animate-spin" /> :
            <Heart size={18} className="mr-1" />
          }
          创建浪漫梦境
        </button>
      </div>
    )}

    {/* 示例视频弹窗 */}
    {showExampleVideo && renderExampleVideoModal()}

    {/* 照片上传弹窗 */}
    {showPhotoUploadModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center">
        <div className="w-full max-w-md bg-white rounded-t-xl p-4 pb-8 animate-slide-up">
          <div className="w-16 h-1 bg-gray-300 rounded-full mx-auto mb-5"></div>

          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">选择照片方式</h3>
            <button
              onClick={() => setShowPhotoUploadModal(false)}
              className="p-1 text-gray-500"
            >
              <X size={20} />
            </button>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <button
              onClick={() => handleAddUserPhoto('camera')}
              className="flex flex-col items-center justify-center p-4 bg-purple-50 rounded-xl border border-purple-100"
            >
              <div className="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                <Camera size={28} className="text-purple-600" />
              </div>
              <span className="text-sm font-medium text-purple-800">拍照</span>
              <span className="text-xs text-gray-500 mt-1">使用相机拍摄新照片</span>
            </button>

            <button
              onClick={() => handleAddUserPhoto('gallery')}
              className="flex flex-col items-center justify-center p-4 bg-pink-50 rounded-xl border border-pink-100"
            >
              <div className="w-14 h-14 bg-pink-100 rounded-full flex items-center justify-center mb-2">
                <FileImage size={28} className="text-pink-600" />
              </div>
              <span className="text-sm font-medium text-pink-800">相册选择</span>
              <span className="text-xs text-gray-500 mt-1">从相册中选择照片</span>
            </button>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg text-xs text-blue-800 flex items-start">
            <Info size={14} className="text-blue-500 mr-2 mt-0.5 shrink-0" />
            <p>您上传的照片仅用于生成视频，不会被用于其他用途，AI会自动提取您的面部特征并合成视频场景。</p>
          </div>
        </div>
      </div>
    )}

    {/* 样式 */}
    <style jsx>{`
      .toggle-checkbox:checked {
        transform: translate3d(100%, 0, 0);
      }

      .loading-animation {
        background: linear-gradient(-45deg, #e6e6e6, #f0f0f0, #ebebeb, #f5f5f5);
        background-size: 400% 400%;
        animation: gradient 2s ease infinite;
      }

      @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }

      .hide-scrollbar::-webkit-scrollbar {
        display: none;
      }

      .hide-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      .pattern-dots {
        background-image: radial-gradient(rgba(255, 255, 255, 0.4) 1px, transparent 1px);
        background-size: 20px 20px;
      }

      .pattern-grid {
        background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 20px 20px;
      }

      .pattern-stars {
        background-image: radial-gradient(white 1px, transparent 1px);
        background-size: 50px 50px;
      }

      .pattern-hearts {
        background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z' fill='rgba(255,255,255,0.1)'/%3E%3C/svg%3E");
        background-size: 24px 24px;
      }

      .animate-slide-up {
        animation: slideUp 0.3s ease-out forwards;
      }

      @keyframes slideUp {
        from {
          transform: translateY(100%);
        }
        to {
          transform: translateY(0);
        }
      }
    `}</style>
  </div>
);
};

export default CustomDreamCreator;