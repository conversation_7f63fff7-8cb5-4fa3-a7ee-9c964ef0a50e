import React, { useState, useEffect } from 'react';
import { ChevronRight, TrendingUp, TrendingDown, Sparkles, Heart, MessageCircle, Gift, AlertCircle, ChevronLeft, Coins, Trophy, Calendar, RefreshCw, Target, Zap, Star, X, Info, ArrowUp, BarChart3, Wallet, Battery } from 'lucide-react';

// 导入头像图片
import characterAvatar from './assets/沈婉.png';

const AICharacterInvestmentPage = () => {
  // 状态管理
  const [showIntro, setShowIntro] = useState(true);
  const [showInfo, setShowInfo] = useState(false);
  const [partyCoins, setPartyCoins] = useState(5280);
  const [virtualFund, setVirtualFund] = useState(12580); 
  const [experienceFund] = useState(10000); 
  const [principalFund, setPrincipalFund] = useState(0); 
  const [totalProfit, setTotalProfit] = useState(2580); 
  const [characterMood, setCharacterMood] = useState('happy');
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [bottomSheetType, setBottomSheetType] = useState('');
  const [showMessage, setShowMessage] = useState(false);
  const [messageContent, setMessageContent] = useState('');
  const [exchangeAmount, setExchangeAmount] = useState('');
  const [todayProfit, setTodayProfit] = useState(158);
  const [profitRate, setProfitRate] = useState(25.8);
  const [refreshing, setRefreshing] = useState(false);
  const [lastDailyCheckIn, setLastDailyCheckIn] = useState(null); 
  const [lastInteractionTime, setLastInteractionTime] = useState({}); 
  const [dailyCheckInDone, setDailyCheckInDone] = useState(false); 
  const [showVitalityTips, setShowVitalityTips] = useState(false);
  const [lastAdviceCategory, setLastAdviceCategory] = useState(null); 
  const [lastAdviceOption, setLastAdviceOption] = useState(null);
  const [currentAdviceCategory, setCurrentAdviceCategory] = useState('risk');

  
  // 理财活力相关状态
  const [vitalityPoints, setVitalityPoints] = useState(80); // 理财活力值
  const [lastVitalityUpdate, setLastVitalityUpdate] = useState(Date.now());
  const [isInvestmentActive, setIsInvestmentActive] = useState(true);
  
  // 持仓信息
  const [holdings, setHoldings] = useState([
    { name: '科技未来', code: '000001', shares: 200, profit: 580, profitRate: 5.8, price: 28.5 },
    { name: '消费龙头', code: '000002', shares: 300, profit: -120, profitRate: -2.1, price: 45.2 },
    { name: '新能源车', code: '000003', shares: 100, profit: 320, profitRate: 8.2, price: 156.8 }
  ]);
  
  // 角色信息
  const character = {
    name: '沈婉',
    style: '稳健型',
    description: '温柔体贴，偏好稳定增长',
    avatar: characterAvatar,
    investmentStyle: '稳健保守' // 投资风格
  };

  // 礼物列表（增加活力值）
  const giftList = [
    { id: 1, name: '鲜花', icon: '🌹', price: 10, vitality: 5, message: '谢谢主人的鲜花！我会更努力的！' },
    { id: 2, name: '咖啡', icon: '☕', price: 20, vitality: 10, message: '咖啡的香味让我精神百倍！' },
    { id: 3, name: '蛋糕', icon: '🎂', price: 50, vitality: 20, message: '哇！蛋糕好甜，就像主人一样！' },
    { id: 4, name: '钻戒', icon: '💍', price: 100, vitality: 40, message: '这...这是给我的吗？我会珍藏一生的！' },
    { id: 5, name: '游艇', icon: '🛥️', price: 200, vitality: 80, message: '天哪！主人太大方了！我们一起去看海吧！' },
    { id: 6, name: '城堡', icon: '🏰', price: 500, vitality: 200, message: '我...我不知道说什么好了，谢谢主人！❤️' }
  ];

  // 建议分类配置
const adviceCategories = {
  risk: {
    title: '风险偏好',
    icon: '🛡️',
    options: [
      { id: 'conservative', title: '保守稳健', desc: '安全第一' },
      { id: 'moderate', title: '稳中求进', desc: '平衡配置' },
      { id: 'aggressive', title: '激进冒险', desc: '高风险高收益' }
    ]
  },
  timing: {
    title: '操作节奏',
    icon: '⏱️',
    options: [
      { id: 'quick', title: '快进快出', desc: '把握短期机会' },
      { id: 'patient', title: '耐心等待', desc: '寻找最佳时机' },
      { id: 'steady', title: '稳定节奏', desc: '按计划执行' }
    ]
  },
  focus: {
    title: '关注重点',
    icon: '🎯',
    options: [
      { id: 'value', title: '价值为王', desc: '寻找低估资产' },
      { id: 'growth', title: '成长优先', desc: '追求高增长' },
      { id: 'trend', title: '趋势跟随', desc: '顺势而为' }
    ]
  }
};

  // 活力值消耗（每小时消耗2点）
  useEffect(() => {
    const timer = setInterval(() => {
      const now = Date.now();
      const hoursPassed = (now - lastVitalityUpdate) / (1000 * 60 * 60);
      const vitalityConsumed = Math.floor(hoursPassed * 2);
      
      if (vitalityConsumed > 0) {
        setVitalityPoints(prev => {
          const newVitality = Math.max(0, prev - vitalityConsumed);
          
          // 活力值低于30时，偶尔提醒
          if (newVitality < 30 && newVitality > 0 && Math.random() < 0.3) {
            const hints = [
              "主人，我感觉有点累了呢...",
              "最近工作有点辛苦，需要补充能量了~",
              "如果能喝杯咖啡就好了..."
            ];
            showCharacterMessage(hints[Math.floor(Math.random() * hints.length)]);
          }
          
          // 活力值为0，停止投资
          if (newVitality === 0 && isInvestmentActive) {
            setIsInvestmentActive(false);
            showCharacterMessage("我太累了，需要休息一下...投资暂时停止了");
            // 自动结算
            handleAutoSettle();
          }
          
          return newVitality;
        });
        setLastVitalityUpdate(now);
      }
    }, 60000); // 每分钟检查一次

    return () => clearInterval(timer);
  }, [lastVitalityUpdate, isInvestmentActive]);

  // 在组件加载时检查是否需要每日签到奖励
useEffect(() => {
  const checkDailyBonus = () => {
    const today = new Date().toDateString();
    const lastCheck = lastDailyCheckIn ? new Date(lastDailyCheckIn).toDateString() : null;
    
    if (lastCheck !== today) {
      // 每日首次查看，增加活力值
      setVitalityPoints(prev => Math.min(100, prev + 10)); // 每日查看+10活力
      setLastDailyCheckIn(Date.now());
      setDailyCheckInDone(true);
      showCharacterMessage("早上好主人！感谢您每天来看我，活力值+10！💕");
    }
  };
  
  checkDailyBonus();
}, []);

  // 自动结算
  const handleAutoSettle = () => {
    const totalWithdrawable = principalFund + totalProfit;
    const convertedCoins = Math.floor(totalWithdrawable / 100 * 0.95); // 扣除5%手续费
    setPartyCoins(prev => prev + convertedCoins);
    setPrincipalFund(0);
    setTotalProfit(0);
    setVirtualFund(experienceFund);
  };

  // 获取活力值颜色
  const getVitalityColor = () => {
    if (vitalityPoints > 60) return 'text-green-500';
    if (vitalityPoints > 30) return 'text-yellow-500';
    return 'text-red-500';
  };

  // 获取活力值图标
  const getVitalityIcon = () => {
    if (vitalityPoints > 60) return '⚡';
    if (vitalityPoints > 30) return '🔋';
    return '🪫';
  };

  // 显示角色消息
  const showCharacterMessage = (message) => {
    setMessageContent(message);
    setShowMessage(true);
    setTimeout(() => setShowMessage(false), 3000);
  };

  // 打开底部弹窗
  const openBottomSheet = (type) => {
    setBottomSheetType(type);
    setShowBottomSheet(true);
  };

  // 关闭底部弹窗
  const closeBottomSheet = () => {
    setShowBottomSheet(false);
    setExchangeAmount('');
  };

  // 处理兑换
  const handleExchange = (isIn) => {
    const amount = parseInt(exchangeAmount);
    if (!amount || amount <= 0) {
      showCharacterMessage("请输入正确的数量哦~");
      return;
    }
    
    if (isIn) {
      // 投入资金
      if (amount > partyCoins) {
        showCharacterMessage("主人的派对币不够哦~");
        return;
      }
      setPartyCoins(prev => prev - amount);
      setPrincipalFund(prev => prev + amount * 100); // 记录本金
      setVirtualFund(prev => prev + amount * 100);
      showCharacterMessage(`谢谢主人的信任！这${amount * 100}元就是我们的梦想启动金！`);
    } else {
      // 提取资产
      const totalWithdrawable = principalFund + totalProfit; // 可提取总额
      const maxExchange = Math.floor(totalWithdrawable / 100);
      
      if (amount > maxExchange) {
        showCharacterMessage(`主人，目前只能提取${maxExchange}个派对币哦~`);
        return;
      }
      
      const withdrawAmount = amount * 100;
      const fee = Math.floor(amount * 0.05);
      
      // 优先从收益中扣除，不足再从本金扣
      if (withdrawAmount <= totalProfit) {
        setTotalProfit(prev => prev - withdrawAmount);
      } else {
        const fromProfit = totalProfit;
        const fromPrincipal = withdrawAmount - fromProfit;
        setTotalProfit(0);
        setPrincipalFund(prev => prev - fromPrincipal);
      }
      
      setPartyCoins(prev => prev + amount - fee);
      setVirtualFund(prev => prev - withdrawAmount);
      showCharacterMessage(`成功提取${amount - fee}个派对币！手续费${fee}个`);
    }
    closeBottomSheet();
  };

  // 给角色建议
  const giveAdvice = (category, option) => {
  const lastAdvice = lastInteractionTime.advice || 0;
  const timeSinceLastAdvice = Date.now() - lastAdvice;
  
  // 生成动态回复
  const generateResponse = () => {
    const responses = {
      conservative: "明白了主人，我会更加谨慎，保护好我们的资产！",
      moderate: "好的！我会平衡风险和收益，稳步前进！",
      aggressive: "收到！让我们大胆一些，追求更高收益！",
      quick: "了解！我会加快操作节奏，抓住短期机会！",
      patient: "好的主人，我会耐心等待最佳时机！",
      steady: "明白了，我会保持稳定的节奏操作！",
      value: "收到！我会重点寻找被低估的优质资产！",
      growth: "好的！我会关注高成长潜力的标的！",
      trend: "明白了！我会顺势而为，跟随市场趋势！"
    };
    return responses[option.id] || "谢谢主人的建议，我会认真考虑的！";
  };
  
  if (timeSinceLastAdvice > 1800000) { // 30分钟冷却
    setVitalityPoints(prev => Math.min(100, prev + 3));
    setLastInteractionTime(prev => ({...prev, advice: Date.now()}));
    showCharacterMessage(generateResponse() + " (活力值+3)");
  } else {
    const remainingTime = Math.ceil((1800000 - timeSinceLastAdvice) / 60000);
    showCharacterMessage(generateResponse() + ` (${remainingTime}分钟后可再次获得活力值)`);
  }
  
  // 记录本次建议
  setLastAdviceCategory(category);
  setLastAdviceOption(option);
  
  closeBottomSheet();
};

  // 送礼物给角色
  const sendGift = (gift) => {
    if (partyCoins < gift.price) {
      showCharacterMessage("主人的派对币不够了呢~");
      return;
    }
    setPartyCoins(prev => prev - gift.price);
    setVitalityPoints(prev => Math.min(100, prev + gift.vitality)); // 最高100
    showCharacterMessage(gift.message);
    
    // 如果之前停止了投资，重新激活
    if (!isInvestmentActive && vitalityPoints + gift.vitality > 0) {
      setIsInvestmentActive(true);
      showCharacterMessage("充满活力！我又可以继续为主人理财了！");
    }
    
    closeBottomSheet();
  };

  // 下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
      const newProfit = Math.floor(Math.random() * 500) - 100;
      setTodayProfit(newProfit);
      showCharacterMessage("数据已更新，主人看看我们今天的表现吧！");
    }, 1000);
  };

  // 新手引导弹窗
  const IntroModal = () => (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4" onClick={() => setShowIntro(false)}>
      <div className="bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up" onClick={e => e.stopPropagation()}>
        <div className="text-center mb-4">
          <div className="w-20 h-20 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mx-auto mb-4 flex items-center justify-center">
            <Sparkles className="w-10 h-10 text-white" />
          </div>
          <h2 className="text-2xl font-bold mb-2">星伴生财</h2>
          <p className="text-gray-600">让{character.name}帮您打理派对币，一起创造财富！</p>
        </div>
        
        <div className="space-y-3 mb-6">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-purple-600 font-bold">1</span>
            </div>
            <div>
              <p className="font-medium">智能代理投资</p>
              <p className="text-sm text-gray-500">{character.name}会根据TA的性格特点，帮您自动投资理财</p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-pink-600 font-bold">2</span>
            </div>
            <div>
              <p className="font-medium">情感化互动</p>
              <p className="text-sm text-gray-500">每日查看投资汇报，给TA建议，一起经历财富起伏</p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-green-600 font-bold">3</span>
            </div>
            <div>
              <p className="font-medium">派对币增值</p>
              <p className="text-sm text-gray-500">投资收益可随时兑换回派对币（收取5%手续费）</p>
            </div>
          </div>
        </div>
        
        <div className="bg-yellow-50 rounded-2xl p-3 mb-6">
          <p className="text-sm text-yellow-800">
            <AlertCircle className="w-4 h-4 inline mr-1" />
            初始10000虚拟资金为体验金，不可提取，仅收益部分可兑换
          </p>
        </div>
        
        <button
          onClick={() => setShowIntro(false)}
          className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold"
        >
          开始体验
        </button>
      </div>
    </div>
  );

  // 功能说明弹窗
  const InfoModal = () => (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4" onClick={() => setShowInfo(false)}>
      <div className="bg-white rounded-3xl p-6 max-w-sm w-full animate-scale-up max-h-[80vh] overflow-y-auto" onClick={e => e.stopPropagation()}>
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold mb-2">功能说明</h2>
        </div>
        
        <div className="space-y-4 text-sm">
          <div>
            <h3 className="font-semibold mb-2 text-purple-600">🎯 怎么玩</h3>
            <p className="text-gray-600">
              1. 投入派对币给AI角色进行投资<br/>
              2. AI会根据其性格特点自动操作<br/>
              3. 查看每日投资报告了解收益<br/>
              4. 给AI建议影响投资策略<br/>
              5. 送礼物增加理财活力值
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2 text-pink-600">💰 资金说明</h3>
            <p className="text-gray-600">
              • 初始10000元为体验金，不可提取<br/>
              • 您投入的本金可随时提取<br/>
              • 投资收益可随时提取<br/>
              • 提取时收取5%手续费<br/>
              • 1派对币 = 100虚拟资金
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2 text-green-600">⚡ 理财活力</h3>
            <p className="text-gray-600">
              • AI需要理财活力才能持续投资<br/>
              • 活力值每小时消耗2点<br/>
              • 活力值为0时自动结算资产<br/>
              • 送礼物可以增加活力值<br/>
              • 每日查看星伴生财就加活力值<br/>
              • 操作资产投取也加活力值<br/>
              • 与虚拟角色进行投资互动也加活力值<br/>
              • 初始活力值为100
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2 text-orange-600">⚠️ 注意事项</h3>
            <p className="text-gray-600">
              • 这是虚拟投资游戏，非真实投资<br/>
              • 投资有风险，可能产生亏损<br/>
              • 合理安排派对币，享受游戏乐趣<br/>
              • 多与AI互动提升投资效果
            </p>
          </div>
        </div>
        
        <button
          onClick={() => setShowInfo(false)}
          className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold mt-6"
        >
          我知道了
        </button>
      </div>
    </div>
  );

  // 底部弹出框
  const BottomSheet = () => {
    const sheetContent = () => {
      switch (bottomSheetType) {
        case 'exchange-in':
          return (
            <>
              <h3 className="text-lg font-bold mb-4">投入派对币</h3>
              <div className="bg-gray-50 rounded-2xl p-4 mb-6">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">当前派对币</span>
                  <span className="font-semibold">{partyCoins}</span>
                </div>
              </div>
              <input
                type="number"
                value={exchangeAmount}
                onChange={(e) => setExchangeAmount(e.target.value)}
                placeholder="请输入投入数量"
                className="w-full px-4 py-3 border border-gray-200 rounded-2xl mb-3 text-lg"
              />
              <p className="text-sm text-gray-500 mb-4">1派对币 = 100虚拟资金</p>
              <div className="grid grid-cols-4 gap-2 mb-6">
                {[10, 50, 100, 200].map(amount => (
                  <button
                    key={amount}
                    onClick={() => setExchangeAmount(amount.toString())}
                    className="py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200"
                  >
                    {amount}
                  </button>
                ))}
              </div>
              <button
                onClick={() => handleExchange(true)}
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold"
              >
                确认投入
              </button>
            </>
          );
          
        case 'exchange-out':
          return (
            <>
              <h3 className="text-lg font-bold mb-4">提取资产</h3>
              <div className="bg-gray-50 rounded-2xl p-4 mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">体验金（不可提取）</span>
                  <span className="font-semibold text-gray-400">¥{experienceFund}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">投入本金</span>
                  <span className="font-semibold">¥{principalFund}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">投资收益</span>
                  <span className="font-semibold text-red-500">¥{totalProfit}</span>
                </div>
                <div className="border-t pt-2 mt-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-800 font-medium">可提取总额</span>
                    <span className="font-bold text-lg">¥{principalFund + totalProfit}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>可兑换派对币</span>
                    <span>{Math.floor((principalFund + totalProfit) / 100)}</span>
                  </div>
                </div>
              </div>
              
              <input
                type="number"
                value={exchangeAmount}
                onChange={(e) => setExchangeAmount(e.target.value)}
                placeholder="请输入提取的派对币数量"
                className="w-full px-4 py-3 border border-gray-200 rounded-2xl mb-3 text-lg"
              />
              <p className="text-sm text-gray-500 mb-4">将收取5%手续费</p>
              
              <div className="grid grid-cols-3 gap-2 mb-6">
                <button
                  onClick={() => setExchangeAmount(Math.floor(totalProfit / 100).toString())}
                  className="py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200"
                  disabled={totalProfit <= 0}
                >
                  仅收益
                </button>
                <button
                  onClick={() => setExchangeAmount(Math.floor(principalFund / 100).toString())}
                  className="py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200"
                  disabled={principalFund <= 0}
                >
                  仅本金
                </button>
                <button
                  onClick={() => setExchangeAmount(Math.floor((principalFund + totalProfit) / 100).toString())}
                  className="py-2 bg-gray-100 rounded-xl text-sm font-medium active:bg-gray-200"
                  disabled={principalFund + totalProfit <= 0}
                >
                  全部
                </button>
              </div>
              
              <button
                onClick={() => handleExchange(false)}
                className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 rounded-2xl font-semibold"
              >
                确认提取
              </button>
            </>
          );
          
        case 'compass':
            return (
                <>
                <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-bold">星语罗盘</h3>
                    <span className="text-sm text-gray-500">{new Date().toLocaleDateString()}</span>
                </div>
                
                {/* 大盘气象解读 */}
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-4 mb-6">
                    <div className="flex items-center gap-2 mb-3">
                    <Target className="w-5 h-5 text-purple-600" />
                    <div className="flex items-center gap-2">
                        <p className="font-medium text-purple-900">今日大盘气象</p>
                        <button 
                            onClick={() => {
                            // 跳转到大盘数据详情页面的逻辑
                            console.log('跳转到大盘数据详情页面');
                            }}
                            className="flex items-center gap-1 text-xs text-purple-600 bg-purple-100 px-2 py-0.5 rounded-full hover:bg-purple-200 active:scale-95 transition-all"
                        >
                            <span>详情</span>
                            <ChevronRight className="w-3 h-3" />
                        </button>
                        </div>
                    </div>
                    <div className="space-y-3">
                    <div className="flex items-start gap-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <Sparkles className="w-5 h-5 text-purple-600" />
                        </div>
                        <div className="flex-1">
                        <p className="text-sm text-gray-700 leading-relaxed">
                            "今天的市场就像春天的花园，处处充满生机！科技板块如同向日葵般昂首向阳，新能源像清晨的露珠闪闪发光。不过消费板块有点像午后慵懒的猫咪，需要时间慢慢苏醒呢~"
                        </p>
                        </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-2 mt-3">
                        <div className="bg-white/60 backdrop-blur rounded-xl p-2 text-center">
                        <p className="text-xs text-gray-600">市场情绪</p>
                        <p className="text-sm font-bold text-green-600">乐观😊</p>
                        </div>
                        <div className="bg-white/60 backdrop-blur rounded-xl p-2 text-center">
                        <p className="text-xs text-gray-600">热门板块</p>
                        <p className="text-sm font-bold text-purple-600">科技🚀</p>
                        </div>
                        <div className="bg-white/60 backdrop-blur rounded-xl p-2 text-center">
                        <p className="text-xs text-gray-600">风险等级</p>
                        <p className="text-sm font-bold text-yellow-600">适中⚡</p>
                        </div>
                    </div>
                    </div>
                </div>
                
                {/* 个人投资汇报 */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4 mb-6">
                    <div className="flex items-start gap-3">
                    <img src={character.avatar} alt="" className="w-12 h-12 rounded-full" />
                    <div className="flex-1">
                        <p className="font-medium mb-1">{character.name}的投资汇报</p>
                        <p className="text-sm text-gray-600">
                        {isInvestmentActive 
                            ? "主人！跟随大盘的春风，我们今天收获满满！科技未来像火箭一样涨了5.8%，我觉得自己就像个小园丁，看着财富之花慢慢绽放~虽然消费龙头有点调皮，但我相信它只是在积蓄力量！"
                            : "主人，我现在像一只需要充电的小机器人，暂时停下来休息了。如果您想让我继续在市场的花园里采蜜，可以送我一些能量礼物哦~"
                        }
                        </p>
                    </div>
                    </div>
                </div>
                
                {/* 今日战绩 */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                    <div className="bg-red-50 rounded-xl p-3">
                    <p className="text-sm text-gray-600 mb-1">今日收益</p>
                    <p className="text-xl font-bold text-red-600">+¥{todayProfit}</p>
                    </div>
                    <div className="bg-blue-50 rounded-xl p-3">
                    <p className="text-sm text-gray-600 mb-1">今日收益率</p>
                    <p className="text-xl font-bold text-blue-600">+1.26%</p>
                    </div>
                </div>
                
                {/* 今日操作记录 */}
                <div className="space-y-3 mb-6">
                    <p className="font-medium flex items-center gap-2">
                    <BarChart3 className="w-4 h-4 text-gray-600" />
                    今日操作轨迹
                    </p>
                    <div className="bg-gray-50 rounded-xl p-3">
                    <div className="flex justify-between items-center">
                        <div>
                        <p className="font-medium">买入 科技未来</p>
                        <p className="text-sm text-gray-500">10:30 顺势而为</p>
                        </div>
                        <TrendingUp className="w-5 h-5 text-red-500" />
                    </div>
                    </div>
                    <div className="bg-gray-50 rounded-xl p-3">
                    <div className="flex justify-between items-center">
                        <div>
                        <p className="font-medium">卖出 传统制造</p>
                        <p className="text-sm text-gray-500">14:20 获利了结</p>
                        </div>
                        <TrendingDown className="w-5 h-5 text-green-500" />
                    </div>
                    </div>
                </div>
                
                {/* 明日展望 */}
                <div className="bg-purple-50 rounded-xl p-4">
                    <p className="font-medium mb-2 flex items-center gap-2">
                    <Star className="w-4 h-4 text-purple-600" />
                    明日展望
                    </p>
                    <p className="text-sm text-gray-600">
                    "明天我想像蜜蜂一样，在新能源的花丛中多采些蜜！听说有政策春风要来，我已经准备好小篮子了。科技股涨得太快，像个调皮的孩子，我会找机会让它休息一下~"
                    </p>
                </div>
                </>
            );
          
        case 'advice':
          return (
            <>
              <h3 className="text-lg font-bold mb-4">给{character.name}一些建议</h3>
              
              {/* 显示上次建议 */}
              {lastAdviceOption && (
                <div className="bg-purple-50 rounded-2xl p-3 mb-4">
                  <p className="text-sm text-purple-600 mb-1">上次建议</p>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-purple-900">{lastAdviceOption.title}</p>
                      <p className="text-xs text-purple-700">{adviceCategories[lastAdviceCategory].title}</p>
                    </div>
                    <span className="text-2xl">{adviceCategories[lastAdviceCategory].icon}</span>
                  </div>
                </div>
              )}
              
              {/* 分类标签 */}
              <div className="flex gap-2 mb-4">
                {Object.entries(adviceCategories).map(([key, category]) => (
                  <button
                    key={key}
                    onClick={() => setCurrentAdviceCategory(key)}
                    className={`flex-1 py-2 px-3 rounded-xl text-sm font-medium transition-colors ${
                      currentAdviceCategory === key 
                        ? 'bg-purple-500 text-white' 
                        : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    <span className="mr-1">{category.icon}</span>
                    {category.title}
                  </button>
                ))}
              </div>
              
              {/* 建议选项 */}
              <div className="space-y-3">
                {adviceCategories[currentAdviceCategory].options.map(option => (
                  <button
                    key={option.id}
                    onClick={() => giveAdvice(currentAdviceCategory, option)}
                    className={`w-full p-4 rounded-2xl text-left transition-all ${
                      lastAdviceOption?.id === option.id && lastAdviceCategory === currentAdviceCategory
                        ? 'bg-purple-100 border-2 border-purple-300'
                        : 'bg-gray-50 border-2 border-transparent hover:border-purple-200'
                    }`}
                  >
                    <p className="font-medium text-gray-900">{option.title}</p>
                    <p className="text-sm text-gray-600">{option.desc}</p>
                    {lastAdviceOption?.id === option.id && lastAdviceCategory === currentAdviceCategory && (
                      <p className="text-xs text-purple-600 mt-1">✓ 当前建议</p>
                    )}
                  </button>
                ))}
              </div>
              
              <div className="mt-4 text-center">
                <p className="text-xs text-gray-500">建议会影响{character.name}的投资策略哦~</p>
              </div>
            </>
          );
          
        case 'reward':
          return (
            <>
              <h3 className="text-lg font-bold mb-6">送礼物给{character.name}</h3>
              <div className="bg-gray-50 rounded-2xl p-4 mb-6">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">当前派对币</span>
                  <div className="flex items-center gap-1">
                    <Coins className="w-4 h-4 text-yellow-500" />
                    <span className="font-semibold">{partyCoins}</span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-3">
                {giftList.map(gift => (
                  <button
                    key={gift.id}
                    onClick={() => sendGift(gift)}
                    className="bg-white border border-gray-200 rounded-2xl p-4 text-center active:scale-95 transition-transform hover:border-purple-300"
                  >
                    <span className="text-3xl mb-2 block">{gift.icon}</span>
                    <p className="font-medium text-sm">{gift.name}</p>
                    <p className="text-xs text-gray-500 flex items-center justify-center gap-1 mt-1">
                      <Coins className="w-3 h-3" />
                      {gift.price}
                    </p>
                    <p className="text-xs text-green-500 mt-1">+{gift.vitality}活力</p>
                  </button>
                ))}
              </div>
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-500">送出礼物会增加理财活力值，让{character.name}更有动力！</p>
              </div>
            </>
          );
          
        default:
          return null;
      }
    };

    return (
      <div className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${showBottomSheet ? 'opacity-100 visible' : 'opacity-0 invisible'}`} onClick={closeBottomSheet}>
        <div className="flex items-end justify-center h-full">
          <div 
            className={`w-full max-w-[390px] bg-white rounded-t-3xl p-6 transition-transform duration-300 ${
              showBottomSheet ? 'translate-y-0' : 'translate-y-full'
            }`}
            onClick={e => e.stopPropagation()}
          >
            <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
            <div className="max-h-[70vh] overflow-y-auto">
              {sheetContent()}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 主界面
  return (
    <div className="fixed inset-0 bg-gray-50 overflow-hidden">
      {/* 手机端容器 */}
      <div className="relative w-full max-w-[390px] h-full mx-auto bg-white shadow-xl">
        {/* 顶部导航栏 */}
        <div className="fixed top-0 left-0 right-0 max-w-[390px] mx-auto bg-white z-30 border-b border-gray-100">
        <div className="flex items-center justify-between px-4 py-3">
            <button className="p-2 -ml-2">
            <ChevronLeft className="w-6 h-6" />
            </button>
            <h1 className="font-semibold text-lg">星伴生财</h1>
            <div className="flex items-center gap-2">
            {/* 新增：星耀排行榜入口 */}
            <button className="p-2 relative" onClick={() => {/* 跳转到排行榜 */}}>
                <Trophy className="w-5 h-5 text-purple-500" />
                {/* 可选：添加小红点提示 */}
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>
            <button className="p-2 -mr-2" onClick={() => setShowInfo(true)}>
                <Info className="w-5 h-5 text-gray-600" />
            </button>
            </div>
        </div>
        </div>

        {/* 主内容区域 */}
        <div className="pt-16 pb-20 h-full overflow-y-auto" onScroll={(e) => {
          if (e.target.scrollTop === 0 && !refreshing) {
            handleRefresh();
          }
        }}>
          {/* 下拉刷新提示 */}
          {refreshing && (
            <div className="flex items-center justify-center py-4">
              <RefreshCw className="w-5 h-5 text-purple-500 animate-spin mr-2" />
              <span className="text-sm text-gray-600">更新数据中...</span>
            </div>
          )}

          {/* 角色卡片 */}
          <div className="px-4 py-4">
            <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-3xl p-5">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <img src={character.avatar} alt={character.name} className="w-14 h-14 rounded-full border-2 border-white shadow-md object-cover" />
                  <div>
                    <h3 className="font-bold text-lg">{character.name}</h3>
                    <p className="text-sm text-gray-600">{character.investmentStyle}</p>
                    {/* 新增：显示当前排名 */}
                    <div className="flex items-center gap-1 mt-1">
                    <Trophy className="w-3 h-3 text-yellow-500" />
                    <span className="text-xs text-yellow-600 font-medium">排行榜第12名</span>
                    </div>
                  </div>
                </div>
                {/* 理财活力值 */}
                <div className="text-center" onClick={() => setShowVitalityTips(true)}>
                <div className="flex items-center gap-1 mb-1">
                  <span className="text-2xl">{getVitalityIcon()}</span>
                  <span className={`font-bold text-lg ${getVitalityColor()}`}>{vitalityPoints}</span>
                  <Info className="w-3 h-3 text-gray-400" /> {/* 添加提示图标 */}
                </div>
                <p className="text-xs text-gray-600">理财活力</p>
              </div>
              </div>
              
              {/* 派对币显示 */}
              <div className="bg-white/40 backdrop-blur rounded-xl px-3 py-2 mb-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Coins className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm text-gray-700">已投派对币</span>
                </div>
                <span className="font-semibold text-gray-800">{partyCoins}</span>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-white/60 backdrop-blur rounded-2xl p-3">
                  <p className="text-sm text-gray-600 mb-1">总资产</p>
                  <p className="text-xl font-bold">¥{virtualFund.toLocaleString()}</p>
                  <p className={`text-sm ${totalProfit >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {totalProfit >= 0 ? '+' : ''}{totalProfit} ({profitRate}%)
                  </p>
                </div>
                <div className="bg-white/60 backdrop-blur rounded-2xl p-3">
                  <p className="text-sm text-gray-600 mb-1">总收益</p>
                  <p className={`text-xl font-bold ${todayProfit >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {todayProfit >= 0 ? '+' : ''}¥{todayProfit}
                  </p>
                  <p className="text-sm text-gray-500">1.26%</p>
                </div>
              </div>
              
              {/* 活力值不足提示 */}
              {vitalityPoints < 20 && vitalityPoints > 0 && (
                <div className="mt-3 bg-yellow-50 rounded-xl p-2 text-center">
                  <p className="text-xs text-yellow-800">活力值较低，送个礼物鼓励一下吧~</p>
                </div>
              )}
              {vitalityPoints === 0 && (
                <div className="mt-3 bg-red-50 rounded-xl p-2 text-center">
                  <p className="text-xs text-red-800">活力耗尽，投资已暂停，送礼物可恢复</p>
                </div>
              )}
            </div>
          </div>

          {/* 快捷操作 */}
          <div className="px-4 mb-4">
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => openBottomSheet('exchange-in')}
                className="bg-white rounded-2xl p-4 shadow-sm active:scale-95 transition-transform"
              >
                <ArrowUp className="w-6 h-6 text-purple-500 mb-2" />
                <p className="font-medium">投入资金</p>
                <p className="text-xs text-gray-500">增加本金</p>
              </button>
              <button
                onClick={() => openBottomSheet('exchange-out')}
                className="bg-white rounded-2xl p-4 shadow-sm active:scale-95 transition-transform"
              >
                <Wallet className="w-6 h-6 text-green-500 mb-2" />
                <p className="font-medium">提取资产</p>
                <p className="text-xs text-gray-500">可提¥{principalFund + totalProfit}</p>
              </button>
            </div>
          </div>

          {/* 持仓列表 */}
          <div className="px-4 mb-4">
            <div className="bg-white rounded-2xl shadow-sm">
              <div className="p-4 border-b border-gray-100">
                <h4 className="font-semibold flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-purple-500" />
                  当前持仓
                </h4>
              </div>
              <div className="divide-y divide-gray-100">
                {holdings.map((stock, index) => (
                  <div key={index} className="p-4 active:bg-gray-50">
                    <div className="flex justify-between items-start mb-1">
                      <div>
                        <p className="font-medium">{stock.name}</p>
                        <p className="text-sm text-gray-500">{stock.code}</p>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${stock.profit >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {stock.profit >= 0 ? '+' : ''}¥{stock.profit}
                        </p>
                        <p className={`text-sm ${stock.profitRate >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {stock.profitRate >= 0 ? '+' : ''}{stock.profitRate}%
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>{stock.shares}股</span>
                      <span>¥{stock.price}/股</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 底部导航栏 */}
        <div className="fixed bottom-0 left-0 right-0 max-w-[390px] mx-auto bg-white border-t border-gray-100">
        <div className="grid grid-cols-3 py-2">
            <button
            onClick={() => openBottomSheet('compass')}  // 改为 compass
            className="flex flex-col items-center gap-1 py-2 active:bg-gray-50"
            >
            <Target className="w-5 h-5 text-gray-600" />  {/* 改用罗盘图标 */}
            <span className="text-xs text-gray-600">星语罗盘</span>
            </button>
            <button
            onClick={() => openBottomSheet('advice')}
            className="flex flex-col items-center gap-1 py-2 active:bg-gray-50"
            >
            <MessageCircle className="w-5 h-5 text-purple-500" />
            <span className="text-xs text-purple-500">给建议</span>
            </button>
            <button 
            onClick={() => openBottomSheet('reward')}
            className="flex flex-col items-center gap-1 py-2 active:bg-gray-50"
            >
            <Gift className="w-5 h-5 text-pink-500" />
            <span className="text-xs text-pink-500">奖励一下</span>
            </button>
        </div>
        </div>

        {/* 新手引导 */}
        {showIntro && <IntroModal />}
        
        {/* 功能说明 */}
        {showInfo && <InfoModal />}

        {/* 底部弹窗 */}
        <BottomSheet />

        {/* 角色消息提示 */}
        {showMessage && (
          <div className="fixed top-20 left-4 right-4 max-w-[358px] mx-auto bg-white rounded-2xl shadow-lg p-4 z-50 animate-slide-down">
            <div className="flex items-start gap-3">
              <img src={character.avatar} alt="" className="w-10 h-10 rounded-full object-cover" />
              <p className="text-sm flex-1">{messageContent}</p>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes scale-up {
          from {
            transform: scale(0.9);
            opacity: 0;
          }
          to {
            transform: scale(1);
            opacity: 1;
          }
        }
        
        @keyframes slide-down {
          from {
            transform: translateY(-20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        
        .animate-scale-up {
          animation: scale-up 0.3s ease-out;
        }
        
        .animate-slide-down {
          animation: slide-down 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default AICharacterInvestmentPage;