import React, { useState, useEffect, useRef } from 'react';
import {
  ChevronLeft, Film, Upload, RefreshCw, Sparkles,
  Trash2, X, Check, AlertTriangle, Loader,
  MessageCircle, Clock, ArrowLeft, Camera, Wand2,
  Paintbrush, RotateCw, ImagePlus, Eye, Copy, Plus, Download,
  PenLine, FileImage, ChevronDown, CheckCircle, Settings,
  Users, Play, Video, Volume2, SlidersHorizontal, Square, RectangleVertical,
  Share, PenTool, User, Coins, DollarSign, PlayCircle
} from 'lucide-react';

// Placeholder for a simple video player component (replace with actual implementation)
const VideoPlayerPlaceholder = ({ src, coverUrl }) => (
  <div className="w-full aspect-video bg-black rounded-lg overflow-hidden relative flex items-center justify-center">
    {/* Optional: Show cover image */}
    {/* <img src={coverUrl} alt="Video cover" className="absolute inset-0 w-full h-full object-cover opacity-50" /> */}
    <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
        <Play size={48} className="text-white text-opacity-80 drop-shadow-lg" />
    </div>
    <p className="absolute bottom-2 left-2 text-white text-xs bg-black bg-opacity-50 px-2 py-1 rounded">播放器占位</p>
    {/* In a real app, use <video controls poster={coverUrl} src={src}></video> */}
  </div>
);

// Character Avatar component (similar to OptimizedDimensionBrush)
const CharacterAvatar = ({ character, size = "lg", className = "" }) => {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12",
    lg: "w-16 h-16",
  };
  
  return (
    <div className={`rounded-full bg-purple-200 flex items-center justify-center ${sizeClasses[size]} ${className} overflow-hidden flex-shrink-0`}>
      {character?.profileImage ? (
        <img 
          src={character.profileImage} 
          alt={character.name}
          className="w-full h-full object-cover"
        />
      ) : (
        <span className="text-purple-700 font-bold text-lg">{character?.name?.charAt(0) || 'A'}</span>
      )}
    </div>
  );
};

const DreamWeaverVideoCreator = ({ onBack, onVideoCreated, characterData, existingAssets = [], userBalance = { partyCoins: 150, starlightCoins: 50 }, preferredPaymentMethod = 'partyCoins' }) => {
  // States
  const [step, setStep] = useState('mode-selection'); // 'mode-selection', 'edit', 'processing', 'complete'
  const [selectedMode, setSelectedMode] = useState(null); // 'characterScene', 'freeDream', 'memoryEcho'
  const [videoDescription, setVideoDescription] = useState('');
  const [originalDescription, setOriginalDescription] = useState('');
  const [selectedReferenceImages, setSelectedReferenceImages] = useState([]);
  const [showReferenceSelector, setShowReferenceSelector] = useState(false);
  const [showLibrarySelector, setShowLibrarySelector] = useState(false);
  const [isEnhancingPrompt, setIsEnhancingPrompt] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('waiting'); // 'waiting', 'processing', 'error'
  const [processingProgress, setProcessingProgress] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState('约1分钟'); // Reduced time estimate
  const [resultVideo, setResultVideo] = useState(null); // { url: '...', coverUrl: '...' }

  // Enhancement modal states (same as CreateImagePage)
  const [showEnhanceModal, setShowEnhanceModal] = useState(false);
  const [selectedEnhanceStyle, setSelectedEnhanceStyle] = useState(null);
  const [customEnhanceText, setCustomEnhanceText] = useState('');

  // Video specific parameters
  const [selectedStyle, setSelectedStyle] = useState(null); // Initially null, let user pick
  const [showStyleOptions, setShowStyleOptions] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState(5); // Default 5 seconds
  const [motionIntensity, setMotionIntensity] = useState('medium'); // Default to medium intensity
  const [includeSound, setIncludeSound] = useState(true);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Payment method states
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(preferredPaymentMethod); // Set default payment method
  const [tempPaymentMethod, setTempPaymentMethod] = useState(selectedPaymentMethod);

  // Payment costs based on video duration and style
  const paymentOptions = {
    partyCoins: { id: 'partyCoins', name: '派对币', icon: <Coins size={16} className="text-yellow-500" />, amount: selectedDuration === 5 ? 100 : 180 },
    starlightCoins: { id: 'starlightCoins', name: '星光币', icon: <DollarSign size={16} className="text-blue-500" />, amount: selectedDuration === 5 ? 20 : 35 },
    watchVideo: { id: 'watchVideo', name: '观看激励视频', icon: <PlayCircle size={16} className="text-green-500" />, description: '观看30秒广告视频免费创作' }
  };

  // Update payment costs when duration changes
  useEffect(() => {
    paymentOptions.partyCoins.amount = selectedDuration === 5 ? 100 : 180;
    paymentOptions.starlightCoins.amount = selectedDuration === 5 ? 20 : 35;
    
    // Update payment method if current one is insufficient
    const currentMethodId = selectedPaymentMethod;
    if (currentMethodId === 'partyCoins' || currentMethodId === 'starlightCoins') {
      const required = paymentOptions[currentMethodId].amount;
      const available = userBalance[currentMethodId];
      
      if (available < required) {
        // Automatically switch to best alternative
        setSelectedPaymentMethod(determineDefaultPaymentMethod());
      }
    }
  }, [selectedDuration]);

  useEffect(() => {
    if (showPaymentMethods) {
      setTempPaymentMethod(selectedPaymentMethod);
    }
  }, [showPaymentMethods, selectedPaymentMethod]);

  const descriptionRef = useRef(null);
  const processingTimer = useRef(null);

  // --- Data Definitions ---

  // Mode definitions
  const modes = {
    characterScene: { id: 'characterScene', name: '角色剧场', description: `创作 ${characterData?.name || '角色'} 主演的场景视频`, icon: <Users size={22} className="text-purple-600" /> },
    freeDream: { id: 'freeDream', name: '自由织梦', description: '不受限地创作任何内容的视频', icon: <Sparkles size={22} className="text-purple-600" /> },
    memoryEcho: { id: 'memoryEcho', name: '记忆回廊', description: `重现 ${characterData?.name || '角色'} 与你的浪漫瞬间`, icon: <Clock size={22} className="text-pink-600" /> },
  };

  // Video style options (example)
  const videoStyles = [
    { id: 'cinematic', name: '电影感', description: '故事性强，氛围浓郁', iconColor: 'bg-gradient-to-br from-amber-400 to-orange-600' },
    { id: 'anime', name: '动漫风', description: '流畅的二次元动画风格', iconColor: 'bg-gradient-to-br from-blue-400 to-indigo-500' },
    { id: 'realistic', name: '写实', description: '接近真实世界的质感', iconColor: 'bg-gradient-to-br from-green-400 to-teal-500' },
    { id: 'dreamy', name: '梦幻', description: '柔和光线，唯美浪漫', iconColor: 'bg-gradient-to-br from-pink-400 to-purple-500' },
    { id: 'cyberpunk', name: '赛博朋克', description: '未来科技，霓虹光影', iconColor: 'bg-gradient-to-br from-cyan-400 to-blue-600' },
    { id: 'random', name: '自定义', description: '可在描述说明里自定义', iconColor: 'bg-gradient-to-br from-gray-400 to-gray-600' }
  ];

  // Duration options - reduced to only 5s and 10s as requested
  const durationOptions = [5, 10]; // In seconds

  // Motion intensity options
  const motionOptions = [
    { id: 'low', name: '低', description: ' subtle movement' },
    { id: 'medium', name: '中', description: ' noticeable motion' },
    { id: 'high', name: '高', description: ' dynamic action' }
  ];

  // Inspirations adapted for video
  const inspirations = [
    `${characterData?.name || '角色'} 在雨中漫步，霓虹灯光反射在湿漉漉的街道上，镜头缓慢推进`,
    `${characterData?.name || '角色'} 在阳光明媚的咖啡馆窗边看书，光影斑驳，微风吹动发梢`,
    `一个充满未来感的城市景象，飞行器穿梭，全息广告闪烁，镜头快速移动`,
    `宁静的森林深处，阳光透过树叶缝隙洒下，小溪潺潺流动，有小动物跑过`,
    `黄昏时分的海滩，海浪轻拍沙滩，夕阳染红天空，${characterData?.name || '角色'}与你并肩散步的背影`
  ];

  // Enhancement styles (copied from CreateImagePage)
  const enhanceStyles = [
    { id: 'more_detailed', name: '更详细', description: '添加更多细节和描述', icon: <Sparkles size={16} className="text-amber-500" /> },
    { id: 'more_concise', name: '更精简', description: '精炼语言，保持关键内容', icon: <Check size={16} className="text-green-500" /> },
    { id: 'more_dramatic', name: '更戏剧化', description: '增加情感色彩和戏剧元素', icon: <Wand2 size={16} className="text-purple-500" /> },
    { id: 'more_professional', name: '更专业', description: '使用更专业的术语和描述', icon: <CheckCircle size={16} className="text-blue-500" /> },
    { id: 'custom', name: '自定义', description: '按照自定义指令润色', icon: <Settings size={16} className="text-gray-500" /> }
  ];

  // --- Effects ---

  // Auto resize textarea
  useEffect(() => {
    if (descriptionRef.current) {
      descriptionRef.current.style.height = 'auto';
      descriptionRef.current.style.height = `${descriptionRef.current.scrollHeight}px`;
    }
  }, [videoDescription]);

  // Generate automatic description for Memory Echo mode
  useEffect(() => {
    if (selectedMode === 'memoryEcho' && step === 'edit') {
      // Simulate automatic generation based on conversation history
      const generateMemoryDescription = async () => {
        setIsEnhancingPrompt(true);
        
        // Simulate API call/processing delay
        setTimeout(() => {
          // Example auto-generated memory description
          const autoDescription = `${characterData?.name || '角色'}和你在樱花飘落的公园散步，阳光透过树叶洒下斑驳光影。${characterData?.name || '角色'}微笑着转向你，风轻轻吹起发丝，背景中粉色花瓣轻舞，记录下这珍贵回忆的瞬间。`;
          setVideoDescription(autoDescription);
          setIsEnhancingPrompt(false);
        }, 1500);
      };
      
      generateMemoryDescription();
    }
  }, [selectedMode, step, characterData]);

  // Simulate processing progress
  useEffect(() => {
    if (step === 'processing') {
      let progress = 0;
      setProcessingProgress(0);
      setProcessingStatus('waiting');
      
      // 3 seconds mock time as requested
      const totalDuration = 3000;
      setEstimatedTime(`约1分钟`); // Reduced time estimate for UI only

      setTimeout(() => {
        setProcessingStatus('processing');
        const startTime = Date.now();

        processingTimer.current = setInterval(() => {
          const elapsedTime = Date.now() - startTime;
          progress = Math.min(100, Math.floor((elapsedTime / totalDuration) * 100));

          setProcessingProgress(progress);

          if (progress >= 100) {
            clearInterval(processingTimer.current);
            setTimeout(() => {
              setStep('complete');
              // Simulate result video URL and potentially cover art
              setResultVideo({
                url: '/api/placeholder/video.mp4', // Placeholder URL
                coverUrl: '/api/placeholder/400/225' // Placeholder cover
              });
            }, 500);
          }
        }, 100); // Update progress more frequently for smoother animation
      }, 500); // Reduced queue time
    }

    return () => {
      if (processingTimer.current) {
        clearInterval(processingTimer.current);
      }
    };
  }, [step]);

  // --- Handlers ---

  // Mode selection
  const handleSelectMode = (modeId) => {
    setSelectedMode(modeId);
    setSelectedStyle(null);
    setVideoDescription(''); // Clear description when changing mode
    setOriginalDescription('');
    setSelectedReferenceImages([]);
    
    // Determine best default payment method based on balance
    const defaultMethod = determineDefaultPaymentMethod();
    setSelectedPaymentMethod(defaultMethod);
    
    setStep('edit');
  };
  
  // Determine the best default payment method based on user's balance
  const determineDefaultPaymentMethod = () => {
    const partyCoinsNeeded = selectedDuration === 5 ? 100 : 180;
    const starlightCoinsNeeded = selectedDuration === 5 ? 20 : 35;
    
    // If user has preferred method with sufficient balance, use that
    if (preferredPaymentMethod === 'partyCoins' && userBalance.partyCoins >= partyCoinsNeeded) {
      return 'partyCoins';
    } else if (preferredPaymentMethod === 'starlightCoins' && userBalance.starlightCoins >= starlightCoinsNeeded) {
      return 'starlightCoins';
    }
    
    // Otherwise, choose based on available balance
    if (userBalance.partyCoins >= partyCoinsNeeded) {
      return 'partyCoins';
    } else if (userBalance.starlightCoins >= starlightCoinsNeeded) {
      return 'starlightCoins';
    } else {
      return 'watchVideo'; // Fallback to watching video if no sufficient coins
    }
  };

  // Handler for showing enhancement options modal
  const handleShowEnhanceOptions = () => {
    if (!videoDescription.trim()) {
      alert('请先输入一些基础描述');
      return;
    }
    
    setSelectedEnhanceStyle(null);
    setCustomEnhanceText('');
    setShowEnhanceModal(true);
  };

  // Handler for enhancing the description with AI
  const handleEnhanceDescription = async () => {
    if (!selectedEnhanceStyle) {
      setShowEnhanceModal(false);
      return;
    }
    
    setShowEnhanceModal(false);
    setIsEnhancingPrompt(true);
    
    // Save original to allow reverting
    if (!originalDescription) {
      setOriginalDescription(videoDescription);
    }
    
    // Simulate AI enhancement (this would call the actual API in production)
    setTimeout(() => {
      const baseText = videoDescription;
      let enhanced = baseText;
      
      // Apply different enhancements based on selected style
      switch (selectedEnhanceStyle.id) {
        case 'more_detailed':
          enhanced = `${baseText}，场景中光线明暗对比强烈，远处霓虹灯将角色轮廓勾勒出科技感十足的蓝紫色光晕，特写镜头捕捉角色眼神中的坚定与神秘，画面质感精细，细节丰富，背景环境的复杂纹理清晰可见。`;
          break;
        case 'more_concise':
          enhanced = baseText.length > 100 
            ? baseText.slice(0, 100).split('，')[0] + '，简洁有力，突出角色特征与关键场景元素。' 
            : baseText + '，精炼有力，重点突出。';
          break;
        case 'more_dramatic':
          enhanced = `${baseText}，戏剧性的光影效果营造出紧张氛围，角色表情凝重而富有张力，姿态充满动感，周围环境似乎也在呼应主角的情绪变化，整体画面具有强烈的情感冲击力。`;
          break;
        case 'more_professional':
          enhanced = `${baseText}，运用专业摄影构图原则，遵循三分法则排布主体，画面色彩采用互补色系，主体与背景形成对比度分离，景深适中，确保细节清晰度与整体氛围并重，光源布局科学合理。`;
          break;
        case 'custom':
          enhanced = customEnhanceText 
            ? `${baseText}，${customEnhanceText}` 
            : baseText;
          break;
        default:
          enhanced = `${baseText}，场景中光线明暗对比强烈，远处霓虹灯将角色轮廓勾勒出科技感十足的蓝紫色光晕，特写镜头捕捉角色眼神中的坚定与神秘。`;
      }
      
      setVideoDescription(enhanced);
      setIsEnhancingPrompt(false);
    }, 1500);
  };

  // Handler for rewriting the description completely
  const handleRewriteDescription = async () => {
    if (!videoDescription.trim()) {
      alert('请先输入一些基础描述');
      return;
    }
    
    setIsEnhancingPrompt(true);
    
    // Save original to allow reverting
    if (!originalDescription) {
      setOriginalDescription(videoDescription);
    }
    
    // Simulate AI rewriting (this would call the actual API in production)
    setTimeout(() => {
      const theme = videoDescription.includes('雨') ? '雨夜' : 
                    videoDescription.includes('咖啡') ? '咖啡厅' : 
                    videoDescription.includes('海滩') ? '海滩' : '城市';
      
      const rewritten = theme === '雨夜' ? 
        `${characterData?.name || '角色'}站在雨水淋漓的城市街头，身穿一件防水风衣，肩部有着未来科技元素的荧光点缀。背景是模糊的霓虹灯广告牌，光线在湿漉漉的地面上形成梦幻般的反射。雨滴在他的帽檐上汇聚，他的目光穿透雨幕，手中全息设备投射出案件关键线索。` :
        theme === '咖啡厅' ?
        `${characterData?.name || '角色'}在一家复古未来主义的咖啡馆里小憩，面前的咖啡杯冒着热气。阳光透过几何形状的彩色玻璃窗照射进来，在他的桌面和面部投下多彩的光斑。他身旁的全息屏幕显示着案件进展，而咖啡馆的其他顾客则是模糊的背景。` :
        theme === '海滩' ?
        `黄昏时分，${characterData?.name || '角色'}站在金色的沙滩上，海浪轻轻拍打着岸边。夕阳的余晖给海面镀上一层金色，角色的侧脸被温暖的光线勾勒出柔和的轮廓。微风吹拂着角色的发丝，远处有几只海鸥掠过天际，整个画面温馨而浪漫。` :
        `${characterData?.name || '角色'}穿行在未来城市的繁忙街道上，周围是来往的行人和悬浮交通工具。角色身着融合了复古与未来元素的服装，目光敏锐地观察着周围。街道两旁是各式全息投影广告和霓虹招牌，远处高楼的玻璃幕墙反射着日落的金色光芒。`;
      
      setVideoDescription(rewritten);
      setIsEnhancingPrompt(false);
    }, 1500);
  };

  // Handler for getting AI inspiration
  const handleGetInspiration = () => {
    // Randomly select an inspiration
    const randomInspiration = inspirations[Math.floor(Math.random() * inspirations.length)];
    setVideoDescription(randomInspiration);
    
    // Focus the textarea after setting inspiration
    if (descriptionRef.current) {
      descriptionRef.current.focus();
    }
  };

  // Handler for clearing the description
  const handleClearDescription = () => {
    if (videoDescription.trim() && !window.confirm('确定要清空当前描述吗？')) {
      return;
    }
    setVideoDescription('');
    setOriginalDescription('');
    
    // Focus the textarea after clearing
    if (descriptionRef.current) {
      descriptionRef.current.focus();
    }
  };

  // Handler for undoing AI enhancement
  const handleUndoEnhancement = () => {
    if (originalDescription) {
      setVideoDescription(originalDescription);
      setOriginalDescription('');
    }
  };

  // Handler for selecting reference images
  const handleAddReferenceImage = (source) => {
    if (source === 'library') {
      setShowLibrarySelector(true);
      setShowReferenceSelector(false);
    } else if (source === 'camera') {
      // This would integrate with device camera in a real app
      alert('调用相机功能');
      setShowReferenceSelector(false);
    } else if (source === 'gallery') {
      // This would integrate with device gallery in a real app
      alert('调用相册功能');
      setShowReferenceSelector(false);
    }
  };

  // Handler for selecting an asset from library
  const handleSelectAsset = (asset) => {
    // Add to selected references if not already included
    if (!selectedReferenceImages.some(img => img.id === asset.id)) {
      setSelectedReferenceImages([...selectedReferenceImages, asset]);
    }
    setShowLibrarySelector(false);
  };

  // Handler for removing a reference image
  const handleRemoveReferenceImage = (id) => {
    setSelectedReferenceImages(selectedReferenceImages.filter(img => img.id !== id));
  };

  // Handler for selecting payment method
  const handleSelectPaymentMethod = (methodId) => {
    setSelectedPaymentMethod(methodId);
    setShowPaymentMethods(false);
  };

  // Handler for proceeding with the selected payment method
  const handleWatchVideo = () => {
    // This would integrate with ad SDK in a real app
    alert('调用广告SDK，播放30秒激励视频');
    // Simulate successful video watch
    setTimeout(() => {
      handleCreateVideo();
    }, 1500);
  };

  // Start video creation
  const handleCreateVideo = () => {
    if (selectedMode !== 'memoryEcho' && !videoDescription.trim()) {
      alert('请输入视频描述'); 
      return;
    }
    if (!selectedStyle) {
      alert('请选择视频风格'); 
      return;
    }
    if (!selectedPaymentMethod) {
      setShowPaymentMethods(true);
      return;
    }

    // Check if user has enough coins
    if (selectedPaymentMethod === 'partyCoins' && userBalance.partyCoins < paymentOptions.partyCoins.amount) {
      alert(`派对币余额不足，您当前有 ${userBalance.partyCoins} 派对币，需要 ${paymentOptions.partyCoins.amount} 派对币`);
      return;
    }
    if (selectedPaymentMethod === 'starlightCoins' && userBalance.starlightCoins < paymentOptions.starlightCoins.amount) {
      alert(`星光币余额不足，您当前有 ${userBalance.starlightCoins} 星光币，需要 ${paymentOptions.starlightCoins.amount} 星光币`);
      return;
    }

    // Handle watch video payment method
    if (selectedPaymentMethod === 'watchVideo') {
      handleWatchVideo();
      return;
    }

    console.log('Starting video creation with:', {
      mode: selectedMode,
      description: videoDescription,
      references: selectedReferenceImages.map(img => img.id),
      style: selectedStyle.id,
      duration: selectedDuration,
      motion: motionIntensity,
      sound: includeSound,
      paymentMethod: selectedPaymentMethod
    });
    setStep('processing');
  };

  // Cancel creation
  const handleCancelCreation = () => {
    if (window.confirm('确定要取消当前创作吗？所有进度将丢失。')) {
      if (processingTimer.current) clearInterval(processingTimer.current);
      // Go back to edit step, don't reset mode selection
      setStep('edit');
      setProcessingProgress(0);
    }
  };

  // Complete creation and save
  const handleCompleteCreation = () => {
    if (onVideoCreated) {
      onVideoCreated({
        id: Date.now(),
        type: 'video',
        url: resultVideo.url,
        coverUrl: resultVideo.coverUrl,
        title: videoDescription.slice(0, 30) + (videoDescription.length > 30 ? '...' : ''),
        createdAt: '刚刚',
        duration: selectedDuration, // Add duration info
        // ... other metadata
      });
    }
    if (onBack) onBack(); // Or navigate to asset library / feed page
  };

  // Filter image assets for reference selection
  const imageAssets = existingAssets.filter(asset => asset.type === 'image');
  
  // Check if selected payment method is valid (sufficient balance)
  const isSelectedPaymentValid = () => {
    if (!selectedPaymentMethod) return false;
    
    if (selectedPaymentMethod === 'watchVideo') return true;
    
    const required = paymentOptions[selectedPaymentMethod].amount;
    const available = userBalance[selectedPaymentMethod];
    return available >= required;
  };

  // Get processing status text for video
  const getProcessingStatusText = () => {
    if (processingStatus === 'waiting') return '排队等候织机';
    if (processingStatus === 'processing') {
      if (processingProgress < 20) return '解析梦境描述';
      if (processingProgress < 40) return '构建光影基础';
      if (processingProgress < 60) return '渲染关键帧画面';
      if (processingProgress < 80) return '编织动态效果';
      if (processingProgress < 95) return '合成音轨 (如果选择)';
      return '梦境即将呈现';
    }
    if (processingStatus === 'error') return '织机遇到故障';
    return '梦境编织中';
  };

  // Get color class based on balance sufficiency
  const getBalanceColorClass = (coinType, required) => {
    const balance = userBalance[coinType] || 0;
    return balance >= required ? "text-green-600" : "text-red-500";
  };

  // --- Render Logic ---

  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen">
      {/* Header - Updated to match CreateImagePage style */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white sticky top-0 z-20">
        <div className="flex items-center">
          <button
            onClick={() => {
                // If in edit step, go back to mode selection, otherwise go back to previous screen
                if (step === 'edit' && selectedMode) {
                    if (window.confirm('返回将丢失当前编辑内容，确定吗？')) {
                        setStep('mode-selection');
                        setSelectedMode(null); // Reset mode
                    }
                } else {
                    onBack();
                }
            }}
            className="p-2 mr-2 rounded-full hover:bg-white hover:bg-opacity-20"
          >
            <ChevronLeft size={20} />
          </button>
          <h1 className="text-lg font-bold flex items-center">
            <Film size={18} className="mr-2"/>
            梦境织机
          </h1>
        </div>
        {step === 'complete' && (
          <button
            
          >
          </button>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto relative">

        {/* Mode Selection Step */}
        {step === 'mode-selection' && (
          <div className="p-4 space-y-6">
            <div className="bg-white rounded-lg p-4 shadow-sm text-center">
               <div className="w-16 h-16 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-2">
                 <Wand2 size={24} className="text-purple-600" />
               </div>
               <h2 className="text-lg font-bold text-gray-800">选择创作模式</h2>
               <p className="text-sm text-gray-600 mt-1">让梦境织机为你编织动态画面</p>
               {/* Display available credits/balance */}
               <div className="flex justify-center gap-4 mt-3">
                  <div className="flex items-center text-sm">
                    <Coins size={14} className="text-yellow-500 mr-1" />
                    <span className="text-yellow-600">{userBalance.partyCoins}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <DollarSign size={14} className="text-blue-500 mr-1" />
                    <span className="text-blue-600">{userBalance.starlightCoins}</span>
                  </div>
               </div>
            </div>
            <div className="space-y-3">
                {Object.values(modes).map(mode => (
                     <button
                       key={mode.id}
                       onClick={() => handleSelectMode(mode.id)}
                       className="w-full bg-white rounded-lg p-4 shadow-sm flex items-center hover:shadow-md transition-shadow border border-transparent hover:border-purple-200"
                     >
                       <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4">
                         {mode.icon}
                       </div>
                       <div className="flex-1 text-left">
                         <h3 className="font-semibold text-gray-800">{mode.name}</h3>
                         <p className="text-xs text-gray-500 mt-1">{mode.description}</p>
                       </div>
                       <ChevronLeft size={18} className="transform rotate-180 text-gray-400" />
                     </button>
                ))}
            </div>
          </div>
        )}

        {/* Edit Step */}
        {step === 'edit' && selectedMode && (
          <div className="p-4 space-y-5">
            {/* Mode Header - Updated to match OptimizedDimensionBrush styles */}
            {selectedMode === 'characterScene' && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <CharacterAvatar character={characterData} size="lg" className="mr-4" />
                  <div>
                    <h2 className="font-bold text-gray-800 flex items-center mb-1">
                      <FileImage size={16} className="mr-2 text-indigo-600" />
                      描绘 {characterData?.name || '角色'} 的瞬间
                    </h2>
                    <p className="text-sm text-gray-600">将使用 {characterData?.name || '角色'} 作为场景主角创建视频</p>
                  </div>
                </div>
              </div>
            )}
            
            {selectedMode === 'freeDream' && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <CharacterAvatar character={characterData} size="lg" className="mr-4" />
                  <div>
                    <h2 className="font-bold text-gray-800 flex items-center mb-1">
                      <PenTool size={16} className="mr-2 text-pink-600" />
                      自由创作
                    </h2>
                    <p className="text-sm text-gray-600">完全释放想象力，创作不受限的视频</p>
                  </div>
                </div>
              </div>
            )}
            
            {/* Memory Echo Mode - Header and Module */}
            {selectedMode === 'memoryEcho' && (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <CharacterAvatar character={characterData} size="lg" className="mr-4" />
                  <div>
                    <h2 className="font-bold text-gray-800 flex items-center mb-1">
                      <Clock size={16} className="mr-2 text-pink-600" />
                      记忆回廊
                    </h2>
                    <p className="text-sm text-gray-600">让 {characterData?.name || '赛博侦探 K'} 根据聊天记忆为您创作专属视频场景</p>
                  </div>
                </div>
              </div>
            )}
            
            {/* Reference Images - Not shown for Memory Echo mode */}
            {selectedMode !== 'memoryEcho' && (
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                 <label className="text-sm font-medium text-gray-700 flex items-center">
                   <FileImage size={16} className="mr-1 text-purple-600" />
                   参考图 <span className="text-xs text-gray-500 ml-1">(可选，用于风格/角色)</span>
                 </label>
                 <button 
                   onClick={() => setShowReferenceSelector(true)}
                   className="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full flex items-center"
                 >
                   <Plus size={14} className="mr-1" /> 添加参考
                 </button>
              </div>
              
              {selectedReferenceImages.length > 0 ? (
                <div className="flex gap-2 overflow-x-auto pb-2 hide-scrollbar">
                  {selectedReferenceImages.map((img) => (
                    <div key={img.id} className="relative flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border border-gray-200 bg-white">
                      <img 
                        src={img.url} 
                        alt={img.title}
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => handleRemoveReferenceImage(img.id)}
                        className="absolute top-1 right-1 w-5 h-5 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-dashed border-gray-300 p-3 text-center">
                  <p className="text-xs text-gray-500">
                    添加参考图片可以让AI更好地理解你想要的风格和场景
                  </p>
                </div>
              )}
            </div>
            )}

            {/* Video Style Selection - Updated to match CreateImagePage style */}
             <div className="mb-4">
               <div className="flex justify-between items-center mb-2">
                 <label className="text-sm font-medium text-gray-700 flex items-center">
                   <Paintbrush size={16} className="mr-1 text-purple-600" />
                   视频风格
                 </label>
               </div>
               
               <div className="relative">
                  <button
                    onClick={() => setShowStyleOptions(!showStyleOptions)}
                    className={`w-full p-3 bg-white rounded-lg border ${selectedStyle ? 'border-gray-200' : 'border-red-300'} flex justify-between items-center`}
                  >
                    {selectedStyle ? (
                       <div className="flex items-center">
                         <div className={`w-8 h-8 rounded-full ${selectedStyle.iconColor} mr-2`}></div>
                         <div className="text-left">
                           <div className="text-sm font-medium">{selectedStyle.name}</div>
                           <div className="text-xs text-gray-500">{selectedStyle.description}</div>
                         </div>
                       </div>
                    ) : (
                       <span className="text-sm text-gray-500">请选择一个视频风格</span>
                    )}
                    <ChevronDown size={18} className="text-gray-400" />
                  </button>
                  
                  {showStyleOptions && (
                    <div className="absolute z-20 top-full left-0 right-0 mt-1 bg-white rounded-lg border border-gray-200 shadow-lg max-h-64 overflow-y-auto">
                       {videoStyles.map((style) => (
                         <button 
                           key={style.id} 
                           onClick={() => {
                             setSelectedStyle(style);
                             setShowStyleOptions(false);
                           }}
                           className={`w-full p-3 flex items-center border-b border-gray-100 last:border-b-0 ${
                             selectedStyle?.id === style.id ? 'bg-purple-50' : 'hover:bg-gray-50'
                           }`}
                         >
                            <div className={`w-8 h-8 rounded-full ${style.iconColor} mr-2`}></div>
                            <div className="text-left flex-1">
                              <div className="text-sm font-medium">{style.name}</div>
                              <div className="text-xs text-gray-500">{style.description}</div>
                            </div>
                            {selectedStyle?.id === style.id && (
                              <CheckCircle size={18} className="text-purple-600 ml-2" />
                            )}
                         </button>
                       ))}
                    </div>
                  )}
               </div>
             </div>

            {/* Duration selection - simplified to only 5s and 10s options */}
            <div className="mb-4">
               <label className="text-sm font-medium text-gray-700 block mb-2 flex items-center">
                 <Clock size={16} className="mr-1 text-purple-600" />
                 视频时长
               </label>
               <div className="grid grid-cols-2 gap-4">
                   <button 
                     onClick={() => setSelectedDuration(5)} 
                     className={`py-2 rounded-lg flex items-center justify-center ${
                       selectedDuration === 5 
                         ? 'bg-purple-100 text-purple-700 border border-purple-300' 
                         : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                     }`}
                   >
                     5秒 (短视频)
                   </button>
                   <button 
                     onClick={() => setSelectedDuration(10)} 
                     className={`py-2 rounded-lg flex items-center justify-center ${
                       selectedDuration === 10 
                         ? 'bg-purple-100 text-purple-700 border border-purple-300' 
                         : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                     }`}
                   >
                     10秒 (标准)
                   </button>
               </div>
            </div>
            
           {/* Memory Echo About section */}
           {selectedMode === 'memoryEcho' && (
              <div className="mb-4 bg-pink-50 rounded-lg p-4 border border-pink-100">
                <h3 className="text-sm font-medium text-pink-800 flex items-center mb-2">
                  <Clock size={16} className="mr-1 text-pink-600" />
                  关于记忆回廊
                </h3>
                {isEnhancingPrompt ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader size={24} className="text-pink-600 animate-spin mr-2" />
                    <p className="text-sm text-pink-600">正在从聊天记录中提取珍贵回忆...</p>
                  </div>
                ) : (
                  <p className="text-xs text-pink-600">
                    记忆回廊将从你与{characterData?.name || '角色'}的聊天记忆中提取珍贵瞬间，创作一段专属于你们的回忆视频。每一个画面都将呈现你们之间最美好的时刻，由AI精心编织成动态影像。
                  </p>
                )}
              </div>
            )}

            {/* Description Input - Not shown for Memory Echo mode */}
            {selectedMode !== 'memoryEcho' && (
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label htmlFor="videoDescription" className="text-sm font-medium text-gray-700 flex items-center">
                    <PenLine size={16} className="mr-1 text-purple-600" />
                    视频描述 <span className="text-red-500 ml-1">*</span>
                  </label>
                  <button 
                    onClick={handleGetInspiration}
                    className="text-xs px-2 py-1 bg-amber-100 text-amber-700 rounded-full flex items-center"
                  >
                    <Sparkles size={14} className="mr-1" /> 给我灵感
                  </button>
                </div>
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <textarea
                    ref={descriptionRef}
                    id="videoDescription"
                    value={videoDescription}
                    onChange={(e) => setVideoDescription(e.target.value)}
                    placeholder="详细描述视频内容、场景、动作、氛围..."
                    className="w-full p-3 min-h-[120px] text-sm border-none focus:ring-0 focus:outline-none resize-none"
                    disabled={isEnhancingPrompt}
                  ></textarea>
                  
                  <div className="flex border-t border-gray-100">
                    <button
                      onClick={handleShowEnhanceOptions}
                      disabled={!videoDescription.trim() || isEnhancingPrompt}
                      className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                        !videoDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-purple-600'
                      }`}
                    >
                      {isEnhancingPrompt ? <Loader size={14} className="mr-1 animate-spin" /> : <Wand2 size={14} className="mr-1" />}
                      润色
                    </button>
                    <div className="w-px h-8 bg-gray-100 my-auto"></div>
                    <button
                      onClick={handleRewriteDescription}
                      disabled={!videoDescription.trim() || isEnhancingPrompt}
                      className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                        !videoDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-indigo-600'
                      }`}
                    >
                      {isEnhancingPrompt ? <Loader size={14} className="mr-1 animate-spin" /> : <RefreshCw size={14} className="mr-1" />}
                      重写
                    </button>
                    <div className="w-px h-8 bg-gray-100 my-auto"></div>
                    <button
                      onClick={handleClearDescription}
                      disabled={!videoDescription.trim() || isEnhancingPrompt}
                      className={`flex-1 py-2 text-xs font-medium flex items-center justify-center ${
                        !videoDescription.trim() || isEnhancingPrompt ? 'text-gray-400' : 'text-red-600'
                      }`}
                    >
                      <Trash2 size={14} className="mr-1" />
                      清空
                    </button>
                  </div>
                </div>
                
                {originalDescription && (
                  <div className="mt-2 flex justify-end">
                    <button
                      onClick={handleUndoEnhancement}
                      className="text-xs text-gray-600 flex items-center"
                    >
                      <ArrowLeft size={12} className="mr-1" />
                      恢复原始描述
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Advanced Options Toggle */}
             <div className="mb-4">
                 <button
                     onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                     className="w-full flex justify-between items-center p-2 bg-gray-100 rounded-lg text-sm text-gray-700 hover:bg-gray-200"
                 >
                     <span className="flex items-center"><SlidersHorizontal size={16} className="mr-2"/> 高级设置</span>
                     <ChevronDown size={18} className={`transition-transform ${showAdvancedOptions ? 'rotate-180' : ''}`} />
                 </button>
             </div>

            {/* Advanced Options Section - Simplified */}
            {showAdvancedOptions && (
                <div className="space-y-4 p-4 bg-white rounded-lg border border-gray-200 animate-fadeIn">
                    {/* Include Sound */}
                    <div>
                         <label className="text-sm font-medium text-gray-700 block mb-2">背景音效</label>
                         <div className="flex items-center space-x-4">
                             <button onClick={() => setIncludeSound(true)} className={`flex items-center space-x-2 p-2 rounded-lg ${includeSound ? 'text-purple-700' : 'text-gray-500'}`}>
                                 <div className={`w-4 h-4 rounded-full border flex items-center justify-center ${includeSound ? 'bg-purple-600 border-purple-600' : 'border-gray-300'}`}>
                                     {includeSound && <div className="w-2 h-2 bg-white rounded-full"></div>}
                                 </div>
                                 <span className="text-sm">开启 (AI智能匹配)</span>
                             </button>
                             <button onClick={() => setIncludeSound(false)} className={`flex items-center space-x-2 p-2 rounded-lg ${!includeSound ? 'text-purple-700' : 'text-gray-500'}`}>
                                 <div className={`w-4 h-4 rounded-full border flex items-center justify-center ${!includeSound ? 'bg-purple-600 border-purple-600' : 'border-gray-300'}`}>
                                     {!includeSound && <div className="w-2 h-2 bg-white rounded-full"></div>}
                                 </div>
                                 <span className="text-sm">无声</span>
                             </button>
                         </div>
                    </div>
                    {/* Motion Intensity */}
                    <div>
                         <label className="text-sm font-medium text-gray-700 block mb-2">运动强度</label>
                         <div className="flex space-x-2">
                             {motionOptions.map(mo => (
                                <button 
                                  key={mo.id} 
                                  onClick={() => setMotionIntensity(mo.id)} 
                                  className={`flex-1 py-2 px-1 text-xs rounded-lg border ${
                                    mo.id === motionIntensity 
                                      ? 'bg-purple-100 text-purple-700 border-purple-300' 
                                      : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50'
                                  }`}
                                >
                                     {mo.name}
                                </button>
                             ))}
                         </div>
                    </div>
                </div>
            )}

            {/* Tips - Not shown for Memory Echo mode */}
            {selectedMode !== 'memoryEcho' && (
              <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
                <h3 className="text-sm font-medium text-blue-800 flex items-center mb-1"> <Sparkles size={16} className="mr-1 text-blue-500" /> 织梦小贴士 </h3>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li className="flex items-start"><Check size={12} className="mr-1 mt-0.5 shrink-0"/> 描述越详细，动作、表情、光线越清晰，效果越好。</li>
                  <li className="flex items-start"><Check size={12} className="mr-1 mt-0.5 shrink-0"/> 参考图能帮助AI更好地还原风格或角色外观。</li>
                  <li className="flex items-start"><Check size={12} className="mr-1 mt-0.5 shrink-0"/> 选择合适的时长和风格，适配你的分享场景。</li>
                </ul>
              </div>
            )}
            <div className="h-20"></div> {/* Spacer for fixed button */}
          </div>
        )}

        {/* Processing Step */}
        {step === 'processing' && (
           <div className="flex flex-col items-center justify-center py-8 px-4">
             <div className="w-full max-w-sm mb-8">
               <div className="flex justify-between items-center mb-2">
                 <div className="text-sm font-medium text-gray-700 flex items-center">
                   <RotateCw size={16} className={`mr-1 text-blue-600 ${processingStatus === 'processing' ? 'animate-spin' : ''}`} />
                   {getProcessingStatusText()}
                 </div>
                 <div className="text-xs text-gray-500">{Math.round(processingProgress)}%</div>
               </div>
               <div className="w-full bg-gray-200 rounded-full h-2 mb-1 overflow-hidden">
                 <div className="h-full bg-blue-600 transition-all duration-300 ease-out" style={{ width: `${processingProgress}%` }}></div>
               </div>
               <div className="text-xs text-gray-500 flex justify-between">
                 <span>预计等待: {estimatedTime}</span>
               </div>
             </div>
             <div className="bg-white rounded-xl p-4 shadow-md w-full max-w-sm">
                {/* Video placeholder animation */}
                <div className="bg-gray-100 rounded-lg overflow-hidden aspect-video mb-4 flex items-center justify-center relative loading-animation-video">
                    <div className="absolute inset-0 flex items-center justify-center">
                       <div className="text-purple-600 bg-white bg-opacity-80 px-3 py-2 rounded-lg shadow-sm">
                          <Loader size={24} className="animate-spin mx-auto mb-2" />
                          <p className="text-xs text-center">视频生成中...</p>
                       </div>
                    </div>
                </div>
               <div className="bg-blue-50 rounded-lg p-3 text-xs text-blue-800">
                 <div className="flex items-center mb-2">
                   <MessageCircle size={14} className="mr-1 text-blue-600" />
                   <span className="font-medium">有用提示</span>
                 </div>
                 <p>视频生成需要一点时间，您可以放心离开此页面，完成后会通过顶部任务栏或通知提醒您。</p>
               </div>
             </div>
           </div>
        )}

        {/* Complete Step */}
        {step === 'complete' && resultVideo && (
           <div className="flex flex-col items-center py-4 px-4">
             <div className="bg-white rounded-xl p-4 shadow-md w-full mb-4">
                {/* Video Player Placeholder */}
                <VideoPlayerPlaceholder src={resultVideo.url} coverUrl={resultVideo.coverUrl} />

                <div className="flex justify-between mb-3 mt-4">
                  <button className="flex-1 mr-1 py-2 rounded-lg bg-purple-100 text-purple-700 text-sm font-medium flex items-center justify-center">
                    <Eye size={16} className="mr-1" />
                    预览
                  </button>
                  <button className="flex-1 ml-1 py-2 rounded-lg bg-gray-100 text-gray-700 text-sm font-medium flex items-center justify-center">
                    <Download size={16} className="mr-1" />
                    下载
                  </button>
                </div>

                <div className="mt-4 space-y-3">
                   <div>
                     <h3 className="text-sm font-medium text-gray-700 mb-1">视频描述</h3>
                     <div className="text-xs text-gray-600 bg-gray-50 rounded-lg p-3 relative">
                       {videoDescription}
                       <button onClick={() => navigator.clipboard.writeText(videoDescription)} className="absolute top-2 right-2 text-purple-600 opacity-50 hover:opacity-100"> 
                         <Copy size={14} /> 
                       </button>
                     </div>
                   </div>
                   <div className="grid grid-cols-2 gap-2 text-xs text-gray-500">
                       <span>风格: {selectedStyle?.name || '未知'}</span>
                       <span>时长: {selectedDuration}秒</span>
                       <span>方向: 竖屏</span>
                       <span>音效: {includeSound ? '开启' : '关闭'}</span>
                   </div>
                </div>
             </div>
             <div className="w-full">
                 {/* 操作按钮 - 修改为匹配OptimizedDimensionBrush组件 */}
                 <div className="grid grid-cols-2 gap-3 mb-3">
                   <button
                     onClick={() => alert('分享功能将在此实现，将视频分享到社交媒体')}
                     className="py-3 rounded-lg font-medium bg-gradient-to-r from-indigo-600 to-purple-600 text-white flex items-center justify-center"
                   >
                     <Share size={18} className="mr-2" />
                     分享
                   </button>
                   
                   <button
                     onClick={handleCompleteCreation}
                     className="py-3 rounded-lg font-medium bg-purple-100 text-purple-700 flex items-center justify-center"
                   >
                     <Download size={18} className="mr-2" />
                     保存
                   </button>
                 </div>
                 
                 {/* 再试一次按钮 */}
                 <div className="space-y-3">
                   <button
                     onClick={() => setStep('edit')}
                     className="w-full py-3 rounded-lg font-medium bg-white border border-purple-300 text-purple-700 flex items-center justify-center"
                   >
                     <RefreshCw size={16} className="mr-2" />
                     修改后重试
                   </button>
                 </div>
             </div>
           </div>
        )}

      </div> {/* End Main Content */}

      {/* Fixed Bottom Buttons */}
      {step === 'edit' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          {/* Payment Method Selection (always shown with default) */}
          <div className="mb-3 p-2 bg-gray-50 rounded-lg flex items-center justify-between">
            <div className="flex items-center">
              {selectedPaymentMethod && paymentOptions[selectedPaymentMethod].icon}
              <span className="ml-2 text-sm font-medium">
                {selectedPaymentMethod ? paymentOptions[selectedPaymentMethod].name : '选择支付方式'}
              </span>
              {selectedPaymentMethod && (selectedPaymentMethod === 'partyCoins' || selectedPaymentMethod === 'starlightCoins') && (
                <span className="ml-2 text-sm">
                  需消耗 <span className="font-bold">{paymentOptions[selectedPaymentMethod].amount}</span>
                  <span className={`ml-1 text-xs ${getBalanceColorClass(selectedPaymentMethod, paymentOptions[selectedPaymentMethod].amount)}`}>
                    (余额: {userBalance[selectedPaymentMethod]})
                  </span>
                </span>
              )}
              {selectedPaymentMethod && selectedPaymentMethod === 'watchVideo' && (
                <span className="ml-2 text-xs text-green-600">
                  观看30秒视频
                </span>
              )}
            </div>
            <button 
              onClick={() => setShowPaymentMethods(true)}
              className="text-xs text-purple-600 border border-purple-200 rounded-lg px-2 py-1"
            >
              {selectedPaymentMethod ? '更换' : '选择'}
            </button>
          </div>
          <button
            onClick={handleCreateVideo}
            disabled={(selectedMode !== 'memoryEcho' && !videoDescription.trim()) || !selectedStyle || isEnhancingPrompt || !isSelectedPaymentValid()}
            className={`w-full py-3 rounded-lg text-sm font-medium ${
              (selectedMode === 'memoryEcho' || videoDescription.trim()) && selectedStyle && !isEnhancingPrompt && isSelectedPaymentValid()
                ? 'bg-green-500 text-white' 
                : 'bg-gray-300 text-gray-500'
            }`}
          >
            <Film size={18} className="mr-2 inline"/>
            开始织梦
          </button>
        </div>
      )}

      {step === 'processing' && (
        <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-white border-t border-gray-200 p-4 z-10">
          <button 
            onClick={handleCancelCreation} 
            className="w-full py-3 rounded-lg bg-red-500 text-white text-sm font-medium"
          >
            取消创建
          </button>
        </div>
      )}

      {/* Payment method selection modal */}
      {showPaymentMethods && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center p-4">
            <div className="bg-white rounded-t-xl w-full max-w-md">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <h2 className="text-lg font-bold">选择支付方式</h2>
                <button 
                onClick={() => setShowPaymentMethods(false)}
                className="text-gray-500 p-1 rounded-full hover:bg-gray-100"
                >
                <X size={20} />
                </button>
            </div>
            
            <div className="p-4">
                <div className="grid grid-cols-1 gap-3">
                {/* Party Coins Option */}
                <div className={`p-4 rounded-lg text-left border ${
                    tempPaymentMethod === 'partyCoins' 
                    ? 'bg-yellow-50 border-yellow-300' 
                    : 'bg-white border-gray-200'
                }`}>
                    <button 
                    onClick={() => setTempPaymentMethod('partyCoins')}
                    className="w-full flex items-center"
                    >
                    <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                        <Coins size={20} className="text-yellow-600" />
                    </div>
                    <div className="flex-1">
                        <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium">派对币</h3>
                        <span className={`text-sm font-medium ${getBalanceColorClass('partyCoins', paymentOptions.partyCoins.amount)}`}>
                            余额: {userBalance.partyCoins}
                        </span>
                        </div>
                        <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-gray-500">用派对币支付视频生成费用</p>
                        <span className="text-xs font-bold text-yellow-600">
                            -  {paymentOptions.partyCoins.amount}
                        </span>
                        </div>
                    </div>
                    {tempPaymentMethod === 'partyCoins' && (
                        <CheckCircle size={18} className="text-yellow-600 ml-2" />
                    )}
                    </button>
                    
                    {/* Add Get Party Coins button */}
                    <div className="mt-3 pl-13">
                    <button 
                        onClick={() => alert('跳转到派对币获取页面')}
                        className="ml-12 px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium flex items-center justify-center"
                    >
                        <Plus size={14} className="mr-1" />
                        获取派对币
                    </button>
                    </div>
                </div>
                
                {/* Starlight Coins Option */}
                <div className={`p-4 rounded-lg text-left border ${
                    tempPaymentMethod === 'starlightCoins' 
                    ? 'bg-blue-50 border-blue-300' 
                    : 'bg-white border-gray-200'
                }`}>
                    <button 
                    onClick={() => setTempPaymentMethod('starlightCoins')}
                    className="w-full flex items-center"
                    >
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <DollarSign size={20} className="text-blue-600" />
                    </div>
                    <div className="flex-1">
                        <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium">星光币</h3>
                        <span className={`text-sm font-medium ${getBalanceColorClass('starlightCoins', paymentOptions.starlightCoins.amount)}`}>
                            余额: {userBalance.starlightCoins}
                        </span>
                        </div>
                        <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-gray-500">用星光币支付视频生成费用</p>
                        <span className="text-xs font-bold text-blue-600">
                            - {paymentOptions.starlightCoins.amount}
                        </span>
                        </div>
                    </div>
                    {tempPaymentMethod === 'starlightCoins' && (
                        <CheckCircle size={18} className="text-blue-600 ml-2" />
                    )}
                    </button>
                    
                    {/* Add Recharge Starlight Coins button */}
                    <div className="mt-3 pl-13">
                    <button 
                        onClick={() => alert('跳转到星光币充值页面')}
                        className="ml-12 px-3 py-1.5 bg-blue-100 text-blue-700 rounded-full text-xs font-medium flex items-center justify-center"
                    >
                        <Plus size={14} className="mr-1" />
                        充值星光币
                    </button>
                    </div>
                </div>
                
                {/* Watch Video Option */}
                <button 
                    onClick={() => setTempPaymentMethod('watchVideo')}
                    className={`p-4 rounded-lg text-left flex items-center border ${
                    tempPaymentMethod === 'watchVideo' 
                        ? 'bg-green-50 border-green-300' 
                        : 'bg-white border-gray-200 hover:bg-gray-50'
                    }`}
                >
                    <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <PlayCircle size={20} className="text-green-600" />
                    </div>
                    <div>
                    <h3 className="text-sm font-medium">观看激励视频</h3>
                    <p className="text-xs text-gray-500 mt-1">观看短视频广告以免费创作</p>
                    </div>
                    {tempPaymentMethod === 'watchVideo' && (
                    <CheckCircle size={18} className="text-green-600 ml-2" />
                    )}
                </button>
                </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
                <button 
                onClick={() => {
                    setSelectedPaymentMethod(tempPaymentMethod);
                    setShowPaymentMethods(false);
                }}
                className="w-full py-3 bg-purple-600 rounded-lg text-white font-medium"
                >
                确认支付方式
                </button>
            </div>
            </div>
        </div>
        )}

      {/* Enhancement options modal */}
      {showEnhanceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl w-full max-w-md overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-bold">选择润色方式</h2>
              <button 
                onClick={() => setShowEnhanceModal(false)}
                className="text-gray-500 p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4 max-h-[50vh] overflow-y-auto">
              <div className="space-y-3">
                {enhanceStyles.map((style) => (
                  <button
                    key={style.id}
                    onClick={() => {
                      setSelectedEnhanceStyle(style);
                      if (style.id !== 'custom') {
                        setCustomEnhanceText('');
                      }
                    }}
                    className={`w-full p-3 border rounded-lg text-left flex items-center transition-colors ${
                      selectedEnhanceStyle?.id === style.id 
                        ? 'border-purple-500 bg-purple-50' 
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                      selectedEnhanceStyle?.id === style.id 
                        ? 'bg-purple-100' 
                        : 'bg-gray-100'
                    }`}>
                      {style.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium">{style.name}</h3>
                      <p className="text-xs text-gray-500">{style.description}</p>
                    </div>
                    {selectedEnhanceStyle?.id === style.id && (
                      <CheckCircle size={18} className="text-purple-600 ml-2" />
                    )}
                  </button>
                ))}
              </div>
              
              {selectedEnhanceStyle?.id === 'custom' && (
                <div className="mt-4">
                  <label className="text-sm font-medium text-gray-700 block mb-2">
                    自定义润色指令
                  </label>
                  <textarea
                    value={customEnhanceText}
                    onChange={(e) => setCustomEnhanceText(e.target.value)}
                    placeholder="例如：添加更多关于场景光线的描述，强调角色的表情..."
                    className="w-full p-3 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[80px] resize-none"
                  ></textarea>
                </div>
              )}
              
              <div className="mt-4 bg-blue-50 rounded-lg p-3 border border-blue-100">
                <h3 className="text-xs font-medium text-blue-800 flex items-center mb-1">
                  <Sparkles size={14} className="mr-1 text-blue-500" />
                  润色效果预览
                </h3>
                <p className="text-xs text-blue-700">
                  {selectedEnhanceStyle ? (
                    selectedEnhanceStyle.id === 'more_detailed' ? 
                      "原始描述将添加更丰富的环境、光线和细节描述。" :
                    selectedEnhanceStyle.id === 'more_concise' ?
                      "将保留关键内容，使描述更加精炼和有力。" :
                    selectedEnhanceStyle.id === 'more_dramatic' ?
                      "将添加更多情感和氛围元素，增强画面的戏剧性。" :
                    selectedEnhanceStyle.id === 'more_professional' ?
                      "将使用更专业的摄影和艺术术语，提升描述的专业性。" :
                    selectedEnhanceStyle.id === 'custom' ?
                      "将根据您的自定义指令进行润色。" :
                      "请选择一种润色方式。"
                  ) : "请选择一种润色方式。"}
                </p>
              </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <div className="flex gap-2">
                <button 
                  onClick={() => setShowEnhanceModal(false)}
                  className="flex-1 py-3 bg-gray-100 rounded-lg text-gray-700 font-medium"
                >
                  取消
                </button>
                <button 
                  onClick={handleEnhanceDescription}
                  disabled={!selectedEnhanceStyle || (selectedEnhanceStyle?.id === 'custom' && !customEnhanceText.trim())}
                  className={`flex-1 py-3 rounded-lg font-medium ${
                    (!selectedEnhanceStyle || (selectedEnhanceStyle?.id === 'custom' && !customEnhanceText.trim())) 
                      ? 'bg-gray-300 text-gray-500' 
                      : 'bg-purple-600 text-white'
                  }`}
                >
                  应用润色
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Reference image selection modal */}
      {showReferenceSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center p-4">
          <div className="bg-white rounded-t-xl w-full max-w-md">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-bold">添加参考图片</h2>
            </div>
            
            <div className="p-4">
              <div className="grid grid-cols-1 gap-3">
                <button 
                  onClick={() => handleAddReferenceImage('library')}
                  className="p-4 bg-purple-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                    <FileImage size={20} className="text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">从素材库选择</h3>
                    <p className="text-xs text-gray-500">使用已有的角色图片作为参考</p>
                  </div>
                </button>
                
                <button 
                  onClick={() => handleAddReferenceImage('camera')}
                  className="p-4 bg-blue-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <Camera size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">拍摄照片</h3>
                    <p className="text-xs text-gray-500">使用相机拍摄新照片作为参考</p>
                  </div>
                </button>
                
                <button 
                  onClick={() => handleAddReferenceImage('gallery')}
                  className="p-4 bg-green-50 rounded-lg text-left flex items-center"
                >
                  <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <Upload size={20} className="text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">从相册选择</h3>
                    <p className="text-xs text-gray-500">选择本地图片作为参考</p>
                  </div>
                </button>
              </div>
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <button 
                onClick={() => setShowReferenceSelector(false)}
                className="w-full py-3 bg-gray-100 rounded-lg text-gray-700 font-medium"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Asset library selection modal */}
      {showLibrarySelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex flex-col">
          <div className="bg-white p-4 flex items-center justify-between border-b border-gray-200">
            <h2 className="text-lg font-bold flex items-center">
              <ChevronLeft 
                size={20} 
                className="mr-2 cursor-pointer" 
                onClick={() => {
                  setShowLibrarySelector(false);
                  setShowReferenceSelector(true);
                }}
              />
              素材库图片
            </h2>
            <button 
              onClick={() => setShowLibrarySelector(false)}
              className="text-gray-500"
            >
              <X size={20} />
            </button>
          </div>
          
          <div className="flex-1 bg-gray-50 p-4 overflow-auto">
            {imageAssets.length > 0 ? (
              <div className="grid grid-cols-2 gap-3">
                {imageAssets.map((asset) => (
                  <div 
                    key={asset.id} 
                    onClick={() => handleSelectAsset(asset)}
                    className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                  >
                    <div className="relative aspect-[4/3] bg-gray-100">
                      <img 
                        src={asset.url} 
                        alt={asset.title}
                        className="w-full h-full object-cover"
                      />
                      {selectedReferenceImages.some(img => img.id === asset.id) && (
                        <div className="absolute inset-0 bg-purple-500 bg-opacity-30 flex items-center justify-center">
                          <div className="bg-white rounded-full p-1">
                            <Check size={18} className="text-purple-600" />
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="p-2">
                      <p className="text-sm font-medium truncate">{asset.title}</p>
                      <p className="text-xs text-gray-500">{asset.createdAt}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                  <FileImage size={24} className="text-gray-400" />
                </div>
                <p className="text-gray-500">暂无图片素材</p>
                <p className="text-xs text-gray-400 mt-1">请先创建一些图片素材</p>
              </div>
            )}
          </div>
          
          <div className="bg-white p-4 border-t border-gray-200">
            <button 
              onClick={() => setShowLibrarySelector(false)}
              className="w-full py-3 bg-purple-600 rounded-lg text-white font-medium"
            >
              完成选择
            </button>
          </div>
        </div>
      )}

      {/* Styles */}
      <style jsx>{`
        /* Reused styles from CreateImagePage */
        .loading-animation-video {
          background: linear-gradient(-45deg, #e6e6e6, #f0f0f0, #ebebeb, #f5f5f5);
          background-size: 400% 400%;
          animation: gradient 2s ease infinite;
        }
        
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>
    </div>
  );
};

export default DreamWeaverVideoCreator;