import React, { useState } from 'react';
import { 
  ChevronLeft, Play, Heart, Eye, Star, Sparkles, 
  Zap, Award, Crown, TrendingUp, Gift, Bookmark, Filter, Shuffle,
  Music, Headphones, HeartPulse, Camera, Plus, Percent
} from 'lucide-react';

const TalentsView = () => {
  const [selectedTab, setSelectedTab] = useState('unlocked');
  const [sortMethod, setSortMethod] = useState('popular');
  const [talentCategory, setTalentCategory] = useState('hot');
  
  // 已解锁的才艺数据
  const unlockedTalents = [
    {
      id: 1,
      title: "案件解析",
      description: "“针对这个东方列车杀人案的案件，你有何看法？”",
      views: 9876,
      likes: 876,
      duration: "12秒",
      thumbnail: "/api/placeholder/120/80",
      isPinned: true
    },
    {
      id: 2,
      title: "调查思考",
      description: "“这些线索背后隐藏着什么样的真相？你有什么推理？”",
      views: 6543,
      likes: 543,
      duration: "8秒",
      thumbnail: "/api/placeholder/120/80"
    },
    {
      id: 3,
      title: "休闲时刻",
      description: "“最近过得怎么样？我正在享用一杯咖啡休息一下。”",
      views: 4321,
      likes: 321,
      duration: "10秒",
      thumbnail: "/api/placeholder/120/80"
    }
  ];
  
  // 可以解锁的才艺分类
  const talentCategories = [
    { id: 'hot', name: '热门', icon: <TrendingUp size={14} /> },
    { id: 'emotion', name: '情感达人', icon: <HeartPulse size={14} /> },
    { id: 'dance', name: '舞蹈达人', icon: <Music size={14} /> },
    { id: 'sports', name: '运动达人', icon: <Zap size={14} /> },
    { id: 'daily', name: '日常生活', icon: <Camera size={14} /> }
  ];
  
  // 可解锁的才艺视频
  const lockableTalents = [
    {
      id: 101,
      title: "赛博约会",
      category: "emotion",
      description: "K 深情对视，表达爱意",
      popularityScore: 98,
      price: 200,
      priceType: "星光币",
      special: "限时优惠",
      thumbnail: "/api/placeholder/130/80",
      hot: true
    },
    {
      id: 102,
      title: "街头机械舞",
      category: "dance",
      description: "K 展示未来风格的机械舞步",
      popularityScore: 92,
      price: 250,
      priceType: "星光币",
      special: null,
      thumbnail: "/api/placeholder/130/80",
      hot: true
    },
    {
      id: 103,
      title: "酷跑特技",
      category: "sports",
      description: "K 在霓虹都市中展示跑酷技巧",
      popularityScore: 85,
      price: 180,
      priceType: "星光币",
      special: null,
      thumbnail: "/api/placeholder/130/80",
      hot: true
    },
    {
      id: 104,
      title: "电子乐演奏",
      category: "dance",
      description: "K 演奏未来风电子乐器",
      popularityScore: 79,
      price: 150,
      priceType: "星光币",
      special: null,
      thumbnail: "/api/placeholder/130/80"
    },
    {
      id: 105,
      title: "情感倾诉",
      category: "emotion",
      description: "K 在雨中深情告白",
      popularityScore: 88,
      price: 220,
      priceType: "星光币",
      special: "新品",
      thumbnail: "/api/placeholder/130/80",
      hot: true
    },
    {
      id: 106,
      title: "日常整理",
      category: "daily",
      description: "K 整理案件资料的日常",
      popularityScore: 74,
      price: 130,
      priceType: "星光币",
      special: null,
      thumbnail: "/api/placeholder/130/80"
    }
  ];
  
  const filteredLockableTalents = talentCategory === 'hot' 
    ? lockableTalents.filter(talent => talent.hot)
    : lockableTalents.filter(talent => talent.category === talentCategory);
  
  // 创建者信息
  const creatorInfo = {
    name: "硬核创作者",
    avatar: "/api/placeholder/100/100",
    partyCoins: 1250,
    talents: 3,
    availableUnlocks: 2
  };
  
  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg">
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white">
        <div className="flex items-center">
          <button className="p-1 mr-2">
            <ChevronLeft size={20} className="text-white" />
          </button>
          <h1 className="text-lg font-bold">角色才艺</h1>
        </div>
        <div className="flex items-center">
          <div className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full mr-2">
            <Sparkles size={12} className="inline mr-1" />
            {creatorInfo.talents}个才艺
          </div>
          <div className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">
            <Plus size={12} className="inline mr-1" />
            {creatorInfo.availableUnlocks}次解锁机会
          </div>
        </div>
      </div>
      
      {/* 创建者信息 */}
      <div className="bg-indigo-900 p-4">
        <div className="bg-white rounded-xl shadow-lg p-3 mt-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full avatar-placeholder mr-3">
                创作
              </div>
              <div>
                <h2 className="text-md font-bold">{creatorInfo.name}</h2>
                <div className="flex items-center mt-1">
                  <div className="text-xs text-amber-600 font-medium flex items-center">
                    <Gift size={12} className="inline mr-1" />
                    {creatorInfo.partyCoins} 星光币
                  </div>
                </div>
              </div>
            </div>
            <button className="px-3 py-1.5 bg-gradient-to-r from-amber-500 to-amber-600 text-white text-xs rounded-full font-medium">
              获取星光币
            </button>
          </div>
        </div>
      </div>
      
      {/* 标签栏 */}
      <div className="border-b border-gray-200 bg-white">
        <div className="flex">
          <button 
            onClick={() => setSelectedTab('unlocked')}
            className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'unlocked' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
          >
            已解锁才艺
          </button>
          <button 
            onClick={() => setSelectedTab('more')}
            className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'more' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
          >
            更多才艺
          </button>
        </div>
      </div>
      
      {/* 标签内容 */}
      <div className="flex-1 overflow-auto p-4">
        {/* 将图片替换为CSS样式的占位符 */}
        <style jsx>{`
          .image-placeholder {
            background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
            background-size: 20px 20px;
            position: relative;
          }
          .image-placeholder::after {
            content: '视频预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 10px;
            color: #666;
          }
          .avatar-placeholder {
            background: linear-gradient(135deg, #d8bfff 0%, #b180ff 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
          }
        `}</style>
        
        {selectedTab === 'unlocked' && (
          <div className="space-y-4">
            {/* 已解锁才艺概览 */}
            <div className="p-4 bg-white rounded-lg shadow-sm">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-md font-semibold flex items-center">
                  <Star size={16} className="mr-2 text-purple-600" />
                  已解锁才艺 ({unlockedTalents.length})
                </h3>
                <div className="flex items-center">
                  <div className="relative inline-block">
                    <select 
                      onChange={(e) => setSortMethod(e.target.value)}
                      value={sortMethod}
                      className="bg-purple-50 text-purple-700 text-xs rounded-full pl-6 pr-6 py-1 appearance-none cursor-pointer"
                    >
                      <option value="popular">按播放量</option>
                      <option value="random">随机</option>
                      <option value="newest">最新</option>
                    </select>
                    <Shuffle size={12} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-purple-600" />
                  </div>
                </div>
              </div>
              
              {/* 才艺视频列表 */}
              <div className="space-y-3">
                {unlockedTalents.map((talent) => (
                  <div key={talent.id} className={`p-3 rounded-lg ${talent.isPinned ? 'bg-purple-50 border border-purple-200' : 'bg-white border border-gray-200'}`}>
                    <div className="flex">
                      <div className="relative mr-3">
                        <div className="w-24 h-16 rounded-md overflow-hidden image-placeholder"></div>
                        <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
                          {talent.duration}
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="w-8 h-8 bg-black bg-opacity-40 rounded-full flex items-center justify-center">
                            <Play size={18} className="text-white ml-1" />
                          </div>
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium text-sm">
                              {talent.isPinned && (
                                <span className="inline-block bg-amber-400 text-white text-xs px-1.5 py-0.5 rounded mr-1">
                                  置顶
                                </span>
                              )}
                              {talent.title}
                            </h4>
                            <p className="text-xs text-gray-500 mt-0.5">{talent.description}</p>
                          </div>
                          <button className="text-gray-400 hover:text-gray-600">
                            {talent.isPinned ? (
                              <Crown size={16} className="text-amber-500" />
                            ) : (
                              <Bookmark size={16} />
                            )}
                          </button>
                        </div>
                        <div className="flex items-center mt-2 text-xs text-gray-500">
                          <div className="flex items-center mr-3">
                            <Eye size={12} className="mr-1" />
                            {talent.views.toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <Heart size={12} className="mr-1" />
                            {talent.likes.toLocaleString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              

            </div>
            
            {/* 才艺数据统计 */}
            <div className="p-4 bg-white rounded-lg shadow-sm">
              <h3 className="text-md font-semibold mb-3 flex items-center">
                <Award size={16} className="mr-2 text-purple-600" />
                才艺数据
              </h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="border border-gray-200 rounded-lg p-3">
                  <div className="text-sm text-gray-500">总播放量</div>
                  <div className="text-xl font-bold text-indigo-600">20,740</div>
                  <div className="text-xs text-green-600 mt-1">↑ 18% 较上周</div>
                </div>
                <div className="border border-gray-200 rounded-lg p-3">
                  <div className="text-sm text-gray-500">平均完播率</div>
                  <div className="text-xl font-bold text-indigo-600">86%</div>
                  <div className="text-xs text-green-600 mt-1">↑ 5% 较上月</div>
                </div>
              </div>
              
              <div className="mt-3 p-3 bg-purple-50 rounded-lg">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 mr-2">
                    <Zap size={16} />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-purple-700">增长提示</div>
                    <div className="text-xs text-purple-600 mt-1">
                      通过解锁更多才艺，你的角色互动率可提升约 35%！尤其是情感类才艺最受欢迎。
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {selectedTab === 'more' && (
          <div className="space-y-4">
            {/* 才艺分类 */}
            <div className="overflow-x-auto">
              <div className="flex space-x-2 pb-2">
                {talentCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setTalentCategory(category.id)}
                    className={`whitespace-nowrap px-3 py-1.5 rounded-full text-xs font-medium flex items-center ${
                      talentCategory === category.id 
                        ? 'bg-purple-600 text-white' 
                        : 'bg-gray-100 text-gray-600'
                    }`}
                  >
                    <span className="mr-1">{category.icon}</span>
                    {category.name}
                  </button>
                ))}
                <button className="whitespace-nowrap px-3 py-1.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600 flex items-center">
                  <Filter size={14} className="mr-1" />
                  筛选
                </button>
              </div>
            </div>
            
            {/* 可解锁才艺列表 */}
            <div className="grid grid-cols-2 gap-3">
              {filteredLockableTalents.map((talent) => (
                <div key={talent.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <div className="relative">
                    <div className="h-20 image-placeholder"></div>
                    <div className="absolute top-2 right-2">
                      <div className="bg-black bg-opacity-60 text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                        <Eye size={12} className="mr-1" />
                        热度 {talent.popularityScore}
                      </div>
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                        <Play size={16} className="text-white ml-1" />
                      </div>
                    </div>
                    {talent.special && (
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-r from-purple-600 to-pink-500 text-white text-xs py-1 text-center">
                        {talent.special}
                      </div>
                    )}
                  </div>
                  <div className="p-2">
                    <h4 className="font-medium text-sm">{talent.title}</h4>
                    <p className="text-xs text-gray-500 mt-0.5 h-8 overflow-hidden">
                      {talent.description}
                    </p>
                    <div className="mt-2 flex items-center justify-between">
                      <div className="text-xs text-amber-600 font-medium">
                        {talent.price} {talent.priceType}
                      </div>
                      <button className="bg-purple-500 text-white text-xs px-2.5 py-1 rounded-full">
                        解锁
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      

    </div>
  );
};

export default TalentsView;