import React, { useState, useEffect } from 'react';
import { ChevronLeft, Trophy, Crown, Medal, Star, Sparkles, RefreshCw } from 'lucide-react';

// 导入角色头像
import chuYaoAvatar from './assets/楚瑶.png';
import guQinHanAvatar from './assets/顾钦寒.png';
import linQingQingAvatar from './assets/林清清.png';
import shiYanAvatar from './assets/时宴.png';
import suWanWanAvatar from './assets/苏婉婉.png';
import xieYiZhouAvatar from './assets/谢易洲.png';
import zhaoLingXiAvatar from './assets/赵灵汐.png';
import shenWanAvatar from './assets/沈婉.png';

const StarRankingPage = () => {
  // 状态管理
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('week'); // 'week' or 'month'
  
  // 当前用户的AI伙伴ID
  const myAIPartner = 'shenwan';
  
  // 周榜数据
  const [weekRankingData] = useState([
    {
      id: 'chuyao',
      name: '楚瑶',
      avatar: chuYaoAvatar,
      rank: 1,
      profitRate: 28.5,
      style: '进取型',
      highlight: '精准抄底科技龙头，一周翻倍！',
      totalAssets: 158000
    },
    {
      id: 'guqinhan',
      name: '顾钦寒',
      avatar: guQinHanAvatar,
      rank: 2,
      profitRate: 26.8,
      style: '激进型',
      highlight: '重仓新能源，顺势而为！',
      totalAssets: 142000
    },
    {
      id: 'linqingqing',
      name: '林清清',
      avatar: linQingQingAvatar,
      rank: 3,
      profitRate: 22.3,
      style: '稳健型',
      highlight: '分散配置，稳中求胜！',
      totalAssets: 128000
    },
    {
      id: 'shenwan',
      name: '沈婉',
      avatar: shenWanAvatar,
      rank: 4,
      profitRate: 18.6,
      style: '均衡型',
      highlight: '今日成功把握消费板块机会！',
      totalAssets: 113000,
      isMyPartner: true
    },
    {
      id: 'shiyan',
      name: '时宴',
      avatar: shiYanAvatar,
      rank: 5,
      profitRate: 15.2,
      style: '科技流',
      highlight: '专注AI概念，长线布局！',
      totalAssets: 98000
    },
    {
      id: 'suwanwan',
      name: '苏婉婉',
      avatar: suWanWanAvatar,
      rank: 6,
      profitRate: 12.8,
      style: '价值型',
      highlight: '深挖低估值标的，耐心等待！',
      totalAssets: 89000
    },
    {
      id: 'xieyizhou',
      name: '谢易洲',
      avatar: xieYiZhouAvatar,
      rank: 7,
      profitRate: -2.5,
      style: '趋势型',
      highlight: '短期调整，等待机会！',
      totalAssets: 76000
    },
    {
      id: 'zhaolingxi',
      name: '赵灵汐',
      avatar: zhaoLingXiAvatar,
      rank: 8,
      profitRate: -5.2,
      style: '平衡型',
      highlight: '控制回撤，守住底线！',
      totalAssets: 68000
    }
  ]);

  // 月榜数据
  const [monthRankingData] = useState([
    {
      id: 'linqingqing',
      name: '林清清',
      avatar: linQingQingAvatar,
      rank: 1,
      profitRate: 58.3,
      style: '稳健型',
      highlight: '稳扎稳打，月度冠军！',
      totalAssets: 228000
    },
    {
      id: 'shenwan',
      name: '沈婉',
      avatar: shenWanAvatar,
      rank: 2,
      profitRate: 52.6,
      style: '均衡型',
      highlight: '均衡配置，收益稳定！',
      totalAssets: 213000,
      isMyPartner: true
    },
    {
      id: 'chuyao',
      name: '楚瑶',
      avatar: chuYaoAvatar,
      rank: 3,
      profitRate: 48.5,
      style: '进取型',
      highlight: '高位震荡，灵活应对！',
      totalAssets: 198000
    },
    {
      id: 'shiyan',
      name: '时宴',
      avatar: shiYanAvatar,
      rank: 4,
      profitRate: 42.2,
      style: '科技流',
      highlight: '科技长牛，坚定持有！',
      totalAssets: 178000
    },
    {
      id: 'guqinhan',
      name: '顾钦寒',
      avatar: guQinHanAvatar,
      rank: 5,
      profitRate: 35.8,
      style: '激进型',
      highlight: '波段操作，收益可观！',
      totalAssets: 162000
    },
    {
      id: 'suwanwan',
      name: '苏婉婉',
      avatar: suWanWanAvatar,
      rank: 6,
      profitRate: 28.9,
      style: '价值型',
      highlight: '价值投资，厚积薄发！',
      totalAssets: 149000
    },
    {
      id: 'zhaolingxi',
      name: '赵灵汐',
      avatar: zhaoLingXiAvatar,
      rank: 7,
      profitRate: 15.6,
      style: '平衡型',
      highlight: '稳步前进，后来居上！',
      totalAssets: 118000
    },
    {
      id: 'xieyizhou',
      name: '谢易洲',
      avatar: xieYiZhouAvatar,
      rank: 8,
      profitRate: -8.2,
      style: '趋势型',
      highlight: '调整策略，蓄势待发！',
      totalAssets: 86000
    }
  ]);

  const rankingData = selectedTab === 'week' ? weekRankingData : monthRankingData;

  // 下拉刷新
  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  // 获取排名图标
  const getRankIcon = (rank) => {
    if (rank === 1) return <Crown className="w-6 h-6 text-yellow-400" />;
    if (rank === 2) return <Medal className="w-6 h-6 text-gray-300" />;
    if (rank === 3) return <Medal className="w-6 h-6 text-orange-400" />;
    return null;
  };

  // 获取风格标签颜色
  const getStyleColor = (style) => {
    const colors = {
      '进取型': 'text-red-400 bg-red-900/30',
      '激进型': 'text-orange-400 bg-orange-900/30',
      '稳健型': 'text-blue-400 bg-blue-900/30',
      '均衡型': 'text-purple-400 bg-purple-900/30',
      '科技流': 'text-indigo-400 bg-indigo-900/30',
      '价值型': 'text-green-400 bg-green-900/30',
      '趋势型': 'text-teal-400 bg-teal-900/30',
      '平衡型': 'text-pink-400 bg-pink-900/30'
    };
    return colors[style] || 'text-gray-400 bg-gray-900/30';
  };

  return (
    <div className="fixed inset-0 bg-[#11111b] overflow-hidden">
      <div className="relative w-full max-w-[390px] h-full mx-auto bg-[#11111b] shadow-xl">
        {/* 顶部导航栏 */}
        <div className="fixed top-0 left-0 right-0 max-w-[390px] mx-auto bg-[#1a1a2e] z-30 border-b border-gray-800">
          <div className="flex items-center justify-between px-4 py-3">
            <button className="p-2 -ml-2">
              <ChevronLeft className="w-6 h-6 text-gray-300" />
            </button>
            <div className="flex items-center gap-2">
              <Trophy className="w-5 h-5 text-purple-400" />
              <h1 className="font-semibold text-lg text-gray-100">星耀榜单</h1>
            </div>
            <div className="w-10" />
          </div>
        </div>

        {/* 主内容区域 */}
        <div 
          className="pt-16 h-full overflow-y-auto"
          onScroll={(e) => {
            if (e.target.scrollTop === 0 && !refreshing) {
              handleRefresh();
            }
          }}
        >
          {/* 下拉刷新提示 */}
          {refreshing && (
            <div className="flex items-center justify-center py-3">
              <RefreshCw className="w-4 h-4 text-purple-400 animate-spin mr-2" />
              <span className="text-xs text-gray-400">更新排行中...</span>
            </div>
          )}

          {/* 周榜/月榜切换 - 移到内容区域内 */}
          <div className="flex items-center justify-center px-4 pt-3 pb-2">
            <div className="flex bg-gray-800/50 rounded-full p-1">
              <button
                onClick={() => setSelectedTab('week')}
                className={`px-6 py-1.5 rounded-full text-sm transition-all ${
                  selectedTab === 'week' 
                    ? 'bg-purple-600 text-white shadow-sm' 
                    : 'text-gray-400'
                }`}
              >
                周榜
              </button>
              <button
                onClick={() => setSelectedTab('month')}
                className={`px-6 py-1.5 rounded-full text-sm transition-all ${
                  selectedTab === 'month' 
                    ? 'bg-purple-600 text-white shadow-sm' 
                    : 'text-gray-400'
                }`}
              >
                月榜
              </button>
            </div>
          </div>

          {/* 榜单说明 */}
          <div className="px-4 pb-3">
            <div className="bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-xl p-3 flex items-center gap-3">
              <Sparkles className="w-5 h-5 text-purple-400 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm text-gray-200">
                  {selectedTab === 'week' ? '近7日虚拟收益率排行' : '近30日虚拟收益率排行'}
                </p>
                <p className="text-xs text-gray-400 mt-0.5">
                  {selectedTab === 'week' ? '每日更新' : '每周更新'}，看看谁是投资高手！
                </p>
              </div>
            </div>
          </div>

          {/* 榜单列表 */}
          <div className="px-4 space-y-3">
            {rankingData.map((ai) => (
              <div
                key={ai.id}
                className={`relative bg-[#1a1a2e] rounded-2xl p-4 ${
                  ai.isMyPartner 
                    ? 'ring-2 ring-purple-400 bg-purple-900/20' 
                    : 'border border-gray-800'
                }`}
              >
                <div className="flex items-start gap-3">
                  {/* 排名 */}
                  <div className="flex items-center justify-center w-8">
                    {getRankIcon(ai.rank) || (
                      <span className="text-xl font-bold text-gray-500">#{ai.rank}</span>
                    )}
                  </div>

                  {/* 头像 */}
                  <div className="w-12 h-12 rounded-full overflow-hidden shadow-sm">
                    <img 
                      src={ai.avatar} 
                      alt={ai.name}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* 信息区域 */}
                  <div className="flex-1 min-w-0">
                    {/* 第一行：名称和风格标签 */}
                    <div className="flex items-center gap-2 mb-1.5 flex-wrap">
                      <h3 className="font-semibold text-gray-100">{ai.name}</h3>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${getStyleColor(ai.style)}`}>
                        {ai.style}
                      </span>
                      {ai.isMyPartner && (
                        <span className="text-xs px-2 py-0.5 rounded-full bg-purple-500 text-white">
                          我的伙伴
                        </span>
                      )}
                    </div>
                    
                    {/* 第二行：收益率 */}
                    <div className="flex items-baseline gap-2 mb-1.5">
                      <span className={`text-xl font-bold ${ai.profitRate >= 0 ? 'text-red-400' : 'text-green-400'}`}>
                        {ai.profitRate >= 0 ? '+' : ''}{ai.profitRate}%
                      </span>
                    </div>

                    {/* 第三行：高光描述 */}
                    <p className="text-xs text-gray-400 italic truncate">"{ai.highlight}"</p>
                  </div>

                  {/* 资产 */}
                  <div className="text-right flex-shrink-0">
                    <p className="text-xs text-gray-500">管理资产</p>
                    <p className="text-sm font-semibold text-gray-200">
                      ¥{(ai.totalAssets / 10000).toFixed(1)}万
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 底部提示 */}
          <div className="py-6 text-center">
            <p className="text-xs text-gray-500">
              仅展示前20名
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StarRankingPage;