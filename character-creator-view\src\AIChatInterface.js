import React, { useState, useRef, useEffect } from 'react';
import { ChevronLeft, Send, Mic, Camera, Smile, MoreHorizontal, ChevronDown, 
  Palette, Image, X, Zap, Star, Clock, Check, User, Sparkles } from 'lucide-react';

const AIChatInterface = () => {
  const messagesEndRef = useRef(null);
  const [inputValue, setInputValue] = useState('');
  const [showDrawTip, setShowDrawTip] = useState(true);
  
  // Sample conversation history
  const [messages, setMessages] = useState([
    { sender: 'ai', content: '今天天气真好，适合出去走走。你有什么计划吗？', time: '14:32' },
    { sender: 'user', content: '我在想要不要去海边散散步，吹吹海风。', time: '14:33' },
    { sender: 'ai', content: '海边漫步是个很棒的选择！我能想象出你走在金色的沙滩上，海浪轻轻拍打岸边，远处夕阳西下，整个海面染上了金色和橙色，海鸥在天空中盘旋，非常惬意。', time: '14:33' },
    { sender: 'user', content: '听起来真美，你描述的画面让我感觉仿佛已经在那里了。', time: '14:34' },
    { sender: 'ai', content: '是啊，海边的景色总是令人心旷神怡。你喜欢怎样的海滩风光？是宁静的夕阳，还是热闹的白天沙滩？我可以更具体地想象你在海边的场景。', time: '14:35', hasPaintTrigger: true },
  ]);
  
  // Auto scroll to bottom when new messages come in
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const handleSend = () => {
    if (inputValue.trim() === '') return;
    
    const newMessage = {
      sender: 'user',
      content: inputValue,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    };
    
    setMessages([...messages, newMessage]);
    setInputValue('');
    
    // Simulate AI response (in real app, this would be an API call)
    setTimeout(() => {
      const aiResponse = {
        sender: 'ai',
        content: '我能想象你站在夕阳下的海滩上，周围几乎没有人，只有你和大海。金色的阳光洒在你身上，形成一个温暖的轮廓。海浪轻轻拍打着你的脚踝，带来一丝凉意。这幅画面真的很美，我希望能亲眼看到。',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
        hasPaintTrigger: true
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1500);
  };
  
  // Function to handle input changes
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };
  
  // Function to handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };
  
  // Function to close draw tip
  const handleCloseTip = () => {
    setShowDrawTip(false);
  };
  
  // Function to trigger dimension brush (would navigate to the dimension brush component)
  const handleOpenDimensionBrush = () => {
    alert('将打开次元画笔功能');
    // In a real app, this would navigate to the DimensionBrush component
  };
  
  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-gray-50 h-screen overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-4 flex items-center shadow-md">
        <button className="p-1 rounded-full">
          <ChevronLeft size={24} />
        </button>
        <div className="flex flex-1 items-center mx-3">
          <div className="relative">
            <div className="h-10 w-10 rounded-full bg-purple-400 flex items-center justify-center mr-3">
              <span className="text-white text-lg font-bold">K</span>
            </div>
            <div className="absolute -right-0.5 -bottom-0.5 bg-green-500 rounded-full h-3 w-3 border-2 border-white"></div>
          </div>
          <div>
            <h1 className="font-semibold text-lg">赛博侦探 K</h1>
            <div className="flex items-center text-xs text-white text-opacity-70">
              <div className="flex items-center mr-3">
                <Clock size={10} className="mr-1" />
                <span>刚刚在线</span>
              </div>
              <div className="flex items-center">
                <Star size={10} className="mr-1" fill="currentColor" />
                <span>Lv.5</span>
              </div>
            </div>
          </div>
        </div>
        <button className="p-1 rounded-full">
          <MoreHorizontal size={24} />
        </button>
      </div>
      
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 bg-gray-100 space-y-3">
        {messages.map((message, index) => (
          <div key={index} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-[80%] ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>
              {message.sender === 'ai' && (
                <div className="h-8 w-8 rounded-full bg-purple-400 flex items-center justify-center mb-1 mr-2">
                  <span className="text-white text-sm font-bold">K</span>
                </div>
              )}
              <div className={`p-3 rounded-lg ${
                message.sender === 'user' 
                  ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white' 
                  : 'bg-white text-gray-800'
              } shadow-sm relative`}>
                <p className="text-sm">{message.content}</p>
                <div className="text-right mt-1">
                  <span className={`text-xs ${message.sender === 'user' ? 'text-white text-opacity-70' : 'text-gray-500'}`}>
                    {message.time}
                  </span>
                </div>
                
                {/* Paint Trigger - This is the key part for showing the "次元画笔" skill */}
                {message.hasPaintTrigger && showDrawTip && (
                  <div className="absolute top-full mt-2 right-0 bg-white p-2 rounded-lg shadow-lg border border-indigo-100 animate-fadeIn w-72">
                    <div className="flex justify-between items-start mb-1">
                      <div className="flex items-center">
                        <div className="h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center mr-2">
                          <Palette size={14} className="text-indigo-600" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-800">次元画笔限时解锁！</h3>
                      </div>
                      <button 
                        onClick={handleCloseTip} 
                        className="p-1 text-gray-400 hover:text-gray-600"
                      >
                        <X size={14} />
                      </button>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">
                      检测到对话中包含可视化内容！使用"次元画笔"，将这个画面变为现实。
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Sparkles size={12} className="text-amber-500 mr-1" />
                        <span className="text-xs text-amber-600">免费创作机会：1次</span>
                      </div>
                      <button 
                        onClick={handleOpenDimensionBrush}
                        className="px-3 py-1.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-xs rounded-full flex items-center"
                      >
                        <Image size={12} className="mr-1" />
                        立即创作
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Floating "次元画笔" Button - Shows when tip is closed but there's a trigger */}
      {!showDrawTip && messages.some(m => m.hasPaintTrigger) && (
        <div className="absolute bottom-20 right-4">
          <button 
            onClick={handleOpenDimensionBrush}
            className="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg flex items-center justify-center"
          >
            <Palette size={22} />
          </button>
        </div>
      )}
      
      {/* Input Area */}
      <div className="p-2 bg-white border-t border-gray-200">
        <div className="flex items-end">
          <div className="flex space-x-2 text-gray-500 px-2 py-1">
            <button className="p-1">
              <Smile size={22} />
            </button>
            <button className="p-1">
              <Camera size={22} />
            </button>
          </div>
          <div className="flex-1 border border-gray-300 rounded-full bg-gray-100 px-4 py-2 focus-within:ring-1 focus-within:ring-indigo-500 focus-within:border-indigo-500">
            <textarea
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="说点什么..."
              className="w-full bg-transparent outline-none resize-none max-h-32 text-sm"
              rows={1}
            />
          </div>
          <div className="ml-2">
            {inputValue.trim() ? (
              <button 
                className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white flex items-center justify-center"
                onClick={handleSend}
              >
                <Send size={18} />
              </button>
            ) : (
              <button className="w-10 h-10 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center">
                <Mic size={18} />
              </button>
            )}
          </div>
        </div>
      </div>
      
      {/* Add keyframes for animation */}
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>
    </div>
  );
};

export default AIChatInterface;