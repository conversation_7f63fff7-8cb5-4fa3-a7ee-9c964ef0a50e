import React, { useState, useEffect } from 'react';
import { 
  ChevronLeft, Calendar, PlusCircle, Clock, Edit, Trash2, Image, Play, 
  Send, Check, X, AlertCircle, Wand2, Sparkles, ArrowRight, Filter, 
  SortDesc, MoreHorizontal, Camera, Video, MessageSquare, Heart, Globe
} from 'lucide-react';

const MetaLiveScheduleManager = () => {
  // States
  const [view, setView] = useState('list'); // 'list', 'create', 'edit', 'generate'
  const [scheduledPosts, setScheduledPosts] = useState([]);
  const [selectedPost, setSelectedPost] = useState(null);
  const [filterOption, setFilterOption] = useState('all'); // 'all', 'today', 'tomorrow', 'thisWeek'
  const [sortOption, setSortOption] = useState('asc'); // 'asc', 'desc'
  
  // Form states for creating/editing posts
  const [formData, setFormData] = useState({
    type: 'image',
    title: '',
    content: '',
    mediaUrl: '/api/placeholder/400/300',
    scheduledDate: new Date().toISOString().split('T')[0],
    scheduledTime: '12:00',
    isPinned: false,
    isHot: false,
    duration: '15秒', // For video
    multipleImages: false, // For image
  });
  
  // AI generation form state
  const [generationOptions, setGenerationOptions] = useState({
    prompt: '请为我的角色生成一周的元•Live动态，风格保持一致，内容包括侦探日常、城市夜景和科技元素。',
    numPosts: 7,
    postTypes: ['image', 'video'],
    themes: ['日常', '侦探工作', '城市风光', '赛博科技'],
    includeHotPost: true,
    inspirationSources: {
      chatHistory: false,
      comments: false,
      news: false
    }
  });
  
  // Load initial data
  useEffect(() => {
    // In a real app, this would fetch from an API
    // For now, let's create some sample scheduled posts
    const today = new Date();
    const samplePosts = [
      {
        id: 1,
        type: 'image',
        title: '早晨的咖啡店',
        content: '每次破案前，我都会在这家安静的咖啡店喝上一杯。黑咖啡，不加糖，就像我的思绪一样清晰。',
        mediaUrl: '/api/placeholder/400/300',
        scheduledDate: new Date(today.getTime() + 86400000).toISOString().split('T')[0], // Tomorrow
        scheduledTime: '08:00',
        isPinned: false,
        isHot: false,
        multipleImages: false,
        status: 'scheduled'
      },
      {
        id: 2,
        type: 'video',
        title: '夜间追踪',
        content: '案件的线索总是引导我到这座城市最黑暗的角落。霓虹灯是我唯一的伴侣，而真相，就藏在阴影之中。',
        mediaUrl: '/api/placeholder/400/220',
        scheduledDate: new Date(today.getTime() + 172800000).toISOString().split('T')[0], // Day after tomorrow
        scheduledTime: '20:00',
        isPinned: false,
        isHot: true,
        duration: '18秒',
        status: 'scheduled'
      },
      {
        id: 3,
        type: 'image',
        title: '数据分析',
        content: '当物理世界的线索用尽时，数据海洋中往往藏着答案。今天分析了超过10TB的加密数据，发现了一个意想不到的模式。',
        mediaUrl: [
          '/api/placeholder/130/130',
          '/api/placeholder/130/130',
          '/api/placeholder/130/130'
        ],
        scheduledDate: new Date(today.getTime() + 259200000).toISOString().split('T')[0], // Three days from now
        scheduledTime: '15:30',
        isPinned: true,
        isHot: false,
        multipleImages: true,
        status: 'scheduled'
      }
    ];
    
    setScheduledPosts(samplePosts);
  }, []);
  
  // Handlers
  const handleCreateNew = () => {
    setFormData({
      type: 'image',
      title: '',
      content: '',
      mediaUrl: '/api/placeholder/400/300',
      scheduledDate: new Date().toISOString().split('T')[0],
      scheduledTime: '12:00',
      isPinned: false,
      isHot: false,
      duration: '15秒',
      multipleImages: false,
    });
    setView('create');
  };
  
  const handleEditPost = (post) => {
    setSelectedPost(post);
    setFormData({
      ...post,
      // Ensure date and time are properly formatted
      scheduledDate: post.scheduledDate,
      scheduledTime: post.scheduledTime
    });
    setView('edit');
  };
  
  const handleDeletePost = (postId) => {
    if (window.confirm('确定要删除这条预定的元•Live动态吗？')) {
      setScheduledPosts(scheduledPosts.filter(post => post.id !== postId));
    }
  };
  
  const handleSubmitForm = () => {
    const newPost = {
      ...formData,
      id: selectedPost ? selectedPost.id : Date.now(), // Use existing ID or create new one
      status: 'scheduled'
    };
    
    if (view === 'edit') {
      // Update existing post
      setScheduledPosts(scheduledPosts.map(post => 
        post.id === newPost.id ? newPost : post
      ));
    } else {
      // Add new post
      setScheduledPosts([...scheduledPosts, newPost]);
    }
    
    // Return to list view
    setView('list');
  };
  
  const handleGenerateWeek = () => {
    // In a real app, this would call an AI service to generate posts
    // For this demo, we'll simulate the generation
    
    const today = new Date();
    const generatedPosts = [];
    
    // Sample themes for variety
    const themes = [
      { title: '清晨思考', content: '城市刚刚苏醒，而我已经在思考今天的谜题。每一缕阳光都可能照亮隐藏的真相。', type: 'image' },
      { title: '赛博酒吧', content: '在这里，信息如同酒精一样流动。今晚我可能会收获一些有价值的线索，或者至少，一些值得思考的问题。', type: 'video' },
      { title: '证据墙', content: '将所有线索拼接在一起，寻找它们之间的联系。有时候真相就在眼前，只是以一种我们不熟悉的形式存在。', type: 'image' },
      { title: '雨夜追踪', content: '雨水冲刷着这座城市的罪恶，但无法带走所有痕迹。今晚，我追踪的不只是嫌疑人，还有正义的可能性。', type: 'video' },
      { title: '数字迷宫', content: '有时候最危险的地方不在现实世界，而在于数据的海洋。今天我潜入了一个加密的服务器，发现了惊人的秘密。', type: 'image' },
      { title: '装备升级', content: '在这个不断变化的世界，侦探的工具也需要升级。今天测试了一些新的科技装备，效果相当不错。', type: 'image' },
      { title: '角落咖啡馆', content: '在这个隐蔽的咖啡馆，我常常能听到城市最真实的声音。今天的对话特别有趣，可能会成为下一个案件的开端。', type: 'video' }
    ];
    
    // Generate posts for the next `numPosts` days
    for (let i = 0; i < generationOptions.numPosts; i++) {
      const theme = themes[i % themes.length];
      const postDate = new Date(today);
      postDate.setDate(today.getDate() + i + 1); // Start from tomorrow
      
      // Randomize post time
      const hours = Math.floor(Math.random() * 12) + 8; // Between 8AM and 8PM
      const minutes = [0, 15, 30, 45][Math.floor(Math.random() * 4)]; // 0, 15, 30, or 45
      
      // Format time as HH:MM
      const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      
      // Create post
      generatedPosts.push({
        id: Date.now() + i,
        type: theme.type,
        title: theme.title,
        content: theme.content,
        mediaUrl: theme.type === 'video' ? '/api/placeholder/400/220' : '/api/placeholder/400/300',
        scheduledDate: postDate.toISOString().split('T')[0],
        scheduledTime: timeStr,
        isPinned: i === 0, // Pin the first post
        isHot: i % 3 === 0, // Make every third post "hot"
        duration: theme.type === 'video' ? `${Math.floor(Math.random() * 10) + 10}秒` : undefined,
        multipleImages: theme.type === 'image' && i % 4 === 0, // Make some image posts have multiple images
        status: 'scheduled'
      });
    }
    
    // Add generated posts to the existing ones
    setScheduledPosts([...scheduledPosts, ...generatedPosts]);
    
    // Show success message
    alert(`成功生成了 ${generatedPosts.length} 条元•Live动态！`);
    
    // Return to list view
    setView('list');
  };
  
  // Filter posts based on selected filter
  const getFilteredPosts = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);
    
    switch (filterOption) {
      case 'today':
        return scheduledPosts.filter(post => {
          const postDate = new Date(post.scheduledDate);
          return postDate.getTime() === today.getTime();
        });
      case 'tomorrow':
        return scheduledPosts.filter(post => {
          const postDate = new Date(post.scheduledDate);
          return postDate.getTime() === tomorrow.getTime();
        });
      case 'thisWeek':
        return scheduledPosts.filter(post => {
          const postDate = new Date(post.scheduledDate);
          return postDate >= today && postDate < nextWeek;
        });
      default:
        return scheduledPosts;
    }
  };
  
  // Sort posts based on selected sort option
  const getSortedPosts = (posts) => {
    return [...posts].sort((a, b) => {
      const dateA = new Date(`${a.scheduledDate}T${a.scheduledTime}`);
      const dateB = new Date(`${b.scheduledDate}T${b.scheduledTime}`);
      
      return sortOption === 'asc' 
        ? dateA.getTime() - dateB.getTime() 
        : dateB.getTime() - dateA.getTime();
    });
  };
  
  // Format date for display
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.getTime() === today.getTime()) {
      return "今天";
    } else if (date.getTime() === tomorrow.getTime()) {
      return "明天";
    } else {
      // Format as MM月DD日
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    }
  };
  
  // Helper function to get day of week
  const getDayOfWeek = (dateStr) => {
    const date = new Date(dateStr);
    const days = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    return days[date.getDay()];
  };
  
  // Filtered and sorted posts
  const displayedPosts = getSortedPosts(getFilteredPosts());
  
  // Group posts by date
  const groupPostsByDate = (posts) => {
    const grouped = {};
    
    posts.forEach(post => {
      if (!grouped[post.scheduledDate]) {
        grouped[post.scheduledDate] = [];
      }
      grouped[post.scheduledDate].push(post);
    });
    
    return grouped;
  };
  
  const groupedPosts = groupPostsByDate(displayedPosts);
  
  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white shadow-sm">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center">
            <button 
              onClick={() => view !== 'list' ? setView('list') : null} 
              className={`mr-2 p-1 rounded-full ${view !== 'list' ? 'bg-gray-100' : 'opacity-0'}`}
            >
              {view !== 'list' && <ChevronLeft size={24} />}
            </button>
            <h1 className="text-xl font-bold">
              {view === 'list' && "元•Live 周计划"}
              {view === 'create' && "创建新动态"}
              {view === 'edit' && "编辑动态"}
              {view === 'generate' && "智能生成"}
            </h1>
          </div>
          
          {view === 'list' && (
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => setView('generate')} 
                className="p-2 bg-purple-100 text-purple-600 rounded-full"
                title="智能生成一周动态"
              >
                <Wand2 size={20} />
              </button>
              <button 
                onClick={handleCreateNew} 
                className="p-2 bg-indigo-600 text-white rounded-full"
                title="创建新动态"
              >
                <PlusCircle size={20} />
              </button>
            </div>
          )}
          
          {(view === 'create' || view === 'edit') && (
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => setView('list')}
                className="p-2 bg-gray-100 text-gray-600 rounded-full"
              >
                <X size={20} />
              </button>
              <button 
                onClick={handleSubmitForm}
                className="p-2 bg-indigo-600 text-white rounded-full"
                disabled={!formData.title || !formData.content}
              >
                <Check size={20} />
              </button>
            </div>
          )}
          
          {/* Removed the header buttons for generate view as requested */}
        </div>
        
        {/* Filter and Sort - Only show in list view */}
        {view === 'list' && (
          <div className="flex justify-between items-center p-3 bg-gray-50 border-t border-b border-gray-200">
            <div className="flex space-x-2">
              <select 
                value={filterOption}
                onChange={(e) => setFilterOption(e.target.value)}
                className="text-sm bg-white border border-gray-300 rounded-lg px-3 py-1.5"
              >
                <option value="all">全部动态</option>
                <option value="today">今天</option>
                <option value="tomorrow">明天</option>
                <option value="thisWeek">本周内</option>
              </select>
              
              <select 
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value)}
                className="text-sm bg-white border border-gray-300 rounded-lg px-3 py-1.5"
              >
                <option value="asc">时间升序</option>
                <option value="desc">时间降序</option>
              </select>
            </div>
            
            <div className="text-sm text-gray-500">
              {displayedPosts.length} 条动态
            </div>
          </div>
        )}
      </div>
      
      {/* Main Content */}
      <div className="flex-1 p-4">
        {/* List View */}
        {view === 'list' && (
          <div className="space-y-6">
            {Object.keys(groupedPosts).length > 0 ? (
              Object.keys(groupedPosts).sort().map(date => (
                <div key={date} className="space-y-3">
                  {/* Date Header */}
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full text-white font-medium text-sm">
                      {date.split('-')[2]}
                    </div>
                    <div>
                      <div className="text-base font-bold">
                        {formatDate(date)} {getDayOfWeek(date)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {groupedPosts[date].length} 条动态
                      </div>
                    </div>
                  </div>
                  
                  {/* Posts for this date */}
                  {groupedPosts[date].map(post => (
                    <div 
                      key={post.id} 
                      className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200"
                    >
                      {/* Post Header */}
                      <div className="p-3 flex justify-between items-center border-b border-gray-100">
                        <div className="flex items-center">
                          <div className={`p-1.5 rounded-full mr-2 ${post.type === 'video' ? 'bg-indigo-100 text-indigo-600' : 'bg-purple-100 text-purple-600'}`}>
                            {post.type === 'video' ? <Play size={16} /> : <Image size={16} />}
                          </div>
                          <div>
                            <h3 className="text-sm font-medium flex items-center">
                              {post.title}
                              {post.isPinned && (
                                <span className="ml-2 bg-amber-100 text-amber-700 text-xs px-1.5 py-0.5 rounded">置顶</span>
                              )}
                              {post.isHot && (
                                <span className="ml-2 bg-red-100 text-red-600 text-xs px-1.5 py-0.5 rounded flex items-center">
                                  热门
                                </span>
                              )}
                            </h3>
                            <div className="text-xs text-gray-500 flex items-center">
                              <Clock size={12} className="mr-1" />
                              {post.scheduledTime}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex space-x-1">
                          <button 
                            onClick={() => handleEditPost(post)}
                            className="p-1.5 bg-gray-100 text-gray-600 rounded-full"
                          >
                            <Edit size={16} />
                          </button>
                          <button 
                            onClick={() => handleDeletePost(post.id)}
                            className="p-1.5 bg-gray-100 text-gray-600 rounded-full"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                      
                      {/* Post Content Preview */}
                      <div className="p-3">
                        <p className="text-sm text-gray-700 line-clamp-2 mb-2">
                          {post.content}
                        </p>
                        
                        {/* Media Preview */}
                        <div className="relative rounded-lg overflow-hidden bg-gray-100 mb-2" style={{height: '80px'}}>
                          {post.type === 'video' && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <Play size={24} className="text-white bg-black bg-opacity-50 rounded-full p-1" />
                            </div>
                          )}
                          {post.multipleImages ? (
                            <div className="flex h-full">
                              <div className="w-1/3 bg-gray-200 border-r border-white"></div>
                              <div className="w-1/3 bg-gray-300 border-r border-white"></div>
                              <div className="w-1/3 bg-gray-200"></div>
                            </div>
                          ) : (
                            <div className="h-full bg-gray-200"></div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))
            ) : (
              <div className="flex flex-col items-center justify-center py-10 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <Calendar size={32} className="text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-700 mb-2">暂无计划的动态</h3>
                <p className="text-sm text-gray-500 mb-6 max-w-xs">
                  创建新的元•Live动态或使用AI智能生成一周的动态计划
                </p>
                <div className="flex space-x-4">
                  <button 
                    onClick={() => setView('generate')}
                    className="px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg text-sm font-medium flex items-center"
                  >
                    <Wand2 size={16} className="mr-2" />
                    智能生成
                  </button>
                  <button 
                    onClick={handleCreateNew}
                    className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg text-sm font-medium flex items-center"
                  >
                    <PlusCircle size={16} className="mr-2" />
                    手动创建
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Create/Edit Form */}
        {(view === 'create' || view === 'edit') && (
          <div className="space-y-6 bg-white p-4 rounded-lg shadow-sm">
            {/* Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">动态类型</label>
              <div className="flex space-x-4">
                <button
                  className={`flex-1 py-2 px-3 rounded-lg flex items-center justify-center ${
                    formData.type === 'image' 
                      ? 'bg-purple-100 text-purple-700 border-2 border-purple-300' 
                      : 'bg-gray-100 text-gray-600 border border-gray-200'
                  }`}
                  onClick={() => setFormData({...formData, type: 'image'})}
                >
                  <Image size={18} className="mr-2" />
                  图片
                </button>
                <button
                  className={`flex-1 py-2 px-3 rounded-lg flex items-center justify-center ${
                    formData.type === 'video' 
                      ? 'bg-indigo-100 text-indigo-700 border-2 border-indigo-300' 
                      : 'bg-gray-100 text-gray-600 border border-gray-200'
                  }`}
                  onClick={() => setFormData({...formData, type: 'video'})}
                >
                  <Play size={18} className="mr-2" />
                  视频
                </button>
              </div>
            </div>
            
            {/* Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">标题</label>
              <input
                id="title"
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                placeholder="输入动态标题..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            
            {/* Content */}
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">内容</label>
              <textarea
                id="content"
                rows={4}
                value={formData.content}
                onChange={(e) => setFormData({...formData, content: e.target.value})}
                placeholder="输入动态内容..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            
            {/* Scheduled Date and Time */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">计划日期</label>
                <div className="relative">
                  <input
                    id="date"
                    type="date"
                    value={formData.scheduledDate}
                    onChange={(e) => setFormData({...formData, scheduledDate: e.target.value})}
                    min={new Date().toISOString().split('T')[0]} // Can't schedule for past dates
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                  <Calendar size={18} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
              </div>
              <div>
                <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-2">计划时间</label>
                <div className="relative">
                  <input
                    id="time"
                    type="time"
                    value={formData.scheduledTime}
                    onChange={(e) => setFormData({...formData, scheduledTime: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                  <Clock size={18} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
              </div>
            </div>
            
            {/* Media Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">上传{formData.type === 'image' ? '图片' : '视频'}</label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center bg-gray-50">
                <div className="mb-4 p-3 bg-purple-100 text-purple-600 rounded-full">
                  {formData.type === 'image' ? <Camera size={24} /> : <Video size={24} />}
                </div>
                <p className="text-sm text-gray-600 mb-2">点击上传或拖拽文件到此处</p>
                <p className="text-xs text-gray-500">支持JPG, PNG, {formData.type === 'video' ? 'MP4, MOV' : 'WEBP, GIF'}</p>
                <button className="mt-4 px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium">
                  选择文件
                </button>
              </div>
            </div>
            
            {/* Additional Options */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">附加选项</label>
              <div className="space-y-3">
                {/* Multiple Images Option (for image type) */}
                {formData.type === 'image' && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Image size={16} className="text-gray-500 mr-2" />
                      <span className="text-sm text-gray-700">使用多张图片</span>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input 
                        type="checkbox" 
                        checked={formData.multipleImages} 
                        onChange={(e) => setFormData({...formData, multipleImages: e.target.checked})}
                        className="sr-only peer"
                      />
                      <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                )}
                
                {/* Duration (for video type) */}
                {formData.type === 'video' && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Clock size={16} className="text-gray-500 mr-2" />
                      <span className="text-sm text-gray-700">视频时长</span>
                    </div>
                    <select
                      value={formData.duration}
                      onChange={(e) => setFormData({...formData, duration: e.target.value})}
                      className="text-sm bg-white border border-gray-300 rounded-lg px-3 py-1.5"
                    >
                      <option value="10秒">10秒</option>
                      <option value="15秒">15秒</option>
                      <option value="30秒">30秒</option>
                      <option value="60秒">60秒</option>
                    </select>
                  </div>
                )}
                
                {/* Is Pinned */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <AlertCircle size={16} className="text-amber-500 mr-2" />
                    <span className="text-sm text-gray-700">置顶动态</span>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      checked={formData.isPinned} 
                      onChange={(e) => setFormData({...formData, isPinned: e.target.checked})}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-amber-500"></div>
                  </label>
                </div>
                
                {/* Is Hot */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Sparkles size={16} className="text-red-500 mr-2" />
                    <span className="text-sm text-gray-700">热门标记</span>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      checked={formData.isHot} 
                      onChange={(e) => setFormData({...formData, isHot: e.target.checked})}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-red-500"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* AI Generate Form */}
        {view === 'generate' && (
          <div className="space-y-6 bg-white p-4 rounded-lg shadow-sm">
            <div className="p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg border border-purple-100">
              <h3 className="text-base font-medium text-purple-800 mb-2 flex items-center">
                <Wand2 size={18} className="mr-2 text-purple-600" />
                AI智能生成元•Live动态
              </h3>
              <p className="text-sm text-purple-700">
                输入角色特点和你期望的内容风格，AI将为你自动生成一周的元•Live动态计划。
              </p>
            </div>
            
            {/* Prompt Input */}
            <div>
              <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">告诉AI你的要求</label>
              <textarea
                id="prompt"
                rows={4}
                value={generationOptions.prompt}
                onChange={(e) => setGenerationOptions({...generationOptions, prompt: e.target.value})}
                placeholder="描述你想要的元•Live动态风格和内容主题..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <p className="mt-1 text-xs text-gray-500">提示: 描述越详细，生成的内容越符合预期。</p>
            </div>
            
            {/* Number of Posts */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">生成数量</label>
              <div className="flex items-center mb-1">
                <input
                  type="range"
                  min="1"
                  max="14"
                  step="1"
                  value={generationOptions.numPosts}
                  onChange={(e) => setGenerationOptions({...generationOptions, numPosts: parseInt(e.target.value)})}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>1天</span>
                <span>{generationOptions.numPosts}天</span>
                <span>14天</span>
              </div>
            </div>
            
            {/* Post Types */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">内容类型</label>
              <div className="flex items-center space-x-4">
                <label className="inline-flex items-center">
                  <input 
                    type="checkbox" 
                    checked={generationOptions.postTypes.includes('image')}
                    onChange={(e) => {
                      const newTypes = e.target.checked 
                        ? [...generationOptions.postTypes, 'image'] 
                        : generationOptions.postTypes.filter(t => t !== 'image');
                      setGenerationOptions({...generationOptions, postTypes: newTypes});
                    }}
                    className="form-checkbox h-4 w-4 text-purple-600 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">图片</span>
                </label>
                <label className="inline-flex items-center">
                  <input 
                    type="checkbox" 
                    checked={generationOptions.postTypes.includes('video')}
                    onChange={(e) => {
                      const newTypes = e.target.checked 
                        ? [...generationOptions.postTypes, 'video'] 
                        : generationOptions.postTypes.filter(t => t !== 'video');
                      setGenerationOptions({...generationOptions, postTypes: newTypes});
                    }}
                    className="form-checkbox h-4 w-4 text-purple-600 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">视频</span>
                </label>
              </div>
            </div>
            
            {/* Themes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">内容主题</label>
              <div className="flex flex-wrap gap-2">
                {generationOptions.themes.map((theme, index) => (
                  <span 
                    key={index} 
                    className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm flex items-center"
                  >
                    {theme}
                    <button 
                      onClick={() => {
                        const newThemes = [...generationOptions.themes];
                        newThemes.splice(index, 1);
                        setGenerationOptions({...generationOptions, themes: newThemes});
                      }}
                      className="ml-1 p-0.5 text-purple-500 hover:text-purple-700"
                    >
                      <X size={14} />
                    </button>
                  </span>
                ))}
                <button 
                  onClick={() => {
                    const newTheme = prompt("请输入新主题");
                    if (newTheme && !generationOptions.themes.includes(newTheme)) {
                      setGenerationOptions({
                        ...generationOptions, 
                        themes: [...generationOptions.themes, newTheme]
                      });
                    }
                  }}
                  className="px-3 py-1 border border-dashed border-gray-300 text-gray-500 rounded-full text-sm"
                >
                  + 添加主题
                </button>
              </div>
            </div>
            
            {/* AI Inspiration Sources - New section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">AI灵感来源</label>
              <div className="space-y-3">
                {/* Chat History */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MessageSquare size={16} className="text-indigo-500 mr-2" />
                    <span className="text-sm text-gray-700">与用户的聊天记录</span>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      checked={generationOptions.inspirationSources.chatHistory} 
                      onChange={(e) => setGenerationOptions({
                        ...generationOptions, 
                        inspirationSources: {
                          ...generationOptions.inspirationSources,
                          chatHistory: e.target.checked
                        }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-500"></div>
                  </label>
                </div>
                
                {/* Post Comments */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Heart size={16} className="text-pink-500 mr-2" />
                    <span className="text-sm text-gray-700">已发元•Live的相关评论</span>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      checked={generationOptions.inspirationSources.comments} 
                      onChange={(e) => setGenerationOptions({
                        ...generationOptions, 
                        inspirationSources: {
                          ...generationOptions.inspirationSources,
                          comments: e.target.checked
                        }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-pink-500"></div>
                  </label>
                </div>
                
                {/* News Trends */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Globe size={16} className="text-blue-500 mr-2" />
                    <span className="text-sm text-gray-700">最近新闻热点</span>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      checked={generationOptions.inspirationSources.news} 
                      onChange={(e) => setGenerationOptions({
                        ...generationOptions, 
                        inspirationSources: {
                          ...generationOptions.inspirationSources,
                          news: e.target.checked
                        }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
                  </label>
                </div>
              </div>
            </div>
            
            {/* Include Hot Post */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Sparkles size={16} className="text-red-500 mr-2" />
                <span className="text-sm text-gray-700">自动添加热门标记</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={generationOptions.includeHotPost}
                  onChange={(e) => setGenerationOptions({...generationOptions, includeHotPost: e.target.checked})}
                  className="sr-only peer"
                />
                <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-red-500"></div>
              </label>
            </div>
            
            {/* Generation Button */}
            <button 
              onClick={handleGenerateWeek}
              className="w-full py-3 rounded-lg bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-semibold flex items-center justify-center"
            >
              <Sparkles size={18} className="mr-2" />
              一键生成 {generationOptions.numPosts} 天元•Live动态
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MetaLiveScheduleManager;