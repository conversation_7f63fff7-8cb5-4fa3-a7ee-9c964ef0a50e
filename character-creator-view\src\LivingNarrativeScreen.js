import React, { useState, useRef, useEffect } from 'react';
// 建议安装 heroicons 以获得更好的图标体验: npm install @heroicons/react
import { PaperAirplaneIcon } from '@heroicons/react/24/solid';

// --- Mock Data ---

// 初始模拟数据
const initialMessages = [
  {
    id: 1,
    type: 'event',
    sender: 'system',
    content: '场景加载：【迷雾之都 - 阴暗码头】夜晚，雨水淅沥，空气中弥漫着咸湿和铁锈的味道。',
    timestamp: Date.now() - 50000,
  },
  {
    id: 2,
    type: 'image',
    sender: 'system',
    imageUrl: 'https://images.unsplash.com/photo-1602521879205-9c6be1003ae6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', // 暗夜码头场景
    imageAlt: '阴暗潮湿的码头，远处有微弱灯光，地面有水洼反射光线',
    timestamp: Date.now() - 45000,
  },
  {
    id: 3,
    type: 'text',
    sender: 'ai',
    characterName: '艾拉',
    characterAvatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80', // 女性侦探头像
    content: '哼，又一个来这鬼地方的。说吧，找我什么事？别浪费我时间，这里的"咖啡"可不便宜。',
    timestamp: Date.now() - 40000,
  },
  {
    id: 4,
    type: 'text',
    sender: 'user',
    content: '你好，艾拉侦探。我听说你是这里最好的信息掮客，我需要你的帮助。',
    timestamp: Date.now() - 30000,
  },
];

// --- React Component ---

const LivingNarrativeScreen = () => {
  const [messages, setMessages] = useState(initialMessages);
  const [inputValue, setInputValue] = useState('');
  const [isAiTyping, setIsAiTyping] = useState(false); // 模拟AI思考
  const messagesEndRef = useRef(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]); // 当消息列表变化时滚动

  // 模拟AI响应
  const simulateAiResponse = (userInput) => {
     setIsAiTyping(true);

     // 基于用户输入决定不同的响应（极简模拟）
     let responseText = "有趣... 你想知道什么？直接点。";
     let shouldGenerateImage = false;

     if (userInput.includes("线索") || userInput.includes("失踪")) {
         responseText = "失踪？这种事在迷雾之都太常见了。给我点具体的，名字？地点？时间？";
     } else if (userInput.includes("危险") || userInput.includes("阴谋")) {
         responseText = "呵，你闻到危险的味道了？这地方到处都是。但如果你想挖得更深，代价可不小。";
         shouldGenerateImage = true; // 假设提及危险会触发场景图片
     } else if (userInput.includes("付钱") || userInput.includes("报酬")) {
         responseText = "哦？看来你懂规矩。很好，我的时间很宝贵。先说说看是什么事，我再决定接不接。";
     }

     const aiMessage = {
       id: Date.now(),
       type: 'text',
       sender: 'ai',
       characterName: '艾拉',
       characterAvatar: 'https://via.placeholder.com/40/A0AEC0/FFFFFF?text=A',
       content: responseText,
       timestamp: Date.now(),
     };

     const generatedImage = shouldGenerateImage ? {
         id: Date.now() + 1,
         type: 'image',
         sender: 'system', // 或者 'ai' 如果是AI描述的想象
         imageUrl: 'https://images.unsplash.com/photo-1519608487953-e999c86e7455?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', // 黑暗小巷场景
         imageAlt: '一张描绘城市阴影中小巷的图片，暗示着潜在的危险',
         timestamp: Date.now() + 1,
     } : null;


     // 模拟延迟
     setTimeout(() => {
         setIsAiTyping(false);
         if (generatedImage) {
            setMessages(prev => [...prev, aiMessage, generatedImage]);
         } else {
            setMessages(prev => [...prev, aiMessage]);
         }
     }, 1500 + Math.random() * 1000); // 模拟思考和生成时间
  };

  const handleSend = () => {
    const trimmedInput = inputValue.trim();
    if (!trimmedInput) return;

    const newUserMessage = {
      id: Date.now(),
      type: 'text',
      sender: 'user',
      content: trimmedInput,
      timestamp: Date.now(),
    };

    setMessages(prev => [...prev, newUserMessage]);
    setInputValue('');

    // 触发模拟AI响应
    simulateAiResponse(trimmedInput);
  };

  const handleInputChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault(); // 防止换行
      handleSend();
    }
  };

  // --- 渲染函数 ---
  const renderMessageContent = (msg) => {
    switch (msg.type) {
      case 'text':
        return (
          <div className={`p-3 rounded-lg max-w-[85%] ${
            msg.sender === 'user'
              ? 'bg-purple-600 text-white ml-auto rounded-br-none' // 用户消息靠右，不同圆角
              : msg.sender === 'ai'
              ? 'bg-gray-700 text-gray-200 mr-auto rounded-bl-none flex items-start space-x-2' // AI消息靠左，不同圆角
              : 'bg-transparent text-gray-400 text-center text-xs italic py-2' // 系统事件消息居中
          }`}>
            {msg.sender === 'ai' && msg.characterAvatar && (
                <img src={msg.characterAvatar} alt={msg.characterName} className="w-6 h-6 rounded-full flex-shrink-0 mt-1" />
            )}
            <div className="flex flex-col">
                {msg.sender === 'ai' && msg.characterName && (
                    <span className="text-xs font-semibold text-purple-300 mb-1">{msg.characterName}</span>
                )}
                <p className="whitespace-pre-wrap break-words">{msg.content}</p> {/* 支持换行和长单词 */}
            </div>

          </div>
        );
      case 'image':
        return (
          <div className="my-2 flex justify-center px-4">
             <img
                src={msg.imageUrl}
                alt={msg.imageAlt || 'Generated image'}
                className="rounded-lg shadow-md max-w-full h-auto max-h-60 object-contain border border-gray-600" // 限制图片高度
             />
          </div>
        );
      case 'event':
         return (
           <div className="text-center text-xs text-gray-400 italic my-3 px-4">
             --- {msg.content} ---
           </div>
         );
      default:
        return null;
    }
  };


  // --- 主渲染 ---
  return (
    <div className="flex flex-col h-screen bg-gradient-to-b from-gray-900 via-black to-gray-900 text-gray-200 font-sans">

      {/* 顶部状态栏 (可选，可添加地点、时间、任务目标等) */}
      <div className="bg-gray-800 bg-opacity-80 backdrop-blur-sm p-2 text-center text-sm shadow-md sticky top-0 z-10">
        <span className="font-semibold text-purple-300">场景：</span>迷雾之都 - 阴暗码头
      </div>

      {/* 消息/叙事流区域 */}
      <div className="flex-grow overflow-y-auto p-4 space-y-4 scroll-smooth">
        {messages.map((msg) => (
          <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            {/* 容器用于控制宽度和对齐，实际样式在renderMessageContent中 */}
             <div className={`${msg.type !== 'event' ? 'w-full' : ''} ${msg.sender === 'ai' ? 'max-w-[85%]' : msg.sender === 'user' ? 'max-w-[85%]' : 'w-full'}`}>
                {renderMessageContent(msg)}
            </div>
          </div>
        ))}
        {isAiTyping && (
            <div className="flex justify-start">
                <div className="bg-gray-700 text-gray-400 p-3 rounded-lg rounded-bl-none mr-auto inline-flex items-center space-x-2">
                    <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-100"></span>
                    <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-200"></span>
                    <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-300"></span>
                </div>
            </div>
        )}
        {/* 用于滚动定位的空div */}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="bg-gray-800 border-t border-gray-700 p-3 sticky bottom-0 z-10">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="输入你的行动或对话..."
            className="flex-grow bg-gray-600 rounded-full py-2 px-4 text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isAiTyping} // AI输入时禁用
          />
          <button
            onClick={handleSend}
            disabled={!inputValue.trim() || isAiTyping}
            className={`p-2 rounded-full transition-colors duration-200 ${
              inputValue.trim() && !isAiTyping
                ? 'bg-purple-600 hover:bg-purple-700 text-white'
                : 'bg-gray-500 text-gray-400 cursor-not-allowed'
            }`}
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default LivingNarrativeScreen;