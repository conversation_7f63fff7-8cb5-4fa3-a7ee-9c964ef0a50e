import React, { useState, useRef, useEffect } from 'react';
import { 
  ChevronLeft, Image, Play, MessageCircle, Heart, 
  Eye, Calendar, PlusCircle, Video, Clock,
  MoreHorizontal, Send, Award, Star, Music, 
  ThumbsUp, Sparkles, Users, TrendingUp, Edit
} from 'lucide-react';

const MetaLive = () => {
  // 状态管理
  const [showCreateMenu, setShowCreateMenu] = useState(false);
  const createButtonRef = useRef(null);
  const createMenuRef = useRef(null);
  
      // 模拟API数据 - 元Live内容列表
  const metaLiveContents = [
    {
      id: 1,
      type: 'video',
      title: '雨夜侦探',
      content: '在霓虹灯闪烁的城市雨夜，我沿着湿滑的街道追踪着一个神秘的身影，雨水顺着风衣滴落，每一步都接近真相...',
      mediaUrl: '/api/placeholder/400/220',
      timestamp: '2小时前',
      statistics: {
        views: 8237,
        likes: 624,
        comments: 42
      },
      isPinned: true,
      isHot: true,
      duration: '15秒'
    },
    {
      id: 2,
      type: 'image',
      title: '午后休息时光',
      content: '案件告一段落，在咖啡厅小憩片刻，窗外的阳光照在桌上，形成了奇妙的光影。偶尔的宁静时刻也是生活的一部分。',
      mediaUrl: '/api/placeholder/400/300',
      timestamp: '昨天',
      statistics: {
        views: 3456,
        likes: 328,
        comments: 31
      },
      isPinned: false,
      isHot: false,
      multipleImages: false
    },
    {
      id: 3,
      type: 'video',
      title: '街头机械舞',
      content: '在城市地下舞厅，展示了我最新学会的机械舞步，霓虹灯下的每一个动作都充满未来感。',
      mediaUrl: '/api/placeholder/400/220',
      timestamp: '2天前',
      statistics: {
        views: 5692,
        likes: 487,
        comments: 58
      },
      isPinned: false,
      isHot: true,
      duration: '12秒'
    },
    {
      id: 4,
      type: 'image',
      title: '案件线索整理',
      content: '将所有收集到的线索拼接在一起，试图找出其中的规律。每一次整理都是对逻辑思维的考验，也是解开谜题的关键一步。',
      mediaUrl: [
        '/api/placeholder/130/130',
        '/api/placeholder/130/130',
        '/api/placeholder/130/130'
      ],
      timestamp: '3天前',
      statistics: {
        views: 2876,
        likes: 231,
        comments: 24
      },
      isPinned: false,
      isHot: false,
      multipleImages: true
    },
    {
      id: 5,
      type: 'video',
      title: '赛博酷跑',
      content: '穿梭在未来城市的高楼之间，展示了最新的都市跑酷技巧。在未来世界，移动方式也需要不断创新。',
      mediaUrl: '/api/placeholder/400/220',
      timestamp: '1周前',
      statistics: {
        views: 7893,
        likes: 612,
        comments: 73
      },
      isPinned: false,
      isHot: true,
      duration: '18秒'
    }
  ];
  
  // 数据统计
  const statistics = {
    totalViews: 28154,
    totalLikes: 2282,
    totalComments: 228,
    growthRate: 18,
    hotContents: 3,
    followers: 537
  };
  
  // 不再需要筛选，直接使用全部内容
  const filteredContents = metaLiveContents;

  // 处理点击外部关闭创建菜单
  useEffect(() => {
    function handleClickOutside(event) {
      if (createMenuRef.current && !createMenuRef.current.contains(event.target) &&
          createButtonRef.current && !createButtonRef.current.contains(event.target)) {
        setShowCreateMenu(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // 格式化数字(例如: 1000 -> 1K)
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'W';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num;
  };

  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg">
      {/* 头部导航 */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white">
        <div className="flex items-center">
          <button className="p-1 mr-2">
            <ChevronLeft size={20} className="text-white" />
          </button>
          <h1 className="text-lg font-bold">元•Live</h1>
        </div>
        {/* 修改：替换加号按钮为"发布动态"按钮 */}
        <button 
          onClick={() => setShowCreateMenu(!showCreateMenu)}
          className="bg-purple-600 hover:bg-purple-500 text-white rounded-full px-4 py-2 flex items-center justify-center shadow-md transition-all duration-200 transform hover:scale-105"
        >
          <Edit size={18} className="mr-1.5" />
          <span className="font-medium text-sm">发布动态</span>
        </button>
      </div>
      
      {/* 升级提示 */}
      <div className="bg-gradient-to-r from-amber-500 to-amber-600 px-4 py-3 text-white">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
            <Award size={18} className="text-white" />
          </div>
          <div>
            <h3 className="font-bold text-sm">恭喜你的角色又升级了！</h3>
            <p className="text-xs mt-0.5">获得了3个创作点，快去给角色发布更多元•Live</p>
          </div>
        </div>
      </div>
      
      {/* 创意灵感板块 */}
      <div className="bg-indigo-900 p-4">
        <div className="bg-white rounded-xl shadow-lg p-4">
          <h2 className="text-md font-bold flex items-center">
            <Sparkles size={18} className="text-purple-600 mr-2" />
            创作灵感
          </h2>
          
          <div className="mt-3 space-y-3">
            {/* 基于现有内容的灵感 */}
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-3">
              <h3 className="text-sm font-medium text-purple-700">粉丝最爱</h3>
              <p className="text-xs text-gray-700 mt-1">
                您的"雨夜侦探"系列视频很受欢迎，可以尝试创作续集，探索更多不同天气与城市场景下的侦探故事。
              </p>
            </div>
            
            {/* 基于热点的灵感 */}
            <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg p-3">
              <h3 className="text-sm font-medium text-amber-700 flex items-center">
                <TrendingUp size={14} className="mr-1" />
                热点话题
              </h3>
              <p className="text-xs text-gray-700 mt-1">
                最近科技展览会很火，可以创作K探索未来科技展览会的内容，与角色的赛博朋克风格很匹配。
              </p>
            </div>
            
            {/* AI推荐 */}
            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-3">
              <h3 className="text-sm font-medium text-blue-700">AI个性化推荐</h3>
              <div className="flex flex-wrap gap-2 mt-2">
                <div className="bg-white text-xs px-2.5 py-1.5 rounded-full text-gray-700 shadow-sm">
                  城市霓虹夜景
                </div>
                <div className="bg-white text-xs px-2.5 py-1.5 rounded-full text-gray-700 shadow-sm">
                  电子音乐创作
                </div>
                <div className="bg-white text-xs px-2.5 py-1.5 rounded-full text-gray-700 shadow-sm">
                  街头机械舞蹈
                </div>
                <div className="bg-white text-xs px-2.5 py-1.5 rounded-full text-gray-700 shadow-sm">
                  未来科技展示
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 简化的头部 */}
      <div className="bg-white sticky top-0 z-10 shadow-sm px-4 py-3">
        <div className="flex justify-between items-center">
          <h2 className="text-base font-bold flex items-center">
            动态时间线
          </h2>
          <div className="text-xs text-purple-600 flex items-center">
            <Clock size={14} className="mr-1" />
            最近更新: 2小时前
          </div>
        </div>
      </div>
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-auto p-4 pt-2">
        <style jsx>{`
          .image-placeholder {
            background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
            background-size: 20px 20px;
            position: relative;
          }
          .video-placeholder {
            background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
            background-size: 20px 20px;
            position: relative;
            aspect-ratio: 16/9;
          }
          .video-placeholder::after {
            content: '视频预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: #666;
          }
          .image-placeholder::after {
            content: '图片预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: #666;
          }
        `}</style>
        
        {/* 日期分组标记 */}
        {filteredContents.length > 0 && (
          <div className="mb-2">
            <div className="inline-block bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full flex items-center">
              <Clock size={12} className="mr-1" />
              {filteredContents[0].timestamp}
            </div>
          </div>
        )}
        
        {/* 动态内容列表 */}
        <div className="space-y-4">
          {filteredContents.map((item, index) => {
            // 检查是否需要显示新的日期分隔
            const showDateSeparator = index > 0 && 
              (item.timestamp.includes('天前') && !filteredContents[index-1].timestamp.includes('天前') ||
               item.timestamp.includes('周前') && !filteredContents[index-1].timestamp.includes('周前') ||
               (item.timestamp.includes('小时前') || item.timestamp.includes('昨天')) && 
               !(filteredContents[index-1].timestamp.includes('小时前') || filteredContents[index-1].timestamp.includes('昨天')));
            
            return (
              <React.Fragment key={item.id}>
                {showDateSeparator && (
                  <div className="mt-4 mb-2">
                    <div className="inline-block bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full flex items-center">
                      <Clock size={12} className="mr-1" />
                      {item.timestamp}
                    </div>
                  </div>
                )}
                
                <div className={`bg-white rounded-lg shadow-sm overflow-hidden ${item.isPinned ? 'border-l-4 border-amber-400' : ''}`}>
                  {/* 内容头部 - 修改：移除标题 */}
                  <div className="p-3 flex justify-between items-center">
                    <div className="flex items-center">
                      <div className={`p-1.5 rounded-full mr-2 ${item.type === 'video' ? 'bg-indigo-100 text-indigo-600' : 'bg-purple-100 text-purple-600'}`}>
                        {item.type === 'video' ? <Play size={16} /> : <Image size={16} />}
                      </div>
                      <div>
                        <div className="flex items-center">
                          {item.isPinned && (
                            <span className="mr-2 bg-amber-100 text-amber-700 text-xs px-1.5 py-0.5 rounded">置顶</span>
                          )}
                          {item.isHot && (
                            <span className="bg-red-100 text-red-600 text-xs px-1.5 py-0.5 rounded flex items-center">
                              <TrendingUp size={10} className="mr-0.5" />
                              热门
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">{item.timestamp}</div>
                      </div>
                    </div>
                    <button className="text-gray-400 p-1">
                      <MoreHorizontal size={16} />
                    </button>
                  </div>
                  
                  {/* 内容主体 */}
                  <div className="px-3 pb-2">
                    <p className="text-sm text-gray-700 mb-3">{item.content}</p>
                    
                    {/* 媒体内容 */}
                    <div className="relative rounded-lg overflow-hidden mb-3">
                      {item.type === 'video' ? (
                        <>
                          <div className="video-placeholder" style={{height: '180px'}}></div>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                              <Play size={22} className="text-white ml-1" />
                            </div>
                          </div>
                          <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-0.5 rounded">
                            {item.duration}
                          </div>
                        </>
                      ) : item.multipleImages ? (
                        // 多图布局
                        <div className="grid grid-cols-3 gap-1">
                          {item.mediaUrl.map((url, imgIndex) => (
                            <div key={imgIndex} className="image-placeholder h-32"></div>
                          ))}
                        </div>
                      ) : (
                        // 单图布局
                        <div className="image-placeholder" style={{height: '220px'}}></div>
                      )}
                    </div>
                    
                    {/* 互动数据 - 修改：删除收藏按钮 */}
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-4">
                        <div className="flex items-center text-gray-500 text-xs">
                          <Eye size={14} className="mr-1" />
                          {formatNumber(item.statistics.views)}
                        </div>
                        <div className="flex items-center text-gray-500 text-xs">
                          <Heart size={14} className="mr-1" />
                          {formatNumber(item.statistics.likes)}
                        </div>
                        <div className="flex items-center text-gray-500 text-xs">
                          <MessageCircle size={14} className="mr-1" />
                          {item.statistics.comments}
                        </div>
                      </div>
                      <div>
                        <button className="p-1.5 bg-purple-50 text-purple-600 rounded-full">
                          <Send size={14} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </React.Fragment>
            );
          })}
        </div>
      </div>
      
      {/* 移除右下角的两个按钮 */}
    </div>
  );
};

export default MetaLive;