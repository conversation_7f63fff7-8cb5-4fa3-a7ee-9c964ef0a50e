[{"C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\EnhancedCreatorView.js": "4", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\TalentsView.js": "5", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreateTalentVideo.js": "6", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\MetaLive.js": "7", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\ImagePublishPage.js": "8", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AICharacterProfile.js": "9", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AssetLibraryPage.js": "10", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\PublishStatusPage.js": "11", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreateVideo.js": "12", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AIMemoryView.js": "13", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreateImagePage.js": "14", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreateMusicPage.js": "15", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\RoadmapVisualization.js": "16", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AIChatInterface.js": "17", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\EnhancedAIChatInterface.js": "18", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\UploadMediaPage.js": "19", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\StarPathPlanPage.js": "20", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\PicAIChatInterface.js": "21", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\DreamWeaverVideoCreator.js": "22", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\OptimizedDimensionBrush.js": "23", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreatorVideoStudio.js": "24", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\MetaLiveScheduleManager.js": "25", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\LivingNarrativeScreen.js": "26", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CustomDreamCreator.js": "27", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\VoiceCallPage.js": "28", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AICharacterInvestmentPage.js": "29", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\StarRankingPage.js": "30", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\StarCompassPage.js": "31", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\StockChatInterface.js": "32", "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AIStockDetailPage.js": "33"}, {"size": 535, "mtime": 1743252654347, "results": "34", "hashOfConfig": "35"}, {"size": 7089, "mtime": 1748435252774, "results": "36", "hashOfConfig": "35"}, {"size": 362, "mtime": 1743252654587, "results": "37", "hashOfConfig": "35"}, {"size": 64591, "mtime": 1744013667704, "results": "38", "hashOfConfig": "35"}, {"size": 17531, "mtime": 1743503668693, "results": "39", "hashOfConfig": "35"}, {"size": 27959, "mtime": 1743317535980, "results": "40", "hashOfConfig": "35"}, {"size": 16327, "mtime": 1743663646099, "results": "41", "hashOfConfig": "35"}, {"size": 36219, "mtime": 1743503668691, "results": "42", "hashOfConfig": "35"}, {"size": 95975, "mtime": 1744625356869, "results": "43", "hashOfConfig": "35"}, {"size": 26339, "mtime": 1743500824899, "results": "44", "hashOfConfig": "35"}, {"size": 15645, "mtime": 1743660886970, "results": "45", "hashOfConfig": "35"}, {"size": 23064, "mtime": 1743498192510, "results": "46", "hashOfConfig": "35"}, {"size": 54479, "mtime": 1744021832797, "results": "47", "hashOfConfig": "35"}, {"size": 60966, "mtime": 1744009409746, "results": "48", "hashOfConfig": "35"}, {"size": 60123, "mtime": 1744011423717, "results": "49", "hashOfConfig": "35"}, {"size": 11274, "mtime": 1743746792439, "results": "50", "hashOfConfig": "35"}, {"size": 10712, "mtime": 1743763282656, "results": "51", "hashOfConfig": "35"}, {"size": 41672, "mtime": 1743927206545, "results": "52", "hashOfConfig": "35"}, {"size": 32493, "mtime": 1744012408201, "results": "53", "hashOfConfig": "35"}, {"size": 19663, "mtime": 1744014953653, "results": "54", "hashOfConfig": "35"}, {"size": 19957, "mtime": 1743926046732, "results": "55", "hashOfConfig": "35"}, {"size": 77469, "mtime": 1744008028846, "results": "56", "hashOfConfig": "35"}, {"size": 51964, "mtime": 1743932921698, "results": "57", "hashOfConfig": "35"}, {"size": 74337, "mtime": 1744010260368, "results": "58", "hashOfConfig": "35"}, {"size": 44351, "mtime": 1744354025957, "results": "59", "hashOfConfig": "35"}, {"size": 9872, "mtime": 1745300068249, "results": "60", "hashOfConfig": "35"}, {"size": 50537, "mtime": 1746602874386, "results": "61", "hashOfConfig": "35"}, {"size": 36148, "mtime": 1748331311578, "results": "62", "hashOfConfig": "35"}, {"size": 47468, "mtime": 1748507086913, "results": "63", "hashOfConfig": "35"}, {"size": 12462, "mtime": 1748335234822, "results": "64", "hashOfConfig": "35"}, {"size": 35346, "mtime": 1748500783195, "results": "65", "hashOfConfig": "35"}, {"size": 35078, "mtime": 1748499409390, "results": "66", "hashOfConfig": "35"}, {"size": 29212, "mtime": 1748490087087, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12ag4he", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\EnhancedCreatorView.js", ["167", "168", "169", "170", "171", "172", "173", "174", "175", "176"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\TalentsView.js", ["177", "178"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreateTalentVideo.js", ["179", "180", "181", "182", "183", "184", "185", "186"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\MetaLive.js", ["187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "200"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\ImagePublishPage.js", ["201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AICharacterProfile.js", ["217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AssetLibraryPage.js", ["234", "235", "236", "237", "238", "239", "240", "241", "242", "243", "244"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\PublishStatusPage.js", ["245", "246", "247", "248"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreateVideo.js", ["249", "250", "251", "252", "253"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AIMemoryView.js", ["254", "255", "256"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreateImagePage.js", ["257", "258", "259", "260", "261", "262"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreateMusicPage.js", ["263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\RoadmapVisualization.js", [], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AIChatInterface.js", ["278", "279", "280", "281"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\EnhancedAIChatInterface.js", ["282"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\UploadMediaPage.js", ["283", "284", "285", "286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\StarPathPlanPage.js", ["298", "299"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\PicAIChatInterface.js", ["300", "301", "302", "303", "304", "305", "306", "307"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\DreamWeaverVideoCreator.js", ["308", "309", "310", "311", "312", "313", "314", "315", "316", "317"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\OptimizedDimensionBrush.js", ["318", "319", "320", "321", "322", "323"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CreatorVideoStudio.js", ["324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335", "336", "337", "338", "339", "340"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\MetaLiveScheduleManager.js", ["341", "342", "343", "344", "345"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\LivingNarrativeScreen.js", [], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\CustomDreamCreator.js", ["346", "347", "348", "349", "350", "351", "352", "353", "354", "355"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\VoiceCallPage.js", ["356", "357", "358", "359"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AICharacterInvestmentPage.js", ["360", "361", "362", "363", "364", "365", "366", "367", "368", "369", "370", "371", "372"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\StarRankingPage.js", ["373", "374", "375"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\StarCompassPage.js", ["376", "377", "378", "379", "380", "381", "382", "383", "384", "385"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\StockChatInterface.js", ["386", "387", "388", "389", "390", "391", "392", "393", "394", "395"], [], "C:\\Users\\<USER>\\Desktop\\角色升级计划\\code\\character-creator-view\\src\\AIStockDetailPage.js", ["396", "397", "398", "399", "400", "401"], [], {"ruleId": "402", "severity": 1, "message": "403", "line": 3, "column": 13, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 18}, {"ruleId": "402", "severity": 1, "message": "406", "line": 3, "column": 20, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 24}, {"ruleId": "402", "severity": 1, "message": "407", "line": 4, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 9}, {"ruleId": "402", "severity": 1, "message": "408", "line": 4, "column": 11, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 17}, {"ruleId": "402", "severity": 1, "message": "409", "line": 6, "column": 28, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 36}, {"ruleId": "402", "severity": 1, "message": "410", "line": 6, "column": 38, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 48}, {"ruleId": "402", "severity": 1, "message": "411", "line": 8, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 30}, {"ruleId": "402", "severity": 1, "message": "412", "line": 259, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 259, "endColumn": 21}, {"ruleId": "402", "severity": 1, "message": "413", "line": 343, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 343, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "414", "line": 459, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 459, "endColumn": 21}, {"ruleId": "402", "severity": 1, "message": "415", "line": 5, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 20}, {"ruleId": "402", "severity": 1, "message": "416", "line": 5, "column": 48, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 55}, {"ruleId": "402", "severity": 1, "message": "417", "line": 3, "column": 30, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 38}, {"ruleId": "402", "severity": 1, "message": "418", "line": 4, "column": 14, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 19}, {"ruleId": "402", "severity": 1, "message": "419", "line": 5, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 22}, {"ruleId": "402", "severity": 1, "message": "420", "line": 5, "column": 24, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "421", "line": 6, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 18}, {"ruleId": "402", "severity": 1, "message": "410", "line": 6, "column": 28, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 38}, {"ruleId": "402", "severity": 1, "message": "422", "line": 22, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 22, "endColumn": 22}, {"ruleId": "423", "severity": 1, "message": "424", "line": 131, "column": 6, "nodeType": "425", "endLine": 131, "endColumn": 24, "suggestions": "426"}, {"ruleId": "402", "severity": 1, "message": "409", "line": 4, "column": 8, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 16}, {"ruleId": "402", "severity": 1, "message": "410", "line": 4, "column": 18, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 28}, {"ruleId": "402", "severity": 1, "message": "427", "line": 4, "column": 30, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 35}, {"ruleId": "402", "severity": 1, "message": "428", "line": 5, "column": 32, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 36}, {"ruleId": "402", "severity": 1, "message": "429", "line": 5, "column": 38, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 43}, {"ruleId": "402", "severity": 1, "message": "421", "line": 6, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 11}, {"ruleId": "402", "severity": 1, "message": "411", "line": 6, "column": 23, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 28}, {"ruleId": "402", "severity": 1, "message": "430", "line": 104, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 104, "endColumn": 19}, {"ruleId": "431", "severity": 1, "message": "432", "line": 286, "column": 46, "nodeType": "433", "messageId": "434", "endLine": 286, "endColumn": 48}, {"ruleId": "431", "severity": 1, "message": "432", "line": 286, "column": 101, "nodeType": "433", "messageId": "434", "endLine": 286, "endColumn": 103}, {"ruleId": "431", "severity": 1, "message": "435", "line": 286, "column": 101, "nodeType": "433", "messageId": "434", "endLine": 286, "endColumn": 103}, {"ruleId": "431", "severity": 1, "message": "435", "line": 287, "column": 46, "nodeType": "433", "messageId": "434", "endLine": 287, "endColumn": 48}, {"ruleId": "431", "severity": 1, "message": "435", "line": 287, "column": 101, "nodeType": "433", "messageId": "434", "endLine": 287, "endColumn": 103}, {"ruleId": "431", "severity": 1, "message": "435", "line": 288, "column": 82, "nodeType": "433", "messageId": "434", "endLine": 288, "endColumn": 84}, {"ruleId": "402", "severity": 1, "message": "436", "line": 1, "column": 27, "nodeType": "404", "messageId": "405", "endLine": 1, "endColumn": 33}, {"ruleId": "402", "severity": 1, "message": "437", "line": 1, "column": 35, "nodeType": "404", "messageId": "405", "endLine": 1, "endColumn": 44}, {"ruleId": "402", "severity": 1, "message": "438", "line": 4, "column": 15, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 20}, {"ruleId": "402", "severity": 1, "message": "417", "line": 4, "column": 22, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 30}, {"ruleId": "402", "severity": 1, "message": "439", "line": 4, "column": 49, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 55}, {"ruleId": "402", "severity": 1, "message": "440", "line": 4, "column": 57, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 63}, {"ruleId": "402", "severity": 1, "message": "419", "line": 5, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 4}, {"ruleId": "402", "severity": 1, "message": "441", "line": 5, "column": 32, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 45}, {"ruleId": "402", "severity": 1, "message": "442", "line": 5, "column": 58, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 63}, {"ruleId": "402", "severity": 1, "message": "443", "line": 6, "column": 13, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 19}, {"ruleId": "402", "severity": 1, "message": "444", "line": 6, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 31}, {"ruleId": "402", "severity": 1, "message": "420", "line": 7, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 19}, {"ruleId": "402", "severity": 1, "message": "445", "line": 18, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 18, "endColumn": 24}, {"ruleId": "402", "severity": 1, "message": "446", "line": 18, "column": 26, "nodeType": "404", "messageId": "405", "endLine": 18, "endColumn": 43}, {"ruleId": "402", "severity": 1, "message": "447", "line": 113, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 113, "endColumn": 24}, {"ruleId": "402", "severity": 1, "message": "448", "line": 212, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 212, "endColumn": 29}, {"ruleId": "402", "severity": 1, "message": "449", "line": 2, "column": 44, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 53}, {"ruleId": "402", "severity": 1, "message": "450", "line": 2, "column": 115, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 118}, {"ruleId": "402", "severity": 1, "message": "451", "line": 2, "column": 127, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 131}, {"ruleId": "402", "severity": 1, "message": "452", "line": 2, "column": 214, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 217}, {"ruleId": "402", "severity": 1, "message": "453", "line": 2, "column": 226, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 231}, {"ruleId": "402", "severity": 1, "message": "454", "line": 38, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 38, "endColumn": 14}, {"ruleId": "402", "severity": 1, "message": "455", "line": 69, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 69, "endColumn": 28}, {"ruleId": "402", "severity": 1, "message": "456", "line": 69, "column": 30, "nodeType": "404", "messageId": "405", "endLine": 69, "endColumn": 51}, {"ruleId": "402", "severity": 1, "message": "457", "line": 295, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 295, "endColumn": 30}, {"ruleId": "423", "severity": 1, "message": "458", "line": 311, "column": 6, "nodeType": "425", "endLine": 311, "endColumn": 8, "suggestions": "459"}, {"ruleId": "402", "severity": 1, "message": "460", "line": 639, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 639, "endColumn": 30}, {"ruleId": "431", "severity": 1, "message": "432", "line": 843, "column": 50, "nodeType": "433", "messageId": "434", "endLine": 843, "endColumn": 52}, {"ruleId": "431", "severity": 1, "message": "432", "line": 843, "column": 105, "nodeType": "433", "messageId": "434", "endLine": 843, "endColumn": 107}, {"ruleId": "431", "severity": 1, "message": "435", "line": 843, "column": 105, "nodeType": "433", "messageId": "434", "endLine": 843, "endColumn": 107}, {"ruleId": "431", "severity": 1, "message": "435", "line": 844, "column": 49, "nodeType": "433", "messageId": "434", "endLine": 844, "endColumn": 51}, {"ruleId": "431", "severity": 1, "message": "435", "line": 844, "column": 104, "nodeType": "433", "messageId": "434", "endLine": 844, "endColumn": 106}, {"ruleId": "431", "severity": 1, "message": "435", "line": 845, "column": 85, "nodeType": "433", "messageId": "434", "endLine": 845, "endColumn": 87}, {"ruleId": "402", "severity": 1, "message": "461", "line": 3, "column": 40, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 50}, {"ruleId": "402", "severity": 1, "message": "439", "line": 3, "column": 139, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 145}, {"ruleId": "402", "severity": 1, "message": "462", "line": 3, "column": 147, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 152}, {"ruleId": "402", "severity": 1, "message": "463", "line": 3, "column": 154, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 163}, {"ruleId": "402", "severity": 1, "message": "420", "line": 3, "column": 165, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 175}, {"ruleId": "402", "severity": 1, "message": "443", "line": 3, "column": 177, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 183}, {"ruleId": "402", "severity": 1, "message": "464", "line": 3, "column": 224, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 236}, {"ruleId": "402", "severity": 1, "message": "465", "line": 3, "column": 238, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 242}, {"ruleId": "423", "severity": 1, "message": "466", "line": 20, "column": 9, "nodeType": "467", "endLine": 99, "endColumn": 4}, {"ruleId": "402", "severity": 1, "message": "468", "line": 153, "column": 15, "nodeType": "404", "messageId": "405", "endLine": 153, "endColumn": 20}, {"ruleId": "402", "severity": 1, "message": "469", "line": 154, "column": 15, "nodeType": "404", "messageId": "405", "endLine": 154, "endColumn": 20}, {"ruleId": "402", "severity": 1, "message": "470", "line": 10, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 10, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "471", "line": 10, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 10, "endColumn": 41}, {"ruleId": "402", "severity": 1, "message": "472", "line": 22, "column": 22, "nodeType": "404", "messageId": "405", "endLine": 22, "endColumn": 35}, {"ruleId": "402", "severity": 1, "message": "473", "line": 39, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 39, "endColumn": 21}, {"ruleId": "402", "severity": 1, "message": "450", "line": 4, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 6}, {"ruleId": "402", "severity": 1, "message": "419", "line": 5, "column": 14, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 15}, {"ruleId": "402", "severity": 1, "message": "427", "line": 7, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 8}, {"ruleId": "402", "severity": 1, "message": "422", "line": 19, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 19, "endColumn": 22}, {"ruleId": "423", "severity": 1, "message": "474", "line": 137, "column": 6, "nodeType": "425", "endLine": 137, "endColumn": 24, "suggestions": "475"}, {"ruleId": "402", "severity": 1, "message": "438", "line": 3, "column": 62, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 67}, {"ruleId": "402", "severity": 1, "message": "476", "line": 4, "column": 54, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 58}, {"ruleId": "402", "severity": 1, "message": "440", "line": 4, "column": 60, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 66}, {"ruleId": "402", "severity": 1, "message": "441", "line": 4, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "477", "line": 5, "column": 18, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "478", "line": 6, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "479", "line": 22, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 22, "endColumn": 41}, {"ruleId": "402", "severity": 1, "message": "480", "line": 90, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 90, "endColumn": 38}, {"ruleId": "402", "severity": 1, "message": "481", "line": 112, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 112, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "439", "line": 3, "column": 23, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 29}, {"ruleId": "402", "severity": 1, "message": "441", "line": 4, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "477", "line": 5, "column": 18, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "482", "line": 6, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 9}, {"ruleId": "402", "severity": 1, "message": "483", "line": 7, "column": 12, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 21}, {"ruleId": "402", "severity": 1, "message": "484", "line": 7, "column": 49, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 58}, {"ruleId": "402", "severity": 1, "message": "485", "line": 8, "column": 15, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 20}, {"ruleId": "402", "severity": 1, "message": "486", "line": 8, "column": 29, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 33}, {"ruleId": "402", "severity": 1, "message": "479", "line": 28, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 28, "endColumn": 41}, {"ruleId": "423", "severity": 1, "message": "487", "line": 156, "column": 18, "nodeType": "404", "endLine": 156, "endColumn": 25}, {"ruleId": "423", "severity": 1, "message": "488", "line": 203, "column": 6, "nodeType": "425", "endLine": 203, "endColumn": 12, "suggestions": "489"}, {"ruleId": "402", "severity": 1, "message": "480", "line": 221, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 221, "endColumn": 38}, {"ruleId": "402", "severity": 1, "message": "481", "line": 243, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 243, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "490", "line": 371, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 371, "endColumn": 29}, {"ruleId": "402", "severity": 1, "message": "491", "line": 490, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 490, "endColumn": 20}, {"ruleId": "402", "severity": 1, "message": "492", "line": 2, "column": 65, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 76}, {"ruleId": "402", "severity": 1, "message": "450", "line": 3, "column": 22, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 25}, {"ruleId": "402", "severity": 1, "message": "493", "line": 3, "column": 40, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 45}, {"ruleId": "402", "severity": 1, "message": "494", "line": 3, "column": 47, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 51}, {"ruleId": "402", "severity": 1, "message": "494", "line": 6, "column": 43, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 47}, {"ruleId": "402", "severity": 1, "message": "419", "line": 4, "column": 11, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 12}, {"ruleId": "402", "severity": 1, "message": "441", "line": 4, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "477", "line": 5, "column": 18, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "440", "line": 5, "column": 36, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 42}, {"ruleId": "402", "severity": 1, "message": "495", "line": 6, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 13}, {"ruleId": "402", "severity": 1, "message": "496", "line": 6, "column": 36, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 39}, {"ruleId": "402", "severity": 1, "message": "497", "line": 6, "column": 41, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 45}, {"ruleId": "402", "severity": 1, "message": "498", "line": 6, "column": 47, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 51}, {"ruleId": "402", "severity": 1, "message": "499", "line": 6, "column": 53, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 61}, {"ruleId": "402", "severity": 1, "message": "492", "line": 7, "column": 23, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "500", "line": 7, "column": 36, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 47}, {"ruleId": "402", "severity": 1, "message": "501", "line": 7, "column": 49, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 57}, {"ruleId": "402", "severity": 1, "message": "502", "line": 8, "column": 15, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 27}, {"ruleId": "402", "severity": 1, "message": "479", "line": 23, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 23, "endColumn": 41}, {"ruleId": "423", "severity": 1, "message": "503", "line": 79, "column": 6, "nodeType": "425", "endLine": 79, "endColumn": 12, "suggestions": "504"}, {"ruleId": "402", "severity": 1, "message": "450", "line": 2, "column": 16, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 19}, {"ruleId": "402", "severity": 1, "message": "438", "line": 2, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 26}, {"ruleId": "402", "severity": 1, "message": "492", "line": 2, "column": 65, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 76}, {"ruleId": "402", "severity": 1, "message": "449", "line": 3, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 30}, {"ruleId": "402", "severity": 1, "message": "450", "line": 3, "column": 52, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 55}, {"ruleId": "402", "severity": 1, "message": "493", "line": 3, "column": 70, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 75}, {"ruleId": "402", "severity": 1, "message": "494", "line": 3, "column": 77, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 81}, {"ruleId": "402", "severity": 1, "message": "417", "line": 4, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 11}, {"ruleId": "402", "severity": 1, "message": "505", "line": 4, "column": 38, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 47}, {"ruleId": "402", "severity": 1, "message": "420", "line": 4, "column": 49, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 59}, {"ruleId": "402", "severity": 1, "message": "441", "line": 4, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "478", "line": 6, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "427", "line": 8, "column": 16, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 21}, {"ruleId": "402", "severity": 1, "message": "506", "line": 8, "column": 23, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 30}, {"ruleId": "402", "severity": 1, "message": "507", "line": 8, "column": 51, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 57}, {"ruleId": "402", "severity": 1, "message": "508", "line": 8, "column": 59, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 76}, {"ruleId": "402", "severity": 1, "message": "494", "line": 9, "column": 19, "nodeType": "404", "messageId": "405", "endLine": 9, "endColumn": 23}, {"ruleId": "423", "severity": 1, "message": "509", "line": 104, "column": 6, "nodeType": "425", "endLine": 104, "endColumn": 24, "suggestions": "510"}, {"ruleId": "402", "severity": 1, "message": "511", "line": 135, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 135, "endColumn": 24}, {"ruleId": "402", "severity": 1, "message": "481", "line": 434, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 434, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "428", "line": 3, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 7}, {"ruleId": "402", "severity": 1, "message": "420", "line": 4, "column": 31, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 41}, {"ruleId": "402", "severity": 1, "message": "512", "line": 4, "column": 52, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 60}, {"ruleId": "402", "severity": 1, "message": "427", "line": 4, "column": 73, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 78}, {"ruleId": "402", "severity": 1, "message": "513", "line": 19, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 19, "endColumn": 22}, {"ruleId": "423", "severity": 1, "message": "514", "line": 46, "column": 6, "nodeType": "425", "endLine": 46, "endColumn": 19, "suggestions": "515"}, {"ruleId": "402", "severity": 1, "message": "441", "line": 4, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "477", "line": 5, "column": 18, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "478", "line": 6, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "411", "line": 8, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 8}, {"ruleId": "402", "severity": 1, "message": "506", "line": 8, "column": 23, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 30}, {"ruleId": "402", "severity": 1, "message": "516", "line": 8, "column": 32, "nodeType": "404", "messageId": "405", "endLine": 8, "endColumn": 49}, {"ruleId": "402", "severity": 1, "message": "517", "line": 9, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 9, "endColumn": 17}, {"ruleId": "402", "severity": 1, "message": "494", "line": 9, "column": 19, "nodeType": "404", "messageId": "405", "endLine": 9, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "518", "line": 81, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 81, "endColumn": 29}, {"ruleId": "402", "severity": 1, "message": "519", "line": 81, "column": 31, "nodeType": "404", "messageId": "405", "endLine": 81, "endColumn": 53}, {"ruleId": "402", "severity": 1, "message": "520", "line": 84, "column": 28, "nodeType": "404", "messageId": "405", "endLine": 84, "endColumn": 47}, {"ruleId": "402", "severity": 1, "message": "521", "line": 85, "column": 27, "nodeType": "404", "messageId": "405", "endLine": 85, "endColumn": 45}, {"ruleId": "402", "severity": 1, "message": "522", "line": 145, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 145, "endColumn": 27}, {"ruleId": "402", "severity": 1, "message": "523", "line": 152, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 152, "endColumn": 26}, {"ruleId": "423", "severity": 1, "message": "509", "line": 207, "column": 6, "nodeType": "425", "endLine": 207, "endColumn": 24, "suggestions": "524"}, {"ruleId": "423", "severity": 1, "message": "525", "line": 254, "column": 6, "nodeType": "425", "endLine": 254, "endColumn": 30, "suggestions": "526"}, {"ruleId": "402", "severity": 1, "message": "481", "line": 443, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 443, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "527", "line": 4, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 7}, {"ruleId": "402", "severity": 1, "message": "420", "line": 4, "column": 49, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 59}, {"ruleId": "402", "severity": 1, "message": "528", "line": 4, "column": 61, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 67}, {"ruleId": "402", "severity": 1, "message": "529", "line": 5, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 11}, {"ruleId": "402", "severity": 1, "message": "530", "line": 5, "column": 13, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 27}, {"ruleId": "402", "severity": 1, "message": "439", "line": 3, "column": 22, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 28}, {"ruleId": "402", "severity": 1, "message": "500", "line": 5, "column": 32, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 43}, {"ruleId": "402", "severity": 1, "message": "441", "line": 5, "column": 45, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 58}, {"ruleId": "402", "severity": 1, "message": "427", "line": 6, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 8}, {"ruleId": "402", "severity": 1, "message": "531", "line": 7, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 22}, {"ruleId": "402", "severity": 1, "message": "532", "line": 7, "column": 24, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 28}, {"ruleId": "402", "severity": 1, "message": "406", "line": 7, "column": 30, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 34}, {"ruleId": "402", "severity": 1, "message": "533", "line": 7, "column": 36, "nodeType": "404", "messageId": "405", "endLine": 7, "endColumn": 45}, {"ruleId": "402", "severity": 1, "message": "534", "line": 139, "column": 27, "nodeType": "404", "messageId": "405", "endLine": 139, "endColumn": 47}, {"ruleId": "402", "severity": 1, "message": "535", "line": 320, "column": 7, "nodeType": "404", "messageId": "405", "endLine": 320, "endColumn": 30}, {"ruleId": "402", "severity": 1, "message": "494", "line": 3, "column": 27, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 31}, {"ruleId": "402", "severity": 1, "message": "536", "line": 5, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 31}, {"ruleId": "402", "severity": 1, "message": "537", "line": 5, "column": 33, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 41}, {"ruleId": "423", "severity": 1, "message": "538", "line": 131, "column": 6, "nodeType": "425", "endLine": 131, "endColumn": 94, "suggestions": "539"}, {"ruleId": "402", "severity": 1, "message": "485", "line": 2, "column": 60, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 65}, {"ruleId": "402", "severity": 1, "message": "409", "line": 2, "column": 129, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 137}, {"ruleId": "402", "severity": 1, "message": "450", "line": 2, "column": 158, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 161}, {"ruleId": "402", "severity": 1, "message": "419", "line": 2, "column": 169, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 170}, {"ruleId": "402", "severity": 1, "message": "540", "line": 2, "column": 206, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 213}, {"ruleId": "402", "severity": 1, "message": "541", "line": 16, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 16, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "542", "line": 16, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 16, "endColumn": 41}, {"ruleId": "402", "severity": 1, "message": "543", "line": 23, "column": 22, "nodeType": "404", "messageId": "405", "endLine": 23, "endColumn": 35}, {"ruleId": "402", "severity": 1, "message": "544", "line": 27, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 27, "endColumn": 26}, {"ruleId": "402", "severity": 1, "message": "545", "line": 28, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 28, "endColumn": 26}, {"ruleId": "402", "severity": 1, "message": "546", "line": 40, "column": 20, "nodeType": "404", "messageId": "405", "endLine": 40, "endColumn": 31}, {"ruleId": "423", "severity": 1, "message": "547", "line": 132, "column": 6, "nodeType": "425", "endLine": 132, "endColumn": 46, "suggestions": "548"}, {"ruleId": "423", "severity": 1, "message": "549", "line": 150, "column": 4, "nodeType": "425", "endLine": 150, "endColumn": 6, "suggestions": "550"}, {"ruleId": "402", "severity": 1, "message": "437", "line": 1, "column": 27, "nodeType": "404", "messageId": "405", "endLine": 1, "endColumn": 36}, {"ruleId": "402", "severity": 1, "message": "428", "line": 2, "column": 45, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 49}, {"ruleId": "402", "severity": 1, "message": "551", "line": 20, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 20, "endColumn": 20}, {"ruleId": "402", "severity": 1, "message": "437", "line": 1, "column": 27, "nodeType": "404", "messageId": "405", "endLine": 1, "endColumn": 36}, {"ruleId": "402", "severity": 1, "message": "552", "line": 2, "column": 23, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 33}, {"ruleId": "402", "severity": 1, "message": "553", "line": 2, "column": 35, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 47}, {"ruleId": "402", "severity": 1, "message": "450", "line": 2, "column": 125, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 128}, {"ruleId": "402", "severity": 1, "message": "451", "line": 2, "column": 130, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 134}, {"ruleId": "402", "severity": 1, "message": "408", "line": 2, "column": 169, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 175}, {"ruleId": "402", "severity": 1, "message": "554", "line": 12, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 12, "endColumn": 23}, {"ruleId": "402", "severity": 1, "message": "555", "line": 12, "column": 25, "nodeType": "404", "messageId": "405", "endLine": 12, "endColumn": 41}, {"ruleId": "402", "severity": 1, "message": "556", "line": 35, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 35, "endColumn": 25}, {"ruleId": "402", "severity": 1, "message": "557", "line": 151, "column": 9, "nodeType": "404", "messageId": "405", "endLine": 151, "endColumn": 27}, {"ruleId": "402", "severity": 1, "message": "498", "line": 3, "column": 27, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 31}, {"ruleId": "402", "severity": 1, "message": "553", "line": 3, "column": 45, "nodeType": "404", "messageId": "405", "endLine": 3, "endColumn": 57}, {"ruleId": "402", "severity": 1, "message": "558", "line": 4, "column": 20, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 33}, {"ruleId": "402", "severity": 1, "message": "559", "line": 4, "column": 47, "nodeType": "404", "messageId": "405", "endLine": 4, "endColumn": 58}, {"ruleId": "402", "severity": 1, "message": "409", "line": 5, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 11}, {"ruleId": "402", "severity": 1, "message": "408", "line": 5, "column": 21, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 27}, {"ruleId": "402", "severity": 1, "message": "560", "line": 5, "column": 39, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 51}, {"ruleId": "402", "severity": 1, "message": "496", "line": 5, "column": 53, "nodeType": "404", "messageId": "405", "endLine": 5, "endColumn": 56}, {"ruleId": "402", "severity": 1, "message": "463", "line": 6, "column": 3, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 12}, {"ruleId": "402", "severity": 1, "message": "451", "line": 6, "column": 34, "nodeType": "404", "messageId": "405", "endLine": 6, "endColumn": 38}, {"ruleId": "402", "severity": 1, "message": "451", "line": 2, "column": 49, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 53}, {"ruleId": "402", "severity": 1, "message": "561", "line": 2, "column": 106, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 115}, {"ruleId": "402", "severity": 1, "message": "421", "line": 2, "column": 162, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 170}, {"ruleId": "402", "severity": 1, "message": "562", "line": 2, "column": 172, "nodeType": "404", "messageId": "405", "endLine": 2, "endColumn": 182}, {"ruleId": "402", "severity": 1, "message": "563", "line": 20, "column": 10, "nodeType": "404", "messageId": "405", "endLine": 20, "endColumn": 16}, {"ruleId": "402", "severity": 1, "message": "564", "line": 20, "column": 18, "nodeType": "404", "messageId": "405", "endLine": 20, "endColumn": 27}, "no-unused-vars", "'Share' is defined but never used.", "Identifier", "unusedVar", "'Gift' is defined but never used.", "'Unlock' is defined but never used.", "'Shield' is defined but never used.", "'Calendar' is defined but never used.", "'PlusCircle' is defined but never used.", "'Users' is defined but never used.", "'starPlanData' is assigned a value but never used.", "'handleUseAsset' is assigned a value but never used.", "'truncateText' is assigned a value but never used.", "'Headphones' is defined but never used.", "'Percent' is defined but never used.", "'Sparkles' is defined but never used.", "'Image' is defined but never used.", "'X' is defined but never used.", "'ArrowRight' is defined but never used.", "'ThumbsUp' is defined but never used.", "'previewVideo' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'descriptionTemplates'. Either include it or remove the dependency array.", "ArrayExpression", ["565"], "'Video' is defined but never used.", "'Star' is defined but never used.", "'Music' is defined but never used.", "'statistics' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "'useRef' is defined but never used.", "'useEffect' is defined but never used.", "'Award' is defined but never used.", "'Upload' is defined but never used.", "'Camera' is defined but never used.", "'AlertTriangle' is defined but never used.", "'Flame' is defined but never used.", "'Trash2' is defined but never used.", "'PanelRight' is defined but never used.", "'showCreateForm' is assigned a value but never used.", "'setShowCreateForm' is assigned a value but never used.", "'characterPhotos' is assigned a value but never used.", "'newImage' is assigned a value but never used.", "'ImageIcon' is defined but never used.", "'Zap' is defined but never used.", "'Info' is defined but never used.", "'Mic' is defined but never used.", "'Smile' is defined but never used.", "'gifts' is assigned a value but never used.", "'showPaymentOptions' is assigned a value but never used.", "'setShowPaymentOptions' is assigned a value but never used.", "'memoryAcquisitionText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'characterImages.length'. Either include it or remove the dependency array.", ["566"], "'handleAbilityInteract' is assigned a value but never used.", "'FilterIcon' is defined but never used.", "'Wand2' is defined but never used.", "'RefreshCw' is defined but never used.", "'MoreVertical' is defined but never used.", "'Edit' is defined but never used.", "The 'assets' array makes the dependencies of useMemo Hook (at line 170) change on every render. To fix this, wrap the initialization of 'assets' in its own useMemo() Hook.", "VariableDeclarator", "'dateA' is assigned a value but never used.", "'dateB' is assigned a value but never used.", "'selectedImage' is assigned a value but never used.", "'setSelectedImage' is assigned a value but never used.", "'setVisibility' is assigned a value but never used.", "'formatNumber' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'videoTemplates'. Either include it or remove the dependency array.", ["567"], "'Moon' is defined but never used.", "'Clock' is defined but never used.", "'ImagePlus' is defined but never used.", "'setEstimatedTime' is assigned a value but never used.", "'determineDefaultPaymentMethod' is assigned a value but never used.", "'handleSelectPaymentMethod' is assigned a value but never used.", "'Music2' is defined but never used.", "'FileMusic' is defined but never used.", "'FileImage' is defined but never used.", "'Heart' is defined but never used.", "'Save' is defined but never used.", "The ref value 'audioRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'audioRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'creationMode', 'generateMusicTitle', 'lyrics', and 'musicPrompt'. Either include them or remove the dependency array.", ["568"], "'handleGenerateLyrics' is assigned a value but never used.", "'musicAssets' is assigned a value but never used.", "'ChevronDown' is defined but never used.", "'Check' is defined but never used.", "'User' is defined but never used.", "'Paintbrush' is defined but never used.", "'Eye' is defined but never used.", "'Copy' is defined but never used.", "'Plus' is defined but never used.", "'Download' is defined but never used.", "'CheckCircle' is defined but never used.", "'Settings' is defined but never used.", "'ExternalLink' is defined but never used.", "React Hook useEffect has missing dependencies: 'generateAIDescription' and 'processingStatus'. Either include them or remove the dependency array.", ["569"], "'ArrowLeft' is defined but never used.", "'Volume2' is defined but never used.", "'Square' is defined but never used.", "'RectangleVertical' is defined but never used.", "React Hook useEffect has missing dependencies: 'determineDefaultPaymentMethod', 'paymentOptions', 'selectedPaymentMethod', and 'userBalance'. Either include them or remove the dependency array.", ["570"], "'durationOptions' is assigned a value but never used.", "'FileText' is defined but never used.", "'videoWatched' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'generatedImage'. Either include it or remove the dependency array.", ["571"], "'SlidersHorizontal' is defined but never used.", "'PenTool' is defined but never used.", "'showAdvancedOptions' is assigned a value but never used.", "'setShowAdvancedOptions' is assigned a value but never used.", "'setVideoOrientation' is assigned a value but never used.", "'setVideoResolution' is assigned a value but never used.", "'orientationOptions' is assigned a value but never used.", "'resolutionOptions' is assigned a value but never used.", ["572"], "React Hook useEffect has a missing dependency: 'videoDescription'. Either include it or remove the dependency array.", ["573"], "'Send' is defined but never used.", "'Filter' is defined but never used.", "'SortDesc' is defined but never used.", "'MoreHorizontal' is defined but never used.", "'ArrowUpRight' is defined but never used.", "'Lock' is defined but never used.", "'ArrowDown' is defined but never used.", "'setSelectedCharacter' is assigned a value but never used.", "'handleCreateCustomDream' is assigned a value but never used.", "'Share2' is defined but never used.", "'Bookmark' is defined but never used.", "React Hook useEffect has missing dependencies: 'getTotalRemainingMinutes' and 'showTimeUpNotice'. Either include them or remove the dependency array.", ["574"], "'Battery' is defined but never used.", "'characterMood' is assigned a value but never used.", "'setCharacterMood' is assigned a value but never used.", "'setProfitRate' is assigned a value but never used.", "'dailyCheckInDone' is assigned a value but never used.", "'showVitalityTips' is assigned a value but never used.", "'setHoldings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAutoSettle'. Either include it or remove the dependency array.", ["575"], "React Hook useEffect has a missing dependency: 'lastDailyCheckIn'. Either include it or remove the dependency array.", ["576"], "'myAIP<PERSON>ner' is assigned a value but never used.", "'TrendingUp' is defined but never used.", "'TrendingDown' is defined but never used.", "'selectedIndex' is assigned a value but never used.", "'setSelectedIndex' is assigned a value but never used.", "'handleViewReport' is assigned a value but never used.", "'handleAnalyzeStock' is assigned a value but never used.", "'MessageCircle' is defined but never used.", "'AlertCircle' is defined but never used.", "'ChevronRight' is defined but never used.", "'LineChart' is defined but never used.", "'ThumbsDown' is defined but never used.", "'aiMood' is assigned a value but never used.", "'setAiMood' is assigned a value but never used.", {"desc": "577", "fix": "578"}, {"desc": "579", "fix": "580"}, {"desc": "581", "fix": "582"}, {"desc": "583", "fix": "584"}, {"desc": "585", "fix": "586"}, {"desc": "587", "fix": "588"}, {"desc": "589", "fix": "590"}, {"desc": "587", "fix": "591"}, {"desc": "592", "fix": "593"}, {"desc": "594", "fix": "595"}, {"desc": "596", "fix": "597"}, {"desc": "598", "fix": "599"}, "Update the dependencies array to be: [descriptionTemplates, selectedCategory]", {"range": "600", "text": "601"}, "Update the dependencies array to be: [characterImages.length]", {"range": "602", "text": "603"}, "Update the dependencies array to be: [selectedCategory, videoTemplates]", {"range": "604", "text": "605"}, "Update the dependencies array to be: [creationMode, generateMusicTitle, lyrics, musicPrompt, step]", {"range": "606", "text": "607"}, "Update the dependencies array to be: [generateAIDescription, processingStatus, step]", {"range": "608", "text": "609"}, "Update the dependencies array to be: [determineDefaultPaymentMethod, paymentOptions, selectedDuration, selectedPaymentMethod, userBalance]", {"range": "610", "text": "611"}, "Update the dependencies array to be: [currentStep, generatedImage]", {"range": "612", "text": "613"}, {"range": "614", "text": "611"}, "Update the dependencies array to be: [step, selectedDuration, videoDescription]", {"range": "615", "text": "616"}, "Update the dependencies array to be: [isInCall, remainingTrialTime, remainingPaidTime, remainingVIPTime, hasUsedTrial, isVIP, getTotalRemainingMinutes, showTimeUpNotice]", {"range": "617", "text": "618"}, "Update the dependencies array to be: [lastVitalityUpdate, isInvestmentActive, handleAutoSettle]", {"range": "619", "text": "620"}, "Update the dependencies array to be: [lastDailyCheckIn]", {"range": "621", "text": "622"}, [4506, 4524], "[descriptionTemplates, selectedCategory]", [11441, 11443], "[characterImages.length]", [3838, 3856], "[selectedCategory, videoTemplates]", [7640, 7646], "[creationM<PERSON>, generateMusicTitle, lyrics, musicPrompt, step]", [3214, 3220], "[generateAIDescription, processingStatus, step]", [5662, 5680], "[determineDefaultPaymentMethod, paymentOptions, selectedDuration, selectedPaymentMethod, userBalance]", [2397, 2410], "[currentStep, generatedImage]", [9750, 9768], [11314, 11338], "[step, selectedDuration, videoDescription]", [4338, 4426], "[isInCall, remainingTrialTime, remainingPaidTime, remainingVIPTime, hasUsedTrial, isVIP, getTotalRemainingMinutes, showTimeUpNotice]", [5198, 5238], "[lastVitalityUpdate, isInvestmentActive, handleAutoSettle]", [5768, 5770], "[lastDailyCheckIn]"]