import React, { useState, useMemo, useEffect } from 'react';
import {
  ChevronLeft, Plus, Search, Filter as FilterIcon, Image as ImageIcon, Video, Mic, Music, Clock, Heart, Send, CheckCircle, Check, X, Eye, Upload, Wand2, RefreshCw, ArrowRight, Trash2, SortAsc, SortDesc, ThumbsUp, Activity, MoreVertical, Edit, HelpCircle, Info
} from 'lucide-react';

// 优化后的 AssetLibraryPage 组件
const AssetLibraryPage = () => {
  // --- 状态管理 ---
  const [selectedAssetId, setSelectedAssetId] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  // 筛选类型: 'all', 'image', 'video', 'audio', 'music'
  const [filterType, setFilterType] = useState('all');
  // 排序类型: 'newest', 'oldest', 'popular', 'usage'
  const [sortType, setSortType] = useState('newest');
  const [showSortOptions, setShowSortOptions] = useState(false); // 控制排序选项显隐
  const [showHelp, setShowHelp] = useState(true); // 控制帮助提示显示
  const [showFABOptions, setShowFABOptions] = useState(false); // 控制FAB选项显示

  // --- 模拟素材数据 (混合类型) ---
  const assets = [
     {
      id: 1,
      type: 'image',
      url: '/api/placeholder/200/300',
      title: '雨夜霓虹',
      createdAt: '今天 09:42',
      usedCount: 2,
      likes: 327,
      duration: null,
      isHD: true,
    },
    {
      id: 101, // 与TalentsView区分
      type: 'video',
      url: '/api/placeholder/200/150',
      title: "案件解析",
      createdAt: '昨天 15:30',
      usedCount: 1,
      likes: 876,
      duration: "12秒",
      isHD: false,
    },
    {
      id: 2,
      type: 'image',
      url: '/api/placeholder/200/300',
      title: '午后阳光',
      createdAt: '今天 08:15',
      usedCount: 0,
      likes: 0,
      duration: null,
      isHD: true,
    },
    {
      id: 201,
      type: 'audio',
      url: null, // 音频可能没有缩略图，用图标代替
      title: "K的晚安低语",
      createdAt: '2天前 10:00',
      usedCount: 5,
      likes: 1024,
      duration: "35秒",
      isHD: null,
    },
    {
        id: 102,
        type: 'video',
        url: '/api/placeholder/200/150',
        title: "街头机械舞",
        createdAt: '3天前 18:20',
        usedCount: 3,
        likes: 512,
        duration: "18秒",
        isHD: true,
    },
    {
      id: 3,
      type: 'image',
      url: '/api/placeholder/200/300',
      title: '线索墙',
      createdAt: '昨天 11:20',
      usedCount: 0,
      likes: 0,
      duration: null,
      isHD: true,
    },
     {
      id: 301,
      type: 'music',
      url: null,
      title: "赛博都市BGM",
      createdAt: '4天前 09:00',
      usedCount: 8,
      likes: 1500,
      duration: "2分15秒",
      isHD: null,
    },
    // ... 可以添加更多不同类型的素材
  ];

  // --- 素材类型定义 ---
  const assetTypes = [
    { id: 'all', name: '全部', icon: <Activity size={16} /> },
    { id: 'image', name: '图片', icon: <ImageIcon size={16} /> },
    { id: 'video', name: '视频', icon: <Video size={16} /> },
    { id: 'audio', name: '语音', icon: <Mic size={16} /> },
    { id: 'music', name: '音乐', icon: <Music size={16} /> },
  ];

   // --- 排序选项定义 ---
   const sortOptions = [
    { id: 'newest', name: '最新创建', icon: <SortDesc size={14}/> }, // Icon indicates descending time
    { id: 'oldest', name: '最早创建', icon: <SortAsc size={14}/> }, // Icon indicates ascending time
    { id: 'popular', name: '最多点赞', icon: <ThumbsUp size={14}/> },
    { id: 'usage', name: '最多使用', icon: <CheckCircle size={14}/> },
  ];

  // FIX 3: Removed automatic hiding of help tooltip. User dismisses manually.
  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     setShowHelp(false);
  //   }, 5000); // 5秒后自动隐藏提示
  //   return () => clearTimeout(timer);
  // }, []);

  // 点击外部区域关闭FAB选项 和 排序选项
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showFABOptions && !event.target.closest('.fab-container')) {
        setShowFABOptions(false);
      }
      if (showSortOptions && !event.target.closest('.sort-button-container')) {
        setShowSortOptions(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showFABOptions, showSortOptions]);

  // --- 过滤和排序逻辑 ---
  const filteredAssets = useMemo(() => {
    return assets
      .filter(asset => {
        if (filterType !== 'all' && asset.type !== filterType) return false;
        if (searchQuery && !(asset.title?.toLowerCase().includes(searchQuery.toLowerCase()) || `ID:${asset.id}`.includes(searchQuery.toUpperCase()))) return false;
        return true;
      })
      .sort((a, b) => {
        // Basic date sorting assuming format or use ID as proxy
        const dateA = new Date(a.createdAt.replace('今天', '').replace('昨天', '').trim()); // Crude parsing
        const dateB = new Date(b.createdAt.replace('今天', '').replace('昨天', '').trim()); // Crude parsing

        switch (sortType) {
          case 'oldest':
            // Better: return dateA - dateB; Proxy:
            return a.id - b.id;
          case 'popular':
            return (b.likes || 0) - (a.likes || 0);
          case 'usage':
            return (b.usedCount || 0) - (a.usedCount || 0);
          case 'newest':
          default:
             // Better: return dateB - dateA; Proxy:
            return b.id - a.id;
        }
      });
  }, [assets, filterType, searchQuery, sortType]);

  // --- 事件处理 ---
  const handleSelectAsset = (id) => {
    setSelectedAssetId(prevId => (prevId === id ? null : id));
  };

  const handleCreateAsset = (assetType) => {
    const selectedAsset = selectedAssetId ? assets.find(a => a.id === selectedAssetId) : null;
    if (selectedAsset) {
      alert(`开始使用素材 [${selectedAsset.title}] 创建新的 ${assetType} 素材... (功能待实现)`);
    } else {
      alert(`开始创建新的 ${assetType} 素材... (功能待实现)`);
    }
    setShowFABOptions(false);
    // Reset selection after deciding to create (optional, depends on desired UX)
    // setSelectedAssetId(null);
  };

  const handlePreviewAsset = (asset) => {
    alert(`预览素材: ${asset.title || `类型: ${asset.type}, ID: ${asset.id}`}\n类型: ${asset.type}\n时长: ${asset.duration || 'N/A'}`);
  };

  const handleUseAsset = () => {
    if (!selectedAssetId) return;
    const selectedAsset = assets.find(a => a.id === selectedAssetId);
    alert(`使用素材 [${selectedAsset?.title || selectedAsset?.type}] 发布动态... (导航到发布页)`);
    setSelectedAssetId(null); // Deselect after using
  };

  const toggleFABOptions = (e) => {
    e.stopPropagation(); // Prevent triggering click outside listener
    setShowFABOptions(!showFABOptions);
  };

  const toggleSortOptions = (e) => {
    e.stopPropagation(); // Prevent triggering click outside listener
    setShowSortOptions(!showSortOptions);
  }

  // --- 获取类型图标 ---
   const getTypeIcon = (type) => {
    switch (type) {
      case 'image': return <ImageIcon size={14} className="text-blue-500" />;
      case 'video': return <Video size={14} className="text-red-500" />;
      case 'audio': return <Mic size={14} className="text-green-500" />;
      case 'music': return <Music size={14} className="text-orange-500" />;
      default: return <Activity size={14} className="text-gray-500" />;
    }
  };

  return (
    // Outer container to ensure fixed positioning context if needed, though not strictly necessary here
    // as the inner container handles the layout.
    <div className="fixed inset-0 flex justify-center bg-gray-100 overflow-hidden">

      {/* Main content container with max-width and relative positioning */}
      {/* FIX 1 & 2: Added `relative` here */}
      <div className="relative w-full max-w-md h-full flex flex-col bg-gray-50 shadow-xl">

        {/* 头部导航 */}
        <div className="sticky top-0 z-20 w-full bg-purple-700 px-4 py-3 flex items-center justify-between text-white flex-shrink-0">
          <div className="flex items-center">
            <button className="p-1 mr-2">
              <ChevronLeft size={20} className="text-white" />
            </button>
            <h1 className="text-lg font-bold">素材库</h1>
          </div>
          {selectedAssetId ? (
             <button
              onClick={handleUseAsset}
              className="bg-white text-purple-700 px-3 py-1.5 rounded-full text-sm font-medium flex items-center shadow-sm hover:bg-purple-50 transition-colors"
            >
              <Send size={16} className="mr-1.5" />
              使用素材发布
            </button>
          ) : (
            <button
              onClick={() => setShowHelp(!showHelp)}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-purple-600 transition-colors"
            >
              <HelpCircle size={20} className="text-white opacity-80" />
            </button>
          )}
        </div>

        {/* 帮助提示 */}
        {showHelp && (
          <div className="bg-purple-50 border-l-4 border-purple-500 px-4 py-3 mx-3 mt-3 rounded shadow-sm text-sm text-purple-700 flex-shrink-0">
            <div className="flex items-start">
              <Info size={16} className="mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium mb-1">使用提示</p>
                <ul className="text-xs space-y-1">
                  <li>• 点击素材卡片进行选择/取消选择。</li>
                  <li>• 选中素材后可点击顶部按钮发布动态。</li>
                  <li>• 点击右下角 '+' 可创建新素材，若已选素材则会将其作为模板。</li>
                  <li>• 创建素材会消耗星光币，音频100，图片300，视频800</li>
                  <li>• 使用搜索、筛选和排序功能快速查找。</li>
                </ul>
              </div>
              <button
                onClick={() => setShowHelp(false)}
                className="ml-2 p-1 text-purple-400 hover:text-purple-600 flex-shrink-0" // Added padding for easier clicking
                aria-label="关闭提示" // Accessibility
              >
                <X size={16} />
              </button>
            </div>
          </div>
        )}

        {/* 筛选和搜索栏 */}
        <div className="sticky top-[56px] z-10 bg-white pt-3 pb-2 px-3 shadow-sm flex-shrink-0">
           <div className="relative mb-3">
             <input
               type="text"
               value={searchQuery}
               onChange={(e) => setSearchQuery(e.target.value)}
               className="w-full border border-gray-300 rounded-full pl-10 pr-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
               placeholder="搜索标题或ID (例: ID:101)"
             />
             <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
           </div>
           <div className="flex justify-between items-center">
             <div className="flex space-x-2 overflow-x-auto pb-1 hide-scrollbar"> {/* Added hide-scrollbar */}
               {assetTypes.map((type) => (
                 <button
                   key={type.id}
                   onClick={() => { setFilterType(type.id); setSelectedAssetId(null); }}
                   className={`whitespace-nowrap px-3 py-1 rounded-full text-xs font-medium flex items-center transition-colors ${
                     filterType === type.id
                       ? 'bg-purple-600 text-white shadow-sm'
                       : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                   }`}
                 >
                   <span className="mr-1">{type.icon}</span>
                   {type.name}
                 </button>
               ))}
             </div>
              {/* FIX: Added container for click outside detection */}
             <div className="relative ml-2 flex-shrink-0 sort-button-container">
                <button
                    onClick={toggleSortOptions}
                    className="p-2 border border-gray-300 rounded-full bg-white text-gray-600 hover:bg-gray-50"
                    aria-label="排序选项" // Accessibility
                >
                   {sortOptions.find(opt => opt.id === sortType)?.icon || <SortAsc size={16}/>}
                </button>
                {showSortOptions && (
                    <div className="absolute right-0 mt-1 w-36 bg-white rounded-md shadow-lg border border-gray-200 z-30">
                        {sortOptions.map(option => (
                            <button
                                key={option.id}
                                onClick={() => { setSortType(option.id); setShowSortOptions(false); }}
                                className={`w-full text-left px-3 py-2 text-sm flex items-center hover:bg-gray-100 ${sortType === option.id ? 'text-purple-600 font-medium' : 'text-gray-700'}`}
                            >
                                <span className="mr-2">{option.icon}</span>
                                {option.name}
                            </button>
                        ))}
                    </div>
                )}
             </div>
           </div>
        </div>

        {/* 主内容区域: 素材网格 */}
        {/* FIX 1 & 2: Increased padding-bottom (`pb-40`) significantly */}
        <div className="flex-1 overflow-y-auto pb-40"> {/* Increased padding */}
          <div className="px-3 pt-3">
            {filteredAssets.length > 0 ? (
              <div className="grid grid-cols-2 gap-3">
                {filteredAssets.map((asset) => {
                  const isSelected = asset.id === selectedAssetId;
                  return (
                    <div
                      key={asset.id}
                      onClick={() => handleSelectAsset(asset.id)}
                      className={`relative rounded-lg overflow-hidden border-2 bg-white shadow-sm transition-all active:scale-95 cursor-pointer group ${ // Added group for hover effects
                        isSelected
                          ? 'border-purple-500 scale-[1.02] shadow-lg'
                          : 'border-transparent hover:shadow-md'
                      }`}
                    >
                      <div className="relative aspect-[4/3] bg-gray-100">
                        {asset.type === 'image' || asset.type === 'video' ? (
                          <div className="absolute inset-0 image-placeholder flex items-center justify-center">
                             {asset.type === 'video' && <Video size={24} className="text-gray-500 opacity-50" />}
                             {asset.type === 'image' && <ImageIcon size={24} className="text-gray-500 opacity-50" />}
                             {/* Placeholder text for testing */}
                             {/* <span className="text-xs text-gray-400">Image/Video Placeholder</span> */}
                          </div>
                          // Use actual image when available:
                          // <img src={asset.url} alt={asset.title || `Asset ${asset.id}`} className="absolute inset-0 w-full h-full object-cover" loading="lazy" />
                        ) : (
                          <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-indigo-50 to-purple-50">
                            {asset.type === 'audio' && <Mic size={32} className="text-purple-400" />}
                            {asset.type === 'music' && <Music size={32} className="text-orange-400" />}
                          </div>
                        )}
                        <div className="absolute top-1.5 left-1.5 bg-black bg-opacity-60 text-white text-xs px-1.5 py-0.5 rounded flex items-center z-[1]"> {/* Ensure z-index */}
                           {getTypeIcon(asset.type)}
                          {asset.duration && <span className="ml-1">{asset.duration}</span>}
                        </div>
                         <button
                             onClick={(e) => { e.stopPropagation(); handlePreviewAsset(asset); }}
                             className="absolute bottom-1.5 right-1.5 p-1.5 bg-black bg-opacity-30 rounded-full text-white opacity-0 group-hover:opacity-100 hover:bg-opacity-60 transition-opacity z-[1]" // Show on hover
                             title="预览"
                             aria-label={`预览 ${asset.title || `素材 ${asset.id}`}`} // Accessibility
                         >
                             <Eye size={14} />
                         </button>
                        {isSelected && (
                          <div className="absolute top-1.5 right-1.5 bg-purple-600 text-white p-1 rounded-full shadow z-[1]">
                            <Check size={14} />
                          </div>
                        )}
                         {/* Optional: Dark overlay on hover for non-selected items */}
                         {!isSelected && <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-opacity pointer-events-none"></div>}
                      </div>

                      <div className="p-2">
                        <p className="text-sm font-medium text-gray-800 truncate" title={asset.title || `素材 ${asset.id}`}>
                          {asset.title || `素材 ${asset.id}`}
                        </p>
                        <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
                          <span className="flex items-center" title={asset.createdAt}> {/* More specific title */}
                            <Clock size={12} className="mr-1 flex-shrink-0" /> {/* Prevent shrink */}
                            <span className="truncate">{asset.createdAt}</span> {/* Truncate if needed */}
                          </span>
                          <div className="flex items-center space-x-2 flex-shrink-0"> {/* Prevent shrink */}
                            <span className="flex items-center" title="使用次数">
                              <CheckCircle size={12} className="mr-0.5" />
                              {asset.usedCount || 0}
                            </span>
                            <span className="flex items-center" title="点赞数">
                              <Heart size={12} className="mr-0.5" />
                              {asset.likes || 0}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center pt-16 text-center text-gray-500">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  {assetTypes.find(t => t.id === filterType)?.icon || <Activity size={24} className="text-gray-400" />}
                </div>
                <p className="text-sm mb-2">
                  {searchQuery ? '未找到匹配的素材' : `暂无${assetTypes.find(t => t.id === filterType)?.name || '该类型'}素材`}
                </p>
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="text-purple-600 text-sm font-medium hover:underline"
                  >
                    清空搜索条件
                  </button>
                )}
                 {filterType !== 'all' && !searchQuery && (
                  <button
                    onClick={() => setFilterType('all')}
                    className="text-purple-600 text-sm font-medium hover:underline mt-1"
                  >
                    查看全部类型
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 底部状态栏 - 选中素材提示 */}
        {/* FIX 2: Changed to `absolute` positioning within the `relative` parent */}
        {/* Using `inset-x-0` to span the width of the parent */}
        {selectedAssetId && (
          <div className="absolute bottom-[88px] inset-x-0 bg-purple-100 py-2 px-4 flex justify-between items-center z-20 border-t border-purple-200"> {/* Adjusted bottom position */}
            <div className="flex items-center text-sm text-purple-700">
              <Check size={16} className="mr-2 flex-shrink-0" />
              <span>已选择 1 个素材</span>
            </div>
            <button
              onClick={() => setSelectedAssetId(null)}
              className="text-purple-600 text-sm font-medium hover:underline"
            >
              取消选择
            </button>
          </div>
        )}

        {/* 创建素材悬浮按钮 (FAB) */}
        {/* FIX 1: Changed to `absolute` positioning within the `relative` parent */}
        <div className="absolute bottom-6 right-6 z-30 fab-container"> {/* Adjusted position if needed */}
           <button
             onClick={toggleFABOptions}
             className={`w-14 h-14 bg-gradient-to-br from-purple-600 to-indigo-600 text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105 ${showFABOptions ? 'rotate-45' : ''} ${selectedAssetId ? 'ring-4 ring-purple-300 ring-offset-1' : ''}`} // Enhanced styles
             aria-haspopup="true" // Accessibility
             aria-expanded={showFABOptions} // Accessibility
             aria-label={showFABOptions ? "关闭创建选项" : "打开创建选项"} // Accessibility
           >
             <Plus size={28} className={`transition-transform duration-300 ${showFABOptions ? 'rotate-90 scale-75' : ''}`} /> {/* Added icon transition */}
           </button>

           {/* FAB选项 - Positioned relative to the FAB container */}
           {showFABOptions && (
             <div className="absolute bottom-16 right-0 mb-2 flex flex-col items-end space-y-2"> {/* Aligned items end */}
               {selectedAssetId && (
                 <div className="bg-white px-3 py-1.5 rounded-lg shadow-md text-xs text-purple-700 mb-1 w-auto whitespace-nowrap"> {/* Auto width */}
                   使用已选素材作模板
                 </div>
               )}
               <div className="space-y-2">
                  {/* Using role="menuitem" for better accessibility if this acts like a menu */}
                  <button onClick={() => handleCreateAsset('图片')} role="menuitem" className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                      <ImageIcon size={16} className="mr-2 text-blue-500"/> 创建图片
                  </button>
                   <button onClick={() => handleCreateAsset('视频')} role="menuitem" className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                      <Video size={16} className="mr-2 text-red-500"/> 创建视频
                  </button>
                   <button onClick={() => handleCreateAsset('语音')} role="menuitem" className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                      <Mic size={16} className="mr-2 text-green-500"/> 创建语音
                  </button>
                    <button onClick={() => handleCreateAsset('音乐')} role="menuitem" className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                      <Music size={16} className="mr-2 text-orange-500"/> 创建音乐
                  </button>
                  <button onClick={() => handleCreateAsset('本地上传')} role="menuitem" className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        本地上传
                  </button>
                  
               </div>
             </div>
           )}
        </div>

        {/* Placeholder Image Style & Scrollbar Hiding */}
        <style jsx>{`
          .image-placeholder {
            background: linear-gradient(45deg, #f0f0f0 25%, #e9e9e9 25%, #e9e9e9 50%, #f0f0f0 50%, #f0f0f0 75%, #e9e9e9 75%, #e9e9e9 100%);
            background-size: 28.28px 28.28px; /* Approx 20px * sqrt(2) */
          }
          .hide-scrollbar::-webkit-scrollbar {
              display: none; /* Safari and Chrome */
          }
          .hide-scrollbar {
              -ms-overflow-style: none;  /* IE and Edge */
              scrollbar-width: none;  /* Firefox */
          }
        `}</style>
      </div>
    </div>
  );
};

export default AssetLibraryPage;