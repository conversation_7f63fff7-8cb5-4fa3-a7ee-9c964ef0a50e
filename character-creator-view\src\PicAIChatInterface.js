import React, { useState, useRef, useEffect } from 'react';
import { ChevronLeft, Send, Mic, Camera, Smile, MoreHorizontal, ChevronDown, 
  Palette, Image as ImageIcon, Share, Download, X, Zap, Star, Clock, Check, User, 
  Sparkles, MessageCircle, Maximize, ArrowLeft, ArrowRight,ThumbsUp } from 'lucide-react';

const PicAIChatInterface = () => {
  const messagesEndRef = useRef(null);
  const [inputValue, setInputValue] = useState('');
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [imageLoadingProgress, setImageLoadingProgress] = useState(0);
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [previewedImage, setPreviewedImage] = useState(null);
  
  // Sample conversation history
  const [messages, setMessages] = useState([
    { sender: 'ai', content: '今天天气真好，适合出去走走。你有什么计划吗？', time: '14:32' },
    { sender: 'user', content: '我在想要不要去海边散散步，吹吹海风。', time: '14:33' },
    { 
      sender: 'ai', 
      content: '海边漫步是个很棒的选择！我能想象出你走在金色的沙滩上，海浪轻轻拍打岸边，远处夕阳西下，整个海面染上了金色和橙色，海鸥在天空中盘旋，非常惬意。', 
      time: '14:33',
      offerPainting: true,
      offerText: '我可以用次元画笔将这个美丽的海边夕阳场景画出来，要看看吗？',
      isOfferPending: true
    },
  ]);
  
  // Auto scroll to bottom when new messages come in
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  // Simulate image generation progress
  useEffect(() => {
    if (isGeneratingImage) {
      const interval = setInterval(() => {
        setImageLoadingProgress(prev => {
          const newProgress = prev + 10;
          if (newProgress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
              // Add the image to the conversation
              setMessages(prev => {
                const newMessages = [...prev];
                // Find the message that's currently generating and update it
                for (let i = newMessages.length - 1; i >= 0; i--) {
                  if (newMessages[i].isGenerating) {
                    newMessages[i] = {
                      ...newMessages[i],
                      isGenerating: false,
                      isOfferPending: false,
                      isOfferAccepted: true,
                      image: {
                        id: 'generated-image-' + Date.now(),
                        url: createPlaceholderImage('sunset-beach')
                      }
                    };
                    break;
                  }
                }
                return newMessages;
              });
              setIsGeneratingImage(false);
              setImageLoadingProgress(0);
            }, 500);
            return 100;
          }
          return newProgress;
        });
      }, 100); // Faster animation for demo purposes
      
      return () => clearInterval(interval);
    }
  }, [isGeneratingImage]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  // Create a placeholder image with CSS gradient based on scene type
  const createPlaceholderImage = (sceneType) => {
    // In a real app, this would be a real image URL
    // For this demo, we're using different gradients as placeholders
    if (sceneType === 'sunset-beach') {
      return 'sunset-beach-gradient';
    } else if (sceneType === 'mountain') {
      return 'mountain-gradient';
    } else {
      return 'default-gradient';
    }
  };
  
  const handleSend = () => {
    if (inputValue.trim() === '') return;
    
    const newMessage = {
      sender: 'user',
      content: inputValue,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    };
    
    setMessages([...messages, newMessage]);
    setInputValue('');
    
    // Simulate AI response (in real app, this would be an API call)
    setTimeout(() => {
      const aiResponse = {
        sender: 'ai',
        content: '我想象着你站在海边，迎着海风，感受大自然的宁静与壮美。海浪有节奏地拍打着岸边，天空中飘着几朵悠闲的白云，海鸥不时掠过海面。如果你愿意，我可以将这个画面永久地记录下来。',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
        offerPainting: true,
        offerText: '让我用次元画笔为你绘制这幅你在海边的场景吧？',
        isOfferPending: true
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1500);
  };
  
  // Handle input changes
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };
  
  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };
  
  // Handle accepting painting offer
  const handleAcceptOffer = (messageIndex) => {
    // Update the message to show it's accepted but image not yet generated
    setMessages(prevMessages => {
      const newMessages = [...prevMessages];
      newMessages[messageIndex] = {
        ...newMessages[messageIndex],
        isOfferPending: false,
        isGenerating: true
      };
      return newMessages;
    });
    
    // Start generating the image
    setIsGeneratingImage(true);
  };
  
  // Handle declining painting offer
  const handleDeclineOffer = (messageIndex) => {
    setMessages(prevMessages => {
      const newMessages = [...prevMessages];
      newMessages[messageIndex] = {
        ...newMessages[messageIndex],
        isOfferPending: false,
        offerDeclined: true
      };
      return newMessages;
    });
  };
  
  // Handle clicking on an image to preview it
  const handleImageClick = (image) => {
    setPreviewedImage(image);
    setShowImagePreview(true);
  };
  
  // Handle sharing the image
  const handleShareImage = () => {
    alert('分享功能已触发');
    // In a real app, this would open the native share dialog
  };
  
  // Handle downloading the image
  const handleDownloadImage = () => {
    alert('下载功能已触发');
    // In a real app, this would download the image
  };
  
  // Handle setting the image as chat background
  const handleSetAsBackground = () => {
    alert('已设置为聊天背景');
    // In a real app, this would set the image as the chat background
    setShowImagePreview(false);
  };
  
  return (
    <div className="flex flex-col max-w-md w-full mx-auto bg-gray-50 h-screen overflow-hidden relative">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-4 flex items-center shadow-md z-10">
        <button className="p-1 rounded-full">
          <ChevronLeft size={24} />
        </button>
        <div className="flex flex-1 items-center mx-3">
          <div className="relative">
            <div className="h-10 w-10 rounded-full bg-purple-400 flex items-center justify-center mr-3">
              <span className="text-white text-lg font-bold">K</span>
            </div>
            <div className="absolute -right-0.5 -bottom-0.5 bg-green-500 rounded-full h-3 w-3 border-2 border-white"></div>
          </div>
          <div>
            <h1 className="font-semibold text-lg">赛博侦探 K</h1>
            <div className="flex items-center text-xs text-white text-opacity-70">
              <div className="flex items-center mr-3">
                <Clock size={10} className="mr-1" />
                <span>刚刚在线</span>
              </div>
              <div className="flex items-center">
                <Star size={10} className="mr-1" fill="currentColor" />
                <span>Lv.5</span>
              </div>
            </div>
          </div>
        </div>
        <button className="p-1 rounded-full">
          <MoreHorizontal size={24} />
        </button>
      </div>
      
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 bg-gray-100 space-y-3">
        {messages.map((message, index) => (
          <div key={index} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-[80%] ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>
              {message.sender === 'ai' && (
                <div className="h-8 w-8 rounded-full bg-purple-400 flex items-center justify-center mb-1 mr-2">
                  <span className="text-white text-sm font-bold">K</span>
                </div>
              )}
              <div className={`p-3 rounded-lg ${
                message.sender === 'user' 
                  ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white' 
                  : 'bg-white text-gray-800'
              } shadow-sm relative`}>
                <p className="text-sm">{message.content}</p>
                
                {/* Display generated image if available */}
                {message.image && (
                  <div className="mt-3 space-y-2">
                    <div 
                      className="rounded-lg overflow-hidden cursor-pointer relative"
                      onClick={() => handleImageClick(message.image)}
                    >
                      {/* Placeholder for the image using CSS gradient */}
                      <div 
                        className={`w-full h-48 ${
                          message.image.url === 'sunset-beach-gradient' 
                            ? 'bg-gradient-to-b from-amber-400 via-orange-500 to-indigo-800' 
                            : message.image.url === 'mountain-gradient'
                              ? 'bg-gradient-to-b from-blue-400 via-purple-500 to-indigo-800'
                              : 'bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-500'
                        } flex items-center justify-center relative`}
                      >
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Maximize size={24} className="text-white text-opacity-80" />
                        </div>
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full">
                          次元画笔作品
                        </div>
                      </div>
                    </div>
                    
                    {/* Image action buttons */}
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => alert('已点赞！')}
                        className="flex-1 py-2 px-3 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-700 text-sm font-medium flex items-center justify-center transition"
                      >
                        <ThumbsUp size={14} className="mr-1" />
                        点赞
                      </button>
                      <button 
                        onClick={() => alert('跳转到次元画笔页面')}
                        className="flex-1 py-2 px-3 bg-indigo-50 hover:bg-indigo-100 text-indigo-600 rounded-lg text-sm font-medium flex items-center justify-center transition"
                      >
                        <Palette size={14} className="mr-1" />
                        查看次元画笔
                      </button>
                    </div>
                  </div>
                )}
                
                <div className="text-right mt-1">
                  <span className={`text-xs ${message.sender === 'user' ? 'text-white text-opacity-70' : 'text-gray-500'}`}>
                    {message.time}
                  </span>
                </div>
              </div>
              
              {/* Painting offer with action buttons */}
              {message.offerPainting && message.isOfferPending && (
                <div className="mt-2 bg-white p-2 rounded-lg shadow-md border border-indigo-100 animate-fadeIn">
                  <p className="text-sm text-gray-600 mb-2">{message.offerText}</p>
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => handleAcceptOffer(index)}
                      className="flex-1 py-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-sm rounded-lg flex items-center justify-center"
                    >
                      <Palette size={14} className="mr-1" />
                      召唤次元画笔
                    </button>
                    <button 
                      onClick={() => handleDeclineOffer(index)}
                      className="flex-1 py-2 bg-gray-100 text-gray-600 text-sm rounded-lg"
                    >
                      暂不需要
                    </button>
                  </div>
                  <p className="text-xs text-gray-400 mt-1 text-center">💡 会先观看视频广告</p>
                </div>
              )}
              
              {/* Image generation in progress indicator */}
              {message.isGenerating && (
                <div className="mt-2 bg-white p-3 rounded-lg shadow-md border border-indigo-100">
                  <div className="flex items-center mb-2">
                    <div className="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center mr-2 animate-pulse">
                      <Palette size={14} className="text-indigo-600" />
                    </div>
                    <p className="text-sm font-medium text-gray-700">次元画笔绘制中...</p>
                  </div>
                  <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 transition-all duration-300"
                      style={{ width: `${imageLoadingProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
              
              {/* Declined offer message */}
              {message.offerDeclined && (
                <div className="mt-1 text-xs text-gray-500 text-center">
                  已取消绘制，在角色主页的“养成互动”里可使用“次元画笔”技能
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Image Preview Modal */}
      {showImagePreview && previewedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex flex-col z-50 animate-fadeIn">
          <div className="p-4 flex items-center text-white">
            <button 
              onClick={() => setShowImagePreview(false)}
              className="p-2"
            >
              <X size={24} />
            </button>
            <h2 className="text-lg font-medium ml-2">次元画笔作品</h2>
          </div>
          
          <div className="flex-1 flex items-center justify-center p-4">
            <div 
              className={`w-full max-w-md h-80 ${
                previewedImage.url === 'sunset-beach-gradient' 
                  ? 'bg-gradient-to-b from-amber-400 via-orange-500 to-indigo-800' 
                  : previewedImage.url === 'mountain-gradient'
                    ? 'bg-gradient-to-b from-blue-400 via-purple-500 to-indigo-800'
                    : 'bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-500'
              } rounded-lg`}
            ></div>
          </div>
          
          <div className="p-4 bg-black bg-opacity-30">
            <div className="flex justify-around">
              <button 
                onClick={handleShareImage}
                className="flex flex-col items-center"
              >
                <div className="w-12 h-12 rounded-full bg-white bg-opacity-10 flex items-center justify-center mb-1">
                  <Share size={20} className="text-white" />
                </div>
                <span className="text-xs text-white">分享</span>
              </button>
              
              <button 
                onClick={handleDownloadImage}
                className="flex flex-col items-center"
              >
                <div className="w-12 h-12 rounded-full bg-white bg-opacity-10 flex items-center justify-center mb-1">
                  <Download size={20} className="text-white" />
                </div>
                <span className="text-xs text-white">保存</span>
              </button>
              
              <button 
                onClick={handleSetAsBackground}
                className="flex flex-col items-center"
              >
                <div className="w-12 h-12 rounded-full bg-white bg-opacity-10 flex items-center justify-center mb-1">
                  <MessageCircle size={20} className="text-white" />
                </div>
                <span className="text-xs text-white">设为背景</span>
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Input Area */}
      <div className="p-2 bg-white border-t border-gray-200 z-10">
        <div className="flex items-end">
          {/* Quick access to Dimension Brush */}
          {messages.some(m => m.offerDeclined) && (
            <button className="p-2 mr-1 text-indigo-600">
              <Palette size={22} />
            </button>
          )}
          
          <div className="flex space-x-1 text-gray-500 px-1">
            <button className="p-1">
              <Smile size={22} />
            </button>
            <button className="p-1">
              <Camera size={22} />
            </button>
          </div>
          <div className="flex-1 border border-gray-300 rounded-full bg-gray-100 px-4 py-2 focus-within:ring-1 focus-within:ring-indigo-500 focus-within:border-indigo-500">
            <textarea
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="说点什么..."
              className="w-full bg-transparent outline-none resize-none max-h-32 text-sm"
              rows={1}
            />
          </div>
          <div className="ml-2">
            {inputValue.trim() ? (
              <button 
                className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white flex items-center justify-center"
                onClick={handleSend}
              >
                <Send size={18} />
              </button>
            ) : (
              <button className="w-10 h-10 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center">
                <Mic size={18} />
              </button>
            )}
          </div>
        </div>
      </div>
      
      {/* Add keyframes for animation */}
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
        .animate-pulse {
          animation: pulse 1.5s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default PicAIChatInterface;