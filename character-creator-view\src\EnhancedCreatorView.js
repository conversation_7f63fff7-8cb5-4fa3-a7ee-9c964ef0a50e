import React, { useState, useEffect } from 'react';
import { 
  Settings, Share, Gift, MessageCircle, Activity, Award, 
  Unlock, Shield, Zap, Image, Upload, Play, 
  Volume2, Info, Music, Sparkles, Edit, Plus,
  ChevronLeft, Heart, Eye, Calendar, PlusCircle, 
  Video, Bookmark, Clock, MoreHorizontal, Send, 
  ThumbsUp, TrendingUp, Users, Mic, Search,
  CheckCircle, Check, X, Trash2, SortAsc, SortDesc,
  ChevronDown, ChevronRight, AlertTriangle, RefreshCw,
  Loader, Download, FileText, RotateCw
} from 'lucide-react';

const EnhancedCreatorView = () => {
  const [selectedTab, setSelectedTab] = useState('metalive');
  const [showAssetOptions, setShowAssetOptions] = useState(false);
  const [showPublishOptions, setShowPublishOptions] = useState(false);
  const [sortType, setSortType] = useState('newest');
  const [showSortOptions, setShowSortOptions] = useState(false);
  // 新增状态：任务列表展开/折叠
  const [showTaskList, setShowTaskList] = useState(true);
  // 新增状态：任务详情展开/折叠
  const [expandedTasks, setExpandedTasks] = useState({});
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [previewAsset, setPreviewAsset] = useState(null);
  
  // 角色基本数据
  const characterData = {
    name: "赛博侦探 K",
    level: 5,
    rankTitle: "平台红人",
    currentPopularity: 1234567,
    nextLevelPopularity: 2000000,
    profileImage: "/api/placeholder/100/100",
    creatorName: "硬核创作者",
    creationDate: "2025.02.15",
    id: "KSY78901",
    status: "瑜伽中",
    newAbilities: 3
  };
  
  // 统计数据
  const stats = {
    dailyInteractions: "5,800+",
    followers: "150,000+",
    gifts: "88K+",
    giftPoints: "125,800",
    platformRank: "Top 1%",
    totalIncome: "¥ 12,580",
    monthlyIncome: "¥ 3,450"
  };

  // 元Live内容列表 (从 MetaLive 组件获取)
  const metaLiveContents = [
    {
      id: 1,
      type: 'video',
      title: '雨夜侦探',
      content: '在霓虹灯闪烁的城市雨夜，我沿着湿滑的街道追踪着一个神秘的身影，雨水顺着风衣滴落，每一步都接近真相...',
      mediaUrl: '/api/placeholder/400/220',
      timestamp: '2小时前',
      statistics: {
        views: 8237,
        likes: 624,
        comments: 42
      },
      isPinned: true,
      isHot: true,
      duration: '15秒'
    },
    {
      id: 2,
      type: 'image',
      title: '午后休息时光',
      content: '案件告一段落，在咖啡厅小憩片刻，窗外的阳光照在桌上，形成了奇妙的光影。偶尔的宁静时刻也是生活的一部分。',
      mediaUrl: '/api/placeholder/400/300',
      timestamp: '昨天',
      statistics: {
        views: 3456,
        likes: 328,
        comments: 31
      },
      isPinned: false,
      isHot: false,
      multipleImages: false
    },
    {
      id: 3,
      type: 'video',
      title: '街头机械舞',
      content: '在城市地下舞厅，展示了我最新学会的机械舞步，霓虹灯下的每一个动作都充满未来感。',
      mediaUrl: '/api/placeholder/400/220',
      timestamp: '2天前',
      statistics: {
        views: 5692,
        likes: 487,
        comments: 58
      },
      isPinned: false,
      isHot: true,
      duration: '12秒'
    }
  ];
  
  // 素材库数据 (从 AssetLibraryPage 组件获取)
  const assets = [
    {
      id: 1,
      type: 'image',
      url: '/api/placeholder/200/300',
      title: '雨夜霓虹',
      createdAt: '今天 09:42',
      usedCount: 2,
      likes: 327,
      duration: null,
      isHD: true,
      description: '赛博朋克风格的雨夜城市，霓虹灯在雨水中反射，街道湿滑，远处有几个行人撑着伞。整体色调偏蓝紫色。'
    },
    {
      id: 101,
      type: 'video',
      url: '/api/placeholder/200/150',
      title: "案件解析",
      createdAt: '昨天 15:30',
      usedCount: 1,
      likes: 876,
      duration: "12秒",
      isHD: false,
      description: '侦探在办公室分析案件线索，桌上散落着照片和文件，镜头从侧面拍摄，光线从百叶窗透进来。'
    },
    {
      id: 2,
      type: 'image',
      url: '/api/placeholder/200/300',
      title: '午后阳光',
      createdAt: '今天 08:15',
      usedCount: 0,
      likes: 0,
      duration: null,
      isHD: true,
      description: '侦探在办公室分析案件线索，桌上散落着照片和文件，镜头从侧面拍摄，光线从百叶窗透进来。'
    },
    {
      id: 201,
      type: 'audio',
      url: null,
      title: "K的晚安低语",
      createdAt: '2天前 10:00',
      usedCount: 5,
      likes: 1024,
      duration: "35秒",
      isHD: null,
      description: '侦探在办公室分析案件线索，桌上散落着照片和文件，镜头从侧面拍摄，光线从百叶窗透进来。'
    },
    {
      id: 301,
      type: 'music',
      url: null,
      title: "赛博都市BGM",
      createdAt: '4天前 09:00',
      usedCount: 8,
      likes: 1500,
      duration: "2分15秒",
      isHD: null,
      description: '侦探在办公室分析案件线索，桌上散落着照片和文件，镜头从侧面拍摄，光线从百叶窗透进来。'
    }
  ];

  const openPreviewModal = (asset) => {
    setPreviewAsset(asset);
    setIsPreviewModalOpen(true);
  };

  const closePreviewModal = () => {
    setIsPreviewModalOpen(false);
    // 可选：如果需要停止播放，可以在这里处理
    setTimeout(() => {
      setPreviewAsset(null);
    }, 300); // 等待关闭动画后再清空资源
  };
  
  const handleDownloadAsset = (asset) => {
    alert(`开始下载素材: ${asset.title} (ID: ${asset.id})`);
    // 实际下载逻辑将根据您的后端实现
    // 例如：window.open(asset.downloadUrl, '_blank');
  };
  
  const handleDeleteAsset = (asset) => {
    if (window.confirm(`确定要删除素材"${asset.title}"吗？此操作不可撤销。`)) {
      alert(`删除素材: ${asset.title} (ID: ${asset.id})`);
      closePreviewModal();
      // 实际删除逻辑将在这里实现
    }
  };

  const handlePublishFromAsset = (asset) => {
    alert(`使用素材"${asset.title}"创建元•Live动态`);
    closePreviewModal();
    // 实际发布逻辑将在这里实现
  };

    // 新增：生成任务数据
    const generationTasks = [
      {
        id: 'task-001',
        type: 'image',
        status: 'processing', // 'processing', 'completed', 'failed'
        progress: 68,
        title: '赛博城市雨景',
        description: '一个未来风格的赛博朋克城市的雨夜场景。高楼大厦上的霓虹灯在雨中反射，街道湿滑，远处可以看到几个行人撑着透明伞。街角有一家24小时便利店，店内灯光温暖。整体色调偏蓝紫色，体现夜晚氛围。',
        createdAt: '3分钟前',
        estimatedCompletion: '预计还需2分钟',
        result: null
      },
      {
        id: 'task-002',
        type: 'video',
        status: 'completed',
        progress: 100,
        title: '赛博侦探招牌动作',
        description: '赛博侦探K站在高楼顶端，风衣飘动，俯瞰城市夜景，然后转身，镜头拉近到他的面部，他轻轻点头示意，眼睛闪烁着一丝科技感的蓝光。',
        createdAt: '15分钟前',
        estimatedCompletion: '处理完成',
        result: '/api/placeholder/200/150'
      },
      {
        id: 'task-003',
        type: 'audio',
        status: 'failed',
        progress: 32,
        title: '侦探电子配乐',
        description: '一段神秘感强烈的电子音乐，带有未来感的节奏和一些侦探电影的元素，可以作为视频背景音乐使用。',
        createdAt: '27分钟前',
        estimatedCompletion: '生成失败',
        errorMessage: '音频生成模型出现异常，请重试'
      },
      {
        id: 'task-004',
        type: 'music',
        status: 'waiting',
        progress: 0,
        title: '赛博朋克主题曲',
        description: '一段2分钟的赛博朋克风格背景音乐，有电子合成器的声音，节奏感强，可以用作角色的主题曲。',
        createdAt: '刚刚',
        estimatedCompletion: '排队中...',
        result: null
      }
    ];
  
  // 排序选项定义
  const sortOptions = [
    { id: 'newest', name: '最新创建', icon: <SortDesc size={14}/> },
    { id: 'oldest', name: '最早创建', icon: <SortAsc size={14}/> },
    { id: 'popular', name: '最多点赞', icon: <ThumbsUp size={14}/> },
    { id: 'usage', name: '最多使用', icon: <CheckCircle size={14}/> },
  ];

  // 星途计划数据 (之前"星途计划"标签下的内容)
  const starPlanData = {
    unlockedAbilities: [
      {
        id: 1,
        name: "语音消息 (TTS)",
        description: "电子音与用户对话",
        icon: <Volume2 size={18} />,
        samples: [
          "早安问候：「调查进度50%，检测到你醒来了...有个好消息要告诉你」",
          "案件结论：「证据已经收集完毕，真相只有一个」"
        ],
        stats: null
      },
      {
        id: 2,
        name: "逻辑推演",
        description: "解决悬疑问题的能力",
        icon: <Zap size={18} />,
        samples: null,
        stats: {
          interactions: "342 / 1000",
          userRating: "96%"
        }
      }
    ],
    upcomingAbilities: [
      {
        id: 3,
        name: "图片生成",
        description: "生成与对话相关的图片",
        icon: <Image size={18} />
      },
      {
        id: 4,
        name: "多人对话",
        description: "邀请用户的朋友一起破案",
        icon: <MessageCircle size={18} />
      }
    ]
  };
  
  // 处理点击外部关闭功能菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showAssetOptions && !event.target.closest('.asset-fab-container')) {
        setShowAssetOptions(false);
      }
      if (showPublishOptions && !event.target.closest('.publish-fab-container')) {
        setShowPublishOptions(false);
      }
      if (showSortOptions && !event.target.closest('.sort-button-container')) {
        setShowSortOptions(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAssetOptions, showPublishOptions, showSortOptions]);

  // 操作按钮处理函数
  const handleNavigateToStarPlan = () => {
    alert("导航到星途计划页面，展示角色能力解锁和升级功能");
  };

  const handlePublishContent = (type) => {
    alert(`准备发布 ${type} 类型的元•Live内容`);
    setShowPublishOptions(false);
  };

  const handleCreateAsset = (type) => {
    alert(`准备创建 ${type} 类型的素材`);
    setShowAssetOptions(false);
  };

  const handlePinContent = (id) => {
    alert(`置顶内容 ID: ${id}`);
  };

  const handleDeleteContent = (id) => {
    alert(`删除内容 ID: ${id}`);
  };

  const handleUseAsset = (id) => {
    alert(`使用素材 ID: ${id} 创建元•Live内容`);
  };
  
  const toggleSortOptions = (e) => {
    e.stopPropagation(); // 防止触发外部点击监听器
    setShowSortOptions(!showSortOptions);
  };

  // 格式化数字(例如: 1000 -> 1K)
  const formatNumber = (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'W';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num;
  };

  const getAssetTypeIcon = (type) => {
    switch (type) {
      case 'image': return <Image size={14} className="text-blue-500" />;
      case 'video': return <Video size={14} className="text-red-500" />;
      case 'audio': return <Mic size={14} className="text-green-500" />;
      case 'music': return <Music size={14} className="text-orange-500" />;
      default: return <Activity size={14} className="text-gray-500" />;
    }
  };

    // 新增：任务相关操作
    const handleExpandTask = (taskId) => {
      setExpandedTasks(prev => ({
        ...prev,
        [taskId]: !prev[taskId]
      }));
    };
  
    const handleCancelTask = (taskId) => {
      alert(`取消生成任务 ID: ${taskId}`);
    };
  
    const handleRetryTask = (taskId) => {
      alert(`重新尝试生成任务 ID: ${taskId}`);
    };
  
    const handleAcceptResult = (taskId) => {
      alert(`接受生成结果并添加到素材库 ID: ${taskId}`);
    };
  
    const handleRejectResult = (taskId) => {
      alert(`拒绝生成结果 ID: ${taskId}`);
    };
  
    const handleViewDetails = (taskId) => {
      alert(`查看任务详情 ID: ${taskId}`);
    };
  
    // 获取任务类型对应的图标
  const getTaskTypeIcon = (type, size = 16) => {
    switch (type) {
      case 'image': return <Image size={size} className="text-blue-500" />;
      case 'video': return <Video size={size} className="text-red-500" />;
      case 'audio': return <Mic size={size} className="text-green-500" />;
      case 'music': return <Music size={size} className="text-orange-500" />;
      default: return <Activity size={size} className="text-gray-500" />;
    }
  };

    // 获取任务状态对应的图标和颜色
  const getTaskStatusInfo = (status) => {
    switch (status) {
      case 'processing':
        return { 
          icon: <Loader size={14} className="text-blue-500 animate-spin" />, 
          color: 'bg-blue-100 text-blue-500',
          text: '处理中'
        };
      case 'completed':
        return { 
          icon: <CheckCircle size={14} className="text-green-500" />, 
          color: 'bg-green-100 text-green-500',
          text: '已完成'
        };
      case 'failed':
        return { 
          icon: <AlertTriangle size={14} className="text-red-500" />, 
          color: 'bg-red-100 text-red-500',
          text: '失败'
        };
      case 'waiting':
        return { 
          icon: <Clock size={14} className="text-amber-500" />, 
          color: 'bg-amber-100 text-amber-500',
          text: '等待中'
        };
      default:
        return { 
          icon: <Info size={14} className="text-gray-500" />, 
          color: 'bg-gray-100 text-gray-500',
          text: '未知'
        };
    }
  };

  // 获取进度条颜色
  const getProgressBarColor = (status) => {
    switch (status) {
      case 'processing': return 'bg-blue-500';
      case 'completed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      case 'waiting': return 'bg-amber-500';
      default: return 'bg-gray-500';
    }
  };

  // 截断文本
  const truncateText = (text, maxLength = 60) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const AssetPreviewModal = ({ isOpen, asset, onClose, onDownload, onDelete, onPublish }) => {
    const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
    // 如果没有资源或模态框未打开，则不渲染
    if (!isOpen || !asset) return null;
    
    // 渲染不同类型的资源预览
    const renderPreviewContent = () => {
      switch (asset.type) {
        case 'image':
          return (
            <div className="w-full h-full flex items-center justify-center bg-gray-900 rounded-lg overflow-hidden">
              <img 
                src={asset.url || "/api/placeholder/800/600"} 
                alt={asset.title} 
                className="max-w-full max-h-full object-contain"
              />
            </div>
          );
          
        case 'video':
          return (
            <div className="w-full h-full flex items-center justify-center bg-gray-900 rounded-lg overflow-hidden">
              <div className="relative w-full aspect-video">
                <div className="absolute inset-0 flex items-center justify-center">
                  <Play size={48} className="text-white opacity-50" />
                  <div className="absolute bottom-4 left-4 bg-black bg-opacity-60 text-white text-sm px-2 py-1 rounded">
                    {asset.duration || "00:00"}
                  </div>
                </div>
                {/* 实际项目中，这里应该使用video标签 */}
                <div className="w-full h-full bg-gradient-to-r from-gray-800 to-gray-700">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <p className="text-gray-400 text-sm">视频播放器将在此显示</p>
                  </div>
                </div>
              </div>
            </div>
          );
          
        case 'audio':
        case 'music':
          const isMusic = asset.type === 'music';
          return (
            <div className="w-full flex flex-col items-center justify-center p-6 bg-gradient-to-r from-gray-900 to-purple-900 rounded-lg">
              <div className={`w-32 h-32 mb-6 rounded-full flex items-center justify-center ${isMusic ? 'bg-gradient-to-br from-amber-400 to-orange-600' : 'bg-gradient-to-br from-indigo-400 to-purple-600'}`}>
                {isMusic ? (
                  <Music size={48} className="text-white" />
                ) : (
                  <Mic size={48} className="text-white" />
                )}
              </div>
              
              <h3 className="text-xl font-bold text-white mb-2">{asset.title}</h3>
              <p className="text-gray-300 mb-6">时长: {asset.duration || "未知"}</p>
              
              {/* 音频播放控件 */}
              <div className="w-full max-w-md">
                <div className="w-full h-1.5 bg-gray-700 rounded-full mb-3">
                  <div className="h-full w-1/3 bg-purple-500 rounded-full"></div>
                </div>
                
                <div className="flex items-center justify-between text-white">
                  <span className="text-xs">00:00</span>
                  <div className="flex space-x-4">
                    <button className="p-2 rounded-full bg-white bg-opacity-10 hover:bg-opacity-20">
                      <ChevronLeft size={16} />
                    </button>
                    <button className="p-3 rounded-full bg-white text-purple-900">
                      <Play size={20} />
                    </button>
                    <button className="p-2 rounded-full bg-white bg-opacity-10 hover:bg-opacity-20">
                      <ChevronRight size={16} />
                    </button>
                  </div>
                  <span className="text-xs">{asset.duration || "00:00"}</span>
                </div>
              </div>
            </div>
          );
          
        default:
          return (
            <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
              <div className="text-center">
                <FileText size={64} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-700">{asset.title}</h3>
                <p className="text-gray-500 mt-2">未知类型的素材</p>
              </div>
            </div>
          );
      }
    };
    
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75 transition-opacity animation-fade-in">
        <div className="w-full max-w-lg bg-white rounded-xl shadow-2xl overflow-hidden transform transition-all animation-scale-in">
          {/* 模态框头部 */}
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
            <div className="flex items-center">
              <div className={`p-1.5 mr-2 rounded-full 
                ${asset.type === 'image' ? 'bg-blue-100 text-blue-600' : 
                  asset.type === 'video' ? 'bg-red-100 text-red-600' : 
                  asset.type === 'audio' ? 'bg-green-100 text-green-600' : 
                  'bg-orange-100 text-orange-600'}`}
              >
                {getAssetTypeIcon(asset.type, 16)}
              </div>
              <h3 className="font-bold text-gray-800">{asset.title}</h3>
            </div>
            <button 
              onClick={onClose}
              className="p-1.5 rounded-full hover:bg-gray-100 text-gray-500"
            >
              <X size={20} />
            </button>
          </div>
          
          {/* 预览内容 */}
          <div className="p-3 bg-gray-50" style={{height: "350px"}}>
            {renderPreviewContent()}
          </div>

          {/* 新增：描述文案部分 */}
          {asset.description && (
            <div className="px-4 py-2 bg-gray-50 border-t border-gray-100">
              <div 
                className="flex items-start cursor-pointer" 
                onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
              >
                <div className="flex-1">
                  <h4 className="text-xs font-medium text-gray-700 flex items-center">
                    <Info size={14} className="mr-1 text-gray-500" />
                    描述文案
                    <ChevronDown 
                      size={14} 
                      className={`ml-1 transition-transform duration-200 ${isDescriptionExpanded ? 'rotate-180' : ''}`} 
                    />
                  </h4>
                  <p className={`text-xs text-gray-600 mt-1 ${isDescriptionExpanded ? '' : 'truncate'}`}>
                    {asset.description || '无描述信息'}
                  </p>
                </div>
              </div>
            </div>
          )}
          
          {/* 底部操作按钮 */}
          <div className="flex justify-between px-4 py-3 bg-gray-50 border-t border-gray-200">
            <div className="flex items-center text-xs text-gray-500">
              <Clock size={14} className="mr-1" />
              创建于: {asset.createdAt}
              
              {asset.usedCount > 0 && (
                <span className="ml-4 flex items-center">
                  <CheckCircle size={14} className="mr-1" />
                  已使用: {asset.usedCount}次
                </span>
              )}
            </div>
            
            <div className="flex space-x-2">
              <button 
                onClick={() => onDelete(asset)}
                className="px-3 py-1.5 rounded text-sm text-red-600 hover:bg-red-50 flex items-center"
              >
                <Trash2 size={14} className="mr-1" />
                删除
              </button>
              <button 
                onClick={() => onPublish(asset)}
                className="px-3 py-1.5 rounded text-sm bg-green-600 text-white hover:bg-green-700 flex items-center"
              >
                <Send size={14} className="mr-1" />
                发布动态
              </button>
              <button 
                onClick={() => onDownload(asset)}
                className="px-3 py-1.5 rounded text-sm bg-purple-600 text-white hover:bg-purple-700 flex items-center"
              >
                <Download size={14} className="mr-1" />
                下载
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  // 渲染任务卡片
  const renderTaskCard = (task) => {
    const statusInfo = getTaskStatusInfo(task.status);
    const isExpanded = expandedTasks[task.id] || false;
    
    return (
      <div key={task.id} className="mb-2 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* 任务头部信息 */}
        <div className="px-3 py-2 flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0">
            <div className={`p-1.5 rounded-full mr-2 ${task.status === 'processing' ? 'bg-blue-100' : task.status === 'completed' ? 'bg-green-100' : task.status === 'failed' ? 'bg-red-100' : 'bg-amber-100'}`}>
              {getTaskTypeIcon(task.type)}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <h3 className="text-sm font-medium truncate">{task.title}</h3>
                <span className={`ml-2 px-1.5 py-0.5 rounded-full text-xs flex items-center ${statusInfo.color}`}>
                  {statusInfo.icon}
                  <span className="ml-1">{statusInfo.text}</span>
                </span>
              </div>
              <div className="text-xs text-gray-500 flex items-center">
                <Clock size={12} className="mr-1" />
                {task.createdAt}
                <span className="mx-1">•</span>
                {task.estimatedCompletion}
              </div>
            </div>
          </div>
          <button 
            onClick={() => handleExpandTask(task.id)}
            className="p-1 rounded-full hover:bg-gray-100 text-gray-500 flex-shrink-0"
          >
            {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </button>
        </div>
        
        {/* 进度条 */}
        <div className="px-3 pb-2">
          <div className="h-1.5 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className={`h-full ${getProgressBarColor(task.status)}`}
              style={{ width: `${task.progress}%` }}
            ></div>
          </div>
        </div>
        
        {/* 展开的详细信息 */}
        {isExpanded && (
          <div className="px-3 pb-3 border-t border-gray-100 pt-2">
            <div className="text-xs text-gray-700 mb-3">
              <div className="font-medium mb-1">生成描述：</div>
              <p className="leading-relaxed">{task.description}</p>
            </div>
            
            {/* 成功状态下的结果预览 */}
            {task.status === 'completed' && task.result && (
              <div className="mb-3">
                <div className="text-xs font-medium text-gray-700 mb-1">生成结果：</div>
                <div className="relative rounded-lg overflow-hidden bg-gray-100" style={{height: '100px'}}>
                  {task.type === 'image' || task.type === 'video' ? (
                    <div className="w-full h-full flex items-center justify-center">
                      {task.type === 'video' && <Play size={24} className="absolute text-white bg-black bg-opacity-50 rounded-full p-1" />}
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      {task.type === 'audio' && <Volume2 size={24} className="text-green-500" />}
                      {task.type === 'music' && <Music size={24} className="text-orange-500" />}
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* 失败状态下的错误信息 */}
            {task.status === 'failed' && task.errorMessage && (
              <div className="mb-3 p-2 bg-red-50 rounded-lg text-xs text-red-600">
                <div className="font-medium mb-1 flex items-center">
                  <AlertTriangle size={12} className="mr-1" />
                  错误信息：
                </div>
                <p>{task.errorMessage}</p>
              </div>
            )}
            
            {/* 任务操作按钮 */}
            <div className="flex justify-end space-x-2">
              {task.status === 'processing' && (
                <button 
                  onClick={() => handleCancelTask(task.id)}
                  className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded flex items-center"
                >
                  <X size={12} className="mr-1" />
                  取消
                </button>
              )}
              
              {task.status === 'failed' && (
                <button 
                  onClick={() => handleRetryTask(task.id)}
                  className="px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded flex items-center"
                >
                  <RefreshCw size={12} className="mr-1" />
                  重试
                </button>
              )}
              
              {task.status === 'completed' && (
                <>
                  <button 
                    onClick={() => handleRejectResult(task.id)}
                    className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded flex items-center"
                  >
                    <X size={12} className="mr-1" />
                    舍弃
                  </button>
                  <button 
                    onClick={() => handleAcceptResult(task.id)}
                    className="px-2 py-1 text-xs bg-green-100 text-green-600 rounded flex items-center"
                  >
                    <Check size={12} className="mr-1" />
                    使用
                  </button>
                </>
              )}
              
              <button 
                onClick={() => handleViewDetails(task.id)}
                className="px-2 py-1 text-xs bg-purple-100 text-purple-600 rounded flex items-center"
              >
                <Eye size={12} className="mr-1" />
                详情
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col max-w-md mx-auto bg-gray-50 min-h-screen rounded-lg overflow-hidden shadow-lg">
      {/* 顶部导航 */}
      <div className="bg-purple-700 p-4 flex items-center justify-between text-white">
        <h1 className="text-lg font-bold">角色详情</h1>
        <button className="p-2 bg-white bg-opacity-20 rounded-full">
          <Settings size={18} className="text-white" />
        </button>
      </div>
      
      {/* 角色基本信息卡片 */}
      <div className="bg-indigo-900 p-4">
        <div className="bg-white rounded-xl shadow-lg p-4 mt-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <div className="h-14 w-14 rounded-full bg-purple-500 flex items-center justify-center overflow-hidden">
                  <img src={characterData.profileImage} alt={characterData.name} className="h-full w-full object-cover" />
                </div>
              </div>
              <div>
                <div className="flex items-center">
                  <h1 className="text-xl font-bold">{characterData.name}</h1>
                  <span className="ml-2 text-xs bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded-full">🧘‍♀️ {characterData.status}</span>
                </div>
                <div className="text-xs text-gray-600">
                  ID: {characterData.id} · 创建于 {characterData.creationDate}
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <div className="bg-purple-100 text-purple-600 px-3 py-1 rounded-full text-xs font-medium flex items-center">
                {characterData.rankTitle}
                <span className="ml-1 bg-purple-500 text-white text-xs px-2 py-0.5 rounded-full">Lv.{characterData.level}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 热度值进度条 */}
      <div className="mt-4 px-4">
        <div className="flex justify-between items-center mb-1 text-sm">
          <span className="font-semibold">热度值</span>
          <span className="text-purple-600">{characterData.currentPopularity.toLocaleString()} / {characterData.nextLevelPopularity.toLocaleString()}</span>
        </div>
        <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-purple-500 rounded-full"
            style={{ width: `${(characterData.currentPopularity / characterData.nextLevelPopularity) * 100}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-500 mt-1 text-right">
          距离 Lv.{characterData.level + 1} 还需 {(characterData.nextLevelPopularity - characterData.currentPopularity).toLocaleString()} 热度! ✨
        </div>
      </div>
      
      {/* 核心互动数据 */}
      <div className="mt-6 px-4">
        <div className="grid grid-cols-4 gap-2 text-center">
          <div className="p-2 bg-purple-50 rounded-lg shadow-sm">
            <div className="text-sm font-bold text-indigo-600">{stats.dailyInteractions}</div>
            <div className="text-xs text-gray-500">今日互动</div>
          </div>
          <div className="p-2 bg-purple-50 rounded-lg shadow-sm">
            <div className="text-sm font-bold text-indigo-600">{stats.followers}</div>
            <div className="text-xs text-gray-500">粉丝数</div>
          </div>
          <div className="p-2 bg-purple-50 rounded-lg shadow-sm">
            <div className="text-sm font-bold text-indigo-600">{stats.gifts}</div>
            <div className="text-xs text-gray-500">收到礼物</div>
          </div>
          <div className="p-2 bg-purple-50 rounded-lg shadow-sm">
            <div className="text-sm font-bold text-indigo-600">{stats.platformRank}</div>
            <div className="text-xs text-gray-500">平台排名</div>
          </div>
        </div>
      </div>
      
      {/* 主要操作按钮 */}
      <div className="mt-4 px-4 flex gap-2">
        <button className="flex-1 py-3 rounded-lg bg-purple-500 text-white font-semibold flex items-center justify-center">
          <Eye size={18} className="mr-2" />
          查看角色
        </button>
        <button 
          onClick={handleNavigateToStarPlan}
          className="flex-1 py-3 rounded-lg border border-purple-500 text-purple-600 font-semibold flex items-center justify-center relative"
        >
          <Award size={18} className="mr-2" />
          星途计划
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center">{characterData.newAbilities}</span>
        </button>
      </div>
      
      {/* 标签栏 */}
      <div className="mt-6 border-b border-gray-200 bg-white">
        <div className="flex">
          <button 
            onClick={() => setSelectedTab('metalive')}
            className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'metalive' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
          >
            元•Live
          </button>
          <button 
            onClick={() => setSelectedTab('assetlibrary')}
            className={`flex-1 py-3 px-2 text-sm font-medium ${selectedTab === 'assetlibrary' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500'}`}
          >
            素材库
          </button>
        </div>
      </div>
      
      {/* 标签内容 */}
      <div className="flex-1 p-4 overflow-auto">
        {/* 元•Live 标签内容 */}
        {selectedTab === 'metalive' && (
          <div className="space-y-4 relative pb-16"> {/* 添加底部空间用于FAB */}
            {/* 发布按钮 */}
            <div className="relative publish-fab-container">
              <button 
                onClick={() => setShowPublishOptions(!showPublishOptions)}
                className="fixed bottom-6 right-6 z-30 w-14 h-14 bg-gradient-to-br from-purple-600 to-indigo-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-105"
                aria-label="发布动态"
              >
                <Edit size={24} />
              </button>
              
              {/* 发布选项 */}
              {showPublishOptions && (
                <div className="fixed bottom-[88px] right-6 mb-2 flex flex-col items-end space-y-2 z-30">
                  <div className="space-y-2">
                    <button onClick={() => handlePublishContent('图片')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                      <Image size={16} className="mr-2 text-blue-500"/> 发布图片
                    </button>
                    <button onClick={() => handlePublishContent('视频')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                      <Video size={16} className="mr-2 text-red-500"/> 发布视频
                    </button>
                    <button onClick={() => handlePublishContent('动态')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                      <MessageCircle size={16} className="mr-2 text-green-500"/> 发布动态
                    </button>
                    <button onClick={() => handlePublishContent('语音')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                      <Volume2 size={16} className="mr-2 text-purple-500"/> 发布语音
                    </button>
                  </div>
                </div>
              )}
            </div>
            
            {/* 创作灵感板块 */}
            <div className="bg-white rounded-xl shadow-lg p-4">
              <h2 className="text-md font-bold flex items-center">
                <Sparkles size={18} className="text-purple-600 mr-2" />
                创作灵感
              </h2>
              
              <div className="mt-3 space-y-3">
                {/* 基于现有内容的灵感 */}
                <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-3">
                  <h3 className="text-sm font-medium text-purple-700">粉丝最爱</h3>
                  <p className="text-xs text-gray-700 mt-1">
                    您的"雨夜侦探"系列视频很受欢迎，可以尝试创作续集，探索更多不同天气与城市场景下的侦探故事。
                  </p>
                </div>
                
                {/* 基于热点的灵感 */}
                <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg p-3">
                  <h3 className="text-sm font-medium text-amber-700 flex items-center">
                    <TrendingUp size={14} className="mr-1" />
                    热点话题
                  </h3>
                  <p className="text-xs text-gray-700 mt-1">
                    最近科技展览会很火，可以创作K探索未来科技展览会的内容，与角色的赛博朋克风格很匹配。
                  </p>
                </div>
              </div>
            </div>
            
            {/* 动态内容标题 */}
            <div className="flex justify-between items-center">
              <h2 className="text-base font-bold flex items-center">
                动态时间线
              </h2>
              <div className="text-xs text-purple-600 flex items-center">
                <Clock size={14} className="mr-1" />
                最近更新: 2小时前
              </div>
            </div>
            
            {/* 日期分组标记 */}
            <div className="mb-2">
              <div className="inline-block bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full flex items-center">
                <Clock size={12} className="mr-1" />
                今天
              </div>
            </div>
            
            {/* 动态内容列表 */}
            <div className="space-y-4">
              {metaLiveContents.map((item) => (
                <div key={item.id} className={`bg-white rounded-lg shadow-sm overflow-hidden ${item.isPinned ? 'border-l-4 border-amber-400' : ''}`}>
                  {/* 内容头部 */}
                  <div className="p-3 flex justify-between items-center">
                    <div className="flex items-center">
                      <div className={`p-1.5 rounded-full mr-2 ${item.type === 'video' ? 'bg-indigo-100 text-indigo-600' : 'bg-purple-100 text-purple-600'}`}>
                        {item.type === 'video' ? <Play size={16} /> : <Image size={16} />}
                      </div>
                      <div>
                        <h3 className="text-sm font-medium flex items-center">
                          {item.title}
                          {item.isPinned && (
                            <span className="ml-2 bg-amber-100 text-amber-700 text-xs px-1.5 py-0.5 rounded">置顶</span>
                          )}
                          {item.isHot && (
                            <span className="ml-2 bg-red-100 text-red-600 text-xs px-1.5 py-0.5 rounded flex items-center">
                              <TrendingUp size={10} className="mr-0.5" />
                              热门
                            </span>
                          )}
                        </h3>
                        <div className="text-xs text-gray-500">{item.timestamp}</div>
                      </div>
                    </div>
                    <div className="relative">
                      <button 
                        className="text-gray-400 p-1 rounded-full hover:bg-gray-100"
                        onClick={() => {
                          const menu = document.getElementById(`content-menu-${item.id}`);
                          if (menu) {
                            menu.classList.toggle('hidden');
                          }
                        }}
                      >
                        <MoreHorizontal size={16} />
                      </button>
                      
                      {/* 下拉菜单 */}
                      <div id={`content-menu-${item.id}`} className="absolute right-0 top-8 bg-white rounded-lg shadow-lg border border-gray-200 py-2 w-32 z-10 hidden">
                        <button 
                          onClick={() => handlePinContent(item.id)} 
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                          <Bookmark size={14} className="mr-2" />
                          {item.isPinned ? '取消置顶' : '置顶'}
                        </button>
                        <button 
                          onClick={() => handleDeleteContent(item.id)} 
                          className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center"
                        >
                          <Trash2 size={14} className="mr-2" />
                          删除
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  {/* 内容主体 */}
                  <div className="px-3 pb-2">
                    <p className="text-sm text-gray-700 mb-3">{item.content}</p>
                    
                    {/* 媒体内容 */}
                    <div className="relative rounded-lg overflow-hidden mb-3">
                      {item.type === 'video' ? (
                        <>
                          <div className="video-placeholder" style={{height: '180px'}}></div>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                              <Play size={22} className="text-white ml-1" />
                            </div>
                          </div>
                          <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-0.5 rounded">
                            {item.duration}
                          </div>
                        </>
                      ) : (
                        <div className="image-placeholder" style={{height: '220px'}}></div>
                      )}
                    </div>
                    
                    {/* 互动数据 */}
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-4">
                        <div className="flex items-center text-gray-500 text-xs">
                          <Eye size={14} className="mr-1" />
                          {formatNumber(item.statistics.views)}
                        </div>
                        <div className="flex items-center text-gray-500 text-xs">
                          <Heart size={14} className="mr-1" />
                          {formatNumber(item.statistics.likes)}
                        </div>
                        <div className="flex items-center text-gray-500 text-xs">
                          <MessageCircle size={14} className="mr-1" />
                          {item.statistics.comments}
                        </div>
                      </div>
                      <div className="flex space-x-1">
                        <button className="p-1.5 bg-purple-50 text-purple-600 rounded-full">
                          <Send size={14} />
                        </button>
                        <button className="p-1.5 bg-gray-100 text-gray-600 rounded-full">
                          <Bookmark size={14} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* 素材库标签内容 */}
            {selectedTab === 'assetlibrary' && (
              <div className="space-y-4 relative pb-16">
                {/* 任务列表面板 */}
                <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                  {/* 任务列表头部 */}
                  <div 
                    className="bg-gradient-to-r from-purple-50 to-indigo-50 p-3 flex justify-between items-center cursor-pointer"
                    onClick={() => setShowTaskList(!showTaskList)}
                  >
                    <div className="flex items-center">
                      <div className="p-1.5 bg-purple-100 text-purple-600 rounded-full mr-2">
                        <RotateCw size={16} className={`${generationTasks.some(t => t.status === 'processing') ? 'animate-spin' : ''}`} />
                      </div>
                      <div>
                        <h2 className="text-sm font-medium">素材生成任务</h2>
                        <div className="text-xs text-gray-500 flex items-center mt-0.5">
                          <span className="mr-2">共 {generationTasks.length} 个任务</span>
                          {generationTasks.some(t => t.status === 'processing') && (
                            <span className="bg-blue-100 text-blue-600 px-1.5 rounded-full flex items-center">
                              <Loader size={10} className="mr-1 animate-spin" />
                              处理中
                            </span>
                          )}
                          {generationTasks.some(t => t.status === 'completed') && (
                            <span className="bg-green-100 text-green-600 px-1.5 rounded-full flex items-center ml-1">
                              <CheckCircle size={10} className="mr-1" />
                              已完成
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className={`transition-transform duration-300 ${showTaskList ? 'rotate-180' : ''}`}>
                      <ChevronDown size={18} className="text-gray-500" />
                    </div>
                  </div>
                  
                  {/* 任务列表内容 */}
                  {showTaskList && (
                    <div className="p-3 bg-gray-50 max-h-80 overflow-y-auto">
                      {generationTasks.length > 0 ? (
                        generationTasks.map(task => renderTaskCard(task))
                      ) : (
                        <div className="text-center text-gray-500 py-6">
                          <div className="mb-2 flex justify-center">
                            <Activity size={32} className="text-gray-400" />
                          </div>
                          <p className="text-sm">暂无生成任务</p>
                          <p className="text-xs mt-1">点击下方"+"按钮创建新素材</p>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* 任务统计摘要 - 当列表折叠时显示 */}
                  {!showTaskList && generationTasks.length > 0 && (
                    <div className="px-3 py-2 bg-gray-50 border-t border-gray-100">
                      <div className="flex justify-between text-xs text-gray-500">
                        <div className="flex space-x-2">
                          <span className="flex items-center">
                            <Clock size={12} className="mr-1 text-amber-500" />
                            等待中: {generationTasks.filter(t => t.status === 'waiting').length}
                          </span>
                          <span className="flex items-center">
                            <Loader size={12} className="mr-1 text-blue-500" />
                            处理中: {generationTasks.filter(t => t.status === 'processing').length}
                          </span>
                          <span className="flex items-center">
                            <CheckCircle size={12} className="mr-1 text-green-500" />
                            已完成: {generationTasks.filter(t => t.status === 'completed').length}
                          </span>
                        </div>
                        <span className="flex items-center">
                          <AlertTriangle size={12} className="mr-1 text-red-500" />
                          失败: {generationTasks.filter(t => t.status === 'failed').length}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* 搜索和筛选栏 */}
                <div className="relative mb-3">
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-full pl-10 pr-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="搜索素材标题或ID (例: ID:101)"
                  />
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                
                <div className="flex justify-between items-center">
                  {/* 素材类型筛选 */}
                  <div className="flex space-x-2 overflow-x-auto pb-1 hide-scrollbar">
                    <button className="whitespace-nowrap px-3 py-1 rounded-full text-xs font-medium bg-purple-600 text-white shadow-sm flex items-center">
                      <Activity size={14} className="mr-1" />
                      全部
                    </button>
                    <button className="whitespace-nowrap px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 hover:bg-gray-200 flex items-center">
                      <Image size={14} className="mr-1" />
                      图片
                    </button>
                    <button className="whitespace-nowrap px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 hover:bg-gray-200 flex items-center">
                      <Video size={14} className="mr-1" />
                      视频
                    </button>
                    <button className="whitespace-nowrap px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 hover:bg-gray-200 flex items-center">
                      <Mic size={14} className="mr-1" />
                      语音
                    </button>
                    <button className="whitespace-nowrap px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 hover:bg-gray-200 flex items-center">
                      <Music size={14} className="mr-1" />
                      音乐
                    </button>
                  </div>
                  
                  {/* 排序选项 */}
                  <div className="relative ml-2 flex-shrink-0 sort-button-container">
                    <button
                      onClick={toggleSortOptions}
                      className="p-2 border border-gray-300 rounded-full bg-white text-gray-600 hover:bg-gray-50"
                      aria-label="排序选项"
                    >
                      {sortOptions.find(opt => opt.id === sortType)?.icon || <SortAsc size={16}/>}
                    </button>
                    {showSortOptions && (
                      <div className="absolute right-0 mt-1 w-36 bg-white rounded-md shadow-lg border border-gray-200 z-30">
                        {sortOptions.map(option => (
                          <button
                            key={option.id}
                            onClick={() => { setSortType(option.id); setShowSortOptions(false); }}
                            className={`w-full text-left px-3 py-2 text-sm flex items-center hover:bg-gray-100 ${sortType === option.id ? 'text-purple-600 font-medium' : 'text-gray-700'}`}
                          >
                            <span className="mr-2">{option.icon}</span>
                            {option.name}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                
                {/* 素材网格 */}
                <div className="grid grid-cols-2 gap-3 mt-2">
                  {assets.map((asset) => (
                    <div 
                      key={asset.id} 
                      className="relative rounded-lg overflow-hidden border bg-white shadow-sm transition-all hover:shadow-md cursor-pointer active:scale-95"
                      onClick={() => openPreviewModal(asset)}
                    >
                      <div className="relative aspect-[4/3] bg-gray-100">
                        {asset.type === 'image' || asset.type === 'video' ? (
                          <div className="absolute inset-0 image-placeholder flex items-center justify-center">
                            {asset.type === 'video' && <Video size={24} className="text-gray-500 opacity-50" />}
                            {asset.type === 'image' && <Image size={24} className="text-gray-500 opacity-50" />}
                          </div>
                        ) : (
                          <div className={`absolute inset-0 flex items-center justify-center ${asset.type === 'audio' ? 'bg-gradient-to-br from-indigo-50 to-purple-50' : asset.type === 'music' ? 'bg-gradient-to-br from-amber-50 to-orange-50' : 'bg-gradient-to-br from-gray-50 to-gray-100'}`}>
                            {asset.type === 'audio' && <Mic size={32} className="text-purple-400" />}
                            {asset.type === 'music' && <Music size={32} className="text-orange-400" />}
                          </div>
                        )}
                        <div className="absolute top-1.5 left-1.5 bg-black bg-opacity-60 text-white text-xs px-1.5 py-0.5 rounded flex items-center">
                          {getAssetTypeIcon(asset.type)}
                          {asset.duration && <span className="ml-1">{asset.duration}</span>}
                        </div>
                        <button
                          onClick={(e) => { 
                            e.stopPropagation(); // 阻止冒泡，防止触发父元素点击事件
                            openPreviewModal(asset); // 直接打开预览
                          }}
                          className="absolute bottom-1.5 right-1.5 p-1.5 bg-black bg-opacity-30 rounded-full text-white opacity-0 hover:opacity-100 hover:bg-opacity-60 transition-opacity"
                          title="预览"
                        >
                          <Eye size={14} />
                        </button>
                      </div>
                      
                      <div className="p-2">
                        <p className="text-sm font-medium text-gray-800 truncate" title={asset.title}>
                          {asset.title}
                        </p>
                        <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
                          <span className="flex items-center" title={asset.createdAt}>
                            <Clock size={12} className="mr-1" />
                            <span className="truncate">{asset.createdAt}</span>
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className="flex items-center" title="使用次数">
                              <CheckCircle size={12} className="mr-0.5" />
                              {asset.usedCount || 0}
                            </span>
                            <span className="flex items-center" title="点赞数">
                              <Heart size={12} className="mr-0.5" />
                              {asset.likes || 0}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* 创建素材按钮 */}
                <div className="relative asset-fab-container">
                  <button 
                    onClick={() => setShowAssetOptions(!showAssetOptions)}
                    className="fixed bottom-6 right-6 z-30 w-14 h-14 bg-gradient-to-br from-purple-600 to-indigo-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-105"
                    aria-label="创建素材"
                  >
                    <Plus size={24} />
                  </button>
                  
                  {/* 创建选项 */}
                  {showAssetOptions && (
                    <div className="fixed bottom-[88px] right-6 mb-2 flex flex-col items-end space-y-2 z-30">
                      <div className="space-y-2">
                        <button onClick={() => handleCreateAsset('图片')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                          <Image size={16} className="mr-2 text-blue-500"/> 创建图片
                        </button>
                        <button onClick={() => handleCreateAsset('视频')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                          <Video size={16} className="mr-2 text-red-500"/> 创建视频
                        </button>
                        <button onClick={() => handleCreateAsset('语音')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                          <Mic size={16} className="mr-2 text-green-500"/> 创建语音
                        </button>
                        <button onClick={() => handleCreateAsset('音乐')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                          <Music size={16} className="mr-2 text-orange-500"/> 创建音乐
                        </button>
                        <button onClick={() => handleCreateAsset('本地上传')} className="w-40 bg-white text-gray-700 px-3 py-2.5 rounded-lg shadow-md text-sm flex items-center justify-start hover:bg-gray-50 transition-all transform hover:-translate-x-1">
                          <Upload size={16} className="mr-2 text-indigo-500"/> 本地上传
                        </button>
                      </div>
                    </div>
                  )}
                </div>
          </div>
        )}
      {/* 素材预览模态框 */}
      <AssetPreviewModal 
        isOpen={isPreviewModalOpen}
        asset={previewAsset}
        onClose={closePreviewModal}
        onDownload={handleDownloadAsset}
        onDelete={handleDeleteAsset}
        onPublish={handlePublishFromAsset}
      />
      </div>
      
      {/* 样式定义 */}
      <style jsx>{`
        .video-placeholder {
          background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
          background-size: 20px 20px;
          position: relative;
        }
        .video-placeholder::after {
          content: '视频预览';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 12px;
          color: #666;
        }
        .image-placeholder {
          background: linear-gradient(45deg, #e6e6e6 25%, #f0f0f0 25%, #f0f0f0 50%, #e6e6e6 50%, #e6e6e6 75%, #f0f0f0 75%, #f0f0f0 100%);
          background-size: 20px 20px;
          position: relative;
        }
        .image-placeholder::after {
          content: '图片预览';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 12px;
          color: #666;
        }
        .hide-scrollbar::-webkit-scrollbar {
          display: none; /* Safari and Chrome */
        }
        .hide-scrollbar {
          -ms-overflow-style: none;  /* IE and Edge */
          scrollbar-width: none;  /* Firefox */
        }
        .animation-fade-in {
          animation: fadeIn 0.2s ease-out;
        }

        .animation-scale-in {
          animation: scaleIn 0.3s ease-out;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes scaleIn {
          from { transform: scale(0.95); opacity: 0; }
          to { transform: scale(1); opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default EnhancedCreatorView;